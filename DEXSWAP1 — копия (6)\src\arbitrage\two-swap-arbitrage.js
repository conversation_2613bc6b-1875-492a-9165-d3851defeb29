#!/usr/bin/env node

/**
 * 🔥 АРБИТРАЖ С 2 СВОПАМИ ДЛЯ ОСНОВНОЙ СИСТЕМЫ
 *
 * ИНТЕГРАЦИЯ С:
 * - MarginFi Flash Loans (44 ключа)
 * - Jupiter API (2 свопа по 20 ключей)
 * - Address Lookup Tables (ALT сжатие)
 * - Bundle System
 */

const { PublicKey, Transaction } = require('@solana/web3.js');

class TwoSwapArbitrage {
    constructor(mainSystem) {
        this.mainSystem = mainSystem;
        this.config = {
            flashLoanAmount: 10000,     // $10,000 USDC
            targetProfit: 50,           // $50 минимум
            keysPerSwap: 20,           // 20 ключей на своп
            totalSwaps: 2,             // 2 свопа
            marginFiKeys: 30,          // MarginFi ключи (официальные данные: 20-30)
            maxRetries: 3,             // Максимум попыток
            slippageBps: 50            // 0.5% slippage
        };

        this.tokens = {
            USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            SOL: 'So11111111111111111111111111111111111111112',
            USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
        };

        this.isRunning = false;
        this.opportunities = [];
        this.executedTrades = [];

        console.log('🔥 Two Swap Arbitrage инициализирован');
        console.log(`   💰 Торговая сумма: $${this.config.flashLoanAmount.toLocaleString()}`);
        console.log(`   🔑 Ключей: MarginFi(${this.config.marginFiKeys}) + Swaps(${this.config.keysPerSwap * this.config.totalSwaps}) = ${this.getTotalKeys()}`);
    }

    /**
     * 🔑 РАСЧЕТ ОБЩИХ КЛЮЧЕЙ
     */
    getTotalKeys() {
        return this.config.marginFiKeys + (this.config.keysPerSwap * this.config.totalSwaps);
    }

    /**
     * 🚀 ЗАПУСК АРБИТРАЖА
     */
    async start() {
        if (this.isRunning) {
            console.log('⚠️ Арбитраж уже запущен');
            return;
        }

        console.log('\n🚀 ЗАПУСК АРБИТРАЖА С 2 СВОПАМИ');
        console.log('═══════════════════════════════════════════════════════════════════════');

        this.isRunning = true;

        // Ждем готовности основных систем
        await this.waitForSystemReady();

        // Запускаем мониторинг возможностей
        this.startOpportunityMonitoring();

        console.log('✅ Арбитраж запущен и мониторит возможности');
    }

    /**
     * ⏳ ОЖИДАНИЕ ГОТОВНОСТИ СИСТЕМЫ
     */
    async waitForSystemReady() {
        console.log('\n⏳ Ожидание готовности основных систем...');

        const maxWait = 60; // 60 секунд
        let waited = 0;

        while (waited < maxWait) {
            const readiness = this.checkSystemReadiness();

            if (readiness.ready) {
                console.log('✅ Все системы готовы к арбитражу!');
                console.log(`   🏦 MarginFi: ${readiness.marginfi ? '✅' : '❌'}`);
                console.log(`   🔑 Wallet: ${readiness.wallet ? '✅' : '❌'}`);
                console.log(`   🌐 Connection: ${readiness.connection ? '✅' : '❌'}`);
                console.log(`   🪐 Jupiter: ${readiness.jupiter ? '✅' : '❌'}`);
                return;
            }

            console.log(`⏳ Ожидание систем... ${waited}/${maxWait}с (${readiness.missing.join(', ')})`);
            await this.delay(2000);
            waited += 2;
        }

        throw new Error('Системы не готовы к арбитражу в течение 60 секунд');
    }

    /**
     * 🔍 ПРОВЕРКА ГОТОВНОСТИ СИСТЕМЫ
     */
    checkSystemReadiness() {
        const checks = {
            marginfi: !!(this.mainSystem.marginfiFlashLoan || this.mainSystem.tradingExecutor?.marginfiFlashLoan),
            wallet: !!(this.mainSystem.wallet || this.mainSystem.tradingExecutor?.wallet),
            connection: !!this.mainSystem.connection,
            jupiter: true // Jupiter API всегда доступен
        };

        const missing = Object.entries(checks)
            .filter(([key, ready]) => !ready)
            .map(([key]) => key);

        return {
            ready: missing.length === 0,
            missing,
            ...checks
        };
    }

    /**
     * 📊 МОНИТОРИНГ АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ
     */
    startOpportunityMonitoring() {
        console.log('\n📊 Запуск мониторинга арбитражных возможностей...');

        // Проверяем возможности каждые 30 секунд
        setInterval(async () => {
            try {
                await this.scanForOpportunities();
            } catch (error) {
                console.error('❌ Ошибка сканирования возможностей:', error.message);
            }
        }, 30000);

        // Первое сканирование сразу
        setTimeout(() => this.scanForOpportunities(), 5000);
    }

    /**
     * 🔍 СКАНИРОВАНИЕ АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ
     */
    async scanForOpportunities() {
        console.log('\n🔍 Сканирование арбитражных возможностей...');

        try {
            // Получаем quotes от Jupiter для 2 свопов
            const opportunity = await this.getJupiterArbitrageOpportunity();

            if (opportunity.isProfitable) {
                console.log(`💰 НАЙДЕНА ПРИБЫЛЬНАЯ ВОЗМОЖНОСТЬ: $${opportunity.netProfit.toFixed(2)}`);

                // Выполняем арбитраж
                await this.executeArbitrage(opportunity);
            } else {
                console.log(`📊 Возможность найдена, но убыточна: $${opportunity.netProfit.toFixed(2)}`);
            }

        } catch (error) {
            console.error('❌ Ошибка сканирования:', error.message);
        }
    }

    /**
     * 🪐 ПОЛУЧЕНИЕ АРБИТРАЖНОЙ ВОЗМОЖНОСТИ ОТ JUPITER
     */
    async getJupiterArbitrageOpportunity() {
        const usdcAmount = this.config.flashLoanAmount * 1000000; // В micro-USDC

        try {
            // Quote 1: USDC → SOL
            const quote1 = await this.getJupiterQuote(
                this.tokens.USDC,
                this.tokens.SOL,
                usdcAmount
            );

            if (!quote1.success) {
                throw new Error('Не удалось получить quote для USDC → SOL');
            }

            // Quote 2: SOL → USDC
            const quote2 = await this.getJupiterQuote(
                this.tokens.SOL,
                this.tokens.USDC,
                quote1.outAmount
            );

            if (!quote2.success) {
                throw new Error('Не удалось получить quote для SOL → USDC');
            }

            // Анализ прибыльности
            const analysis = this.analyzeProfitability(quote1, quote2);

            return {
                quote1,
                quote2,
                ...analysis,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            console.error('❌ Ошибка получения Jupiter quotes:', error.message);
            return { isProfitable: false, error: error.message };
        }
    }

    /**
     * 📡 ПОЛУЧЕНИЕ JUPITER QUOTE
     */
    async getJupiterQuote(inputMint, outputMint, amount) {
        try {
            const params = new URLSearchParams({
                inputMint,
                outputMint,
                amount: amount.toString(),
                slippageBps: this.config.slippageBps.toString(),
                maxAccounts: '40'
            });

            const response = await fetch(`https://lite-api.jup.ag/swap/v1/quote?${params}`);

            if (!response.ok) {
                throw new Error(`Jupiter Quote API error: ${response.status}`);
            }

            const data = await response.json();
            return { ...data, success: true };

        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 📈 АНАЛИЗ ПРИБЫЛЬНОСТИ
     */
    analyzeProfitability(quote1, quote2) {
        const startAmount = this.config.flashLoanAmount;
        const finalAmount = quote2.outAmount / 1000000; // Конвертируем в USDC

        const grossProfit = finalAmount - startAmount;
        const flashLoanFee = startAmount * 0.0009; // 0.09% MarginFi
        const gasFee = 0.02; // $0.02 gas
        const totalFees = flashLoanFee + gasFee;
        const netProfit = grossProfit - totalFees;

        const isProfitable = netProfit >= this.config.targetProfit;

        return {
            startAmount,
            finalAmount,
            grossProfit,
            netProfit,
            totalFees,
            isProfitable,
            profitPercent: (netProfit / startAmount) * 100
        };
    }

    /**
     * ⚛️ ВЫПОЛНЕНИЕ АРБИТРАЖА
     */
    async executeArbitrage(opportunity) {
        console.log('\n⚛️ ВЫПОЛНЕНИЕ АРБИТРАЖА...');
        console.log(`💰 Ожидаемая прибыль: $${opportunity.netProfit.toFixed(2)}`);

        try {
            // 1. Получаем swap instructions от Jupiter
            const swapInstructions1 = await this.getJupiterSwapInstructions(opportunity.quote1);
            const swapInstructions2 = await this.getJupiterSwapInstructions(opportunity.quote2);

            // 2. Создаем атомарную транзакцию
            const atomicTransaction = await this.createAtomicTransaction(
                swapInstructions1,
                swapInstructions2,
                opportunity
            );

            // 3. Выполняем транзакцию
            const signature = await this.executeTransaction(atomicTransaction);

            console.log(`✅ Арбитраж выполнен успешно!`);
            console.log(`🔗 Signature: ${signature}`);
            console.log(`💰 Прибыль: $${opportunity.netProfit.toFixed(2)}`);

            // Сохраняем в статистику
            this.executedTrades.push({
                ...opportunity,
                signature,
                executedAt: new Date().toISOString(),
                status: 'success'
            });

        } catch (error) {
            console.error('❌ Ошибка выполнения арбитража:', error.message);
        }
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ JUPITER SWAP INSTRUCTIONS
     */
    async getJupiterSwapInstructions(quote) {
        try {
            const wallet = this.mainSystem.wallet || this.mainSystem.tradingExecutor?.wallet;
            if (!wallet) {
                throw new Error('Wallet не найден');
            }

            // 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПАРАМЕТРЫ БЕЗ SYSTEM PROGRAM ОШИБОК!
            const swapRequest = {
                quoteResponse: quote,
                userPublicKey: wallet.publicKey.toString(),

                // 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДЛЯ SOL ТРАНЗАКЦИЙ НУЖЕН wrapAndUnwrapSol!
                wrapAndUnwrapSol: true,                // ✅ ОБЯЗАТЕЛЬНО для SOL транзакций!
                useTokenLedger: false,                 // ❌ НЕ создавать token ledger инструкции!
                skipUserAccountsRpcCalls: true,        // ✅ Пропускаем RPC вызовы
                asLegacyTransaction: false,            // ✅ Versioned Transactions
                computeUnitPriceMicroLamports: 1000
            };

            const response = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(swapRequest)
            });

            if (!response.ok) {
                throw new Error(`Jupiter Swap Instructions error: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            throw new Error(`Ошибка получения swap instructions: ${error.message}`);
        }
    }

    /**
     * ⚛️ СОЗДАНИЕ АТОМАРНОЙ ТРАНЗАКЦИИ
     */
    async createAtomicTransaction(swapInstructions1, swapInstructions2, opportunity) {
        console.log('⚛️ Создание атомарной транзакции...');

        // Используем существующий atomic transaction builder
        if (this.mainSystem.atomicTransactionBuilder) {
            return await this.mainSystem.atomicTransactionBuilder.createArbitrageTransaction({
                flashLoanAmount: this.config.flashLoanAmount,
                swapInstructions1,
                swapInstructions2,
                expectedProfit: opportunity.netProfit
            });
        }

        // 🚫 FALLBACK ЗАПРЕЩЕН! ТОЛЬКО АТОМАРНЫЕ ТРАНЗАКЦИИ!
        console.log('🚫 FALLBACK К ПРОСТОЙ ТРАНЗАКЦИИ ЗАПРЕЩЕН!');
        console.log('💡 Причина: Система должна работать только через атомарные транзакции');

        throw new Error('Atomic transaction builder недоступен. Fallback запрещен!');
        if (swapInstructions1.swapInstruction) {
            transaction.add(swapInstructions1.swapInstruction);
        }
        if (swapInstructions2.swapInstruction) {
            transaction.add(swapInstructions2.swapInstruction);
        }

        return transaction;
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ ТРАНЗАКЦИИ
     */
    async executeTransaction(transaction) {
        const wallet = this.mainSystem.wallet || this.mainSystem.tradingExecutor?.wallet;
        const connection = this.mainSystem.connection;

        if (!wallet || !connection) {
            throw new Error('Wallet или Connection не найдены');
        }

        // Подписываем и отправляем транзакцию
        transaction.feePayer = wallet.publicKey;

        // 🔧 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ SOLANA RPC ДЛЯ BLOCKHASH
        const solanaRpcConnection = new (require('@solana/web3.js').Connection)('https://api.mainnet-beta.solana.com', 'confirmed');
        const { blockhash } = await solanaRpcConnection.getLatestBlockhash('confirmed');
        transaction.recentBlockhash = blockhash;

        transaction.sign(wallet);

        const signature = await connection.sendRawTransaction(transaction.serialize());

        // 🚀 НЕ ЖДЕМ ПОДТВЕРЖДЕНИЯ - МАКСИМАЛЬНАЯ СКОРОСТЬ!
        console.log(`✅ Транзакция отправлена: ${signature}`);
        console.log(`🌐 Explorer: https://solscan.io/tx/${signature}`);

        return signature;
    }

    /**
     * 📊 СТАТИСТИКА АРБИТРАЖА
     */
    getStats() {
        const totalTrades = this.executedTrades.length;
        const totalProfit = this.executedTrades.reduce((sum, trade) => sum + trade.netProfit, 0);
        const avgProfit = totalTrades > 0 ? totalProfit / totalTrades : 0;

        return {
            totalTrades,
            totalProfit,
            avgProfit,
            isRunning: this.isRunning,
            totalKeys: this.getTotalKeys()
        };
    }

    /**
     * ⏰ ЗАДЕРЖКА
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 🛑 ОСТАНОВКА АРБИТРАЖА
     */
    stop() {
        this.isRunning = false;
        console.log('🛑 Арбитраж остановлен');
    }
}

module.exports = TwoSwapArbitrage;
