const { Connection, Keypair } = require('@solana/web3.js');
const fs = require('fs');

async function checkBalance() {
  try {
    // Загружаем wallet.json
    const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
    const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
    const address = keypair.publicKey.toString();
    
    console.log(`📍 Адрес кошелька: ${address}`);
    
    // Подключаемся к RPC
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const balance = await connection.getBalance(keypair.publicKey);
    const solBalance = balance / 1e9;
    
    console.log(`💰 Баланс: ${balance} lamports`);
    console.log(`💰 Баланс: ${solBalance.toFixed(6)} SOL`);
    console.log(`💰 Баланс: ~$${(solBalance * 153).toFixed(2)} USD`);
    
    if (balance > 0) {
      console.log('🎉 УСПЕХ! Баланс найден!');
    } else {
      console.log('⚠️ Баланс равен нулю');
    }
    
  } catch (error) {
    console.error(`❌ Ошибка: ${error.message}`);
  }
}

checkBalance();
