/**
 * 🔍 АНАЛИЗАТОР НЕПОКРЫТЫХ КЛЮЧЕЙ
 * 
 * Находит все ключи которые НЕ покрываются Jupiter ALT
 * и показывает что именно нужно добавить в кастомную ALT
 */

const { Connection, PublicKey, Keypair, clusterApiUrl } = require('@solana/web3.js');

class UncoveredKeysAnalyzer {
  constructor() {
    this.connection = new Connection(clusterApiUrl('mainnet-beta'), 'confirmed');
    this.wallet = {
      publicKey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'),
      payer: Keypair.generate()
    };
    
    console.log('🔍 Анализатор непокрытых ключей инициализирован');
  }

  /**
   * 🔍 ГЛАВНЫЙ АНАЛИЗ НЕПОКРЫТЫХ КЛЮЧЕЙ
   */
  async analyzeUncoveredKeys() {
    try {
      console.log('\n🔍 ===== АНАЛИЗ НЕПОКРЫТЫХ КЛЮЧЕЙ =====\n');

      // 1. СОЗДАЕМ ТЕСТОВЫЕ ИНСТРУКЦИИ
      console.log('1️⃣ Создаем тестовые Flash Loan + Jupiter инструкции...');
      const testInstructions = await this.createTestInstructions();
      console.log(`✅ Создано ${testInstructions.length} тестовых инструкций`);

      // 2. ПОЛУЧАЕМ JUPITER ALT
      console.log('\n2️⃣ Получаем Jupiter ALT...');
      const jupiterALT = await this.getJupiterALT();
      console.log(`✅ Получено ${jupiterALT.length} Jupiter ALT таблиц`);

      // 3. АНАЛИЗИРУЕМ ПОКРЫТИЕ
      console.log('\n3️⃣ Анализируем покрытие ключей...');
      const analysis = await this.analyzeCoverage(testInstructions, jupiterALT);

      // 4. ПОКАЗЫВАЕМ РЕЗУЛЬТАТЫ
      console.log('\n4️⃣ Результаты анализа:');
      this.displayResults(analysis);

      // 5. СОЗДАЕМ СПИСОК ДЛЯ КАСТОМНОЙ ALT
      console.log('\n5️⃣ Создаем список для кастомной ALT...');
      this.createCustomALTList(analysis.uncoveredKeys);

      return analysis;

    } catch (error) {
      console.error(`❌ Ошибка анализа: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔧 СОЗДАНИЕ ТЕСТОВЫХ ИНСТРУКЦИЙ
   */
  async createTestInstructions() {
    const instructions = [];

    // MarginFi Flash Loan инструкции (симуляция)
    const marginFiKeys = [
      'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA', // MarginFi Program
      '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8', // MarginFi Group
      '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB', // USDC Bank
      'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh', // SOL Bank
      'HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV', // USDT Bank
      this.wallet.publicKey.toString(),                    // User Wallet
      '********************************************', // MarginFi Account
    ];

    // Системные программы
    const systemKeys = [
      '********************************',                    // System Program
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',        // Token Program
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',        // Associated Token Program
      'ComputeBudget111111111111111111111111111111',         // Compute Budget Program
      'Sysvar1nstructions1111111111111111111111111',         // Sysvar Instructions
    ];

    // Токены
    const tokenKeys = [
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',        // USDC
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',        // USDT
      'So********************************111111112',         // SOL
    ];

    // Создаем фейковые инструкции с этими ключами
    const allKeys = [...marginFiKeys, ...systemKeys, ...tokenKeys];
    
    // Группируем по типам инструкций
    const instructionGroups = [
      { name: 'MarginFi Borrow', keys: marginFiKeys.slice(0, 10) },
      { name: 'Jupiter Swap', keys: [...systemKeys, ...tokenKeys] },
      { name: 'MarginFi Repay', keys: marginFiKeys.slice(5) },
      { name: 'System Instructions', keys: systemKeys }
    ];

    instructionGroups.forEach((group, index) => {
      const instruction = {
        programId: new PublicKey(group.keys[0]),
        keys: group.keys.map(key => ({
          pubkey: new PublicKey(key),
          isSigner: false,
          isWritable: false
        })),
        data: Buffer.alloc(0)
      };
      
      instructions.push(instruction);
      console.log(`   ${index + 1}. ${group.name}: ${group.keys.length} ключей`);
    });

    return instructions;
  }

  /**
   * 🪐 ПОЛУЧЕНИЕ JUPITER ALT (СИМУЛЯЦИЯ)
   */
  async getJupiterALT() {
    // Симулируем Jupiter ALT с типичными адресами
    const jupiterAddresses = [
      'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4', // Jupiter Program
      'D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf', // Jupiter Route
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (может быть в Jupiter ALT)
      // ... Jupiter обычно не включает MarginFi и пользовательские ключи
    ];

    const { AddressLookupTableAccount } = require('@solana/web3.js');
    
    const jupiterALT = new AddressLookupTableAccount({
      key: Keypair.generate().publicKey,
      state: {
        addresses: jupiterAddresses.map(addr => new PublicKey(addr)),
        authority: new PublicKey('********************************'),
        deactivationSlot: BigInt('18446744073709551615'),
        lastExtendedSlot: 0,
        lastExtendedSlotStartIndex: 0
      }
    });

    console.log(`   Jupiter ALT: ${jupiterAddresses.length} адресов`);
    
    return [jupiterALT];
  }

  /**
   * 📊 АНАЛИЗ ПОКРЫТИЯ КЛЮЧЕЙ
   */
  async analyzeCoverage(instructions, jupiterALT) {
    // Собираем все ключи из инструкций
    const allInstructionKeys = new Set();
    const keysByInstruction = [];

    instructions.forEach((instruction, index) => {
      const instructionKeys = [];
      
      // Program ID
      allInstructionKeys.add(instruction.programId.toString());
      instructionKeys.push({
        type: 'programId',
        address: instruction.programId.toString(),
        instruction: index
      });

      // Account keys
      if (instruction.keys) {
        instruction.keys.forEach((key, keyIndex) => {
          allInstructionKeys.add(key.pubkey.toString());
          instructionKeys.push({
            type: 'accountKey',
            address: key.pubkey.toString(),
            instruction: index,
            keyIndex: keyIndex
          });
        });
      }

      keysByInstruction.push(instructionKeys);
    });

    // Собираем все ключи из Jupiter ALT
    const jupiterALTKeys = new Set();
    jupiterALT.forEach(alt => {
      alt.state.addresses.forEach(addr => {
        jupiterALTKeys.add(addr.toString());
      });
    });

    // Находим покрытые и непокрытые ключи
    const coveredKeys = [];
    const uncoveredKeys = [];

    Array.from(allInstructionKeys).forEach(key => {
      if (jupiterALTKeys.has(key)) {
        coveredKeys.push(key);
      } else {
        uncoveredKeys.push(key);
      }
    });

    return {
      totalKeys: allInstructionKeys.size,
      jupiterALTKeys: jupiterALTKeys.size,
      coveredKeys,
      uncoveredKeys,
      keysByInstruction,
      coveragePercent: Math.round((coveredKeys.length / allInstructionKeys.size) * 100)
    };
  }

  /**
   * 📊 ОТОБРАЖЕНИЕ РЕЗУЛЬТАТОВ
   */
  displayResults(analysis) {
    console.log('═'.repeat(60));
    console.log('📊 РЕЗУЛЬТАТЫ АНАЛИЗА ПОКРЫТИЯ КЛЮЧЕЙ');
    console.log('═'.repeat(60));
    
    console.log(`📊 Всего ключей в инструкциях: ${analysis.totalKeys}`);
    console.log(`🪐 Jupiter ALT ключей: ${analysis.jupiterALTKeys}`);
    console.log(`✅ Покрыто Jupiter ALT: ${analysis.coveredKeys.length} (${analysis.coveragePercent}%)`);
    console.log(`❌ НЕ покрыто: ${analysis.uncoveredKeys.length} ключей`);
    
    console.log('\n✅ ПОКРЫТЫЕ КЛЮЧИ (Jupiter ALT):');
    analysis.coveredKeys.forEach((key, index) => {
      const name = this.getKeyName(key);
      console.log(`   ${index + 1}. ${key.slice(0, 8)}... (${name})`);
    });

    console.log('\n❌ НЕПОКРЫТЫЕ КЛЮЧИ (нужны в кастомной ALT):');
    analysis.uncoveredKeys.forEach((key, index) => {
      const name = this.getKeyName(key);
      console.log(`   ${index + 1}. ${key.slice(0, 8)}... (${name})`);
    });

    console.log('\n💰 ЭКОНОМИЯ ОТ КАСТОМНОЙ ALT:');
    const savings = analysis.uncoveredKeys.length * 31; // 32 байта -> 1 байт
    console.log(`   Экономия: ${analysis.uncoveredKeys.length} × 31 байт = ${savings} байт`);
    console.log(`   Стоимость ALT: ~$0.45 (один раз)`);
    console.log(`   Окупается: с первой транзакции!`);
  }

  /**
   * 📋 СОЗДАНИЕ СПИСКА ДЛЯ КАСТОМНОЙ ALT
   */
  createCustomALTList(uncoveredKeys) {
    console.log('═'.repeat(60));
    console.log('📋 СПИСОК ДЛЯ КАСТОМНОЙ ALT');
    console.log('═'.repeat(60));

    const customALTList = uncoveredKeys.map(key => ({
      address: key,
      name: this.getKeyName(key),
      category: this.getKeyCategory(key)
    }));

    // Группируем по категориям
    const categories = {};
    customALTList.forEach(item => {
      if (!categories[item.category]) {
        categories[item.category] = [];
      }
      categories[item.category].push(item);
    });

    Object.keys(categories).forEach(category => {
      console.log(`\n📂 ${category.toUpperCase()}:`);
      categories[category].forEach((item, index) => {
        console.log(`   ${index + 1}. '${item.address}', // ${item.name}`);
      });
    });

    console.log(`\n📊 ИТОГО: ${uncoveredKeys.length} адресов для кастомной ALT`);
    console.log(`📊 Свободных слотов: ${256 - uncoveredKeys.length} (из 256)`);

    // Сохраняем в файл
    const fs = require('fs');
    const customALTConfig = {
      version: '1.0',
      created: new Date().toISOString(),
      description: 'Custom ALT for uncovered static keys',
      addresses: customALTList,
      stats: {
        total: uncoveredKeys.length,
        freeSlots: 256 - uncoveredKeys.length,
        categories: Object.keys(categories).map(cat => ({
          name: cat,
          count: categories[cat].length
        }))
      }
    };

    fs.writeFileSync('custom-alt-config.json', JSON.stringify(customALTConfig, null, 2));
    console.log('\n✅ Конфигурация сохранена в custom-alt-config.json');
  }

  /**
   * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ КЛЮЧА
   */
  getKeyName(address) {
    const names = {
      '********************************': 'System Program',
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
      'ComputeBudget111111111111111111111111111111': 'Compute Budget Program',
      'Sysvar1nstructions1111111111111111111111111': 'Sysvar Instructions',
      'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi Program',
      '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8': 'MarginFi Group',
      '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB': 'USDC Bank',
      'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh': 'SOL Bank',
      'HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV': 'USDT Bank',
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC Token',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT Token',
      'So********************************111111112': 'SOL Token',
      'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter Program',
      'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV': 'User Wallet',
      '********************************************': 'MarginFi Account'
    };

    return names[address] || 'Unknown Address';
  }

  /**
   * 📂 ПОЛУЧЕНИЕ КАТЕГОРИИ КЛЮЧА
   */
  getKeyCategory(address) {
    const systemPrograms = [
      '********************************',
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
      'ComputeBudget111111111111111111111111111111',
      'Sysvar1nstructions1111111111111111111111111'
    ];

    const marginFiKeys = [
      'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
      '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8',
      '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB',
      'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',
      'HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV'
    ];

    const tokens = [
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'So********************************111111112'
    ];

    const userKeys = [
      'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
      '********************************************'
    ];

    if (systemPrograms.includes(address)) return 'system';
    if (marginFiKeys.includes(address)) return 'marginfi';
    if (tokens.includes(address)) return 'tokens';
    if (userKeys.includes(address)) return 'user';
    
    return 'other';
  }
}

// Запуск анализа
async function main() {
  try {
    const analyzer = new UncoveredKeysAnalyzer();
    await analyzer.analyzeUncoveredKeys();
    console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
  } catch (error) {
    console.error('\n💥 АНАЛИЗ ПРОВАЛЕН:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = UncoveredKeysAnalyzer;
