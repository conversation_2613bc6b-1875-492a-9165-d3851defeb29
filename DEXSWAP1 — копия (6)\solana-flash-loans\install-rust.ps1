# 🦀 УСТАНОВКА RUST TOOLCHAIN
# Скрипт для установки Rust и необходимых компонентов

Write-Host "🦀 Installing Rust toolchain for Solana arbitrage engine..." -ForegroundColor Green

# Проверяем, установлен ли уже Rust
if (Get-Command "cargo" -ErrorAction SilentlyContinue) {
    Write-Host "✅ Rust is already installed" -ForegroundColor Green
    cargo --version
    rustc --version
} else {
    Write-Host "📥 Downloading and installing Rust..." -ForegroundColor Yellow

    # Скачиваем и запускаем rustup-init
    $rustupUrl = "https://win.rustup.rs/x86_64"
    $rustupPath = "$env:TEMP\rustup-init.exe"

    try {
        Invoke-WebRequest -Uri $rustupUrl -OutFile $rustupPath
        Write-Host "✅ Downloaded rustup-init.exe" -ForegroundColor Green

        # Запускаем установку с параметрами по умолчанию
        & $rustupPath -y --default-toolchain stable

        # Обновляем PATH для текущей сессии
        $cargoPath = "$env:USERPROFILE\.cargo\bin"
        $env:PATH += ";$cargoPath"

        Write-Host "✅ Rust installed successfully!" -ForegroundColor Green

        # Проверяем установку
        cargo --version
        rustc --version

    } catch {
        Write-Host "❌ Failed to install Rust: $_" -ForegroundColor Red
        exit 1
    } finally {
        # Удаляем временный файл
        if (Test-Path $rustupPath) {
            Remove-Item $rustupPath
        }
    }
}

# Добавляем необходимые компоненты
Write-Host "🔧 Adding required Rust components..." -ForegroundColor Yellow

$components = @(
    "clippy",
    "rustfmt"
)

foreach ($component in $components) {
    Write-Host "Adding $component..." -ForegroundColor Cyan
    rustup component add $component
}

# Устанавливаем napi-cli для Node.js интеграции
Write-Host "📦 Installing napi-cli..." -ForegroundColor Yellow
if (Get-Command "npm" -ErrorAction SilentlyContinue) {
    npm install -g @napi-rs/cli
    Write-Host "✅ napi-cli installed" -ForegroundColor Green
} else {
    Write-Host "⚠️  npm not found. Please install Node.js first." -ForegroundColor Yellow
}

Write-Host "🎉 Rust setup completed!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Restart your terminal or run: refreshenv" -ForegroundColor White
Write-Host "   2. Navigate to rust-engine directory" -ForegroundColor White
Write-Host "   3. Run: cargo check" -ForegroundColor White
