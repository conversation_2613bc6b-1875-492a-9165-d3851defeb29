/**
 * 🔍 ПРЯМАЯ ПРОВЕРКА ПОЗИЦИЙ ЧЕРЕЗ RPC
 */

require('dotenv').config();
const { Connection, PublicKey } = require('@solana/web3.js');

async function checkPositionsDirectly() {
    console.log('🔍 ПРЯМАЯ ПРОВЕРКА ПОЗИЦИЙ...\n');
    
    const connection = new Connection(process.env.QUICKNODE_RPC_URL, 'confirmed');
    console.log(`🚀 Используем QuickNode RPC: ${process.env.QUICKNODE_RPC_URL.slice(0, 50)}...`);
    
    const positions = [
        '6J6FngedRDg9ZtfctTH4o6895DyNZXDq2i3vtYyREF4U',
        'HVqWWZFsRT2oqAF6ip4W4GomwABevdCtAZUmU5ak4cLd'
    ];
    
    for (let i = 0; i < positions.length; i++) {
        const pos = positions[i];
        console.log(`\n=== ПОЗИЦИЯ ${i+1}: ${pos} ===`);
        
        try {
            const account = await connection.getAccountInfo(new PublicKey(pos));
            if (account) {
                console.log(`✅ Позиция существует!`);
                console.log(`   Owner: ${account.owner.toString()}`);
                console.log(`   Data length: ${account.data.length} bytes`);
                console.log(`   Lamports: ${account.lamports}`);
                console.log(`   Executable: ${account.executable}`);
                
                // Проверяем что owner это Meteora DLMM программа
                const meteoraDLMMProgram = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
                if (account.owner.toString() === meteoraDLMMProgram) {
                    console.log(`   ✅ Owner корректный - это Meteora DLMM программа`);
                } else {
                    console.log(`   ⚠️ Owner НЕ Meteora DLMM программа`);
                }
            } else {
                console.log(`❌ Позиция НЕ существует!`);
            }
        } catch (error) {
            console.log(`❌ Ошибка: ${error.message}`);
        }
    }
    
    console.log('\n🔍 ПРОВЕРКА ЧЕРЕЗ METEORA DLMM SDK...');
    
    try {
        const DLMM = require('@meteora-ag/dlmm').default;
        const wallet = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        
        const pools = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'  // Pool 2
        ];
        
        for (let i = 0; i < pools.length; i++) {
            console.log(`\n--- POOL ${i+1}: ${pools[i]} ---`);
            try {
                const dlmm = await DLMM.create(connection, new PublicKey(pools[i]));
                console.log(`✅ DLMM пул загружен`);
                console.log(`   Active Bin ID: ${dlmm.lbPair.activeId}`);
                console.log(`   Bin Step: ${dlmm.lbPair.binStep}`);
                
                const { userPositions } = await dlmm.getPositionsByUserAndLbPair(wallet);
                console.log(`   Найдено позиций пользователя: ${userPositions.length}`);
                
                userPositions.forEach((pos, idx) => {
                    console.log(`     Позиция ${idx+1}: ${pos.publicKey.toString()}`);
                    const binData = pos.positionData.positionBinData;
                    console.log(`       Bins: ${binData.length}`);
                    
                    let totalX = 0, totalY = 0;
                    binData.forEach(bin => {
                        totalX += parseInt(bin.xAmount || 0);
                        totalY += parseInt(bin.yAmount || 0);
                    });
                    console.log(`       Total X: ${totalX}, Total Y: ${totalY}`);
                });
                
            } catch (error) {
                console.log(`   ❌ Ошибка DLMM: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.log(`❌ Ошибка загрузки DLMM SDK: ${error.message}`);
    }
    
    console.log('\n✅ ПРОВЕРКА ЗАВЕРШЕНА!');
}

checkPositionsDirectly().catch(console.error);
