#!/usr/bin/env node

/**
 * 🔧 ЕДИНЫЙ НОРМАЛИЗАТОР ИНСТРУКЦИЙ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Единая точка нормализации ВСЕХ инструкций
 * 📋 ПОДДЕРЖКА: Jupiter, MarginFi, Orca, Raydium, Meteora
 * 🔧 ФУНКЦИИ: Конвертация форматов, валидация, стандартизация
 * 
 * АРХИТЕКТУРА:
 * Raw Instructions → Unified Normalizer → Standard Solana Instructions
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');

class UnifiedInstructionNormalizer {
  constructor() {
    this.stats = {
      normalized: 0,
      errors: 0,
      jupiterInstructions: 0,
      marginfiInstructions: 0,
      solanaInstructions: 0,
      otherInstructions: 0
    };
    
    console.log('🔧 ЕДИНЫЙ НОРМАЛИЗАТОР ИНСТРУКЦИЙ ИНИЦИАЛИЗИРОВАН');
  }

  /**
   * 🎯 ГЛАВНАЯ ФУНКЦИЯ: НОРМАЛИЗАЦИЯ ЛЮБОЙ ИНСТРУКЦИИ
   * @param {Object|TransactionInstruction} instruction - Инструкция в любом формате
   * @param {string} source - Источник инструкции (Jupiter, MarginFi, etc.)
   * @returns {TransactionInstruction} - Стандартная Solana инструкция
   */
  normalizeInstruction(instruction, source = 'UNKNOWN') {
    try {
      // Если уже TransactionInstruction - возвращаем как есть
      if (instruction instanceof TransactionInstruction) {
        this.stats.solanaInstructions++;
        return instruction;
      }

      // Определяем тип инструкции и нормализуем
      if (this.isJupiterInstruction(instruction)) {
        this.stats.jupiterInstructions++;
        return this.normalizeJupiterInstruction(instruction);
      }

      if (this.isMarginFiInstruction(instruction)) {
        this.stats.marginfiInstructions++;
        return this.normalizeMarginFiInstruction(instruction);
      }

      // Общая нормализация для остальных
      this.stats.otherInstructions++;
      return this.normalizeGenericInstruction(instruction, source);

    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Ошибка нормализации инструкции (${source}): ${error.message}`);
      throw new Error(`Failed to normalize instruction from ${source}: ${error.message}`);
    }
  }

  /**
   * 🪐 НОРМАЛИЗАЦИЯ JUPITER ИНСТРУКЦИИ
   */
  normalizeJupiterInstruction(instruction) {
    console.log(`🪐 Нормализация Jupiter инструкции...`);

    // Валидация обязательных полей
    if (!instruction.programId) {
      throw new Error('Jupiter instruction missing programId');
    }

    // Нормализация programId
    const programId = this.normalizePublicKey(instruction.programId);

    // Нормализация accounts/keys
    let keys = [];
    if (instruction.accounts && Array.isArray(instruction.accounts)) {
      // Jupiter формат: accounts
      keys = this.normalizeJupiterAccounts(instruction.accounts);
    } else if (instruction.keys && Array.isArray(instruction.keys)) {
      // Уже правильный формат: keys
      keys = this.normalizeAccountMetas(instruction.keys);
    }

    // Нормализация data
    const data = this.normalizeInstructionData(instruction.data);

    const normalized = new TransactionInstruction({
      programId,
      keys,
      data
    });

    console.log(`✅ Jupiter инструкция нормализована: ${programId.toString().slice(0, 8)}...`);
    this.stats.normalized++;
    return normalized;
  }

  /**
   * 🏦 НОРМАЛИЗАЦИЯ MARGINFI ИНСТРУКЦИИ
   */
  normalizeMarginFiInstruction(instruction) {
    console.log(`🏦 Нормализация MarginFi инструкции...`);

    // MarginFi инструкции обычно уже в правильном формате
    if (instruction instanceof TransactionInstruction) {
      return instruction;
    }

    // Если это объект, нормализуем как обычную инструкцию
    return this.normalizeGenericInstruction(instruction, 'MARGINFI');
  }

  /**
   * 🔧 ОБЩАЯ НОРМАЛИЗАЦИЯ ИНСТРУКЦИИ
   */
  normalizeGenericInstruction(instruction, source) {
    console.log(`🔧 Общая нормализация инструкции (${source})...`);

    if (!instruction.programId) {
      throw new Error(`Generic instruction missing programId (${source})`);
    }

    const programId = this.normalizePublicKey(instruction.programId);
    
    let keys = [];
    if (instruction.accounts) {
      keys = this.normalizeJupiterAccounts(instruction.accounts);
    } else if (instruction.keys) {
      keys = this.normalizeAccountMetas(instruction.keys);
    }

    const data = this.normalizeInstructionData(instruction.data);

    const normalized = new TransactionInstruction({
      programId,
      keys,
      data
    });

    console.log(`✅ Общая инструкция нормализована (${source}): ${programId.toString().slice(0, 8)}...`);
    this.stats.normalized++;
    return normalized;
  }

  /**
   * 🔍 ОПРЕДЕЛЕНИЕ ТИПА ИНСТРУКЦИИ
   */
  isJupiterInstruction(instruction) {
    // Jupiter инструкции имеют поле accounts (не keys)
    return instruction && 
           instruction.programId && 
           instruction.accounts && 
           Array.isArray(instruction.accounts) &&
           !instruction.keys;
  }

  isMarginFiInstruction(instruction) {
    // MarginFi инструкции обычно уже TransactionInstruction
    return instruction instanceof TransactionInstruction ||
           (instruction && instruction.programId && 
            instruction.programId.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
  }

  /**
   * 🔑 НОРМАЛИЗАЦИЯ PublicKey
   */
  normalizePublicKey(publicKeyInput) {
    try {
      if (publicKeyInput instanceof PublicKey) {
        return publicKeyInput;
      }

      if (typeof publicKeyInput === 'string') {
        return new PublicKey(publicKeyInput);
      }

      if (publicKeyInput && typeof publicKeyInput.toBase58 === 'function') {
        return new PublicKey(publicKeyInput.toBase58());
      }

      if (publicKeyInput && typeof publicKeyInput.toString === 'function') {
        return new PublicKey(publicKeyInput.toString());
      }

      throw new Error(`Unsupported PublicKey format: ${typeof publicKeyInput}`);
    } catch (error) {
      throw new Error(`Failed to normalize PublicKey: ${error.message}`);
    }
  }

  /**
   * 🪐 НОРМАЛИЗАЦИЯ JUPITER ACCOUNTS → KEYS
   */
  normalizeJupiterAccounts(accounts) {
    if (!Array.isArray(accounts)) {
      console.warn('⚠️ Jupiter accounts is not an array, returning empty keys');
      return [];
    }

    const keys = [];

    for (let i = 0; i < accounts.length; i++) {
      try {
        const account = accounts[i];
        
        if (!account || !account.pubkey) {
          console.warn(`⚠️ Пропускаем account ${i}: отсутствует pubkey`);
          continue;
        }

        const normalizedKey = {
          pubkey: this.normalizePublicKey(account.pubkey),
          isSigner: Boolean(account.isSigner),
          isWritable: Boolean(account.isWritable)
        };

        keys.push(normalizedKey);

      } catch (error) {
        console.warn(`⚠️ Ошибка нормализации account ${i}: ${error.message}`);
        continue;
      }
    }

    return keys;
  }

  /**
   * 🔧 НОРМАЛИЗАЦИЯ СУЩЕСТВУЮЩИХ KEYS
   */
  normalizeAccountMetas(keys) {
    if (!Array.isArray(keys)) {
      console.warn('⚠️ Keys is not an array, returning empty keys');
      return [];
    }

    const normalizedKeys = [];

    for (let i = 0; i < keys.length; i++) {
      try {
        const key = keys[i];

        if (!key) {
          console.warn(`⚠️ Пропускаем key ${i}: key is null/undefined`);
          continue;
        }

        let pubkey, isSigner, isWritable;

        if (key.pubkey) {
          // Правильный формат AccountMeta
          pubkey = this.normalizePublicKey(key.pubkey);
          isSigner = Boolean(key.isSigner);
          isWritable = Boolean(key.isWritable);
        } else {
          console.warn(`⚠️ Пропускаем key ${i}: неизвестная структура`);
          continue;
        }

        const normalizedKey = {
          pubkey,
          isSigner,
          isWritable
        };

        normalizedKeys.push(normalizedKey);

      } catch (error) {
        console.warn(`⚠️ Ошибка нормализации key ${i}: ${error.message}`);
        continue;
      }
    }

    return normalizedKeys;
  }

  /**
   * 📊 НОРМАЛИЗАЦИЯ INSTRUCTION DATA
   */
  normalizeInstructionData(data) {
    try {
      // Если уже Buffer
      if (Buffer.isBuffer(data)) {
        return data;
      }

      // Если строка (base64)
      if (typeof data === 'string') {
        return Buffer.from(data, 'base64');
      }

      // Если Uint8Array
      if (data instanceof Uint8Array) {
        return Buffer.from(data);
      }

      // Если массив чисел
      if (Array.isArray(data)) {
        return Buffer.from(data);
      }

      // Если null/undefined - пустой Buffer
      if (!data) {
        return Buffer.alloc(0);
      }

      throw new Error(`Unsupported data format: ${typeof data}`);

    } catch (error) {
      console.warn(`⚠️ Ошибка нормализации data: ${error.message}, используем пустой Buffer`);
      return Buffer.alloc(0);
    }
  }

  /**
   * 📋 МАССОВАЯ НОРМАЛИЗАЦИЯ ИНСТРУКЦИЙ
   */
  normalizeInstructions(instructions, source = 'UNKNOWN') {
    console.log(`\n📋 МАССОВАЯ НОРМАЛИЗАЦИЯ ИНСТРУКЦИЙ (${source})`);
    console.log(`   Получено инструкций: ${instructions.length}`);

    if (!Array.isArray(instructions)) {
      throw new Error('Instructions must be an array');
    }

    const normalizedInstructions = [];
    const errors = [];

    for (let i = 0; i < instructions.length; i++) {
      try {
        const normalized = this.normalizeInstruction(instructions[i], `${source}_${i}`);
        normalizedInstructions.push(normalized);
      } catch (error) {
        errors.push({ index: i, error: error.message });
        console.warn(`⚠️ Пропускаем инструкцию ${i}: ${error.message}`);
      }
    }

    console.log(`✅ Нормализация завершена:`);
    console.log(`   Успешно: ${normalizedInstructions.length}/${instructions.length}`);
    console.log(`   Ошибок: ${errors.length}`);

    if (errors.length > 0) {
      console.log(`⚠️ Ошибки нормализации:`);
      errors.forEach(err => {
        console.log(`   ${err.index}: ${err.error}`);
      });
    }

    return {
      instructions: normalizedInstructions,
      errors,
      stats: {
        total: instructions.length,
        normalized: normalizedInstructions.length,
        failed: errors.length
      }
    };
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.normalized > 0 ? 
        ((this.stats.normalized / (this.stats.normalized + this.stats.errors)) * 100).toFixed(1) + '%' : 
        '0%'
    };
  }

  /**
   * 🔄 СБРОС СТАТИСТИКИ
   */
  resetStats() {
    this.stats = {
      normalized: 0,
      errors: 0,
      jupiterInstructions: 0,
      marginfiInstructions: 0,
      solanaInstructions: 0,
      otherInstructions: 0
    };
  }
}

module.exports = UnifiedInstructionNormalizer;
