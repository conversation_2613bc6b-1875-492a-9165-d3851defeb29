#!/usr/bin/env node

/**
 * 🔍 АНАЛИЗ ВЕСА ТРАНЗАКЦИИ И СЖАТИЯ
 * 
 * Анализирует:
 * 1. Точный вес каждой инструкции
 * 2. Дублирующиеся адреса для ALT таблиц
 * 3. Потенциал сжатия в ликвидности
 * 4. Почему сжатие работает не на 100%
 */

const { 
    Connection, 
    PublicKey, 
    Keypair,
    Transaction,
    AddressLookupTableProgram
} = require('@solana/web3.js');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ ПОЛНУЮ СИСТЕМУ
const CompleteFlashLoanWithLiquidity = require('./complete-flash-loan-with-liquidity.js');

require('dotenv').config();

class TransactionWeightAnalyzer {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 🔥 ПОЛНАЯ СИСТЕМА
        this.flashLoanSystem = null;
        
        console.log('🔍 TRANSACTION WEIGHT ANALYZER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ АНАЛИЗАТОРА ВЕСА...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ...');
        this.flashLoanSystem = new CompleteFlashLoanWithLiquidity();
        await this.flashLoanSystem.initialize();
        
        console.log('   ✅ Готов к анализу веса транзакции');
    }

    /**
     * 📏 ТОЧНЫЙ РАСЧЕТ РАЗМЕРА ИНСТРУКЦИИ
     */
    calculateInstructionSize(instruction) {
        let size = 0;
        
        // 1 байт - индекс программы
        size += 1;
        
        // 1 байт - количество аккаунтов
        size += 1;
        
        // По 1 байту на каждый аккаунт (индекс в таблице аккаунтов)
        size += (instruction.keys?.length || 0);
        
        // 4 байта - длина данных
        size += 4;
        
        // Данные инструкции
        size += (instruction.data?.length || 0);
        
        return size;
    }

    /**
     * 📊 АНАЛИЗ ВСЕХ АДРЕСОВ В ТРАНЗАКЦИИ
     */
    analyzeAddresses(instructions) {
        const addressMap = new Map();
        const programIds = new Set();
        
        instructions.forEach((instruction, instrIndex) => {
            // Программа
            const programId = instruction.programId.toString();
            programIds.add(programId);
            
            if (!addressMap.has(programId)) {
                addressMap.set(programId, {
                    address: programId,
                    type: 'program',
                    usageCount: 0,
                    instructions: []
                });
            }
            addressMap.get(programId).usageCount++;
            addressMap.get(programId).instructions.push(instrIndex);
            
            // Аккаунты
            instruction.keys?.forEach(key => {
                const address = key.pubkey.toString();
                
                if (!addressMap.has(address)) {
                    addressMap.set(address, {
                        address: address,
                        type: 'account',
                        usageCount: 0,
                        instructions: [],
                        isSigner: key.isSigner,
                        isWritable: key.isWritable
                    });
                }
                addressMap.get(address).usageCount++;
                addressMap.get(address).instructions.push(instrIndex);
            });
        });
        
        return {
            addressMap,
            programIds: Array.from(programIds),
            totalUniqueAddresses: addressMap.size
        };
    }

    /**
     * 🎯 РАСЧЕТ ПОТЕНЦИАЛА СЖАТИЯ
     */
    calculateCompressionPotential(addressAnalysis) {
        const { addressMap } = addressAnalysis;
        
        // Адреса, используемые более 1 раза (кандидаты для ALT)
        const compressionCandidates = Array.from(addressMap.values())
            .filter(addr => addr.usageCount > 1)
            .sort((a, b) => b.usageCount - a.usageCount);
        
        // Расчет экономии
        let totalSavings = 0;
        let altTableSize = 0;
        
        compressionCandidates.forEach(candidate => {
            // Экономия: (32 байта - 1 байт) * (количество использований - 1)
            // -1 потому что первое использование все равно занимает место в ALT таблице
            const savings = (32 - 1) * (candidate.usageCount - 1);
            totalSavings += savings;
            altTableSize += 32; // Каждый адрес в ALT таблице занимает 32 байта
        });
        
        return {
            compressionCandidates,
            totalSavings,
            altTableSize,
            netSavings: totalSavings - altTableSize,
            compressionRatio: totalSavings > 0 ? (totalSavings / (totalSavings + altTableSize)) : 0
        };
    }

    /**
     * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ИНСТРУКЦИЙ
     */
    analyzeInstructionDetails(instructions) {
        console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ КАЖДОЙ ИНСТРУКЦИИ:');
        console.log('=' .repeat(80));
        
        let totalSize = 0;
        const instructionDetails = [];
        
        instructions.forEach((instruction, index) => {
            const programId = instruction.programId.toString();
            const programName = this.getProgramName(programId);
            const size = this.calculateInstructionSize(instruction);
            const accountCount = instruction.keys?.length || 0;
            const dataSize = instruction.data?.length || 0;
            
            const detail = {
                index,
                programId,
                programName,
                accountCount,
                dataSize,
                totalSize: size,
                accounts: instruction.keys?.map(key => ({
                    address: key.pubkey.toString(),
                    isSigner: key.isSigner,
                    isWritable: key.isWritable
                })) || []
            };
            
            instructionDetails.push(detail);
            totalSize += size;
            
            console.log(`📋 ИНСТРУКЦИЯ ${index}:`);
            console.log(`   🏷️  Программа: ${programName} (${programId.slice(0, 8)}...)`);
            console.log(`   👥 Аккаунтов: ${accountCount}`);
            console.log(`   📊 Данных: ${dataSize} байт`);
            console.log(`   📏 Размер: ${size} байт`);
            
            if (accountCount > 0) {
                console.log(`   🔑 Аккаунты:`);
                instruction.keys.forEach((key, keyIndex) => {
                    const flags = [];
                    if (key.isSigner) flags.push('signer');
                    if (key.isWritable) flags.push('writable');
                    console.log(`      ${keyIndex}: ${key.pubkey.toString().slice(0, 8)}... (${flags.join(', ') || 'readonly'})`);
                });
            }
            console.log('');
        });
        
        console.log(`📊 ОБЩИЙ РАЗМЕР ИНСТРУКЦИЙ: ${totalSize} байт`);
        
        return {
            instructionDetails,
            totalInstructionSize: totalSize
        };
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ ПРОГРАММЫ
     */
    getProgramName(programId) {
        const programNames = {
            'ComputeBudget111111111111111111111111111111': 'ComputeBudget',
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi',
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
            '11111111111111111111111111111111': 'System Program',
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token'
        };
        
        return programNames[programId] || 'Unknown Program';
    }

    /**
     * 🧪 ПОЛНЫЙ АНАЛИЗ ТРАНЗАКЦИИ
     */
    async runFullAnalysis() {
        console.log('\n🧪 ЗАПУСК ПОЛНОГО АНАЛИЗА ВЕСА ТРАНЗАКЦИИ...');
        console.log('🔍 АНАЛИЗИРУЕМ КАЖДЫЙ БАЙТ И ВОЗМОЖНОСТИ СЖАТИЯ');
        console.log('=' .repeat(80));
        
        try {
            // 🔥 СОЗДАЕМ ПОЛНУЮ ТРАНЗАКЦИЮ
            console.log('🔥 СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ...');
            const result = await this.flashLoanSystem.createCompleteFlashLoanTransaction();
            
            if (!result.success) {
                throw new Error('Не удалось создать транзакцию для анализа');
            }
            
            const instructions = result.instructions;
            console.log(`✅ Транзакция создана: ${instructions.length} инструкций`);
            
            // 📏 АНАЛИЗ РАЗМЕРА ИНСТРУКЦИЙ
            const instructionAnalysis = this.analyzeInstructionDetails(instructions);
            
            // 📊 АНАЛИЗ АДРЕСОВ
            console.log('\n📊 АНАЛИЗ АДРЕСОВ И ДУБЛИРОВАНИЯ:');
            console.log('=' .repeat(50));
            
            const addressAnalysis = this.analyzeAddresses(instructions);
            
            console.log(`📋 Уникальных адресов: ${addressAnalysis.totalUniqueAddresses}`);
            console.log(`🏷️  Программ: ${addressAnalysis.programIds.length}`);
            
            // 🎯 АНАЛИЗ ПОТЕНЦИАЛА СЖАТИЯ
            console.log('\n🎯 АНАЛИЗ ПОТЕНЦИАЛА СЖАТИЯ:');
            console.log('=' .repeat(50));
            
            const compressionAnalysis = this.calculateCompressionPotential(addressAnalysis);
            
            console.log(`🔄 Кандидатов для сжатия: ${compressionAnalysis.compressionCandidates.length}`);
            console.log(`💾 Потенциальная экономия: ${compressionAnalysis.totalSavings} байт`);
            console.log(`📊 Размер ALT таблицы: ${compressionAnalysis.altTableSize} байт`);
            console.log(`✨ Чистая экономия: ${compressionAnalysis.netSavings} байт`);
            console.log(`📈 Коэффициент сжатия: ${(compressionAnalysis.compressionRatio * 100).toFixed(1)}%`);
            
            // 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ КАНДИДАТОВ
            if (compressionAnalysis.compressionCandidates.length > 0) {
                console.log('\n🔍 ТОП КАНДИДАТЫ ДЛЯ СЖАТИЯ:');
                console.log('=' .repeat(50));
                
                compressionAnalysis.compressionCandidates.slice(0, 10).forEach((candidate, index) => {
                    const savings = (32 - 1) * (candidate.usageCount - 1);
                    console.log(`${index + 1}. ${candidate.address.slice(0, 8)}... (${candidate.type})`);
                    console.log(`   📊 Использований: ${candidate.usageCount}`);
                    console.log(`   💾 Экономия: ${savings} байт`);
                    console.log(`   📋 В инструкциях: ${candidate.instructions.join(', ')}`);
                    console.log('');
                });
            }
            
            // 📊 ИТОГОВЫЙ ОТЧЕТ
            console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ:');
            console.log('=' .repeat(50));
            
            const baseTransactionSize = 64; // Заголовок транзакции
            const totalSize = baseTransactionSize + instructionAnalysis.totalInstructionSize;
            const compressedSize = totalSize - compressionAnalysis.netSavings;
            
            console.log(`📏 Размер заголовка: ${baseTransactionSize} байт`);
            console.log(`📏 Размер инструкций: ${instructionAnalysis.totalInstructionSize} байт`);
            console.log(`📏 Общий размер: ${totalSize} байт`);
            console.log(`🗜️  Размер после сжатия: ${compressedSize} байт`);
            console.log(`🎯 Лимит Solana: 1232 байт`);
            console.log(`📈 Превышение: ${totalSize - 1232} байт`);
            console.log(`📈 Превышение после сжатия: ${compressedSize - 1232} байт`);
            
            // 🔍 АНАЛИЗ ПРОБЛЕМ СЖАТИЯ
            console.log('\n🔍 АНАЛИЗ ПРОБЛЕМ СЖАТИЯ:');
            console.log('=' .repeat(50));
            
            if (compressionAnalysis.netSavings < (totalSize - 1232)) {
                console.log('❌ ПРОБЛЕМА: Сжатие недостаточно для помещения в лимит');
                console.log('🔧 ПРИЧИНЫ:');
                console.log('   1. Много уникальных адресов (используются только 1 раз)');
                console.log('   2. Большой размер данных инструкций');
                console.log('   3. Накладные расходы ALT таблиц');
                
                // Анализ уникальных адресов
                const uniqueAddresses = Array.from(addressAnalysis.addressMap.values())
                    .filter(addr => addr.usageCount === 1);
                
                console.log(`📊 Уникальных адресов (1 использование): ${uniqueAddresses.length}`);
                console.log(`💾 Размер уникальных адресов: ${uniqueAddresses.length * 32} байт`);
            }
            
            return {
                success: true,
                totalSize,
                compressedSize,
                compressionSavings: compressionAnalysis.netSavings,
                instructionCount: instructions.length,
                uniqueAddresses: addressAnalysis.totalUniqueAddresses,
                compressionCandidates: compressionAnalysis.compressionCandidates.length
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА АНАЛИЗА: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
    async function main() {
        const analyzer = new TransactionWeightAnalyzer();
        
        try {
            await analyzer.initialize();
            const results = await analyzer.runFullAnalysis();
            
            if (results.success) {
                console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!');
                process.exit(0);
            } else {
                console.log('\n❌ АНАЛИЗ ЗАВЕРШЕН С ОШИБКАМИ');
                process.exit(1);
            }
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
            process.exit(1);
        }
    }
    
    main().catch(console.error);
}

module.exports = TransactionWeightAnalyzer;
