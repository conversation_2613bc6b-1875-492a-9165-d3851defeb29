/*!
 * 🎯 LEADER SCHEDULE MONITOR
 * Основной монитор расписания лидеров с высокой производительностью
 */

use super::{
    LeaderScheduleConfig, LeaderS<PERSON>uleError, LeaderScheduleResult, PerformanceStats,
    EpochInfo, SlotLeaderInfo, OptimalConnectionTiming,
    rpc_client::SolanaRpcClient, cache::{LeaderCache, CacheConfig}, predictor::LeaderPredictor
};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, error, info, warn};

/// 🎯 Основной монитор расписания лидеров
pub struct LeaderScheduleMonitor {
    config: LeaderScheduleConfig,
    rpc_client: Arc<SolanaRpcClient>,
    cache: Arc<LeaderCache>,
    predictor: Arc<RwLock<LeaderPredictor>>,
    stats: Arc<RwLock<PerformanceStats>>,
    is_running: Arc<RwLock<bool>>,
    start_time: Instant,
}

impl LeaderScheduleMonitor {
    /// Создание нового монитора
    pub fn new(config: LeaderScheduleConfig) -> LeaderScheduleResult<Self> {
        info!("Creating leader schedule monitor with config: {:?}", config);

        // Создаем RPC клиент
        let rpc_client = Arc::new(SolanaRpcClient::new(
            config.rpc_url.clone(),
            config.rpc_timeout_ms,
            config.max_rpc_retries,
            config.retry_delay_ms,
        )?);

        // Создаем кеш
        let cache_config = CacheConfig {
            max_schedules: config.schedule_cache_size,
            max_predictions: config.prediction_cache_size,
            ..Default::default()
        };
        let cache = Arc::new(LeaderCache::new(cache_config));

        // Создаем предиктор
        let predictor = Arc::new(RwLock::new(LeaderPredictor::new(
            rpc_client.clone(),
            cache.clone(),
        )));

        // Инициализируем статистику
        let stats = Arc::new(RwLock::new(PerformanceStats {
            last_update: chrono::Utc::now().timestamp() as u64,
            schedule_cache_size: 0,
            prediction_cache_size: 0,
            successful_predictions: 0,
            total_predictions: 0,
            prediction_accuracy: 0.0,
            avg_update_time: 0.0,
            rpc_errors: 0,
            uptime_seconds: 0,
        }));

        Ok(Self {
            config,
            rpc_client,
            cache,
            predictor,
            stats,
            is_running: Arc::new(RwLock::new(false)),
            start_time: Instant::now(),
        })
    }

    /// Запуск мониторинга
    pub async fn start_monitoring(&self) -> LeaderScheduleResult<()> {
        let mut is_running = self.is_running.write().await;
        if *is_running {
            return Err(LeaderScheduleError::ConfigError("Monitor is already running".to_string()));
        }

        info!("Starting leader schedule monitoring");

        // Инициализируем предиктор
        {
            let mut predictor = self.predictor.write().await;
            predictor.initialize().await?;
        }

        *is_running = true;
        drop(is_running);

        // Запускаем основной цикл мониторинга
        let monitor_task = self.run_monitoring_loop();

        // Запускаем фоновую очистку кеша
        let cache_cleanup_task = self.cache.start_background_cleanup();

        // Запускаем обновление статистики
        let stats_update_task = self.run_stats_update_loop();

        // Ждем завершения всех задач
        tokio::select! {
            result = monitor_task => {
                error!("Monitoring loop ended: {:?}", result);
                result
            }
            _ = cache_cleanup_task => {
                error!("Cache cleanup ended unexpectedly");
                Ok(())
            }
            _ = stats_update_task => {
                error!("Stats update ended unexpectedly");
                Ok(())
            }
        }
    }

    /// Основной цикл мониторинга
    async fn run_monitoring_loop(&self) -> LeaderScheduleResult<()> {
        let mut interval = interval(Duration::from_millis(self.config.update_interval_ms));

        info!("Started monitoring loop with {}ms interval", self.config.update_interval_ms);

        loop {
            interval.tick().await;

            // Проверяем, нужно ли продолжать работу
            if !*self.is_running.read().await {
                info!("Monitoring loop stopped by request");
                break;
            }

            let update_start = Instant::now();

            // Выполняем обновление
            match self.perform_update().await {
                Ok(_) => {
                    let update_duration = update_start.elapsed();
                    debug!("Update completed in {:?}", update_duration);

                    // Обновляем статистику
                    self.update_performance_stats(update_duration).await;
                }
                Err(e) => {
                    error!("Update failed: {}", e);
                    self.increment_error_count().await;
                }
            }
        }

        Ok(())
    }

    /// Выполнение обновления
    async fn perform_update(&self) -> LeaderScheduleResult<()> {
        // Получаем текущую информацию об эпохе
        let epoch_info = self.rpc_client.get_epoch_info().await?;

        debug!("Current epoch: {}, slot: {}", epoch_info.epoch, epoch_info.absolute_slot);

        // Предварительно загружаем расписания для текущей и следующей эпохи
        self.preload_schedules(&epoch_info).await?;

        // Обновляем предсказания
        self.update_predictions(&epoch_info).await?;

        Ok(())
    }

    /// Предварительная загрузка расписаний
    async fn preload_schedules(&self, epoch_info: &EpochInfo) -> LeaderScheduleResult<()> {
        let current_epoch = epoch_info.epoch;
        let next_epoch = current_epoch + 1;

        // Проверяем и загружаем расписание текущей эпохи
        if self.cache.get_schedule(current_epoch).is_none() {
            debug!("Loading schedule for current epoch {}", current_epoch);
            match self.rpc_client.get_leader_schedule(Some(epoch_info.absolute_slot)).await {
                Ok(schedule) => {
                    if let Err(e) = self.cache.put_schedule(schedule) {
                        warn!("Failed to cache schedule for epoch {}: {}", current_epoch, e);
                    }
                }
                Err(e) => {
                    warn!("Failed to load schedule for epoch {}: {}", current_epoch, e);
                }
            }
        }

        // Проверяем и загружаем расписание следующей эпохи
        if self.cache.get_schedule(next_epoch).is_none() {
            debug!("Loading schedule for next epoch {}", next_epoch);
            let next_epoch_first_slot = epoch_info.absolute_slot +
                (epoch_info.slots_in_epoch - epoch_info.slot_index);

            match self.rpc_client.get_leader_schedule(Some(next_epoch_first_slot)).await {
                Ok(schedule) => {
                    if let Err(e) = self.cache.put_schedule(schedule) {
                        warn!("Failed to cache schedule for epoch {}: {}", next_epoch, e);
                    }
                }
                Err(e) => {
                    debug!("Next epoch schedule not yet available: {}", e);
                }
            }
        }

        Ok(())
    }

    /// Обновление предсказаний
    async fn update_predictions(&self, _epoch_info: &EpochInfo) -> LeaderScheduleResult<()> {
        let predictor = self.predictor.read().await;

        // Предсказываем лидеров на заданное количество слотов вперед
        match predictor.predict_next_leaders(self.config.prediction_slots_ahead).await {
            Ok(predictions) => {
                debug!("Successfully predicted {} leaders", predictions.len());

                // Обновляем статистику успешных предсказаний
                let mut stats = self.stats.write().await;
                stats.successful_predictions += 1;
                stats.total_predictions += 1;
                stats.prediction_accuracy = stats.successful_predictions as f64 / stats.total_predictions as f64;
            }
            Err(e) => {
                warn!("Failed to predict leaders: {}", e);

                // Обновляем статистику неудачных предсказаний
                let mut stats = self.stats.write().await;
                stats.total_predictions += 1;
                stats.prediction_accuracy = stats.successful_predictions as f64 / stats.total_predictions as f64;
            }
        }

        Ok(())
    }

    /// Обновление статистики производительности
    async fn update_performance_stats(&self, update_duration: Duration) {
        let mut stats = self.stats.write().await;

        stats.last_update = chrono::Utc::now().timestamp() as u64;
        stats.uptime_seconds = self.start_time.elapsed().as_secs();

        // Обновляем среднее время обновления (экспоненциальное скользящее среднее)
        let new_update_time = update_duration.as_millis() as f64;
        if stats.avg_update_time == 0.0 {
            stats.avg_update_time = new_update_time;
        } else {
            stats.avg_update_time = stats.avg_update_time * 0.9 + new_update_time * 0.1;
        }

        // Обновляем размеры кешей
        let cache_info = self.cache.get_cache_info();
        stats.schedule_cache_size = cache_info.get("schedule_cache_size")
            .and_then(|s| s.parse().ok())
            .unwrap_or(0);
        stats.prediction_cache_size = cache_info.get("prediction_cache_size")
            .and_then(|s| s.parse().ok())
            .unwrap_or(0);
    }

    /// Увеличение счетчика ошибок
    async fn increment_error_count(&self) {
        let mut stats = self.stats.write().await;
        stats.rpc_errors += 1;
    }

    /// Цикл обновления статистики
    async fn run_stats_update_loop(&self) {
        let mut interval = interval(Duration::from_secs(10)); // Обновляем статистику каждые 10 секунд

        loop {
            interval.tick().await;

            if !*self.is_running.read().await {
                break;
            }

            // Логируем статистику
            let stats = self.stats.read().await;
            if self.config.enable_detailed_logging {
                info!(
                    "Stats: uptime={}s, avg_update={:.1}ms, accuracy={:.1}%, errors={}",
                    stats.uptime_seconds,
                    stats.avg_update_time,
                    stats.prediction_accuracy * 100.0,
                    stats.rpc_errors
                );
            }
        }
    }

    /// Остановка мониторинга
    pub async fn stop(&self) {
        info!("Stopping leader schedule monitoring");
        let mut is_running = self.is_running.write().await;
        *is_running = false;
    }

    /// Получение предсказаний лидеров
    pub async fn predict_next_leaders(&self, slots_ahead: u64) -> LeaderScheduleResult<Vec<SlotLeaderInfo>> {
        let predictor = self.predictor.read().await;
        predictor.predict_next_leaders(slots_ahead).await
    }

    /// Получение оптимального времени подключения
    pub async fn get_optimal_connection_timing(&self, target_slot: u64) -> LeaderScheduleResult<OptimalConnectionTiming> {
        let predictor = self.predictor.read().await;
        predictor.get_optimal_connection_timing(target_slot).await
    }

    /// Получение статистики производительности
    pub async fn get_performance_stats(&self) -> PerformanceStats {
        self.stats.read().await.clone()
    }

    /// Проверка здоровья монитора
    pub async fn health_check(&self) -> bool {
        // Проверяем, что монитор запущен
        if !*self.is_running.read().await {
            return false;
        }

        // Проверяем здоровье предиктора
        let predictor = self.predictor.read().await;
        predictor.health_check().await
    }

    /// Получение детальной информации о мониторе
    pub async fn get_monitor_info(&self) -> std::collections::HashMap<String, String> {
        let mut info = std::collections::HashMap::new();

        info.insert("is_running".to_string(), self.is_running.read().await.to_string());
        info.insert("uptime_seconds".to_string(), self.start_time.elapsed().as_secs().to_string());
        info.insert("update_interval_ms".to_string(), self.config.update_interval_ms.to_string());
        info.insert("prediction_slots_ahead".to_string(), self.config.prediction_slots_ahead.to_string());

        // Добавляем статистику RPC клиента
        let rpc_stats = self.rpc_client.get_stats();
        for (key, value) in rpc_stats {
            info.insert(format!("rpc_{}", key), value);
        }

        // Добавляем статистику кеша
        let cache_info = self.cache.get_cache_info();
        for (key, value) in cache_info {
            info.insert(format!("cache_{}", key), value);
        }

        // Добавляем статистику предиктора
        let predictor = self.predictor.read().await;
        let predictor_stats = predictor.get_stats();
        for (key, value) in predictor_stats {
            info.insert(format!("predictor_{}", key), value);
        }

        info
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_monitor_creation() {
        let config = LeaderScheduleConfig::default();
        let monitor = LeaderScheduleMonitor::new(config);

        assert!(monitor.is_ok());

        if let Ok(monitor) = monitor {
            assert!(!*monitor.is_running.read().await);
        }
    }

    #[tokio::test]
    async fn test_monitor_health_check() {
        let config = LeaderScheduleConfig {
            rpc_url: "https://api.devnet.solana.com".to_string(),
            ..Default::default()
        };

        let monitor = LeaderScheduleMonitor::new(config).unwrap();

        // Монитор не запущен, поэтому health check должен вернуть false
        assert!(!monitor.health_check().await);
    }

    #[tokio::test]
    async fn test_monitor_stats() {
        let config = LeaderScheduleConfig::default();
        let monitor = LeaderScheduleMonitor::new(config).unwrap();

        let stats = monitor.get_performance_stats().await;
        assert_eq!(stats.successful_predictions, 0);
        assert_eq!(stats.total_predictions, 0);
        assert_eq!(stats.rpc_errors, 0);
    }
}
