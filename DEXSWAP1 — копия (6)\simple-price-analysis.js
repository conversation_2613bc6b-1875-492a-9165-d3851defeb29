#!/usr/bin/env node

/**
 * 🔍 ПРОСТОЙ АНАЛИЗ ЦЕНОВОЙ СИТУАЦИИ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Анализ ситуации с покупкой по $150 и продажей по $163
 * 🚨 БЕЗ ЗАВИСИМОСТЕЙ - только чистый JavaScript
 */

console.log('🔍 ЗАПУСК АНАЛИЗА ЦЕНОВОЙ СИТУАЦИИ');
console.log('═'.repeat(60));

/**
 * 🚨 АНАЛИЗ СИТУАЦИИ: ПОКУПКА $150, ПРОДАЖА $163
 */
function analyzePriceSituation() {
    console.log('\n🚨 АНАЛИЗ ЦЕНОВОЙ СИТУАЦИИ');
    console.log('═'.repeat(60));
    console.log('📊 Данные из скриншота:');
    console.log('   💰 SOL цена (верх): $163.12244');
    console.log('   💱 Exchange rate: 1 SOL ≈ 150.739253 USDC');
    console.log('   📈 Price Impact: 6.94%');
    console.log('   📉 Minimum received: 150.588513 USDC');
    console.log('═'.repeat(60));

    // Подготавливаем данные для анализа (ОБНОВЛЕННЫЕ)
    const priceData = {
        token: 'SOL/USDC',
        buyPrice: 150.777976,    // Цена покупки через USDC (ОБНОВЛЕНО)
        sellPrice: 163.9099485,  // Рыночная цена SOL (ОБНОВЛЕНО)
        poolPrice: 163.84,       // Current Pool Price
        priceImpact: 6.94,       // Из предыдущего скриншота
        minReceived: 150.588513, // Из предыдущего скриншота
        tradeAmount: 1,          // 1 SOL

        // ЛИКВИДНОСТЬ ПУЛА
        totalValueLocked: 90563.50,
        solLiquidity: 237.37,
        usdcLiquidity: 51655.75
    };

    // Расчеты
    const spread = ((priceData.sellPrice - priceData.buyPrice) / priceData.buyPrice) * 100;
    const potentialProfit = priceData.sellPrice - priceData.buyPrice;
    const actualReceived = priceData.minReceived;
    const actualProfit = priceData.sellPrice - actualReceived;
    const realSpread = ((actualReceived - priceData.buyPrice) / priceData.buyPrice) * 100;

    console.log(`\n🧮 РАСЧЕТНЫЕ ДАННЫЕ:`);
    console.log(`   Теоретический спред: ${spread.toFixed(4)}%`);
    console.log(`   Потенциальная прибыль: $${potentialProfit.toFixed(2)}`);
    console.log(`   Реальная прибыль: $${actualProfit.toFixed(2)}`);
    console.log(`   Реальный спред: ${realSpread.toFixed(4)}%`);
    console.log(`\n💧 ЛИКВИДНОСТЬ ПУЛА:`);
    console.log(`   Total Value Locked: $${priceData.totalValueLocked.toLocaleString()}`);
    console.log(`   SOL в пуле: ${priceData.solLiquidity} (~$${(priceData.solLiquidity * priceData.sellPrice).toLocaleString()})`);
    console.log(`   USDC в пуле: ${priceData.usdcLiquidity.toLocaleString()}`);

    // Анализ ликвидности
    const liquidityRatio = priceData.totalValueLocked / 1000000; // В миллионах
    console.log(`   Размер пула: ${liquidityRatio < 0.1 ? 'ОЧЕНЬ МАЛЕНЬКИЙ' : liquidityRatio < 1 ? 'МАЛЕНЬКИЙ' : 'СРЕДНИЙ'}`);

    // Оценка price impact для разных сумм
    console.log(`\n📊 ОЦЕНКА PRICE IMPACT:`);
    const tradeAmounts = [100, 500, 1000, 5000, 10000]; // USD
    tradeAmounts.forEach(amount => {
        const impactEstimate = (amount / priceData.totalValueLocked) * 100 * 2; // Примерная формула
        console.log(`   $${amount}: ~${impactEstimate.toFixed(2)}% impact`);
    });

    // 1. 🚨 ПРОВЕРКА АНОМАЛИЙ
    console.log('\n🔍 АНАЛИЗ АНОМАЛИЙ...');
    
    const anomalies = [];
    
    // Проверка спреда
    if (spread > 5.0) {
        anomalies.push({
            type: 'HIGH_SPREAD',
            severity: 'HIGH',
            message: `Спред ${spread.toFixed(2)}% слишком высок для нормального рынка`
        });
    }
    
    // Проверка price impact
    if (priceData.priceImpact > 5.0) {
        anomalies.push({
            type: 'HIGH_PRICE_IMPACT',
            severity: 'CRITICAL',
            message: `Price impact ${priceData.priceImpact}% критически высок`
        });
    }
    
    // Проверка реальной прибыли
    if (actualProfit < 5.0) {
        anomalies.push({
            type: 'LOW_REAL_PROFIT',
            severity: 'HIGH',
            message: `Реальная прибыль $${actualProfit.toFixed(2)} слишком мала`
        });
    }

    // 2. 🧠 ДЕТАЛЬНЫЙ АНАЛИЗ
    console.log('\n🧠 ДЕТАЛЬНЫЙ АНАЛИЗ...');
    
    console.log('📊 Анализ спреда:');
    if (spread > 8.0) {
        console.log('   ❌ КРИТИЧНО: Спред больше 8% указывает на серьезные проблемы');
    } else if (spread > 5.0) {
        console.log('   ⚠️ ВЫСОКИЙ: Спред больше 5% требует осторожности');
    } else {
        console.log('   ✅ НОРМАЛЬНЫЙ: Спред в приемлемых пределах');
    }
    
    console.log('📈 Анализ price impact:');
    if (priceData.priceImpact > 5.0) {
        console.log('   ❌ КРИТИЧНО: Price impact больше 5% - пул неликвиден');
    } else if (priceData.priceImpact > 2.0) {
        console.log('   ⚠️ ВЫСОКИЙ: Price impact больше 2% - ограниченная ликвидность');
    } else {
        console.log('   ✅ ПРИЕМЛЕМЫЙ: Price impact в нормальных пределах');
    }

    // 3. 💡 ВОЗМОЖНЫЕ ПРИЧИНЫ
    console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ СИТУАЦИИ:');
    
    const causes = [
        {
            cause: 'ILLIQUID_POOL',
            probability: 'HIGH',
            description: 'Пул с низкой ликвидностью',
            impact: 'Высокий price impact съест прибыль'
        },
        {
            cause: 'THIN_ORDER_BOOK',
            probability: 'HIGH',
            description: 'Тонкая книга заказов',
            impact: 'Реальная цена будет хуже ожидаемой'
        },
        {
            cause: 'STALE_PRICE_DATA',
            probability: 'MEDIUM',
            description: 'Устаревшие данные о цене',
            impact: 'Арбитраж уже недоступен'
        },
        {
            cause: 'ORACLE_ISSUES',
            probability: 'MEDIUM',
            description: 'Проблемы с оракулом цен',
            impact: 'Неточные данные о рыночной цене'
        }
    ];

    causes.forEach((cause, i) => {
        console.log(`   ${i + 1}. [${cause.probability}] ${cause.description}`);
        console.log(`      Влияние: ${cause.impact}`);
    });

    // 4. ⚠️ РИСКИ
    console.log('\n⚠️ ИДЕНТИФИКАЦИЯ РИСКОВ:');

    const risks = [];

    if (spread > 5.0) {
        risks.push('Нереально высокий спред');
    }

    if (priceData.priceImpact > 5.0) {
        risks.push('Критический price impact');
    }

    if (actualProfit < 5.0) {
        risks.push('Низкая реальная прибыль');
    }

    // Анализ ликвидности
    if (priceData.totalValueLocked < 100000) {
        risks.push('Очень низкая ликвидность пула ($90k TVL)');
    }

    console.log(`   Основные проблемы: ${risks.join(', ')}`);

    // Детальный анализ ликвидности
    console.log(`\n💧 АНАЛИЗ ЛИКВИДНОСТИ:`);
    console.log(`   TVL: $${priceData.totalValueLocked.toLocaleString()} - МАЛЕНЬКИЙ ПУЛЛ`);
    console.log(`   Рекомендуемый максимум сделки: $100-500`);
    console.log(`   При сделке $1000+: price impact >2%`);
    console.log(`   При сделке $5000+: price impact >10%`);

    // 5. 💡 РЕКОМЕНДАЦИИ
    console.log('\n💡 РЕКОМЕНДАЦИИ:');
    
    const isRealOpportunity = spread < 5.0 && priceData.priceImpact < 2.0 && actualProfit > 10.0;
    const shouldTrade = isRealOpportunity && anomalies.length === 0;
    
    console.log(`   Реальная возможность: ${isRealOpportunity ? 'ДА' : 'НЕТ'}`);
    console.log(`   Рекомендация торговать: ${shouldTrade ? 'ДА' : 'НЕТ'}`);
    
    const actions = [
        'НЕ ТОРГОВАТЬ - слишком высокий price impact',
        'Проверить ликвидность пула вручную',
        'Сравнить с ценами других DEX',
        'Проверить актуальность данных'
    ];
    
    if (priceData.priceImpact > 2.0) {
        actions.push('Если торговать - уменьшить размер позиции в 5-10 раз');
    }
    
    actions.forEach(action => {
        console.log(`   • ${action}`);
    });

    // 📋 ИТОГОВЫЙ ОТЧЕТ
    console.log('\n📋 ИТОГОВЫЙ ОТЧЕТ');
    console.log('═'.repeat(60));

    console.log('🎯 ЗАКЛЮЧЕНИЕ:');
    
    if (spread > 5.0 && priceData.priceImpact > 5.0) {
        console.log('❌ Это НЕ реальная арбитражная возможность');
        console.log('📊 Причины:');
        console.log('   • Спред 8.2% слишком высок для нормального рынка');
        console.log('   • Price impact 6.94% съест большую часть прибыли');
        console.log('   • Пул имеет очень низкую ликвидность');
        console.log('   • Реальная прибыль будет близка к нулю или отрицательна');
    } else {
        console.log('✅ Возможность может быть реальной, но требует осторожности');
    }

    console.log('\n🚨 ГЛАВНЫЕ РИСКИ:');
    risks.forEach(risk => {
        console.log(`   • ${risk}`);
    });

    console.log('\n🎓 УРОК:');
    console.log('   Большие спреды часто указывают на проблемы с ликвидностью,');
    console.log('   а не на реальные арбитражные возможности. Всегда проверяйте');
    console.log('   price impact перед выполнением сделки!');

    console.log('\n📊 ДЕТАЛЬНЫЕ РАСЧЕТЫ:');
    console.log(`   Покупка: $${priceData.buyPrice}`);
    console.log(`   Продажа: $${priceData.sellPrice}`);
    console.log(`   Теоретическая прибыль: $${potentialProfit.toFixed(2)}`);
    console.log(`   Price impact: ${priceData.priceImpact}%`);
    console.log(`   Реально получите: $${actualReceived}`);
    console.log(`   Реальная прибыль: $${actualProfit.toFixed(2)}`);
    console.log(`   Потеря от slippage: $${(potentialProfit - actualProfit).toFixed(2)}`);

    console.log('═'.repeat(60));

    return {
        isRealOpportunity: isRealOpportunity,
        shouldTrade: shouldTrade,
        riskLevel: anomalies.length > 2 ? 'CRITICAL' : anomalies.length > 0 ? 'HIGH' : 'LOW',
        mainConcerns: risks,
        actualProfit: actualProfit,
        priceImpactLoss: potentialProfit - actualProfit
    };
}

// 🚀 ЗАПУСК АНАЛИЗА
try {
    const result = analyzePriceSituation();
    
    console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
    console.log(`Результат: ${result.isRealOpportunity ? 'РЕАЛЬНАЯ ВОЗМОЖНОСТЬ' : 'ЛОЖНЫЙ СИГНАЛ'}`);
    console.log(`Реальная прибыль: $${result.actualProfit.toFixed(2)}`);
    console.log(`Потеря от price impact: $${result.priceImpactLoss.toFixed(2)}`);
    
} catch (error) {
    console.error('\n💥 ОШИБКА АНАЛИЗА!');
    console.error(error);
}
