/**
 * 🔍 ПРОВЕРКА ДИАПАЗОНОВ BINS ПОЗИЦИЙ
 * Проверяет, какие bin ranges покрывают наши позиции
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

async function checkPositionBinRanges() {
    console.log('🔍 ПРОВЕРКА ДИАПАЗОНОВ BINS ПОЗИЦИЙ...\n');

    // Используем централизованный RPC менеджер
    const CentralizedRPCManager = require('./centralized-rpc-manager');
    const rpcManager = new CentralizedRPCManager();
    const connection = rpcManager.getConnection();
    const wallet = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');

    const positions = [
        {
            name: 'POOL_1',
            poolAddress: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            positionAddress: 'CfKmtCvWTsXSEAwJaHBPyrRmoanPqZfJSNmyJFFVnpmn'
        },
        {
            name: 'POOL_2',
            poolAddress: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
            positionAddress: '7ydf4bfon7f8YSu8xR35QsKU5Tynb5CKJ36fr3vnycis'
        }
    ];

    for (const pos of positions) {
        console.log(`\n=== ${pos.name} ===`);
        console.log(`Pool: ${pos.poolAddress}`);
        console.log(`Position: ${pos.positionAddress}`);

        try {
            // Создаем DLMM instance
            const dlmmPool = await DLMM.create(connection, new PublicKey(pos.poolAddress));
            console.log(`✅ DLMM пул загружен`);

            // Получаем активный bin
            const activeBin = await dlmmPool.getActiveBin();
            console.log(`📍 Текущий активный bin ID: ${activeBin.binId}`);
            console.log(`💰 Цена активного bin: $${activeBin.price ? Number(activeBin.price).toFixed(4) : 'N/A'}`);

            // Получаем позиции пользователя
            const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(wallet);
            const targetPosition = userPositions.find(p => 
                p.publicKey.toString() === pos.positionAddress
            );

            if (!targetPosition) {
                console.log(`❌ Позиция не найдена!`);
                continue;
            }

            console.log(`✅ Позиция найдена`);

            // Анализируем bin data позиции
            const binData = targetPosition.positionData.positionBinData;
            console.log(`📊 Всего bins в позиции: ${binData.length}`);

            if (binData.length > 0) {
                const binIds = binData.map(bin => bin.binId).sort((a, b) => a - b);
                const minBinId = Math.min(...binIds);
                const maxBinId = Math.max(...binIds);

                console.log(`📈 ДИАПАЗОН ПОЗИЦИИ:`);
                console.log(`   Min Bin ID: ${minBinId}`);
                console.log(`   Max Bin ID: ${maxBinId}`);
                console.log(`   Диапазон: ${maxBinId - minBinId + 1} bins`);

                // Проверяем, входит ли активный bin в диапазон позиции
                const activeBinInRange = activeBin.binId >= minBinId && activeBin.binId <= maxBinId;
                console.log(`\n🎯 ПРОВЕРКА СОВМЕСТИМОСТИ:`);
                console.log(`   Активный bin: ${activeBin.binId}`);
                console.log(`   Диапазон позиции: ${minBinId} - ${maxBinId}`);
                console.log(`   ${activeBinInRange ? '✅ АКТИВНЫЙ BIN В ДИАПАЗОНЕ ПОЗИЦИИ' : '❌ АКТИВНЫЙ BIN ВНЕ ДИАПАЗОНА ПОЗИЦИИ'}`);

                if (!activeBinInRange) {
                    const distance = activeBin.binId < minBinId ? 
                        minBinId - activeBin.binId : 
                        activeBin.binId - maxBinId;
                    console.log(`   ⚠️ Расстояние до ближайшего bin: ${distance} bins`);
                    console.log(`   🔧 ТРЕБУЕТСЯ: Расширить диапазон позиции или создать новую позицию`);
                }

                // Показываем детали bins
                console.log(`\n📋 ДЕТАЛИ BINS ПОЗИЦИИ:`);
                binData.slice(0, 5).forEach((bin, idx) => {
                    const xAmount = parseInt(bin.xAmount || 0);
                    const yAmount = parseInt(bin.yAmount || 0);
                    const hasLiquidity = xAmount > 0 || yAmount > 0;
                    console.log(`   Bin ${idx + 1}: ID=${bin.binId}, X=${xAmount}, Y=${yAmount} ${hasLiquidity ? '💰' : '⚪'}`);
                });
                if (binData.length > 5) {
                    console.log(`   ... и еще ${binData.length - 5} bins`);
                }
            }

        } catch (error) {
            console.log(`❌ Ошибка: ${error.message}`);
        }
    }

    console.log('\n✅ ПРОВЕРКА ЗАВЕРШЕНА!');
}

checkPositionBinRanges().catch(console.error);
