/**
 * 🚫 JUPITER API GUARD ПОЛНОСТЬЮ ОТКЛЮЧЕН!
 *
 * Этот файл больше не блокирует Jupiter API запросы.
 * Все endpoints разрешены для торговли.
 */

console.log('🚫 JUPITER API GUARD ПОЛНОСТЬЮ ОТКЛЮЧЕН - ВСЕ ENDPOINTS РАЗРЕШЕНЫ!');

// 🔥 СОЗДАЕМ ПУСТОЙ GUARD ДЛЯ СОВМЕСТИМОСТИ
const jupiterApiGuard = {
  checkAccess: () => ({ allowed: true, reason: 'Guard disabled' }),
  blockRequest: () => ({ allowed: true, reason: 'Guard disabled' }),
  getStats: () => ({ allowed: 0, blocked: 0, total: 0, blockRate: 0 }),
  addAllowedEndpoint: () => {},
  removeAllowedEndpoint: () => {}
};

// 🚫 ПУСТОЙ КЛАСС ДЛЯ СОВМЕСТИМОСТИ
class JupiterApiGuard {
  constructor() {
    console.log('🚫 JupiterApiGuard отключен - все запросы разрешены');
  }

  checkAccess() { return { allowed: true, reason: 'Guard disabled' }; }
  blockRequest() { return { allowed: true, reason: 'Guard disabled' }; }
  getStats() { return { allowed: 0, blocked: 0, total: 0, blockRate: 0 }; }
  addAllowedEndpoint() {}
  removeAllowedEndpoint() {}
}

module.exports = {
  jupiterApiGuard,
  JupiterApiGuard
};
