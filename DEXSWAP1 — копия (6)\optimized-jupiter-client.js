/**
 * 🔥 OPTIMIZED JUPITER CLIENT - ПРЕДОТВРАЩЕНИЕ ЗАВИСАНИЙ
 * 
 * ✅ Таймауты для всех запросов
 * ✅ Кэширование результатов
 * ✅ Батчевые запросы
 * ✅ Retry логика с экспоненциальным backoff
 * ✅ Rate limiting protection
 * 
 * 🎯 ЦЕЛЬ: Избежать зависаний на Jupiter API запросах
 */

const axios = require('axios');
const { TRADING_CONFIG } = require('./trading-config');

class OptimizedJupiterClient {
    constructor() {
        this.baseURL = TRADING_CONFIG.JUPITER_CONFIG.API_ENDPOINTS.BASE_URL;
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.lastRequestTime = 0;
        this.minRequestInterval = 100; // 100ms между запросами
        
        // Создаем оптимизированный axios клиент
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: TRADING_CONFIG.JUPITER_CONFIG.REQUEST_PARAMS.timeout || 5000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'OptimizedArbitrageBot/1.0'
            }
        });
    }

    /**
     * 🔥 КЭШИРОВАНИЕ РЕЗУЛЬТАТОВ ДЛЯ ИЗБЕЖАНИЯ ПОВТОРНЫХ ЗАПРОСОВ
     */
    getCacheKey(endpoint, params) {
        return `${endpoint}_${JSON.stringify(params)}`;
    }

    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < 30000) { // 30 секунд TTL
            console.log(`🔥 Используем кэшированный результат для ${key.slice(0, 50)}...`);
            return cached.data;
        }
        return null;
    }

    setCache(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
        
        // Очищаем старые записи
        if (this.cache.size > 100) {
            const oldestKey = this.cache.keys().next().value;
            this.cache.delete(oldestKey);
        }
    }

    /**
     * 🔥 RATE LIMITING PROTECTION
     */
    async waitForRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.minRequestInterval) {
            const waitTime = this.minRequestInterval - timeSinceLastRequest;
            console.log(`⏳ Rate limiting: ждем ${waitTime}мс...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastRequestTime = Date.now();
    }

    /**
     * 🔥 RETRY ЛОГИКА С ЭКСПОНЕНЦИАЛЬНЫМ BACKOFF
     */
    async makeRequestWithRetry(endpoint, params, maxRetries = 2) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                await this.waitForRateLimit();
                
                console.log(`🌐 Jupiter API запрос (попытка ${attempt + 1}/${maxRetries + 1}): ${endpoint}`);
                
                const response = await this.client.get(endpoint, { params });
                
                if (response.status === 200 && response.data) {
                    console.log(`✅ Jupiter API успешно: ${endpoint}`);
                    return response.data;
                }
                
                throw new Error(`Invalid response: status ${response.status}`);
                
            } catch (error) {
                lastError = error;
                console.log(`❌ Jupiter API ошибка (попытка ${attempt + 1}): ${error.message}`);
                
                if (attempt < maxRetries) {
                    const backoffTime = Math.pow(2, attempt) * 1000; // Экспоненциальный backoff
                    console.log(`⏳ Ждем ${backoffTime}мс перед повтором...`);
                    await new Promise(resolve => setTimeout(resolve, backoffTime));
                }
            }
        }
        
        throw new Error(`Jupiter API failed after ${maxRetries + 1} attempts: ${lastError.message}`);
    }

    /**
     * 🔥 ОПТИМИЗИРОВАННЫЙ QUOTE ЗАПРОС
     */
    async getQuote(inputMint, outputMint, amount, options = {}) {
        const cacheKey = this.getCacheKey('quote', { inputMint, outputMint, amount });
        
        // Проверяем кэш
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }

        const params = {
            inputMint,
            outputMint,
            amount,
            ...TRADING_CONFIG.JUPITER_CONFIG.REQUEST_PARAMS,
            ...options
        };

        try {
            const result = await this.makeRequestWithRetry('/v6/quote', params);
            
            // Кэшируем результат
            this.setCache(cacheKey, result);
            
            return result;
            
        } catch (error) {
            console.error(`❌ Критическая ошибка Jupiter Quote: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 ОПТИМИЗИРОВАННЫЙ SWAP INSTRUCTIONS ЗАПРОС
     */
    async getSwapInstructions(quoteResponse, userPublicKey, options = {}) {
        const cacheKey = this.getCacheKey('swap', { 
            route: quoteResponse.routePlan?.[0]?.swapInfo?.ammKey || 'unknown',
            amount: quoteResponse.inAmount,
            user: userPublicKey.toString()
        });
        
        // Проверяем кэш
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }

        const requestBody = {
            quoteResponse,
            userPublicKey: userPublicKey.toString(),
            wrapAndUnwrapSol: true,
            useSharedAccounts: true,
            feeAccount: null,
            trackingAccount: null,
            computeUnitPriceMicroLamports: 'auto',
            prioritizationFeeLamports: 'auto',
            asLegacyTransaction: false,
            useTokenLedger: false,
            destinationTokenAccount: null,
            ...options
        };

        try {
            await this.waitForRateLimit();
            
            console.log(`🌐 Jupiter Swap Instructions запрос...`);
            
            const response = await this.client.post('/v6/swap-instructions', requestBody);
            
            if (response.status === 200 && response.data) {
                console.log(`✅ Jupiter Swap Instructions получены`);
                
                // Кэшируем результат
                this.setCache(cacheKey, response.data);
                
                return response.data;
            }
            
            throw new Error(`Invalid response: status ${response.status}`);
            
        } catch (error) {
            console.error(`❌ Критическая ошибка Jupiter Swap Instructions: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 БАТЧЕВЫЙ ЗАПРОС ДЛЯ МНОЖЕСТВЕННЫХ QUOTE
     */
    async getBatchQuotes(requests) {
        console.log(`🔥 Батчевый запрос ${requests.length} quotes...`);
        
        const results = [];
        
        // Обрабатываем по 3 запроса одновременно для избежания rate limiting
        for (let i = 0; i < requests.length; i += 3) {
            const batch = requests.slice(i, i + 3);
            
            const batchPromises = batch.map(async (request) => {
                try {
                    return await this.getQuote(
                        request.inputMint,
                        request.outputMint,
                        request.amount,
                        request.options
                    );
                } catch (error) {
                    console.error(`❌ Ошибка в батчевом запросе: ${error.message}`);
                    return null;
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
            
            // Пауза между батчами
            if (i + 3 < requests.length) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }
        
        console.log(`✅ Батчевый запрос завершен: ${results.filter(r => r).length}/${requests.length} успешно`);
        
        return results;
    }

    /**
     * 🔥 ОЧИСТКА КЭША
     */
    clearCache() {
        this.cache.clear();
        console.log('🔥 Кэш Jupiter API очищен');
    }

    /**
     * 🔥 СТАТИСТИКА КЭША
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            hitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
        };
    }
}

module.exports = OptimizedJupiterClient;
