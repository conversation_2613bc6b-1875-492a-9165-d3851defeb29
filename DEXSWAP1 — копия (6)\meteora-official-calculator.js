/**
 * 🎯 METEORA DLMM ОФИЦИАЛЬНЫЙ КАЛЬКУЛЯТОР
 * 
 * Основан на официальной документации Meteora DLMM
 * Источники: docs.meteora.ag/overview/products/dlmm/
 */

class MeteoraOfficialCalculator {
    constructor() {
        // РЕАЛЬНЫЕ параметры из пула
        this.REAL_PARAMS = {
            // Протокольная комиссия (реальная из пула)
            protocol_fee: 0.00086, // 0.086% (0.86 basis points)

            // Максимальное количество бинов на позицию
            max_bins_per_position: 69,

            // Реальные параметры комиссий (нужно получить из пула)
            typical_base_fee: 0.0004, // 0.04% (4 basis points)
            typical_variable_fee: 0.004, // 0.4% (40 basis points)

            // Параметры для расчета динамических комиссий
            volatility_params: {
                filter_period: 1, // tf = 1 секунда
                decay_period: 5,  // td = 5 секунд
                decay_factor: 0.5 // R = 0.5
            }
        };
        
        console.log('🎯 MeteoraOfficialCalculator инициализирован');
        console.log('📚 Основан на официальной документации Meteora DLMM');
    }

    /**
     * 📊 РАСЧЕТ БАЗОВОЙ КОМИССИИ (из официальной документации)
     * Формула: fb = B · s
     */
    calculateBaseFee(baseFactor, binStep) {
        const baseFee = baseFactor * binStep;
        
        console.log(`📈 Расчет базовой комиссии:`);
        console.log(`   Базовый фактор (B): ${baseFactor}`);
        console.log(`   Bin Step (s): ${binStep} basis points`);
        console.log(`   Базовая комиссия (fb): ${baseFee} basis points`);
        
        return baseFee / 10000; // Конвертируем в десятичную дробь
    }

    /**
     * 🌊 РАСЧЕТ ПЕРЕМЕННОЙ КОМИССИИ (из официальной документации)
     * Формула: fv(k) = A(va(k) · s)²
     */
    calculateVariableFee(variableFeeControl, volatilityAccumulator, binStep) {
        const variableFee = variableFeeControl * Math.pow(volatilityAccumulator * binStep, 2);
        
        console.log(`🌊 Расчет переменной комиссии:`);
        console.log(`   Параметр контроля (A): ${variableFeeControl}`);
        console.log(`   Аккумулятор волатильности (va): ${volatilityAccumulator}`);
        console.log(`   Bin Step (s): ${binStep}`);
        console.log(`   Переменная комиссия (fv): ${variableFee}`);
        
        return variableFee / 10000; // Конвертируем в десятичную дробь
    }

    /**
     * 📊 РАСЧЕТ АККУМУЛЯТОРА ВОЛАТИЛЬНОСТИ (из официальной документации)
     * Формула: va(k) = vr + |ir - (activeID + k)|
     */
    calculateVolatilityAccumulator(volatilityReference, indexReference, activeID, k) {
        const volatilityAccumulator = volatilityReference + Math.abs(indexReference - (activeID + k));
        
        console.log(`📊 Расчет аккумулятора волатильности:`);
        console.log(`   Ссылка на волатильность (vr): ${volatilityReference}`);
        console.log(`   Индексная ссылка (ir): ${indexReference}`);
        console.log(`   Активный ID: ${activeID}`);
        console.log(`   k: ${k}`);
        console.log(`   Аккумулятор волатильности (va): ${volatilityAccumulator}`);
        
        return volatilityAccumulator;
    }

    /**
     * 💰 РАСЧЕТ ОБЩЕЙ КОМИССИИ ДЛЯ БИНА (из официальной документации)
     * Формула: fee = (swap_amount)k · (fb + fv)k
     */
    calculateBinFee(swapAmount, baseFee, variableFee) {
        const totalFeeRate = baseFee + variableFee;
        const totalFee = swapAmount * totalFeeRate;
        
        console.log(`💰 Расчет комиссии для бина:`);
        console.log(`   Сумма свопа: $${swapAmount.toLocaleString()}`);
        console.log(`   Базовая комиссия: ${(baseFee * 100).toFixed(4)}%`);
        console.log(`   Переменная комиссия: ${(variableFee * 100).toFixed(4)}%`);
        console.log(`   Общая комиссия: ${(totalFeeRate * 100).toFixed(4)}%`);
        console.log(`   Сумма комиссии: $${totalFee.toFixed(2)}`);
        
        return {
            totalFeeRate,
            totalFee,
            baseFee,
            variableFee
        };
    }

    /**
     * 🎯 РАСЧЕТ ДОЛИ LP В АКТИВНОМ БИНЕ (из официальной документации)
     */
    calculateLPShareInActiveBin(lpLiquidityInBin, totalLiquidityInBin) {
        const sharePercent = (lpLiquidityInBin / totalLiquidityInBin) * 100;
        
        console.log(`🎯 Расчет доли LP в активном бине:`);
        console.log(`   Ликвидность LP в бине: $${lpLiquidityInBin.toLocaleString()}`);
        console.log(`   Общая ликвидность в бине: $${totalLiquidityInBin.toLocaleString()}`);
        console.log(`   Доля LP: ${sharePercent.toFixed(2)}%`);
        
        return sharePercent / 100; // Возвращаем как десятичную дробь
    }

    /**
     * 🔥 РАСЧЕТ ZERO-SLIPPAGE ТОРГОВЛИ В АКТИВНОМ БИНЕ
     */
    calculateZeroSlippageTrading(params) {
        console.log('\n🔥 РАСЧЕТ ZERO-SLIPPAGE ТОРГОВЛИ В АКТИВНОМ БИНЕ:');
        console.log('=' .repeat(70));
        
        const {
            swapAmount,
            activeBinLiquidity,
            lpLiquidityInBin,
            baseFactor,
            binStep,
            variableFeeControl,
            volatilityAccumulator
        } = params;
        
        // 1. Проверяем, что торговля остается в активном бине
        console.log(`\n✅ ПРОВЕРКА ZERO-SLIPPAGE УСЛОВИЙ:`);
        console.log(`   Сумма торговли: $${swapAmount.toLocaleString()}`);
        console.log(`   Ликвидность активного бина: $${activeBinLiquidity.toLocaleString()}`);
        
        if (swapAmount > activeBinLiquidity) {
            console.log(`   ❌ ТОРГОВЛЯ ВЫХОДИТ ЗА ПРЕДЕЛЫ БИНА!`);
            console.log(`   ⚠️ Будет price impact и slippage!`);
            return null;
        }
        
        console.log(`   ✅ ТОРГОВЛЯ ОСТАЕТСЯ В БИНЕ - ZERO SLIPPAGE!`);
        
        // 2. Рассчитываем комиссии по официальным формулам
        const baseFee = this.calculateBaseFee(baseFactor, binStep);
        const variableFee = this.calculateVariableFee(variableFeeControl, volatilityAccumulator, binStep);
        const binFeeResult = this.calculateBinFee(swapAmount, baseFee, variableFee);
        
        // 3. Рассчитываем долю LP в активном бине
        const lpShare = this.calculateLPShareInActiveBin(lpLiquidityInBin, activeBinLiquidity);
        
        // 4. Рассчитываем доход LP от комиссий
        const lpFeeIncome = binFeeResult.totalFee * lpShare;
        
        // 5. Протокольная комиссия (реальная)
        const protocolFee = binFeeResult.totalFee * this.REAL_PARAMS.protocol_fee;
        const netFeeToLPs = binFeeResult.totalFee - protocolFee;
        const lpNetIncome = netFeeToLPs * lpShare;
        
        console.log(`\n💰 ИТОГОВЫЙ РАСЧЕТ:`);
        console.log(`   Общая комиссия: $${binFeeResult.totalFee.toFixed(2)}`);
        console.log(`   Протокольная комиссия (${(this.REAL_PARAMS.protocol_fee * 100).toFixed(3)}%): $${protocolFee.toFixed(2)}`);
        console.log(`   Комиссия для LP: $${netFeeToLPs.toFixed(2)}`);
        console.log(`   Доля LP в бине: ${(lpShare * 100).toFixed(2)}%`);
        console.log(`   Доход LP: $${lpNetIncome.toFixed(2)}`);
        
        return {
            totalFee: binFeeResult.totalFee,
            protocolFee,
            netFeeToLPs,
            lpShare,
            lpNetIncome,
            zeroSlippage: true,
            priceImpact: 0
        };
    }

    /**
     * 🎯 РАСЧЕТ АРБИТРАЖА С СОБСТВЕННОЙ ЛИКВИДНОСТЬЮ
     */
    calculateArbitrageWithOwnLiquidity(arbitrageParams) {
        console.log('\n🎯 РАСЧЕТ АРБИТРАЖА С СОБСТВЕННОЙ ЛИКВИДНОСТЬЮ:');
        console.log('=' .repeat(70));
        
        const {
            tradingVolume,
            priceDifferencePercent,
            pool1Params,
            pool2Params
        } = arbitrageParams;
        
        // Валовая прибыль от арбитража
        const grossArbitrageProfit = tradingVolume * (priceDifferencePercent / 100);
        
        console.log(`📈 Валовая прибыль от арбитража:`);
        console.log(`   Объем торговли: $${tradingVolume.toLocaleString()}`);
        console.log(`   Разница в цене: ${priceDifferencePercent}%`);
        console.log(`   Валовая прибыль: $${grossArbitrageProfit.toFixed(2)}`);
        
        // Расчет для Pool 1 (покупка)
        const pool1Result = this.calculateZeroSlippageTrading({
            swapAmount: tradingVolume,
            ...pool1Params
        });
        
        // Расчет для Pool 2 (продажа)
        const pool2Result = this.calculateZeroSlippageTrading({
            swapAmount: tradingVolume,
            ...pool2Params
        });
        
        if (!pool1Result || !pool2Result) {
            console.log(`❌ Невозможно выполнить zero-slippage арбитраж!`);
            return null;
        }
        
        // Общий доход от комиссий
        const totalFeeIncome = pool1Result.lpNetIncome + pool2Result.lpNetIncome;
        
        // Чистая прибыль
        const netProfit = grossArbitrageProfit + totalFeeIncome;
        
        console.log(`\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ АРБИТРАЖА:`);
        console.log(`   Валовая прибыль от арбитража: $${grossArbitrageProfit.toFixed(2)}`);
        console.log(`   Доход от комиссий Pool 1: $${pool1Result.lpNetIncome.toFixed(2)}`);
        console.log(`   Доход от комиссий Pool 2: $${pool2Result.lpNetIncome.toFixed(2)}`);
        console.log(`   Общий доход от комиссий: $${totalFeeIncome.toFixed(2)}`);
        console.log(`   ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(2)}`);
        console.log(`   Доходность: ${((netProfit / tradingVolume) * 100).toFixed(4)}%`);
        
        return {
            grossArbitrageProfit,
            totalFeeIncome,
            netProfit,
            profitPercent: (netProfit / tradingVolume) * 100,
            pool1Result,
            pool2Result
        };
    }
}

// Экспорт для использования
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MeteoraOfficialCalculator;
}

// Пример использования
if (require.main === module) {
    const calculator = new MeteoraOfficialCalculator();
    
    // Пример расчета арбитража с разницей 0.05%
    const arbitrageResult = calculator.calculateArbitrageWithOwnLiquidity({
        tradingVolume: 1000000, // $1M
        priceDifferencePercent: 0.05, // 0.05%
        
        pool1Params: {
            activeBinLiquidity: 2000000, // $2M в активном бине
            lpLiquidityInBin: 1500000,   // $1.5M ваша ликвидность
            baseFactor: 100,             // Базовый фактор
            binStep: 25,                 // 25 basis points
            variableFeeControl: 0.1,     // Параметр контроля
            volatilityAccumulator: 2     // Аккумулятор волатильности
        },
        
        pool2Params: {
            activeBinLiquidity: 1800000, // $1.8M в активном бине
            lpLiquidityInBin: 1200000,   // $1.2M ваша ликвидность
            baseFactor: 100,             // Базовый фактор
            binStep: 25,                 // 25 basis points
            variableFeeControl: 0.1,     // Параметр контроля
            volatilityAccumulator: 2     // Аккумулятор волатильности
        }
    });
    
    if (arbitrageResult) {
        console.log('\n✅ Арбитраж возможен с официальными параметрами Meteora!');
    }
}
