class BoundedMap extends Map {
  constructor(maxSize = 1000, ttl = 300000) {
    super();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.timestamps = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 30000);
  }
  
  set(key, value) {
    this.cleanup();
    if (this.size >= this.maxSize) {
      const firstKey = this.keys().next().value;
      this.delete(firstKey);
      this.timestamps.delete(firstKey);
    }
    this.timestamps.set(key, Date.now());
    return super.set(key, value);
  }
  
  get(key) {
    const value = super.get(key);
    if (value !== undefined) {
      this.timestamps.set(key, Date.now());
    }
    return value;
  }
  
  delete(key) {
    this.timestamps.delete(key);
    return super.delete(key);
  }
  
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];
    for (const [key, timestamp] of this.timestamps) {
      if (now - timestamp > this.ttl) {
        expiredKeys.push(key);
      }
    }
    for (const key of expiredKeys) {
      this.delete(key);
    }
    if (expiredKeys.length > 0) {
      console.log(`🧹 BoundedMap: очищено ${expiredKeys.length} expired entries`);
    }
  }
  
  clear() {
    this.timestamps.clear();
    return super.clear();
  }
  
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

module.exports = BoundedMap;