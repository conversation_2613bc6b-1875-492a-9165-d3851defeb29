#!/usr/bin/env node

/**
 * 🔥 ВОССТАНОВЛЕННАЯ РАБОЧАЯ FLASH LOAN СИСТЕМА
 * 
 * ОСНОВАНА НА РАБОЧЕЙ СТРУКТУРЕ ИЗ BMeteora.js + FLASHMeteora.js
 * ИСПОЛЬЗУЕТ ТОЛЬКО ПРОВЕРЕННЫЕ РАБОЧИЕ КОМПОНЕНТЫ
 */

const { 
    Connection, 
    PublicKey, 
    Keypair, 
    Transaction,
    ComputeBudgetProgram
} = require('@solana/web3.js');
const {
    getAssociatedTokenAddress,
    createAssociatedTokenAccountInstruction
} = require('@solana/spl-token');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ ТОЛЬКО РАБОЧИЕ КОМПОНЕНТЫ
const LowLevelMarginFiIntegration = require('./low-level-marginfi-integration.js');
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');

require('dotenv').config();

class RestoredWorkingFlashLoan {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 🏊 РАБОЧИЙ DLMM ПУЛ (ПРОВЕРЕННЫЙ)
        this.POOL_ADDRESS = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
        
        // 🪙 ТОКЕНЫ
        this.TOKENS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112')
        };
        
        // 🔥 РАБОЧИЕ КОМПОНЕНТЫ
        this.lowLevelMarginFi = null;
        this.meteoraSDK = null;
        
        console.log('🔥 RESTORED WORKING FLASH LOAN ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ LOW-LEVEL MARGINFI (РАБОЧИЙ)
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ LOW-LEVEL MARGINFI...');
        this.lowLevelMarginFi = new LowLevelMarginFiIntegration(this.connection, this.wallet);
        this.lowLevelMarginFi.parentBot = this;
        await this.lowLevelMarginFi.initialize();
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK (РАБОЧИЙ)
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK...');
        this.meteoraSDK = new MeteoraHybridImplementation(this.connection, this.wallet);
        
        console.log('   ✅ Готов к работе');
    }

    /**
     * 🔥 СОЗДАНИЕ 4 SWAP ИНСТРУКЦИЙ (КАК В РАБОЧЕЙ ВЕРСИИ)
     */
    async create4SwapInstructions() {
        console.log('\n🔥 СОЗДАНИЕ 4 SWAP ИНСТРУКЦИЙ (РАБОЧАЯ СТРУКТУРА)...');

        const instructions = [];

        // Получаем token аккаунты
        const userUSDC = await getAssociatedTokenAddress(this.TOKENS.USDC, this.wallet.publicKey);
        const userSOL = await getAssociatedTokenAddress(this.TOKENS.SOL, this.wallet.publicKey);

        console.log(`   User USDC: ${userUSDC.toString()}`);
        console.log(`   User SOL: ${userSOL.toString()}`);

        // 🔥 ПРОВЕРЯЕМ И СОЗДАЕМ ОТСУТСТВУЮЩИЕ TOKEN ACCOUNTS
        console.log('\n🔥 ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ TOKEN ACCOUNTS...');

        const usdcAccountInfo = await this.connection.getAccountInfo(userUSDC);
        const solAccountInfo = await this.connection.getAccountInfo(userSOL);

        console.log(`   USDC Account существует: ${usdcAccountInfo ? 'ДА' : 'НЕТ'}`);
        console.log(`   SOL Account существует: ${solAccountInfo ? 'ДА' : 'НЕТ'}`);

        // 🔥 СОЗДАЕМ ОТСУТСТВУЮЩИЕ АККАУНТЫ В ОТДЕЛЬНОЙ ТРАНЗАКЦИИ
        const ataInstructions = [];

        if (!usdcAccountInfo) {
            console.log('🔧 СОЗДАЕМ USDC TOKEN ACCOUNT...');
            const createUSDCIx = createAssociatedTokenAccountInstruction(
                this.wallet.publicKey, // payer
                userUSDC,              // ata
                this.wallet.publicKey, // owner
                this.TOKENS.USDC       // mint
            );
            ataInstructions.push(createUSDCIx);
            console.log('   ✅ USDC ATA инструкция подготовлена');
        }

        if (!solAccountInfo) {
            console.log('🔧 СОЗДАЕМ SOL TOKEN ACCOUNT...');
            const createSOLIx = createAssociatedTokenAccountInstruction(
                this.wallet.publicKey, // payer
                userSOL,               // ata
                this.wallet.publicKey, // owner
                this.TOKENS.SOL        // mint
            );
            ataInstructions.push(createSOLIx);
            console.log('   ✅ SOL ATA инструкция подготовлена');
        }

        // 🚀 ОТПРАВЛЯЕМ ATA ТРАНЗАКЦИЮ ЕСЛИ НУЖНО
        if (ataInstructions.length > 0) {
            console.log(`\n🚀 ОТПРАВКА ATA ТРАНЗАКЦИИ (${ataInstructions.length} инструкций)...`);

            const ataTransaction = new Transaction();
            ataInstructions.forEach(ix => ataTransaction.add(ix));

            try {
                const { blockhash } = await this.connection.getLatestBlockhash();
                ataTransaction.recentBlockhash = blockhash;
                ataTransaction.feePayer = this.wallet.publicKey;

                ataTransaction.sign(this.wallet);

                const signature = await this.connection.sendRawTransaction(
                    ataTransaction.serialize(),
                    { skipPreflight: false, preflightCommitment: 'processed' }
                );

                console.log(`✅ ATA транзакция отправлена: ${signature}`);

                // Ждем подтверждения
                await this.connection.confirmTransaction(signature, 'processed');
                console.log(`✅ ATA транзакция подтверждена`);

            } catch (error) {
                console.error(`❌ Ошибка создания ATA: ${error.message}`);
                throw error;
            }
        }

        console.log(`🔧 ATA аккаунты готовы`);
        
        // 🔥 ПАРАМЕТРЫ КАК В РАБОЧЕЙ ВЕРСИИ
        const POOL_ADDRESS = this.POOL_ADDRESS.toString();
        const conversionAmount = 262738 * 1e6; // $262K
        const firstSwapAmount = 210190 * 1e6;  // $210K
        const secondSwapAmount = 854 * 1e9;    // ~854 SOL
        
        try {
            // 1️⃣ КОНВЕРТАЦИЯ USDC → SOL
            console.log('1️⃣ СОЗДАНИЕ КОНВЕРТАЦИИ USDC → SOL...');
            const conversionResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                conversionAmount,
                Math.floor(conversionAmount * 0.98), // 2% slippage
                true, // USDC → SOL
                userUSDC,
                userSOL
            );
            
            if (conversionResult.success) {
                instructions.push(...conversionResult.instructions);
                console.log(`   ✅ Конвертация: ${conversionResult.instructions.length} инструкций`);
            } else {
                throw new Error(`Конвертация провалена: ${conversionResult.error}`);
            }
            
            // 2️⃣ ПЕРВЫЙ АРБИТРАЖНЫЙ SWAP (USDC → SOL)
            console.log('2️⃣ СОЗДАНИЕ ПЕРВОГО АРБИТРАЖНОГО SWAP...');
            const firstSwapResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                firstSwapAmount,
                null, // minAmountOut рассчитается SDK
                true, // USDC → SOL
                userUSDC,
                userSOL
            );
            
            if (firstSwapResult.success) {
                instructions.push(...firstSwapResult.instructions);
                console.log(`   ✅ Первый swap: ${firstSwapResult.instructions.length} инструкций`);
            } else {
                throw new Error(`Первый swap провален: ${firstSwapResult.error}`);
            }
            
            // 3️⃣ ВТОРОЙ АРБИТРАЖНЫЙ SWAP (SOL → USDC)
            console.log('3️⃣ СОЗДАНИЕ ВТОРОГО АРБИТРАЖНОГО SWAP...');
            const secondSwapResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                secondSwapAmount,
                null, // minAmountOut рассчитается SDK
                false, // SOL → USDC
                userSOL,
                userUSDC
            );
            
            if (secondSwapResult.success) {
                instructions.push(...secondSwapResult.instructions);
                console.log(`   ✅ Второй swap: ${secondSwapResult.instructions.length} инструкций`);
            } else {
                throw new Error(`Второй swap провален: ${secondSwapResult.error}`);
            }
            
            // 4️⃣ КОНВЕРТАЦИЯ SOL → USDC (ОБРАТНО)
            console.log('4️⃣ СОЗДАНИЕ ОБРАТНОЙ КОНВЕРТАЦИИ SOL → USDC...');

            // 🔥 ИСПОЛЬЗУЕМ ФИКСИРОВАННУЮ СУММУ ВМЕСТО 'ALL_SOL_BALANCE'
            const reconversionAmount = 1000 * 1e9; // 1000 SOL (достаточно для теста)

            const reconversionResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                reconversionAmount, // Фиксированная сумма
                null,
                false, // SOL → USDC
                userSOL,
                userUSDC
            );
            
            if (reconversionResult.success) {
                instructions.push(...reconversionResult.instructions);
                console.log(`   ✅ Обратная конвертация: ${reconversionResult.instructions.length} инструкций`);
            } else {
                throw new Error(`Обратная конвертация провалена: ${reconversionResult.error}`);
            }
            
            console.log(`🎉 ВСЕ 4 SWAP ИНСТРУКЦИИ СОЗДАНЫ: ${instructions.length} инструкций`);
            
            return {
                success: true,
                instructions: instructions,
                count: instructions.length
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ SWAP ИНСТРУКЦИЙ: ${error.message}`);
            return {
                success: false,
                error: error.message,
                instructions: []
            };
        }
    }

    /**
     * 🔥 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ (РАБОЧАЯ СТРУКТУРА)
     */
    async createWorkingFlashLoanTransaction() {
        console.log('\n🔥 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ...');
        console.log('📋 ИСПОЛЬЗУЕМ РАБОЧУЮ СТРУКТУРУ ИЗ BMeteora.js + FLASHMeteora.js');
        
        try {
            // 1️⃣ СОЗДАЕМ 4 SWAP ИНСТРУКЦИИ
            const swapResult = await this.create4SwapInstructions();
            
            if (!swapResult.success) {
                throw new Error(`Swap инструкции провалены: ${swapResult.error}`);
            }
            
            console.log(`✅ Swap инструкции готовы: ${swapResult.count}`);
            
            // 2️⃣ СОЗДАЕМ FLASH LOAN ЧЕРЕЗ LOW-LEVEL MARGINFI
            console.log('\n🔥 СОЗДАНИЕ FLASH LOAN ЧЕРЕЗ LOW-LEVEL MARGINFI...');
            
            const flashLoanAmount = 875793 * 1e6; // $875K USDC
            
            const flashLoanResult = await this.lowLevelMarginFi.createStealthBorrowRepayCycle(
                flashLoanAmount / 1e6, // Конвертируем в доллары
                this.lowLevelMarginFi.marginfiAccount,
                swapResult.instructions // 🔥 ПЕРЕДАЕМ РАБОЧИЕ SWAP ИНСТРУКЦИИ!
            );
            
            if (flashLoanResult.success) {
                console.log(`🎉 FLASH LOAN ТРАНЗАКЦИЯ СОЗДАНА УСПЕШНО!`);
                console.log(`📊 Всего инструкций: ${flashLoanResult.instructions?.length || 'N/A'}`);
                
                return {
                    success: true,
                    transaction: flashLoanResult.transaction,
                    instructions: flashLoanResult.instructions,
                    swapCount: swapResult.count
                };
            } else {
                throw new Error(`Flash Loan провален: ${flashLoanResult.error}`);
            }
            
        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ FLASH LOAN: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🧪 ТЕСТ ПОЛНОЙ СИСТЕМЫ
     */
    async testFullSystem() {
        console.log('\n🧪 ТЕСТ ПОЛНОЙ ВОССТАНОВЛЕННОЙ СИСТЕМЫ...');
        
        try {
            // Создаем транзакцию
            const result = await this.createWorkingFlashLoanTransaction();
            
            if (result.success) {
                console.log(`🎉 СИСТЕМА ПОЛНОСТЬЮ РАБОЧАЯ!`);
                console.log(`✅ Swap инструкций: ${result.swapCount}`);
                console.log(`✅ Flash Loan: СОЗДАН`);
                console.log(`✅ Транзакция: ГОТОВА`);
                
                // 🧪 СИМУЛЯЦИЯ (ОПЦИОНАЛЬНО)
                if (result.transaction) {
                    console.log('\n🧪 ЗАПУСК СИМУЛЯЦИИ...');
                    
                    try {
                        const simulation = await this.connection.simulateTransaction(
                            result.transaction,
                            { sigVerify: false, commitment: 'processed' }
                        );
                        
                        if (simulation.value.err) {
                            console.log(`❌ Симуляция провалена: ${JSON.stringify(simulation.value.err)}`);
                        } else {
                            console.log(`✅ СИМУЛЯЦИЯ УСПЕШНА!`);
                            console.log(`   Compute Units: ${simulation.value.unitsConsumed || 'N/A'}`);
                        }
                    } catch (simError) {
                        console.log(`⚠️ Симуляция недоступна: ${simError.message}`);
                    }
                }
                
                return { success: true, result };
            } else {
                console.log(`❌ СИСТЕМА НЕ РАБОТАЕТ: ${result.error}`);
                return { success: false, error: result.error };
            }
            
        } catch (error) {
            console.error(`💥 КРИТИЧЕСКАЯ ОШИБКА: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

// 🚀 ЗАПУСК
if (require.main === module) {
    async function main() {
        const flashLoan = new RestoredWorkingFlashLoan();
        
        try {
            await flashLoan.initialize();
            const result = await flashLoan.testFullSystem();
            
            if (result.success) {
                console.log('\n🎉 ВОССТАНОВЛЕННАЯ СИСТЕМА ПОЛНОСТЬЮ РАБОТАЕТ!');
            } else {
                console.log('\n❌ СИСТЕМА ТРЕБУЕТ ДОПОЛНИТЕЛЬНЫХ ИСПРАВЛЕНИЙ');
            }
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        }
    }
    
    main().catch(console.error);
}

module.exports = RestoredWorkingFlashLoan;
