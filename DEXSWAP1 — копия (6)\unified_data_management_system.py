#!/usr/bin/env python3
"""
🚀 UNIFIED DATA MANAGEMENT SYSTEM
Максимально эффективная система для управления данными и ретестирования
Интеграция 90+ стратегий с Immunefi данными
"""

import asyncio
import json
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestTarget:
    """Цель для тестирования"""
    target_id: str
    target_type: str  # immunefi_program, contract, api_endpoint, github_repo
    name: str
    url: str
    contracts: List[str]
    endpoints: List[str]
    metadata: Dict[str, Any]
    priority_score: float
    last_tested: Optional[datetime] = None
    test_count: int = 0
    vulnerability_count: int = 0
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        if self.last_tested:
            data['last_tested'] = self.last_tested.isoformat()
        return data

@dataclass
class TestResult:
    """Результат тестирования"""
    result_id: str
    target_id: str
    strategy_name: str
    strategy_type: str  # quantum, ai, mathematical, future
    test_timestamp: datetime
    success: bool
    vulnerability_found: bool
    vulnerability_type: str
    severity: str
    confidence: float
    execution_time: float
    raw_data: Dict[str, Any]
    proof_of_concept: str
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['test_timestamp'] = self.test_timestamp.isoformat()
        return data

class UnifiedDataManager:
    """Унифицированный менеджер данных"""
    
    def __init__(self, db_path: str = "unified_bug_hunting.db"):
        self.db_path = db_path
        self.conn = None
        self.strategy_cache = {}
        self.target_cache = {}
        
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        await self.initialize_database()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие соединения"""
        if self.conn:
            self.conn.close()
    
    async def initialize_database(self):
        """Инициализация базы данных"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row
        
        # Создание таблиц
        await self._create_tables()
        logger.info(f"База данных инициализирована: {self.db_path}")
    
    async def _create_tables(self):
        """Создание таблиц базы данных"""
        cursor = self.conn.cursor()
        
        # Таблица целей тестирования
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_targets (
                target_id TEXT PRIMARY KEY,
                target_type TEXT NOT NULL,
                name TEXT NOT NULL,
                url TEXT,
                contracts TEXT,  -- JSON array
                endpoints TEXT,  -- JSON array
                metadata TEXT,   -- JSON object
                priority_score REAL,
                last_tested TEXT,
                test_count INTEGER DEFAULT 0,
                vulnerability_count INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Таблица результатов тестирования
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_results (
                result_id TEXT PRIMARY KEY,
                target_id TEXT NOT NULL,
                strategy_name TEXT NOT NULL,
                strategy_type TEXT NOT NULL,
                test_timestamp TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                vulnerability_found BOOLEAN NOT NULL,
                vulnerability_type TEXT,
                severity TEXT,
                confidence REAL,
                execution_time REAL,
                raw_data TEXT,  -- JSON object
                proof_of_concept TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (target_id) REFERENCES test_targets (target_id)
            )
        """)
        
        # Таблица стратегий
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategies (
                strategy_name TEXT PRIMARY KEY,
                strategy_type TEXT NOT NULL,
                enabled BOOLEAN DEFAULT TRUE,
                success_rate REAL DEFAULT 0.0,
                avg_execution_time REAL DEFAULT 0.0,
                total_executions INTEGER DEFAULT 0,
                vulnerabilities_found INTEGER DEFAULT 0,
                last_used TEXT,
                configuration TEXT,  -- JSON object
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Таблица сессий тестирования
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_sessions (
                session_id TEXT PRIMARY KEY,
                session_name TEXT,
                start_time TEXT NOT NULL,
                end_time TEXT,
                targets_tested INTEGER DEFAULT 0,
                strategies_used INTEGER DEFAULT 0,
                vulnerabilities_found INTEGER DEFAULT 0,
                total_execution_time REAL DEFAULT 0.0,
                configuration TEXT,  -- JSON object
                status TEXT DEFAULT 'running',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Индексы для производительности
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_target_type ON test_targets(target_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_priority_score ON test_targets(priority_score DESC)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_timestamp ON test_results(test_timestamp)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_vulnerability_found ON test_results(vulnerability_found)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_strategy_type ON test_results(strategy_type)")
        
        self.conn.commit()
    
    async def import_immunefi_data(self, immunefi_programs: List[Dict]) -> int:
        """Импорт данных программ Immunefi"""
        imported_count = 0
        
        for program in immunefi_programs:
            target = TestTarget(
                target_id=self._generate_target_id(program),
                target_type="immunefi_program",
                name=program.get('name', 'Unknown'),
                url=program.get('url', ''),
                contracts=program.get('contracts', []),
                endpoints=program.get('endpoints', []),
                metadata={
                    'max_bounty': program.get('max_bounty', 'Private'),
                    'total_paid': program.get('total_paid', 'Private'),
                    'vault_tvl': program.get('vault_tvl', 'Private'),
                    'kyc_required': program.get('kyc_required', False),
                    'poc_required': program.get('poc_required', False),
                    'vulnerability_types': program.get('vulnerability_types', []),
                    'ecosystem': program.get('ecosystem', ''),
                    'last_updated': program.get('last_updated', ''),
                },
                priority_score=program.get('priority_score', 0.5)
            )
            
            await self.save_target(target)
            imported_count += 1
        
        logger.info(f"Импортировано {imported_count} программ Immunefi")
        return imported_count
    
    def _generate_target_id(self, data: Dict) -> str:
        """Генерация уникального ID цели"""
        key_data = f"{data.get('name', '')}{data.get('url', '')}{data.get('slug', '')}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def save_target(self, target: TestTarget):
        """Сохранение цели тестирования"""
        cursor = self.conn.cursor()
        
        cursor.execute("""
            INSERT OR REPLACE INTO test_targets 
            (target_id, target_type, name, url, contracts, endpoints, metadata, 
             priority_score, last_tested, test_count, vulnerability_count, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            target.target_id,
            target.target_type,
            target.name,
            target.url,
            json.dumps(target.contracts),
            json.dumps(target.endpoints),
            json.dumps(target.metadata),
            target.priority_score,
            target.last_tested.isoformat() if target.last_tested else None,
            target.test_count,
            target.vulnerability_count,
            datetime.now().isoformat()
        ))
        
        self.conn.commit()
        self.target_cache[target.target_id] = target
    
    async def save_test_result(self, result: TestResult):
        """Сохранение результата тестирования"""
        cursor = self.conn.cursor()
        
        cursor.execute("""
            INSERT INTO test_results 
            (result_id, target_id, strategy_name, strategy_type, test_timestamp,
             success, vulnerability_found, vulnerability_type, severity, confidence,
             execution_time, raw_data, proof_of_concept)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            result.result_id,
            result.target_id,
            result.strategy_name,
            result.strategy_type,
            result.test_timestamp.isoformat(),
            result.success,
            result.vulnerability_found,
            result.vulnerability_type,
            result.severity,
            result.confidence,
            result.execution_time,
            json.dumps(result.raw_data),
            result.proof_of_concept
        ))
        
        # Обновляем статистику цели
        if result.vulnerability_found:
            cursor.execute("""
                UPDATE test_targets 
                SET vulnerability_count = vulnerability_count + 1,
                    test_count = test_count + 1,
                    last_tested = ?,
                    updated_at = ?
                WHERE target_id = ?
            """, (
                result.test_timestamp.isoformat(),
                datetime.now().isoformat(),
                result.target_id
            ))
        else:
            cursor.execute("""
                UPDATE test_targets 
                SET test_count = test_count + 1,
                    last_tested = ?,
                    updated_at = ?
                WHERE target_id = ?
            """, (
                result.test_timestamp.isoformat(),
                datetime.now().isoformat(),
                result.target_id
            ))
        
        self.conn.commit()
    
    async def get_targets_for_testing(self, 
                                    target_type: Optional[str] = None,
                                    min_priority: float = 0.0,
                                    max_test_count: int = 100,
                                    limit: int = 50) -> List[TestTarget]:
        """Получение целей для тестирования"""
        cursor = self.conn.cursor()
        
        query = """
            SELECT * FROM test_targets 
            WHERE priority_score >= ? AND test_count <= ?
        """
        params = [min_priority, max_test_count]
        
        if target_type:
            query += " AND target_type = ?"
            params.append(target_type)
        
        query += " ORDER BY priority_score DESC, test_count ASC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        targets = []
        for row in rows:
            target = TestTarget(
                target_id=row['target_id'],
                target_type=row['target_type'],
                name=row['name'],
                url=row['url'] or '',
                contracts=json.loads(row['contracts'] or '[]'),
                endpoints=json.loads(row['endpoints'] or '[]'),
                metadata=json.loads(row['metadata'] or '{}'),
                priority_score=row['priority_score'],
                last_tested=datetime.fromisoformat(row['last_tested']) if row['last_tested'] else None,
                test_count=row['test_count'],
                vulnerability_count=row['vulnerability_count']
            )
            targets.append(target)
        
        return targets
    
    async def get_retest_candidates(self, 
                                  days_since_last_test: int = 7,
                                  min_vulnerability_count: int = 1) -> List[TestTarget]:
        """Получение кандидатов для ретестирования"""
        cursor = self.conn.cursor()
        
        cutoff_date = (datetime.now() - timedelta(days=days_since_last_test)).isoformat()
        
        cursor.execute("""
            SELECT * FROM test_targets 
            WHERE (last_tested IS NULL OR last_tested < ?)
            AND vulnerability_count >= ?
            ORDER BY vulnerability_count DESC, priority_score DESC
        """, (cutoff_date, min_vulnerability_count))
        
        rows = cursor.fetchall()
        
        targets = []
        for row in rows:
            target = TestTarget(
                target_id=row['target_id'],
                target_type=row['target_type'],
                name=row['name'],
                url=row['url'] or '',
                contracts=json.loads(row['contracts'] or '[]'),
                endpoints=json.loads(row['endpoints'] or '[]'),
                metadata=json.loads(row['metadata'] or '{}'),
                priority_score=row['priority_score'],
                last_tested=datetime.fromisoformat(row['last_tested']) if row['last_tested'] else None,
                test_count=row['test_count'],
                vulnerability_count=row['vulnerability_count']
            )
            targets.append(target)
        
        return targets
    
    async def get_strategy_performance(self) -> Dict[str, Dict]:
        """Получение статистики производительности стратегий"""
        cursor = self.conn.cursor()
        
        cursor.execute("""
            SELECT 
                strategy_name,
                strategy_type,
                COUNT(*) as total_executions,
                AVG(execution_time) as avg_execution_time,
                SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful_executions,
                SUM(CASE WHEN vulnerability_found THEN 1 ELSE 0 END) as vulnerabilities_found,
                AVG(confidence) as avg_confidence
            FROM test_results 
            GROUP BY strategy_name, strategy_type
            ORDER BY vulnerabilities_found DESC, avg_confidence DESC
        """)
        
        rows = cursor.fetchall()
        
        performance = {}
        for row in rows:
            success_rate = row['successful_executions'] / row['total_executions'] if row['total_executions'] > 0 else 0
            vulnerability_rate = row['vulnerabilities_found'] / row['total_executions'] if row['total_executions'] > 0 else 0
            
            performance[row['strategy_name']] = {
                'strategy_type': row['strategy_type'],
                'total_executions': row['total_executions'],
                'avg_execution_time': row['avg_execution_time'],
                'success_rate': success_rate,
                'vulnerability_rate': vulnerability_rate,
                'vulnerabilities_found': row['vulnerabilities_found'],
                'avg_confidence': row['avg_confidence']
            }
        
        return performance
    
    async def generate_testing_report(self, session_id: Optional[str] = None) -> Dict:
        """Генерация отчета о тестировании"""
        cursor = self.conn.cursor()
        
        # Общая статистика
        cursor.execute("SELECT COUNT(*) as total_targets FROM test_targets")
        total_targets = cursor.fetchone()['total_targets']
        
        cursor.execute("SELECT COUNT(*) as total_results FROM test_results")
        total_results = cursor.fetchone()['total_results']
        
        cursor.execute("SELECT COUNT(*) as total_vulnerabilities FROM test_results WHERE vulnerability_found = 1")
        total_vulnerabilities = cursor.fetchone()['total_vulnerabilities']
        
        # Статистика по типам стратегий
        cursor.execute("""
            SELECT 
                strategy_type,
                COUNT(*) as executions,
                SUM(CASE WHEN vulnerability_found THEN 1 ELSE 0 END) as vulnerabilities
            FROM test_results 
            GROUP BY strategy_type
        """)
        
        strategy_stats = {}
        for row in cursor.fetchall():
            strategy_stats[row['strategy_type']] = {
                'executions': row['executions'],
                'vulnerabilities': row['vulnerabilities'],
                'success_rate': row['vulnerabilities'] / row['executions'] if row['executions'] > 0 else 0
            }
        
        # Топ уязвимые цели
        cursor.execute("""
            SELECT name, vulnerability_count, test_count
            FROM test_targets 
            WHERE vulnerability_count > 0
            ORDER BY vulnerability_count DESC
            LIMIT 10
        """)
        
        top_vulnerable = [dict(row) for row in cursor.fetchall()]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'session_id': session_id,
            'summary': {
                'total_targets': total_targets,
                'total_test_results': total_results,
                'total_vulnerabilities': total_vulnerabilities,
                'vulnerability_rate': total_vulnerabilities / total_results if total_results > 0 else 0
            },
            'strategy_performance': strategy_stats,
            'top_vulnerable_targets': top_vulnerable,
            'performance_metrics': await self.get_strategy_performance()
        }

async def main():
    """Демонстрация системы управления данными"""
    print("🚀 UNIFIED DATA MANAGEMENT SYSTEM")
    print("=" * 60)
    
    async with UnifiedDataManager() as dm:
        # Пример импорта данных
        sample_immunefi_data = [
            {
                'name': 'Test Protocol',
                'url': 'https://immunefi.com/bounty/test/',
                'contracts': ['0x1234...'],
                'endpoints': ['https://api.test.com'],
                'max_bounty': '$100K',
                'priority_score': 0.8
            }
        ]
        
        await dm.import_immunefi_data(sample_immunefi_data)
        
        # Получение целей для тестирования
        targets = await dm.get_targets_for_testing(limit=5)
        print(f"📊 Найдено {len(targets)} целей для тестирования")
        
        # Генерация отчета
        report = await dm.generate_testing_report()
        print(f"📄 Отчет сгенерирован: {report['summary']}")

if __name__ == "__main__":
    asyncio.run(main())
