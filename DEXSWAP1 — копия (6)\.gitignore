# 🔐 КРИТИЧЕСКИ ВАЖНО: Файлы с приватными ключами и API ключами
.env
.env.solana
.env.development.local
.env.test.local
.env.production.local
.env.local
*.key
*.pem
private-keys/
secrets/

# 🔐 Кошельки и ключи
wallet.json
keypair.json
id.json
*.wallet

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# node_modules
node_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# 🗑️ Временные файлы и кэши
*.tmp
*.temp
cache/
logs/
debug/

# 🔧 IDE и редакторы
.vscode/
.idea/
*.swp
*.swo
*~

# 🏗️ Build артефакты
build/
target/
*.exe
*.dll
*.so
*.dylib

# 🧪 Тестовые файлы
test-*.js
*-test.js
*.test.js

# 📊 Отчеты и аналитика
reports/
analytics/
*.report
*.analysis

# 🔄 Backup файлы
*.bak
*.backup
backup/
