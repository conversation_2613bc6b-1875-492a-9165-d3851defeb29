#!/usr/bin/env node

/**
 * 🎯 ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР НАШЕЙ СТРАТЕГИИ
 * 
 * Точный расчет по нашей логике:
 * 1. Flash Loan $1,800,000 USDC
 * 2. Добавить ликвидность $1,400,000 USDC в средний пул
 * 3. Купить SOL за $400,000 USDC в большом пуле (дешево)
 * 4. Продать SOL в нашем пуле (дорого из-за нашей ликвидности)
 * 5. Забрать ликвидность $1,400,000 + прибыль
 * 6. Вернуть займ $1,800,000 USDC
 */

class CorrectStrategyCalculator {
    constructor() {
        // 🎯 ТОЧНЫЕ ПАРАМЕТРЫ ТВОЕЙ СТРАТЕГИИ
        this.STRATEGY = {
            flash_loan: 1800000,      // $1.8M USDC займ (ТОЧНО КАК ТЫ СКАЗАЛ)
            liquidity_add: 1400000,   // $1.4M USDC в ликвидность (ТОЧНО КАК ТЫ СКАЗАЛ)
            trading_amount: 400000,   // $400K USDC на торговлю (ТОЧНО КАК ТЫ СКАЗАЛ)
            target_roi: 3.0           // 3% цель
        };

        // 📊 АКТУАЛЬНЫЕ ДАННЫЕ ПУЛОВ (РЕАЛЬНЫЕ ЦЕНЫ)
        this.POOLS = {
            large: {
                name: 'Большой пул (покупка SOL)',
                tvl: 4050000,
                sol_price: 171.01,        // АКТУАЛЬНАЯ ЦЕНА
                liquidity: 'высокая',
                slippage: 0.002           // 0.2% slippage
            },
            medium: {
                name: 'Средний пул (наша ликвидность)',
                tvl: 1200000,             // Меньший пул = больше влияние
                sol_price: 171.01,        // Базовая цена (до нашей ликвидности)
                bin_step: 10,             // 10 bps
                dynamic_fee: 0.005        // 0.5%
            }
        };

        console.log('🎯 ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР СТРАТЕГИИ ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🧮 РАСЧЕТ ВЛИЯНИЯ НАШЕЙ ЛИКВИДНОСТИ НА ЦЕНУ
     */
    calculateLiquidityImpact() {
        console.log('\n🧮 РАСЧЕТ ВЛИЯНИЯ НАШЕЙ ЛИКВИДНОСТИ...');
        
        const ourLiquidity = this.STRATEGY.liquidity_add;
        const poolTvl = this.POOLS.medium.tvl;
        const liquidityRatio = ourLiquidity / poolTvl;
        
        console.log(`   Наша ликвидность: $${ourLiquidity.toLocaleString()}`);
        console.log(`   TVL пула: $${poolTvl.toLocaleString()}`);
        console.log(`   Наша доля: ${(liquidityRatio * 100).toFixed(1)}%`);
        
        // Влияние на цену: чем больше ликвидности, тем выше цена
        // Для DLMM: 77.8% ликвидности = ~15-20% рост цены
        const priceImpactPercent = liquidityRatio * 25; // 25% влияние на 100% ликвидности
        const newPrice = this.POOLS.medium.sol_price * (1 + priceImpactPercent / 100);
        const priceIncrease = newPrice - this.POOLS.medium.sol_price;
        
        console.log(`   Влияние на цену: +${priceImpactPercent.toFixed(2)}%`);
        console.log(`   Новая цена SOL: $${newPrice.toFixed(2)}`);
        console.log(`   Рост цены: +$${priceIncrease.toFixed(2)}`);
        
        return {
            liquidityRatio: liquidityRatio,
            priceImpactPercent: priceImpactPercent,
            oldPrice: this.POOLS.medium.sol_price,
            newPrice: newPrice,
            priceIncrease: priceIncrease
        };
    }

    /**
     * 📊 РАСЧЕТ BINS ДЛЯ ПОКРЫТИЯ ТОРГОВЛИ
     */
    calculateOptimalBins(liquidityImpact) {
        console.log('\n📊 РАСЧЕТ BINS ДЛЯ ПОКРЫТИЯ $400K ТОРГОВЛИ...');
        
        const tradingAmount = this.STRATEGY.trading_amount;
        const buyPrice = this.POOLS.large.sol_price;
        const solToBuy = tradingAmount / buyPrice;
        
        console.log(`   Покупаем: ${solToBuy.toFixed(2)} SOL за $${tradingAmount.toLocaleString()}`);
        console.log(`   Цена покупки: $${buyPrice}`);
        
        // Создаем bins выше текущей цены для продажи SOL
        const bins = [];
        const binStep = this.POOLS.medium.bin_step / 10000; // 0.001 (10 bps)
        const basePrice = this.POOLS.medium.sol_price;
        const liquidityPerBin = this.STRATEGY.liquidity_add / 8; // 8 основных bins
        
        for (let i = 1; i <= 8; i++) {
            const binPrice = basePrice * Math.pow(1 + binStep, i); // Цены ВЫШЕ текущей
            const solCoverage = liquidityPerBin / binPrice;
            
            bins.push({
                index: i,
                price: binPrice,
                usdcAmount: liquidityPerBin,
                solCoverage: solCoverage
            });
            
            console.log(`   Bin ${i}: $${binPrice.toFixed(2)} | USDC: $${liquidityPerBin.toLocaleString()} | Покрытие: ${solCoverage.toFixed(2)} SOL`);
        }
        
        const totalCoverage = bins.reduce((sum, bin) => sum + bin.solCoverage, 0);
        const coverageRatio = totalCoverage / solToBuy;
        
        console.log(`\n   Общее покрытие: ${totalCoverage.toFixed(2)} SOL`);
        console.log(`   Нужно покрыть: ${solToBuy.toFixed(2)} SOL`);
        console.log(`   Коэффициент покрытия: ${(coverageRatio * 100).toFixed(1)}%`);
        console.log(`   ${coverageRatio >= 1.5 ? '✅ ДОСТАТОЧНО' : '❌ НЕДОСТАТОЧНО'} (цель: 150%+)`);
        
        return {
            bins: bins,
            totalCoverage: totalCoverage,
            solToBuy: solToBuy,
            coverageRatio: coverageRatio,
            sufficient: coverageRatio >= 1.5
        };
    }

    /**
     * 💰 РАСЧЕТ ПРИБЫЛЬНОСТИ СТРАТЕГИИ
     */
    calculateProfitability(liquidityImpact, binsData) {
        console.log('\n💰 РАСЧЕТ ПРИБЫЛЬНОСТИ СТРАТЕГИИ...');
        
        const tradingAmount = this.STRATEGY.trading_amount;
        const solToBuy = binsData.solToBuy;
        
        console.log('\n📋 ПОШАГОВЫЙ РАСЧЕТ ПРИБЫЛИ:');
        
        // ШАГ 1: Покупка SOL в большом пуле
        const buyPrice = this.POOLS.large.sol_price;
        const buySlippage = tradingAmount * this.POOLS.large.slippage;
        const buyCost = tradingAmount + buySlippage;
        console.log(`   1️⃣ Покупка: ${solToBuy.toFixed(2)} SOL по $${buyPrice} + slippage $${buySlippage} = $${buyCost.toLocaleString()}`);
        
        // ШАГ 2: Продажа SOL в нашем пуле
        // Цена будет падать по мере продажи, используем среднюю цену
        const maxPrice = liquidityImpact.newPrice;
        const minPrice = liquidityImpact.oldPrice;
        const avgSellPrice = (maxPrice + minPrice) / 2; // Средняя цена продажи
        const sellRevenue = solToBuy * avgSellPrice;
        const sellSlippage = sellRevenue * 0.003; // 0.3% slippage при продаже
        const netSellRevenue = sellRevenue - sellSlippage;
        
        console.log(`   2️⃣ Продажа: ${solToBuy.toFixed(2)} SOL по средней цене $${avgSellPrice.toFixed(2)} = $${sellRevenue.toLocaleString()}`);
        console.log(`      Slippage при продаже: -$${sellSlippage.toFixed(0)}`);
        console.log(`      Чистая выручка: $${netSellRevenue.toLocaleString()}`);
        
        // ШАГ 3: Прибыль от торговли
        const tradingProfit = netSellRevenue - buyCost;
        console.log(`   3️⃣ Прибыль от торговли: $${netSellRevenue.toLocaleString()} - $${buyCost.toLocaleString()} = $${tradingProfit.toFixed(0)}`);
        
        // ШАГ 4: Комиссии от торговли в нашем пуле
        const tradingVolume = sellRevenue;
        const ourLiquidityShare = this.STRATEGY.liquidity_add / (this.POOLS.medium.tvl + this.STRATEGY.liquidity_add);
        const totalFees = tradingVolume * this.POOLS.medium.dynamic_fee;
        const ourFeeShare = totalFees * ourLiquidityShare;
        
        console.log(`   4️⃣ Комиссии:`);
        console.log(`      Объем торговли: $${tradingVolume.toLocaleString()}`);
        console.log(`      Наша доля ликвидности: ${(ourLiquidityShare * 100).toFixed(1)}%`);
        console.log(`      Общие комиссии: $${totalFees.toFixed(0)}`);
        console.log(`      Наша доля комиссий: $${ourFeeShare.toFixed(0)}`);
        
        // ШАГ 5: Общие расходы
        const gasFees = 8000; // Gas для сложной транзакции
        const protocolFees = (buyCost + sellRevenue) * 0.001; // 0.1% protocol fees
        const totalCosts = gasFees + protocolFees;
        
        console.log(`   5️⃣ Дополнительные расходы:`);
        console.log(`      Gas fees: $${gasFees}`);
        console.log(`      Protocol fees: $${protocolFees.toFixed(0)}`);
        console.log(`      Всего доп. расходов: $${totalCosts.toFixed(0)}`);
        
        // ШАГ 6: Итоговая прибыль
        const totalProfit = tradingProfit + ourFeeShare - totalCosts;
        const roi = (totalProfit / this.STRATEGY.flash_loan) * 100;
        const isProfitable = roi >= this.STRATEGY.target_roi;
        
        console.log(`\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
        console.log(`   💰 Прибыль от торговли: $${tradingProfit.toFixed(0)}`);
        console.log(`   💰 Доход от комиссий: $${ourFeeShare.toFixed(0)}`);
        console.log(`   💸 Дополнительные расходы: -$${totalCosts.toFixed(0)}`);
        console.log(`   🎯 ЧИСТАЯ ПРИБЫЛЬ: $${totalProfit.toFixed(0)}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}% (цель: ${this.STRATEGY.target_roi}%)`);
        console.log(`   ${isProfitable ? '✅ ПРИБЫЛЬНО' : '❌ НЕ ПРИБЫЛЬНО'}`);
        
        return {
            tradingProfit: tradingProfit,
            feeIncome: ourFeeShare,
            totalCosts: totalCosts,
            netProfit: totalProfit,
            roi: roi,
            isProfitable: isProfitable,
            details: {
                buyPrice: buyPrice,
                avgSellPrice: avgSellPrice,
                solToBuy: solToBuy,
                buyCost: buyCost,
                sellRevenue: netSellRevenue,
                ourLiquidityShare: ourLiquidityShare
            }
        };
    }

    /**
     * 🚀 ПОЛНЫЙ РАСЧЕТ СТРАТЕГИИ
     */
    async calculateCompleteStrategy() {
        console.log('🚀 ПОЛНЫЙ РАСЧЕТ НАШЕЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        try {
            // 1. Влияние ликвидности на цену
            const liquidityImpact = this.calculateLiquidityImpact();
            
            // 2. Расчет bins для покрытия
            const binsData = this.calculateOptimalBins(liquidityImpact);
            
            if (!binsData.sufficient) {
                throw new Error('Недостаточное покрытие торговли bins');
            }
            
            // 3. Расчет прибыльности
            const profitability = this.calculateProfitability(liquidityImpact, binsData);
            
            console.log('\n🎉 РАСЧЕТ ЗАВЕРШЕН!');
            console.log(`💰 Чистая прибыль: $${profitability.netProfit.toFixed(0)}`);
            console.log(`📈 ROI: ${profitability.roi.toFixed(2)}%`);
            console.log(`🎯 Статус: ${profitability.isProfitable ? 'ПРИБЫЛЬНО' : 'НЕ ПРИБЫЛЬНО'}`);
            
            return {
                liquidityImpact,
                binsData,
                profitability,
                success: true
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА РАСЧЕТА:', error.message);
            return {
                error: error.message,
                success: false
            };
        }
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    async function runCorrectCalculation() {
        const calculator = new CorrectStrategyCalculator();
        const result = await calculator.calculateCompleteStrategy();
        
        if (result.success) {
            console.log('\n✅ РАСЧЕТ УСПЕШЕН!');
        } else {
            console.log('\n❌ РАСЧЕТ ПРОВАЛЕН!');
        }
    }
    
    runCorrectCalculation().catch(console.error);
}

module.exports = CorrectStrategyCalculator;
