/**
 * 🔧 ЦЕНТРАЛИЗОВАННАЯ КОНФИГУРАЦИЯ ТОКЕН-АККАУНТОВ КОШЕЛЬКА
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🔑 КОШЕЛЕК: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV
 * 📅 СОЗДАНО: 2025-07-02 (активированы все аккаунты)
 * 💰 СТОИМОСТЬ АКТИВАЦИИ: ~0.006132 SOL
 * 🎯 JUPITER: Использует единые настройки из jupiter-config-unified.js
 * ═══════════════════════════════════════════════════════════════════════════════
 */

// ❌ УБРАНО: Импорт настроек Jupiter - они не нужны в конфиге токен-аккаунтов

// 🔑 ОСНОВНАЯ ИНФОРМАЦИЯ КОШЕЛЬКА
const WALLET_CONFIG = {
  address: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
  network: 'mainnet-beta',
  activated: '2025-07-02',
  totalCost: '0.006132 SOL'
};

// 🪙 ТОКЕН МИНТЫ (НЕ МЕНЯЮТСЯ)
const TOKEN_MINTS = {
  SOL: 'So11111111111111111111111111111111111111112',
  USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
  WSOL: 'So11111111111111111111111111111111111111112' // Тот же что и SOL
};

// 🏦 ТОКЕН-АККАУНТЫ КОШЕЛЬКА (НОВЫЕ АДРЕСА!)
const TOKEN_ACCOUNTS = {
  // SOL - нативный, использует адрес кошелька
  SOL: {
    address: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
    mint: 'So11111111111111111111111111111111111111112',
    decimals: 9,
    isNative: true,
    symbol: 'SOL',
    name: 'Solana'
  },

  // USDC - Associated Token Account
  USDC: {
    address: '********************************************',
    mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    decimals: 6,
    isNative: false,
    symbol: 'USDC',
    name: 'USD Coin',
    activationTx: '472UcmhK7TZinfdDp27b96pUHqFxoVaF8SjRBe97SfS5HcnQ9kybg3oTXqhYnnpVB2H2wQUenoED89QtiNHVg2wJ'
  },

  // USDT - Associated Token Account  
  USDT: {
    address: 'G4BsTJW29mFeowsotpYkTrk5qT9w6r6DA4qFr2HkQhDQ',
    mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    decimals: 6,
    isNative: false,
    symbol: 'USDT',
    name: 'Tether USD',
    activationTx: '2iGzxBv4xzHYPCCVSwZ1JcwgZG6LUVhfhAMmu3pY7xehDRwzLyymWLmkE29DakYVVJKqyrQmu31odCsP72YGzs3M'
  },

  // WSOL - Wrapped SOL Associated Token Account
  WSOL: {
    address: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
    mint: 'So11111111111111111111111111111111111111112',
    decimals: 9,
    isNative: false, // Wrapped SOL - это токен-аккаунт
    symbol: 'WSOL',
    name: 'Wrapped SOL',
    activationTx: '3c3E4G8mSuNjpEohE5aRxiF5wUPd74sWw34CYfaC2fB4tbKziREHiqZEFoSVedaApZ5s5hkWD5pxk463mMBfiPNb',
    initialBalance: '0.003039 SOL',
    // 🔥 НАСТРОЙКИ ДЛЯ JUPITER - ИСПРАВЛЕНО ДЛЯ АВТОМАТИЧЕСКОГО closeAccount
    autoUnwrapWSOLToSOL: true,         // ✅ АВТОМАТИЧЕСКИ разворачивать WSOL в SOL
    skipCloseAccount: false,           // ✅ НЕ пропускать закрытие аккаунта
    manualWSOLHandling: false          // ✅ АВТОМАТИЧЕСКАЯ обработка WSOL Jupiter
  }
};

// 📋 СПИСОК ТОЛЬКО АДРЕСОВ (ДЛЯ JUPITER И ALT)
const TOKEN_ACCOUNT_ADDRESSES = {
  USDC: '********************************************',
  USDT: 'G4BsTJW29mFeowsotpYkTrk5qT9w6r6DA4qFr2HkQhDQ',
  WSOL: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
  SOL: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV' // Кошелек для нативного SOL
};

// 🔄 МАППИНГ МИНТ → ТОКЕН-АККАУНТ
const MINT_TO_ACCOUNT_MAP = {
  'So11111111111111111111111111111111111111112': 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // SOL (нативный)
  'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': '********************************************', // USDC
  'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'G4BsTJW29mFeowsotpYkTrk5qT9w6r6DA4qFr2HkQhDQ'  // USDT
};

// 🔄 МАППИНГ ТОКЕН-АККАУНТ → МИНТ
const ACCOUNT_TO_MINT_MAP = {
  'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV': 'So11111111111111111111111111111111111111112', // SOL
  '********************************************': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
  'G4BsTJW29mFeowsotpYkTrk5qT9w6r6DA4qFr2HkQhDQ': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
  '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk': 'So11111111111111111111111111111111111111112'  // WSOL
};

// 📊 МАССИВ ВСЕХ ТОКЕН-АККАУНТОВ (ДЛЯ ALT)
const ALL_TOKEN_ACCOUNT_ADDRESSES = [
  '********************************************', // USDC
  'G4BsTJW29mFeowsotpYkTrk5qT9w6r6DA4qFr2HkQhDQ', // USDT
  '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk'  // WSOL
  // SOL не включаем - он нативный
];

// 🔧 ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ

/**
 * Получить токен-аккаунт по минту
 */
function getTokenAccountByMint(mint) {
  return MINT_TO_ACCOUNT_MAP[mint] || null;
}

/**
 * Получить минт по токен-аккаунту
 */
function getMintByTokenAccount(account) {
  return ACCOUNT_TO_MINT_MAP[account] || null;
}

/**
 * Получить информацию о токене по символу
 */
function getTokenInfo(symbol) {
  return TOKEN_ACCOUNTS[symbol] || null;
}

/**
 * Получить все токен-аккаунты для Jupiter
 */
function getJupiterTokenAccounts() {
  return {
    userPublicKey: WALLET_CONFIG.address,
    tokenAccounts: TOKEN_ACCOUNT_ADDRESSES
  };
}

/**
 * Проверить, является ли токен нативным SOL
 */
function isNativeSOL(mint) {
  return mint === 'So11111111111111111111111111111111111111112';
}

/**
 * 🎯 ПОЛУЧИТЬ НАСТРОЙКИ WSOL ДЛЯ JUPITER (ЕДИНЫЕ НАСТРОЙКИ)
 */
function getWSolSettings() {
  const wsolConfig = TOKEN_ACCOUNTS.WSOL;
  return {
    address: wsolConfig.address,
    mint: wsolConfig.mint
    // ✅ ТОЛЬКО ИНФОРМАЦИЯ О ТОКЕН-АККАУНТЕ - БЕЗ НАСТРОЕК JUPITER
  };
}

// ❌ УДАЛЕН КОД РУЧНОГО closeAccount
// 🎯 Jupiter автоматически добавляет closeAccount при wrapAndUnwrapSol: true
// � Настройки Jupiter берутся из jupiter-config-unified.js

/**
 * Получить правильный аккаунт для токена (SOL = кошелек, остальные = ATA)
 */
function getCorrectTokenAccount(mint) {
  if (isNativeSOL(mint)) {
    return WALLET_CONFIG.address; // Для SOL используем адрес кошелька
  }
  return getTokenAccountByMint(mint);
}

// 📋 СТАРЫЕ АДРЕСА (ДЛЯ СПРАВКИ И МИГРАЦИИ)
const OLD_TOKEN_ACCOUNTS = {
  USDC: 'EhjoGAwQU2LAWSVg2QVX3XMdLX7DhAnLfN2GmgfQW1Bt', // Старый USDC
  USDT: '8BKTCSBtfrMSV3vrHGhRzerPeLPyMjr4MpNx2pNHNpq5', // Старый USDT
  WALLET: 'HiA7DqQ9xpAEVQ7Vu8kA3fqWHYrKrc78xx3FTjGbfgJU' // Старый кошелек
};

// 🔍 ФУНКЦИЯ ВАЛИДАЦИИ
function validateTokenAccounts() {
  console.log('🔍 ВАЛИДАЦИЯ КОНФИГУРАЦИИ ТОКЕН-АККАУНТОВ:');
  console.log(`🔑 Кошелек: ${WALLET_CONFIG.address}`);
  console.log(`📊 Токенов настроено: ${Object.keys(TOKEN_ACCOUNTS).length}`);
  
  for (const [symbol, info] of Object.entries(TOKEN_ACCOUNTS)) {
    console.log(`   ${symbol}: ${info.address} (${info.isNative ? 'нативный' : 'ATA'})`);
  }
  
  return true;
}

// 📤 ЭКСПОРТ
module.exports = {
  // Основные конфиги
  WALLET_CONFIG,
  TOKEN_MINTS,
  TOKEN_ACCOUNTS,
  TOKEN_ACCOUNT_ADDRESSES,
  
  // Маппинги
  MINT_TO_ACCOUNT_MAP,
  ACCOUNT_TO_MINT_MAP,
  ALL_TOKEN_ACCOUNT_ADDRESSES,
  
  // Функции
  getTokenAccountByMint,
  getMintByTokenAccount,
  getTokenInfo,
  getJupiterTokenAccounts,
  isNativeSOL,
  getCorrectTokenAccount,
  getWSolSettings,
  validateTokenAccounts,

  // ❌ УДАЛЕНЫ closeAccount ФУНКЦИИ - Jupiter делает это автоматически
  
  // Для справки
  OLD_TOKEN_ACCOUNTS
};

// 🧪 АВТОМАТИЧЕСКАЯ ВАЛИДАЦИЯ ПРИ ИМПОРТЕ
if (require.main === module) {
  validateTokenAccounts();
}
