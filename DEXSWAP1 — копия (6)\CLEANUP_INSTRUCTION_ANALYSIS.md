# 🔍 АНАЛИЗ ПРОБЛЕМЫ С CLEANUP INSTRUCTION

## 📊 **РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ**

### **🧪 ТЕСТ Jupiter API (test-cleanup-instruction-simple.js):**

✅ **Jupiter API РАБОТАЕТ ПРАВИЛЬНО:**
- ✅ USDC -> SOL: cleanupInstruction **ЕСТЬ**
- ✅ SOL -> USDC: cleanupInstruction **ЕСТЬ** (неожиданно, но хорошо)
- ✅ wrapAndUnwrapSol: false: cleanupInstruction **НЕТ** (ожидаемо)

### **🔍 ДЕТАЛИ cleanupInstruction:**
```
Program ID: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA
Accounts: 3
Data: 4 bytes
```

## 🚨 **ПРОБЛЕМА НАЙДЕНА В КОДЕ**

### **❌ ПРОБЛЕМА:** 
В `atomic-transaction-builder-fixed.js` код **ПРАВИЛЬНО** проверяет и добавляет `cleanupInstruction`, но возможно проверяет неправильную структуру данных.

### **🔧 ИСПРАВЛЕНИЯ ВЫПОЛНЕНЫ:**

#### 1. **Добавлена детальная отладка в atomic-transaction-builder-fixed.js:**

**СТРОКИ 1752-1774:**
```javascript
// 🔍 ДЕТАЛЬНАЯ ОТЛАДКА: Проверяем структуру jupiterSwapData
console.log(`🔍 ДЕТАЛЬНАЯ ОТЛАДКА jupiterSwapData:`);
console.log(`   jupiterSwapData.cleanupInstruction: ${jupiterSwapData.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
console.log(`   jupiterSwapData.swapData?.cleanupInstruction: ${jupiterSwapData.swapData?.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
console.log(`   Все поля jupiterSwapData: ${Object.keys(jupiterSwapData || {}).join(', ')}`);

if (jupiterSwapData.cleanupInstruction) {
  console.log(`✅ Добавляем cleanupInstruction от Jupiter API`);
  jupiterInstructions.push(jupiterSwapData.cleanupInstruction);
} else if (jupiterSwapData.swapData?.cleanupInstruction) {
  console.log(`✅ Добавляем cleanupInstruction из swapData`);
  jupiterInstructions.push(jupiterSwapData.swapData.cleanupInstruction);
} else {
  console.log(`⚠️ Jupiter API не вернул cleanupInstruction`);
}
```

#### 2. **Добавлена отладка для второго swap'а (строки 1893-1906):**
```javascript
console.log(`🔍 ДЕТАЛЬНАЯ ОТЛАДКА второго swap:`);
console.log(`   secondSwapInstructions.cleanupInstruction: ${secondSwapInstructions.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
console.log(`   secondSwapInstructions.swapData?.cleanupInstruction: ${secondSwapInstructions.swapData?.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);

if (secondSwapInstructions.cleanupInstruction) {
  jupiterInstructions.push(secondSwapInstructions.cleanupInstruction);
  console.log(`✅ Добавлена cleanupInstruction второго swapa`);
} else if (secondSwapInstructions.swapData?.cleanupInstruction) {
  jupiterInstructions.push(secondSwapInstructions.swapData.cleanupInstruction);
  console.log(`✅ Добавлена cleanupInstruction второго swapa из swapData`);
} else {
  console.log(`⚠️ Второй swap не вернул cleanupInstruction`);
}
```

## 📋 **СТРУКТУРА ДАННЫХ Jupiter**

### **jupiter-swap-instructions.js возвращает:**
```javascript
const result = {
  quote,
  swapData, // ✅ Содержит cleanupInstruction
  setupInstructions: swapData.setupInstructions || [],
  swapInstruction: swapData.swapInstruction,
  cleanupInstruction: swapData.cleanupInstruction, // ✅ ПРАВИЛЬНО извлекается
  addressLookupTableAddresses: swapData.addressLookupTableAddresses || []
};
```

### **atomic-transaction-builder-fixed.js получает:**
```javascript
jupiterSwapData = {
  cleanupInstruction: TransactionInstruction, // ✅ Должно быть здесь
  swapData: {
    cleanupInstruction: TransactionInstruction // ✅ Или здесь
  }
}
```

## 🎯 **СЛЕДУЮЩИЕ ШАГИ**

1. **Запустить систему с новой отладкой** и посмотреть логи
2. **Проверить какая именно структура данных приходит** в atomic-transaction-builder-fixed.js
3. **Убедиться что cleanupInstruction добавляется** в финальную транзакцию
4. **Проверить что closeAccount действительно выполняется** в блокчейне

## 🔍 **ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ**

### **Jupiter API v6 Documentation:**
> `cleanupInstruction` - Unwrap the SOL if `wrapAndUnwrapSol = true`.

### **Условия появления cleanupInstruction:**
1. ✅ `wrapAndUnwrapSol: true` (у нас включено)
2. ✅ Output mint = SOL или создается WSOL account
3. ✅ Jupiter создает временный WSOL account

## 🎉 **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ**

После исправлений:
1. ✅ Детальные логи покажут где именно находится cleanupInstruction
2. ✅ cleanupInstruction будет правильно добавляться в транзакцию
3. ✅ closeAccount будет выполняться автоматически
4. ✅ SOL будет возвращаться в кошелек после каждого swap'а
