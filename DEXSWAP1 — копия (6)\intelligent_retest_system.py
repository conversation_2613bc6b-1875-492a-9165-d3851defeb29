#!/usr/bin/env python3
"""
🔄 INTELLIGENT RETEST SYSTEM
Максимально эффективная система ретестирования с адаптивными стратегиями
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
import logging
from enum import Enum

from unified_data_management_system import UnifiedDataManager, TestTarget, TestResult
from strategy_integration_engine import StrategyIntegrationEngine, StrategyExecution

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RetestReason(Enum):
    """Причины ретестирования"""
    TIME_BASED = "time_based"           # По времени
    STRATEGY_UPDATE = "strategy_update" # Обновление стратегии
    NEW_VULNERABILITY = "new_vuln"      # Новая уязвимость в похожих системах
    HIGH_VALUE_TARGET = "high_value"    # Высокоценная цель
    FAILED_PREVIOUS = "failed_prev"     # Предыдущий тест провалился
    USER_REQUEST = "user_request"       # Запрос пользователя
    ADAPTIVE_LEARNING = "adaptive"      # Адаптивное обучение

@dataclass
class RetestTask:
    """Задача ретестирования"""
    task_id: str
    target_id: str
    reason: RetestReason
    priority: float
    strategies_to_use: List[str]
    created_at: datetime
    scheduled_for: datetime
    attempts: int = 0
    max_attempts: int = 3
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict:
        return {
            'task_id': self.task_id,
            'target_id': self.target_id,
            'reason': self.reason.value,
            'priority': self.priority,
            'strategies_to_use': self.strategies_to_use,
            'created_at': self.created_at.isoformat(),
            'scheduled_for': self.scheduled_for.isoformat(),
            'attempts': self.attempts,
            'max_attempts': self.max_attempts,
            'metadata': self.metadata or {}
        }

class IntelligentRetestSystem:
    """Интеллектуальная система ретестирования"""
    
    def __init__(self, data_manager: UnifiedDataManager, strategy_engine: StrategyIntegrationEngine):
        self.data_manager = data_manager
        self.strategy_engine = strategy_engine
        self.retest_queue: List[RetestTask] = []
        self.active_tasks: Set[str] = set()
        self.learning_data = {}
        
        # Конфигурация ретестирования
        self.config = {
            'time_based_interval_days': 7,      # Ретест каждые 7 дней
            'high_value_interval_days': 3,      # Высокоценные цели каждые 3 дня
            'failed_retry_interval_hours': 6,   # Повтор неудачных через 6 часов
            'max_concurrent_retests': 5,        # Максимум одновременных ретестов
            'adaptive_threshold': 0.8,          # Порог для адаптивного обучения
            'priority_weights': {
                RetestReason.HIGH_VALUE_TARGET: 1.0,
                RetestReason.NEW_VULNERABILITY: 0.9,
                RetestReason.STRATEGY_UPDATE: 0.8,
                RetestReason.ADAPTIVE_LEARNING: 0.7,
                RetestReason.TIME_BASED: 0.5,
                RetestReason.FAILED_PREVIOUS: 0.6,
                RetestReason.USER_REQUEST: 0.9
            }
        }
    
    async def analyze_retest_candidates(self) -> List[RetestTask]:
        """Анализ кандидатов для ретестирования"""
        logger.info("🔍 Анализ кандидатов для ретестирования...")
        
        candidates = []
        
        # 1. Временное ретестирование
        time_based_candidates = await self._find_time_based_candidates()
        candidates.extend(time_based_candidates)
        
        # 2. Высокоценные цели
        high_value_candidates = await self._find_high_value_candidates()
        candidates.extend(high_value_candidates)
        
        # 3. Неудачные предыдущие тесты
        failed_candidates = await self._find_failed_candidates()
        candidates.extend(failed_candidates)
        
        # 4. Адаптивное обучение
        adaptive_candidates = await self._find_adaptive_candidates()
        candidates.extend(adaptive_candidates)
        
        # 5. Обновления стратегий
        strategy_update_candidates = await self._find_strategy_update_candidates()
        candidates.extend(strategy_update_candidates)
        
        # Сортировка по приоритету
        candidates.sort(key=lambda x: x.priority, reverse=True)
        
        logger.info(f"📋 Найдено {len(candidates)} кандидатов для ретестирования")
        return candidates
    
    async def _find_time_based_candidates(self) -> List[RetestTask]:
        """Поиск кандидатов для временного ретестирования"""
        cutoff_date = datetime.now() - timedelta(days=self.config['time_based_interval_days'])
        targets = await self.data_manager.get_retest_candidates(
            days_since_last_test=self.config['time_based_interval_days'],
            min_vulnerability_count=0
        )
        
        tasks = []
        for target in targets:
            # Выбираем стратегии на основе предыдущих результатов
            recommended_strategies = await self._get_recommended_strategies(target)
            
            task = RetestTask(
                task_id=f"time_{target.target_id}_{int(time.time())}",
                target_id=target.target_id,
                reason=RetestReason.TIME_BASED,
                priority=self.config['priority_weights'][RetestReason.TIME_BASED] * target.priority_score,
                strategies_to_use=recommended_strategies,
                created_at=datetime.now(),
                scheduled_for=datetime.now(),
                metadata={'original_priority': target.priority_score}
            )
            tasks.append(task)
        
        return tasks
    
    async def _find_high_value_candidates(self) -> List[RetestTask]:
        """Поиск высокоценных целей"""
        targets = await self.data_manager.get_targets_for_testing(
            min_priority=0.8,  # Высокий приоритет
            limit=20
        )
        
        tasks = []
        cutoff_date = datetime.now() - timedelta(days=self.config['high_value_interval_days'])
        
        for target in targets:
            # Проверяем, нужно ли ретестирование
            if not target.last_tested or target.last_tested < cutoff_date:
                # Используем расширенный набор стратегий для высокоценных целей
                all_strategies = list(self.strategy_engine.loaded_strategies.keys())
                
                task = RetestTask(
                    task_id=f"highval_{target.target_id}_{int(time.time())}",
                    target_id=target.target_id,
                    reason=RetestReason.HIGH_VALUE_TARGET,
                    priority=self.config['priority_weights'][RetestReason.HIGH_VALUE_TARGET] * target.priority_score,
                    strategies_to_use=all_strategies[:15],  # Топ-15 стратегий
                    created_at=datetime.now(),
                    scheduled_for=datetime.now(),
                    metadata={
                        'high_value': True,
                        'bounty_info': target.metadata.get('max_bounty', 'Unknown')
                    }
                )
                tasks.append(task)
        
        return tasks
    
    async def _find_failed_candidates(self) -> List[RetestTask]:
        """Поиск неудачных тестов для повтора"""
        # Получаем цели с низким успехом тестирования
        targets = await self.data_manager.get_targets_for_testing(limit=100)
        
        tasks = []
        cutoff_date = datetime.now() - timedelta(hours=self.config['failed_retry_interval_hours'])
        
        for target in targets:
            # Анализируем успешность предыдущих тестов
            if target.test_count > 0:
                success_rate = target.vulnerability_count / target.test_count
                
                # Если успешность низкая, но цель перспективная
                if success_rate < 0.3 and target.priority_score > 0.6:
                    # Выбираем альтернативные стратегии
                    alternative_strategies = await self._get_alternative_strategies(target)
                    
                    task = RetestTask(
                        task_id=f"failed_{target.target_id}_{int(time.time())}",
                        target_id=target.target_id,
                        reason=RetestReason.FAILED_PREVIOUS,
                        priority=self.config['priority_weights'][RetestReason.FAILED_PREVIOUS] * target.priority_score,
                        strategies_to_use=alternative_strategies,
                        created_at=datetime.now(),
                        scheduled_for=datetime.now() + timedelta(minutes=30),  # Небольшая задержка
                        metadata={
                            'previous_success_rate': success_rate,
                            'retry_attempt': True
                        }
                    )
                    tasks.append(task)
        
        return tasks
    
    async def _find_adaptive_candidates(self) -> List[RetestTask]:
        """Поиск кандидатов для адаптивного обучения"""
        # Анализируем производительность стратегий
        strategy_performance = await self.data_manager.get_strategy_performance()
        
        # Находим стратегии с высокой эффективностью
        high_performing_strategies = []
        for strategy_name, stats in strategy_performance.items():
            if (stats['vulnerability_rate'] > self.config['adaptive_threshold'] and 
                stats['total_executions'] > 5):
                high_performing_strategies.append(strategy_name)
        
        if not high_performing_strategies:
            return []
        
        # Выбираем цели, которые не тестировались этими стратегиями
        targets = await self.data_manager.get_targets_for_testing(limit=50)
        
        tasks = []
        for target in targets:
            # Проверяем, какие стратегии уже использовались
            # (здесь нужен дополнительный запрос к базе данных)
            
            task = RetestTask(
                task_id=f"adaptive_{target.target_id}_{int(time.time())}",
                target_id=target.target_id,
                reason=RetestReason.ADAPTIVE_LEARNING,
                priority=self.config['priority_weights'][RetestReason.ADAPTIVE_LEARNING] * target.priority_score,
                strategies_to_use=high_performing_strategies[:10],
                created_at=datetime.now(),
                scheduled_for=datetime.now() + timedelta(minutes=15),
                metadata={
                    'adaptive_learning': True,
                    'high_performing_strategies': high_performing_strategies
                }
            )
            tasks.append(task)
        
        return tasks[:10]  # Ограничиваем количество
    
    async def _find_strategy_update_candidates(self) -> List[RetestTask]:
        """Поиск кандидатов после обновления стратегий"""
        # Здесь можно реализовать логику отслеживания обновлений стратегий
        # Пока возвращаем пустой список
        return []
    
    async def _get_recommended_strategies(self, target: TestTarget) -> List[str]:
        """Получение рекомендованных стратегий для цели"""
        # Используем рекомендации из движка стратегий
        recommendations = self.strategy_engine.get_strategy_recommendations(target)
        
        # Добавляем стратегии на основе истории
        strategy_performance = await self.data_manager.get_strategy_performance()
        
        # Сортируем по эффективности
        sorted_strategies = sorted(
            strategy_performance.items(),
            key=lambda x: x[1]['vulnerability_rate'],
            reverse=True
        )
        
        # Комбинируем рекомендации и топ стратегии
        combined = recommendations + [s[0] for s in sorted_strategies[:10]]
        
        # Удаляем дубликаты и ограничиваем количество
        unique_strategies = list(dict.fromkeys(combined))
        return unique_strategies[:8]
    
    async def _get_alternative_strategies(self, target: TestTarget) -> List[str]:
        """Получение альтернативных стратегий для неудачных тестов"""
        all_strategies = list(self.strategy_engine.loaded_strategies.keys())
        
        # Исключаем стратегии, которые уже использовались
        # (здесь нужен запрос к истории тестирования)
        
        # Выбираем стратегии разных типов
        strategy_types = ['quantum', 'ai', 'mathematical', 'future', 'basic']
        alternative_strategies = []
        
        for strategy_type in strategy_types:
            type_strategies = [
                name for name, config in self.strategy_engine.loaded_strategies.items()
                if config['type'] == strategy_type
            ]
            if type_strategies:
                alternative_strategies.extend(type_strategies[:2])  # По 2 стратегии каждого типа
        
        return alternative_strategies[:10]
    
    async def schedule_retest(self, task: RetestTask):
        """Планирование задачи ретестирования"""
        # Проверяем, не запланирована ли уже эта цель
        existing_task = next(
            (t for t in self.retest_queue if t.target_id == task.target_id and t.reason == task.reason),
            None
        )
        
        if existing_task:
            # Обновляем приоритет если новая задача важнее
            if task.priority > existing_task.priority:
                existing_task.priority = task.priority
                existing_task.strategies_to_use = task.strategies_to_use
            return
        
        self.retest_queue.append(task)
        logger.info(f"📅 Запланирован ретест: {task.task_id} (приоритет: {task.priority:.2f})")
    
    async def execute_retest_queue(self, max_concurrent: Optional[int] = None):
        """Выполнение очереди ретестирования"""
        if not max_concurrent:
            max_concurrent = self.config['max_concurrent_retests']
        
        # Сортируем очередь по приоритету и времени
        self.retest_queue.sort(key=lambda x: (x.priority, x.scheduled_for), reverse=True)
        
        # Фильтруем задачи, готовые к выполнению
        ready_tasks = [
            task for task in self.retest_queue
            if (task.scheduled_for <= datetime.now() and 
                task.target_id not in self.active_tasks and
                task.attempts < task.max_attempts)
        ]
        
        if not ready_tasks:
            logger.info("📭 Нет готовых задач для ретестирования")
            return
        
        # Ограничиваем количество одновременных задач
        tasks_to_execute = ready_tasks[:max_concurrent]
        
        logger.info(f"🚀 Выполнение {len(tasks_to_execute)} задач ретестирования")
        
        # Выполняем задачи параллельно
        semaphore = asyncio.Semaphore(max_concurrent)
        tasks = [
            self._execute_single_retest(task, semaphore)
            for task in tasks_to_execute
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Обрабатываем результаты
        for i, result in enumerate(results):
            task = tasks_to_execute[i]
            
            if isinstance(result, Exception):
                logger.error(f"Ошибка выполнения задачи {task.task_id}: {result}")
                task.attempts += 1
                
                # Перепланируем если не превышен лимит попыток
                if task.attempts < task.max_attempts:
                    task.scheduled_for = datetime.now() + timedelta(hours=1)
                else:
                    self.retest_queue.remove(task)
            else:
                # Успешное выполнение
                self.retest_queue.remove(task)
                logger.info(f"✅ Задача {task.task_id} выполнена успешно")
    
    async def _execute_single_retest(self, task: RetestTask, semaphore: asyncio.Semaphore):
        """Выполнение одной задачи ретестирования"""
        async with semaphore:
            try:
                # Отмечаем задачу как активную
                self.active_tasks.add(task.target_id)
                
                # Получаем цель из базы данных
                targets = await self.data_manager.get_targets_for_testing(limit=1000)
                target = next((t for t in targets if t.target_id == task.target_id), None)
                
                if not target:
                    raise ValueError(f"Цель {task.target_id} не найдена")
                
                logger.info(f"🎯 Ретестирование {target.name} ({task.reason.value})")
                
                # Выполняем стратегии
                results = []
                for strategy_name in task.strategies_to_use:
                    try:
                        if strategy_name in self.strategy_engine.loaded_strategies:
                            result = await self.strategy_engine.execute_strategy(strategy_name, target)
                            results.append(result)
                            
                            # Небольшая задержка между стратегиями
                            await asyncio.sleep(0.2)
                    except Exception as e:
                        logger.error(f"Ошибка стратегии {strategy_name}: {e}")
                        continue
                
                # Анализируем результаты
                vulnerabilities_found = [r for r in results if r.vulnerability_found]
                
                logger.info(f"📊 Ретест {task.task_id}: {len(vulnerabilities_found)}/{len(results)} стратегий нашли уязвимости")
                
                # Обновляем обучающие данные
                await self._update_learning_data(task, results)
                
                return {
                    'task_id': task.task_id,
                    'target_id': task.target_id,
                    'strategies_executed': len(results),
                    'vulnerabilities_found': len(vulnerabilities_found),
                    'success': True
                }
                
            except Exception as e:
                logger.error(f"Ошибка выполнения ретеста {task.task_id}: {e}")
                raise
            finally:
                # Убираем из активных задач
                self.active_tasks.discard(task.target_id)
    
    async def _update_learning_data(self, task: RetestTask, results: List[StrategyExecution]):
        """Обновление данных для обучения"""
        # Сохраняем данные о эффективности стратегий в контексте ретестирования
        learning_key = f"{task.reason.value}_{task.target_id}"
        
        self.learning_data[learning_key] = {
            'task_reason': task.reason.value,
            'target_priority': task.priority,
            'strategies_used': [r.strategy_name for r in results],
            'successful_strategies': [r.strategy_name for r in results if r.success],
            'vulnerability_strategies': [r.strategy_name for r in results if r.vulnerability_found],
            'avg_execution_time': sum(r.execution_time for r in results) / len(results) if results else 0,
            'timestamp': datetime.now().isoformat()
        }
    
    async def get_retest_statistics(self) -> Dict[str, Any]:
        """Получение статистики ретестирования"""
        total_tasks = len(self.retest_queue)
        active_tasks = len(self.active_tasks)
        
        # Статистика по причинам
        reason_stats = {}
        for task in self.retest_queue:
            reason = task.reason.value
            if reason not in reason_stats:
                reason_stats[reason] = {'count': 0, 'avg_priority': 0}
            reason_stats[reason]['count'] += 1
            reason_stats[reason]['avg_priority'] += task.priority
        
        # Вычисляем средние приоритеты
        for reason, stats in reason_stats.items():
            if stats['count'] > 0:
                stats['avg_priority'] /= stats['count']
        
        return {
            'total_queued_tasks': total_tasks,
            'active_tasks': active_tasks,
            'reason_statistics': reason_stats,
            'learning_data_points': len(self.learning_data),
            'queue_status': [
                {
                    'task_id': task.task_id,
                    'reason': task.reason.value,
                    'priority': task.priority,
                    'scheduled_for': task.scheduled_for.isoformat(),
                    'attempts': task.attempts
                }
                for task in sorted(self.retest_queue, key=lambda x: x.priority, reverse=True)[:10]
            ]
        }

async def main():
    """Демонстрация системы ретестирования"""
    print("🔄 INTELLIGENT RETEST SYSTEM")
    print("=" * 60)
    
    async with UnifiedDataManager() as dm:
        strategy_engine = StrategyIntegrationEngine(dm)
        await strategy_engine.initialize_strategies()
        
        retest_system = IntelligentRetestSystem(dm, strategy_engine)
        
        # Анализ кандидатов
        candidates = await retest_system.analyze_retest_candidates()
        print(f"📋 Найдено {len(candidates)} кандидатов для ретестирования")
        
        # Планирование задач
        for candidate in candidates[:5]:  # Первые 5 для демо
            await retest_system.schedule_retest(candidate)
        
        # Получение статистики
        stats = await retest_system.get_retest_statistics()
        print(f"📊 Статистика очереди: {stats['total_queued_tasks']} задач")
        
        # Выполнение очереди
        await retest_system.execute_retest_queue(max_concurrent=2)

if __name__ == "__main__":
    asyncio.run(main())
