@echo off
echo 🦀 Installing Rust toolchain for Solana arbitrage engine...

REM Check if Rust is already installed
where cargo >nul 2>nul
if %ERRORLEVEL% == 0 (
    echo ✅ Rust is already installed
    cargo --version
    rustc --version
    goto :components
)

echo 📥 Downloading and installing Rust...

REM Download rustup-init
curl -o "%TEMP%\rustup-init.exe" "https://win.rustup.rs/x86_64"

if %ERRORLEVEL% neq 0 (
    echo ❌ Failed to download rustup-init
    exit /b 1
)

REM Install Rust with default settings
"%TEMP%\rustup-init.exe" -y --default-toolchain stable

REM Add cargo to PATH for current session
set "PATH=%PATH%;%USERPROFILE%\.cargo\bin"

echo ✅ Rust installed successfully!

:components
echo 🔧 Adding required Rust components...
rustup component add clippy
rustup component add rustfmt

echo 🎉 Rust setup completed!
echo 📋 Next steps:
echo    1. Restart your terminal
echo    2. Navigate to rust-engine directory
echo    3. Run: cargo check

pause
