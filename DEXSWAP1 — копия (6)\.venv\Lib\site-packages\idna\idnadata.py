# This file is automatically generated by tools/idna-data

__version__ = "15.1.0"
scripts = {
    "Greek": (
        0x37000000374,
        0x37500000378,
        0x37A0000037E,
        0x37F00000380,
        0x38400000385,
        0x38600000387,
        0x3880000038B,
        0x38C0000038D,
        0x38E000003A2,
        0x3A3000003E2,
        0x3F000000400,
        0x1D2600001D2B,
        0x1D5D00001D62,
        0x1D6600001D6B,
        0x1DBF00001DC0,
        0x1F0000001F16,
        0x1F1800001F1E,
        0x1F2000001F46,
        0x1F4800001F4E,
        0x1F5000001F58,
        0x1F5900001F5A,
        0x1F5B00001F5C,
        0x1F5D00001F5E,
        0x1F5F00001F7E,
        0x1F8000001FB5,
        0x1FB600001FC5,
        0x1FC600001FD4,
        0x1FD600001FDC,
        0x1FDD00001FF0,
        0x1FF200001FF5,
        0x1FF600001FFF,
        0x212600002127,
        0xAB650000AB66,
        0x101400001018F,
        0x101A0000101A1,
        0x1D2000001D246,
    ),
    "Han": (
        0x2E8000002E9A,
        0x2E9B00002EF4,
        0x2F0000002FD6,
        0x300500003006,
        0x300700003008,
        0x30210000302A,
        0x30380000303C,
        0x340000004DC0,
        0x4E000000A000,
        0xF9000000FA6E,
        0xFA700000FADA,
        0x16FE200016FE4,
        0x16FF000016FF2,
        0x200000002A6E0,
        0x2A7000002B73A,
        0x2B7400002B81E,
        0x2B8200002CEA2,
        0x2CEB00002EBE1,
        0x2EBF00002EE5E,
        0x2F8000002FA1E,
        0x300000003134B,
        0x31350000323B0,
    ),
    "Hebrew": (
        0x591000005C8,
        0x5D0000005EB,
        0x5EF000005F5,
        0xFB1D0000FB37,
        0xFB380000FB3D,
        0xFB3E0000FB3F,
        0xFB400000FB42,
        0xFB430000FB45,
        0xFB460000FB50,
    ),
    "Hiragana": (
        0x304100003097,
        0x309D000030A0,
        0x1B0010001B120,
        0x1B1320001B133,
        0x1B1500001B153,
        0x1F2000001F201,
    ),
    "Katakana": (
        0x30A1000030FB,
        0x30FD00003100,
        0x31F000003200,
        0x32D0000032FF,
        0x330000003358,
        0xFF660000FF70,
        0xFF710000FF9E,
        0x1AFF00001AFF4,
        0x1AFF50001AFFC,
        0x1AFFD0001AFFF,
        0x1B0000001B001,
        0x1B1200001B123,
        0x1B1550001B156,
        0x1B1640001B168,
    ),
}
joining_types = {
    0xAD: 84,
    0x300: 84,
    0x301: 84,
    0x302: 84,
    0x303: 84,
    0x304: 84,
    0x305: 84,
    0x306: 84,
    0x307: 84,
    0x308: 84,
    0x309: 84,
    0x30A: 84,
    0x30B: 84,
    0x30C: 84,
    0x30D: 84,
    0x30E: 84,
    0x30F: 84,
    0x310: 84,
    0x311: 84,
    0x312: 84,
    0x313: 84,
    0x314: 84,
    0x315: 84,
    0x316: 84,
    0x317: 84,
    0x318: 84,
    0x319: 84,
    0x31A: 84,
    0x31B: 84,
    0x31C: 84,
    0x31D: 84,
    0x31E: 84,
    0x31F: 84,
    0x320: 84,
    0x321: 84,
    0x322: 84,
    0x323: 84,
    0x324: 84,
    0x325: 84,
    0x326: 84,
    0x327: 84,
    0x328: 84,
    0x329: 84,
    0x32A: 84,
    0x32B: 84,
    0x32C: 84,
    0x32D: 84,
    0x32E: 84,
    0x32F: 84,
    0x330: 84,
    0x331: 84,
    0x332: 84,
    0x333: 84,
    0x334: 84,
    0x335: 84,
    0x336: 84,
    0x337: 84,
    0x338: 84,
    0x339: 84,
    0x33A: 84,
    0x33B: 84,
    0x33C: 84,
    0x33D: 84,
    0x33E: 84,
    0x33F: 84,
    0x340: 84,
    0x341: 84,
    0x342: 84,
    0x343: 84,
    0x344: 84,
    0x345: 84,
    0x346: 84,
    0x347: 84,
    0x348: 84,
    0x349: 84,
    0x34A: 84,
    0x34B: 84,
    0x34C: 84,
    0x34D: 84,
    0x34E: 84,
    0x34F: 84,
    0x350: 84,
    0x351: 84,
    0x352: 84,
    0x353: 84,
    0x354: 84,
    0x355: 84,
    0x356: 84,
    0x357: 84,
    0x358: 84,
    0x359: 84,
    0x35A: 84,
    0x35B: 84,
    0x35C: 84,
    0x35D: 84,
    0x35E: 84,
    0x35F: 84,
    0x360: 84,
    0x361: 84,
    0x362: 84,
    0x363: 84,
    0x364: 84,
    0x365: 84,
    0x366: 84,
    0x367: 84,
    0x368: 84,
    0x369: 84,
    0x36A: 84,
    0x36B: 84,
    0x36C: 84,
    0x36D: 84,
    0x36E: 84,
    0x36F: 84,
    0x483: 84,
    0x484: 84,
    0x485: 84,
    0x486: 84,
    0x487: 84,
    0x488: 84,
    0x489: 84,
    0x591: 84,
    0x592: 84,
    0x593: 84,
    0x594: 84,
    0x595: 84,
    0x596: 84,
    0x597: 84,
    0x598: 84,
    0x599: 84,
    0x59A: 84,
    0x59B: 84,
    0x59C: 84,
    0x59D: 84,
    0x59E: 84,
    0x59F: 84,
    0x5A0: 84,
    0x5A1: 84,
    0x5A2: 84,
    0x5A3: 84,
    0x5A4: 84,
    0x5A5: 84,
    0x5A6: 84,
    0x5A7: 84,
    0x5A8: 84,
    0x5A9: 84,
    0x5AA: 84,
    0x5AB: 84,
    0x5AC: 84,
    0x5AD: 84,
    0x5AE: 84,
    0x5AF: 84,
    0x5B0: 84,
    0x5B1: 84,
    0x5B2: 84,
    0x5B3: 84,
    0x5B4: 84,
    0x5B5: 84,
    0x5B6: 84,
    0x5B7: 84,
    0x5B8: 84,
    0x5B9: 84,
    0x5BA: 84,
    0x5BB: 84,
    0x5BC: 84,
    0x5BD: 84,
    0x5BF: 84,
    0x5C1: 84,
    0x5C2: 84,
    0x5C4: 84,
    0x5C5: 84,
    0x5C7: 84,
    0x610: 84,
    0x611: 84,
    0x612: 84,
    0x613: 84,
    0x614: 84,
    0x615: 84,
    0x616: 84,
    0x617: 84,
    0x618: 84,
    0x619: 84,
    0x61A: 84,
    0x61C: 84,
    0x620: 68,
    0x622: 82,
    0x623: 82,
    0x624: 82,
    0x625: 82,
    0x626: 68,
    0x627: 82,
    0x628: 68,
    0x629: 82,
    0x62A: 68,
    0x62B: 68,
    0x62C: 68,
    0x62D: 68,
    0x62E: 68,
    0x62F: 82,
    0x630: 82,
    0x631: 82,
    0x632: 82,
    0x633: 68,
    0x634: 68,
    0x635: 68,
    0x636: 68,
    0x637: 68,
    0x638: 68,
    0x639: 68,
    0x63A: 68,
    0x63B: 68,
    0x63C: 68,
    0x63D: 68,
    0x63E: 68,
    0x63F: 68,
    0x640: 67,
    0x641: 68,
    0x642: 68,
    0x643: 68,
    0x644: 68,
    0x645: 68,
    0x646: 68,
    0x647: 68,
    0x648: 82,
    0x649: 68,
    0x64A: 68,
    0x64B: 84,
    0x64C: 84,
    0x64D: 84,
    0x64E: 84,
    0x64F: 84,
    0x650: 84,
    0x651: 84,
    0x652: 84,
    0x653: 84,
    0x654: 84,
    0x655: 84,
    0x656: 84,
    0x657: 84,
    0x658: 84,
    0x659: 84,
    0x65A: 84,
    0x65B: 84,
    0x65C: 84,
    0x65D: 84,
    0x65E: 84,
    0x65F: 84,
    0x66E: 68,
    0x66F: 68,
    0x670: 84,
    0x671: 82,
    0x672: 82,
    0x673: 82,
    0x675: 82,
    0x676: 82,
    0x677: 82,
    0x678: 68,
    0x679: 68,
    0x67A: 68,
    0x67B: 68,
    0x67C: 68,
    0x67D: 68,
    0x67E: 68,
    0x67F: 68,
    0x680: 68,
    0x681: 68,
    0x682: 68,
    0x683: 68,
    0x684: 68,
    0x685: 68,
    0x686: 68,
    0x687: 68,
    0x688: 82,
    0x689: 82,
    0x68A: 82,
    0x68B: 82,
    0x68C: 82,
    0x68D: 82,
    0x68E: 82,
    0x68F: 82,
    0x690: 82,
    0x691: 82,
    0x692: 82,
    0x693: 82,
    0x694: 82,
    0x695: 82,
    0x696: 82,
    0x697: 82,
    0x698: 82,
    0x699: 82,
    0x69A: 68,
    0x69B: 68,
    0x69C: 68,
    0x69D: 68,
    0x69E: 68,
    0x69F: 68,
    0x6A0: 68,
    0x6A1: 68,
    0x6A2: 68,
    0x6A3: 68,
    0x6A4: 68,
    0x6A5: 68,
    0x6A6: 68,
    0x6A7: 68,
    0x6A8: 68,
    0x6A9: 68,
    0x6AA: 68,
    0x6AB: 68,
    0x6AC: 68,
    0x6AD: 68,
    0x6AE: 68,
    0x6AF: 68,
    0x6B0: 68,
    0x6B1: 68,
    0x6B2: 68,
    0x6B3: 68,
    0x6B4: 68,
    0x6B5: 68,
    0x6B6: 68,
    0x6B7: 68,
    0x6B8: 68,
    0x6B9: 68,
    0x6BA: 68,
    0x6BB: 68,
    0x6BC: 68,
    0x6BD: 68,
    0x6BE: 68,
    0x6BF: 68,
    0x6C0: 82,
    0x6C1: 68,
    0x6C2: 68,
    0x6C3: 82,
    0x6C4: 82,
    0x6C5: 82,
    0x6C6: 82,
    0x6C7: 82,
    0x6C8: 82,
    0x6C9: 82,
    0x6CA: 82,
    0x6CB: 82,
    0x6CC: 68,
    0x6CD: 82,
    0x6CE: 68,
    0x6CF: 82,
    0x6D0: 68,
    0x6D1: 68,
    0x6D2: 82,
    0x6D3: 82,
    0x6D5: 82,
    0x6D6: 84,
    0x6D7: 84,
    0x6D8: 84,
    0x6D9: 84,
    0x6DA: 84,
    0x6DB: 84,
    0x6DC: 84,
    0x6DF: 84,
    0x6E0: 84,
    0x6E1: 84,
    0x6E2: 84,
    0x6E3: 84,
    0x6E4: 84,
    0x6E7: 84,
    0x6E8: 84,
    0x6EA: 84,
    0x6EB: 84,
    0x6EC: 84,
    0x6ED: 84,
    0x6EE: 82,
    0x6EF: 82,
    0x6FA: 68,
    0x6FB: 68,
    0x6FC: 68,
    0x6FF: 68,
    0x70F: 84,
    0x710: 82,
    0x711: 84,
    0x712: 68,
    0x713: 68,
    0x714: 68,
    0x715: 82,
    0x716: 82,
    0x717: 82,
    0x718: 82,
    0x719: 82,
    0x71A: 68,
    0x71B: 68,
    0x71C: 68,
    0x71D: 68,
    0x71E: 82,
    0x71F: 68,
    0x720: 68,
    0x721: 68,
    0x722: 68,
    0x723: 68,
    0x724: 68,
    0x725: 68,
    0x726: 68,
    0x727: 68,
    0x728: 82,
    0x729: 68,
    0x72A: 82,
    0x72B: 68,
    0x72C: 82,
    0x72D: 68,
    0x72E: 68,
    0x72F: 82,
    0x730: 84,
    0x731: 84,
    0x732: 84,
    0x733: 84,
    0x734: 84,
    0x735: 84,
    0x736: 84,
    0x737: 84,
    0x738: 84,
    0x739: 84,
    0x73A: 84,
    0x73B: 84,
    0x73C: 84,
    0x73D: 84,
    0x73E: 84,
    0x73F: 84,
    0x740: 84,
    0x741: 84,
    0x742: 84,
    0x743: 84,
    0x744: 84,
    0x745: 84,
    0x746: 84,
    0x747: 84,
    0x748: 84,
    0x749: 84,
    0x74A: 84,
    0x74D: 82,
    0x74E: 68,
    0x74F: 68,
    0x750: 68,
    0x751: 68,
    0x752: 68,
    0x753: 68,
    0x754: 68,
    0x755: 68,
    0x756: 68,
    0x757: 68,
    0x758: 68,
    0x759: 82,
    0x75A: 82,
    0x75B: 82,
    0x75C: 68,
    0x75D: 68,
    0x75E: 68,
    0x75F: 68,
    0x760: 68,
    0x761: 68,
    0x762: 68,
    0x763: 68,
    0x764: 68,
    0x765: 68,
    0x766: 68,
    0x767: 68,
    0x768: 68,
    0x769: 68,
    0x76A: 68,
    0x76B: 82,
    0x76C: 82,
    0x76D: 68,
    0x76E: 68,
    0x76F: 68,
    0x770: 68,
    0x771: 82,
    0x772: 68,
    0x773: 82,
    0x774: 82,
    0x775: 68,
    0x776: 68,
    0x777: 68,
    0x778: 82,
    0x779: 82,
    0x77A: 68,
    0x77B: 68,
    0x77C: 68,
    0x77D: 68,
    0x77E: 68,
    0x77F: 68,
    0x7A6: 84,
    0x7A7: 84,
    0x7A8: 84,
    0x7A9: 84,
    0x7AA: 84,
    0x7AB: 84,
    0x7AC: 84,
    0x7AD: 84,
    0x7AE: 84,
    0x7AF: 84,
    0x7B0: 84,
    0x7CA: 68,
    0x7CB: 68,
    0x7CC: 68,
    0x7CD: 68,
    0x7CE: 68,
    0x7CF: 68,
    0x7D0: 68,
    0x7D1: 68,
    0x7D2: 68,
    0x7D3: 68,
    0x7D4: 68,
    0x7D5: 68,
    0x7D6: 68,
    0x7D7: 68,
    0x7D8: 68,
    0x7D9: 68,
    0x7DA: 68,
    0x7DB: 68,
    0x7DC: 68,
    0x7DD: 68,
    0x7DE: 68,
    0x7DF: 68,
    0x7E0: 68,
    0x7E1: 68,
    0x7E2: 68,
    0x7E3: 68,
    0x7E4: 68,
    0x7E5: 68,
    0x7E6: 68,
    0x7E7: 68,
    0x7E8: 68,
    0x7E9: 68,
    0x7EA: 68,
    0x7EB: 84,
    0x7EC: 84,
    0x7ED: 84,
    0x7EE: 84,
    0x7EF: 84,
    0x7F0: 84,
    0x7F1: 84,
    0x7F2: 84,
    0x7F3: 84,
    0x7FA: 67,
    0x7FD: 84,
    0x816: 84,
    0x817: 84,
    0x818: 84,
    0x819: 84,
    0x81B: 84,
    0x81C: 84,
    0x81D: 84,
    0x81E: 84,
    0x81F: 84,
    0x820: 84,
    0x821: 84,
    0x822: 84,
    0x823: 84,
    0x825: 84,
    0x826: 84,
    0x827: 84,
    0x829: 84,
    0x82A: 84,
    0x82B: 84,
    0x82C: 84,
    0x82D: 84,
    0x840: 82,
    0x841: 68,
    0x842: 68,
    0x843: 68,
    0x844: 68,
    0x845: 68,
    0x846: 82,
    0x847: 82,
    0x848: 68,
    0x849: 82,
    0x84A: 68,
    0x84B: 68,
    0x84C: 68,
    0x84D: 68,
    0x84E: 68,
    0x84F: 68,
    0x850: 68,
    0x851: 68,
    0x852: 68,
    0x853: 68,
    0x854: 82,
    0x855: 68,
    0x856: 82,
    0x857: 82,
    0x858: 82,
    0x859: 84,
    0x85A: 84,
    0x85B: 84,
    0x860: 68,
    0x862: 68,
    0x863: 68,
    0x864: 68,
    0x865: 68,
    0x867: 82,
    0x868: 68,
    0x869: 82,
    0x86A: 82,
    0x870: 82,
    0x871: 82,
    0x872: 82,
    0x873: 82,
    0x874: 82,
    0x875: 82,
    0x876: 82,
    0x877: 82,
    0x878: 82,
    0x879: 82,
    0x87A: 82,
    0x87B: 82,
    0x87C: 82,
    0x87D: 82,
    0x87E: 82,
    0x87F: 82,
    0x880: 82,
    0x881: 82,
    0x882: 82,
    0x883: 67,
    0x884: 67,
    0x885: 67,
    0x886: 68,
    0x889: 68,
    0x88A: 68,
    0x88B: 68,
    0x88C: 68,
    0x88D: 68,
    0x88E: 82,
    0x898: 84,
    0x899: 84,
    0x89A: 84,
    0x89B: 84,
    0x89C: 84,
    0x89D: 84,
    0x89E: 84,
    0x89F: 84,
    0x8A0: 68,
    0x8A1: 68,
    0x8A2: 68,
    0x8A3: 68,
    0x8A4: 68,
    0x8A5: 68,
    0x8A6: 68,
    0x8A7: 68,
    0x8A8: 68,
    0x8A9: 68,
    0x8AA: 82,
    0x8AB: 82,
    0x8AC: 82,
    0x8AE: 82,
    0x8AF: 68,
    0x8B0: 68,
    0x8B1: 82,
    0x8B2: 82,
    0x8B3: 68,
    0x8B4: 68,
    0x8B5: 68,
    0x8B6: 68,
    0x8B7: 68,
    0x8B8: 68,
    0x8B9: 82,
    0x8BA: 68,
    0x8BB: 68,
    0x8BC: 68,
    0x8BD: 68,
    0x8BE: 68,
    0x8BF: 68,
    0x8C0: 68,
    0x8C1: 68,
    0x8C2: 68,
    0x8C3: 68,
    0x8C4: 68,
    0x8C5: 68,
    0x8C6: 68,
    0x8C7: 68,
    0x8C8: 68,
    0x8CA: 84,
    0x8CB: 84,
    0x8CC: 84,
    0x8CD: 84,
    0x8CE: 84,
    0x8CF: 84,
    0x8D0: 84,
    0x8D1: 84,
    0x8D2: 84,
    0x8D3: 84,
    0x8D4: 84,
    0x8D5: 84,
    0x8D6: 84,
    0x8D7: 84,
    0x8D8: 84,
    0x8D9: 84,
    0x8DA: 84,
    0x8DB: 84,
    0x8DC: 84,
    0x8DD: 84,
    0x8DE: 84,
    0x8DF: 84,
    0x8E0: 84,
    0x8E1: 84,
    0x8E3: 84,
    0x8E4: 84,
    0x8E5: 84,
    0x8E6: 84,
    0x8E7: 84,
    0x8E8: 84,
    0x8E9: 84,
    0x8EA: 84,
    0x8EB: 84,
    0x8EC: 84,
    0x8ED: 84,
    0x8EE: 84,
    0x8EF: 84,
    0x8F0: 84,
    0x8F1: 84,
    0x8F2: 84,
    0x8F3: 84,
    0x8F4: 84,
    0x8F5: 84,
    0x8F6: 84,
    0x8F7: 84,
    0x8F8: 84,
    0x8F9: 84,
    0x8FA: 84,
    0x8FB: 84,
    0x8FC: 84,
    0x8FD: 84,
    0x8FE: 84,
    0x8FF: 84,
    0x900: 84,
    0x901: 84,
    0x902: 84,
    0x93A: 84,
    0x93C: 84,
    0x941: 84,
    0x942: 84,
    0x943: 84,
    0x944: 84,
    0x945: 84,
    0x946: 84,
    0x947: 84,
    0x948: 84,
    0x94D: 84,
    0x951: 84,
    0x952: 84,
    0x953: 84,
    0x954: 84,
    0x955: 84,
    0x956: 84,
    0x957: 84,
    0x962: 84,
    0x963: 84,
    0x981: 84,
    0x9BC: 84,
    0x9C1: 84,
    0x9C2: 84,
    0x9C3: 84,
    0x9C4: 84,
    0x9CD: 84,
    0x9E2: 84,
    0x9E3: 84,
    0x9FE: 84,
    0xA01: 84,
    0xA02: 84,
    0xA3C: 84,
    0xA41: 84,
    0xA42: 84,
    0xA47: 84,
    0xA48: 84,
    0xA4B: 84,
    0xA4C: 84,
    0xA4D: 84,
    0xA51: 84,
    0xA70: 84,
    0xA71: 84,
    0xA75: 84,
    0xA81: 84,
    0xA82: 84,
    0xABC: 84,
    0xAC1: 84,
    0xAC2: 84,
    0xAC3: 84,
    0xAC4: 84,
    0xAC5: 84,
    0xAC7: 84,
    0xAC8: 84,
    0xACD: 84,
    0xAE2: 84,
    0xAE3: 84,
    0xAFA: 84,
    0xAFB: 84,
    0xAFC: 84,
    0xAFD: 84,
    0xAFE: 84,
    0xAFF: 84,
    0xB01: 84,
    0xB3C: 84,
    0xB3F: 84,
    0xB41: 84,
    0xB42: 84,
    0xB43: 84,
    0xB44: 84,
    0xB4D: 84,
    0xB55: 84,
    0xB56: 84,
    0xB62: 84,
    0xB63: 84,
    0xB82: 84,
    0xBC0: 84,
    0xBCD: 84,
    0xC00: 84,
    0xC04: 84,
    0xC3C: 84,
    0xC3E: 84,
    0xC3F: 84,
    0xC40: 84,
    0xC46: 84,
    0xC47: 84,
    0xC48: 84,
    0xC4A: 84,
    0xC4B: 84,
    0xC4C: 84,
    0xC4D: 84,
    0xC55: 84,
    0xC56: 84,
    0xC62: 84,
    0xC63: 84,
    0xC81: 84,
    0xCBC: 84,
    0xCBF: 84,
    0xCC6: 84,
    0xCCC: 84,
    0xCCD: 84,
    0xCE2: 84,
    0xCE3: 84,
    0xD00: 84,
    0xD01: 84,
    0xD3B: 84,
    0xD3C: 84,
    0xD41: 84,
    0xD42: 84,
    0xD43: 84,
    0xD44: 84,
    0xD4D: 84,
    0xD62: 84,
    0xD63: 84,
    0xD81: 84,
    0xDCA: 84,
    0xDD2: 84,
    0xDD3: 84,
    0xDD4: 84,
    0xDD6: 84,
    0xE31: 84,
    0xE34: 84,
    0xE35: 84,
    0xE36: 84,
    0xE37: 84,
    0xE38: 84,
    0xE39: 84,
    0xE3A: 84,
    0xE47: 84,
    0xE48: 84,
    0xE49: 84,
    0xE4A: 84,
    0xE4B: 84,
    0xE4C: 84,
    0xE4D: 84,
    0xE4E: 84,
    0xEB1: 84,
    0xEB4: 84,
    0xEB5: 84,
    0xEB6: 84,
    0xEB7: 84,
    0xEB8: 84,
    0xEB9: 84,
    0xEBA: 84,
    0xEBB: 84,
    0xEBC: 84,
    0xEC8: 84,
    0xEC9: 84,
    0xECA: 84,
    0xECB: 84,
    0xECC: 84,
    0xECD: 84,
    0xECE: 84,
    0xF18: 84,
    0xF19: 84,
    0xF35: 84,
    0xF37: 84,
    0xF39: 84,
    0xF71: 84,
    0xF72: 84,
    0xF73: 84,
    0xF74: 84,
    0xF75: 84,
    0xF76: 84,
    0xF77: 84,
    0xF78: 84,
    0xF79: 84,
    0xF7A: 84,
    0xF7B: 84,
    0xF7C: 84,
    0xF7D: 84,
    0xF7E: 84,
    0xF80: 84,
    0xF81: 84,
    0xF82: 84,
    0xF83: 84,
    0xF84: 84,
    0xF86: 84,
    0xF87: 84,
    0xF8D: 84,
    0xF8E: 84,
    0xF8F: 84,
    0xF90: 84,
    0xF91: 84,
    0xF92: 84,
    0xF93: 84,
    0xF94: 84,
    0xF95: 84,
    0xF96: 84,
    0xF97: 84,
    0xF99: 84,
    0xF9A: 84,
    0xF9B: 84,
    0xF9C: 84,
    0xF9D: 84,
    0xF9E: 84,
    0xF9F: 84,
    0xFA0: 84,
    0xFA1: 84,
    0xFA2: 84,
    0xFA3: 84,
    0xFA4: 84,
    0xFA5: 84,
    0xFA6: 84,
    0xFA7: 84,
    0xFA8: 84,
    0xFA9: 84,
    0xFAA: 84,
    0xFAB: 84,
    0xFAC: 84,
    0xFAD: 84,
    0xFAE: 84,
    0xFAF: 84,
    0xFB0: 84,
    0xFB1: 84,
    0xFB2: 84,
    0xFB3: 84,
    0xFB4: 84,
    0xFB5: 84,
    0xFB6: 84,
    0xFB7: 84,
    0xFB8: 84,
    0xFB9: 84,
    0xFBA: 84,
    0xFBB: 84,
    0xFBC: 84,
    0xFC6: 84,
    0x102D: 84,
    0x102E: 84,
    0x102F: 84,
    0x1030: 84,
    0x1032: 84,
    0x1033: 84,
    0x1034: 84,
    0x1035: 84,
    0x1036: 84,
    0x1037: 84,
    0x1039: 84,
    0x103A: 84,
    0x103D: 84,
    0x103E: 84,
    0x1058: 84,
    0x1059: 84,
    0x105E: 84,
    0x105F: 84,
    0x1060: 84,
    0x1071: 84,
    0x1072: 84,
    0x1073: 84,
    0x1074: 84,
    0x1082: 84,
    0x1085: 84,
    0x1086: 84,
    0x108D: 84,
    0x109D: 84,
    0x135D: 84,
    0x135E: 84,
    0x135F: 84,
    0x1712: 84,
    0x1713: 84,
    0x1714: 84,
    0x1732: 84,
    0x1733: 84,
    0x1752: 84,
    0x1753: 84,
    0x1772: 84,
    0x1773: 84,
    0x17B4: 84,
    0x17B5: 84,
    0x17B7: 84,
    0x17B8: 84,
    0x17B9: 84,
    0x17BA: 84,
    0x17BB: 84,
    0x17BC: 84,
    0x17BD: 84,
    0x17C6: 84,
    0x17C9: 84,
    0x17CA: 84,
    0x17CB: 84,
    0x17CC: 84,
    0x17CD: 84,
    0x17CE: 84,
    0x17CF: 84,
    0x17D0: 84,
    0x17D1: 84,
    0x17D2: 84,
    0x17D3: 84,
    0x17DD: 84,
    0x1807: 68,
    0x180A: 67,
    0x180B: 84,
    0x180C: 84,
    0x180D: 84,
    0x180F: 84,
    0x1820: 68,
    0x1821: 68,
    0x1822: 68,
    0x1823: 68,
    0x1824: 68,
    0x1825: 68,
    0x1826: 68,
    0x1827: 68,
    0x1828: 68,
    0x1829: 68,
    0x182A: 68,
    0x182B: 68,
    0x182C: 68,
    0x182D: 68,
    0x182E: 68,
    0x182F: 68,
    0x1830: 68,
    0x1831: 68,
    0x1832: 68,
    0x1833: 68,
    0x1834: 68,
    0x1835: 68,
    0x1836: 68,
    0x1837: 68,
    0x1838: 68,
    0x1839: 68,
    0x183A: 68,
    0x183B: 68,
    0x183C: 68,
    0x183D: 68,
    0x183E: 68,
    0x183F: 68,
    0x1840: 68,
    0x1841: 68,
    0x1842: 68,
    0x1843: 68,
    0x1844: 68,
    0x1845: 68,
    0x1846: 68,
    0x1847: 68,
    0x1848: 68,
    0x1849: 68,
    0x184A: 68,
    0x184B: 68,
    0x184C: 68,
    0x184D: 68,
    0x184E: 68,
    0x184F: 68,
    0x1850: 68,
    0x1851: 68,
    0x1852: 68,
    0x1853: 68,
    0x1854: 68,
    0x1855: 68,
    0x1856: 68,
    0x1857: 68,
    0x1858: 68,
    0x1859: 68,
    0x185A: 68,
    0x185B: 68,
    0x185C: 68,
    0x185D: 68,
    0x185E: 68,
    0x185F: 68,
    0x1860: 68,
    0x1861: 68,
    0x1862: 68,
    0x1863: 68,
    0x1864: 68,
    0x1865: 68,
    0x1866: 68,
    0x1867: 68,
    0x1868: 68,
    0x1869: 68,
    0x186A: 68,
    0x186B: 68,
    0x186C: 68,
    0x186D: 68,
    0x186E: 68,
    0x186F: 68,
    0x1870: 68,
    0x1871: 68,
    0x1872: 68,
    0x1873: 68,
    0x1874: 68,
    0x1875: 68,
    0x1876: 68,
    0x1877: 68,
    0x1878: 68,
    0x1885: 84,
    0x1886: 84,
    0x1887: 68,
    0x1888: 68,
    0x1889: 68,
    0x188A: 68,
    0x188B: 68,
    0x188C: 68,
    0x188D: 68,
    0x188E: 68,
    0x188F: 68,
    0x1890: 68,
    0x1891: 68,
    0x1892: 68,
    0x1893: 68,
    0x1894: 68,
    0x1895: 68,
    0x1896: 68,
    0x1897: 68,
    0x1898: 68,
    0x1899: 68,
    0x189A: 68,
    0x189B: 68,
    0x189C: 68,
    0x189D: 68,
    0x189E: 68,
    0x189F: 68,
    0x18A0: 68,
    0x18A1: 68,
    0x18A2: 68,
    0x18A3: 68,
    0x18A4: 68,
    0x18A5: 68,
    0x18A6: 68,
    0x18A7: 68,
    0x18A8: 68,
    0x18A9: 84,
    0x18AA: 68,
    0x1920: 84,
    0x1921: 84,
    0x1922: 84,
    0x1927: 84,
    0x1928: 84,
    0x1932: 84,
    0x1939: 84,
    0x193A: 84,
    0x193B: 84,
    0x1A17: 84,
    0x1A18: 84,
    0x1A1B: 84,
    0x1A56: 84,
    0x1A58: 84,
    0x1A59: 84,
    0x1A5A: 84,
    0x1A5B: 84,
    0x1A5C: 84,
    0x1A5D: 84,
    0x1A5E: 84,
    0x1A60: 84,
    0x1A62: 84,
    0x1A65: 84,
    0x1A66: 84,
    0x1A67: 84,
    0x1A68: 84,
    0x1A69: 84,
    0x1A6A: 84,
    0x1A6B: 84,
    0x1A6C: 84,
    0x1A73: 84,
    0x1A74: 84,
    0x1A75: 84,
    0x1A76: 84,
    0x1A77: 84,
    0x1A78: 84,
    0x1A79: 84,
    0x1A7A: 84,
    0x1A7B: 84,
    0x1A7C: 84,
    0x1A7F: 84,
    0x1AB0: 84,
    0x1AB1: 84,
    0x1AB2: 84,
    0x1AB3: 84,
    0x1AB4: 84,
    0x1AB5: 84,
    0x1AB6: 84,
    0x1AB7: 84,
    0x1AB8: 84,
    0x1AB9: 84,
    0x1ABA: 84,
    0x1ABB: 84,
    0x1ABC: 84,
    0x1ABD: 84,
    0x1ABE: 84,
    0x1ABF: 84,
    0x1AC0: 84,
    0x1AC1: 84,
    0x1AC2: 84,
    0x1AC3: 84,
    0x1AC4: 84,
    0x1AC5: 84,
    0x1AC6: 84,
    0x1AC7: 84,
    0x1AC8: 84,
    0x1AC9: 84,
    0x1ACA: 84,
    0x1ACB: 84,
    0x1ACC: 84,
    0x1ACD: 84,
    0x1ACE: 84,
    0x1B00: 84,
    0x1B01: 84,
    0x1B02: 84,
    0x1B03: 84,
    0x1B34: 84,
    0x1B36: 84,
    0x1B37: 84,
    0x1B38: 84,
    0x1B39: 84,
    0x1B3A: 84,
    0x1B3C: 84,
    0x1B42: 84,
    0x1B6B: 84,
    0x1B6C: 84,
    0x1B6D: 84,
    0x1B6E: 84,
    0x1B6F: 84,
    0x1B70: 84,
    0x1B71: 84,
    0x1B72: 84,
    0x1B73: 84,
    0x1B80: 84,
    0x1B81: 84,
    0x1BA2: 84,
    0x1BA3: 84,
    0x1BA4: 84,
    0x1BA5: 84,
    0x1BA8: 84,
    0x1BA9: 84,
    0x1BAB: 84,
    0x1BAC: 84,
    0x1BAD: 84,
    0x1BE6: 84,
    0x1BE8: 84,
    0x1BE9: 84,
    0x1BED: 84,
    0x1BEF: 84,
    0x1BF0: 84,
    0x1BF1: 84,
    0x1C2C: 84,
    0x1C2D: 84,
    0x1C2E: 84,
    0x1C2F: 84,
    0x1C30: 84,
    0x1C31: 84,
    0x1C32: 84,
    0x1C33: 84,
    0x1C36: 84,
    0x1C37: 84,
    0x1CD0: 84,
    0x1CD1: 84,
    0x1CD2: 84,
    0x1CD4: 84,
    0x1CD5: 84,
    0x1CD6: 84,
    0x1CD7: 84,
    0x1CD8: 84,
    0x1CD9: 84,
    0x1CDA: 84,
    0x1CDB: 84,
    0x1CDC: 84,
    0x1CDD: 84,
    0x1CDE: 84,
    0x1CDF: 84,
    0x1CE0: 84,
    0x1CE2: 84,
    0x1CE3: 84,
    0x1CE4: 84,
    0x1CE5: 84,
    0x1CE6: 84,
    0x1CE7: 84,
    0x1CE8: 84,
    0x1CED: 84,
    0x1CF4: 84,
    0x1CF8: 84,
    0x1CF9: 84,
    0x1DC0: 84,
    0x1DC1: 84,
    0x1DC2: 84,
    0x1DC3: 84,
    0x1DC4: 84,
    0x1DC5: 84,
    0x1DC6: 84,
    0x1DC7: 84,
    0x1DC8: 84,
    0x1DC9: 84,
    0x1DCA: 84,
    0x1DCB: 84,
    0x1DCC: 84,
    0x1DCD: 84,
    0x1DCE: 84,
    0x1DCF: 84,
    0x1DD0: 84,
    0x1DD1: 84,
    0x1DD2: 84,
    0x1DD3: 84,
    0x1DD4: 84,
    0x1DD5: 84,
    0x1DD6: 84,
    0x1DD7: 84,
    0x1DD8: 84,
    0x1DD9: 84,
    0x1DDA: 84,
    0x1DDB: 84,
    0x1DDC: 84,
    0x1DDD: 84,
    0x1DDE: 84,
    0x1DDF: 84,
    0x1DE0: 84,
    0x1DE1: 84,
    0x1DE2: 84,
    0x1DE3: 84,
    0x1DE4: 84,
    0x1DE5: 84,
    0x1DE6: 84,
    0x1DE7: 84,
    0x1DE8: 84,
    0x1DE9: 84,
    0x1DEA: 84,
    0x1DEB: 84,
    0x1DEC: 84,
    0x1DED: 84,
    0x1DEE: 84,
    0x1DEF: 84,
    0x1DF0: 84,
    0x1DF1: 84,
    0x1DF2: 84,
    0x1DF3: 84,
    0x1DF4: 84,
    0x1DF5: 84,
    0x1DF6: 84,
    0x1DF7: 84,
    0x1DF8: 84,
    0x1DF9: 84,
    0x1DFA: 84,
    0x1DFB: 84,
    0x1DFC: 84,
    0x1DFD: 84,
    0x1DFE: 84,
    0x1DFF: 84,
    0x200B: 84,
    0x200D: 67,
    0x200E: 84,
    0x200F: 84,
    0x202A: 84,
    0x202B: 84,
    0x202C: 84,
    0x202D: 84,
    0x202E: 84,
    0x2060: 84,
    0x2061: 84,
    0x2062: 84,
    0x2063: 84,
    0x2064: 84,
    0x206A: 84,
    0x206B: 84,
    0x206C: 84,
    0x206D: 84,
    0x206E: 84,
    0x206F: 84,
    0x20D0: 84,
    0x20D1: 84,
    0x20D2: 84,
    0x20D3: 84,
    0x20D4: 84,
    0x20D5: 84,
    0x20D6: 84,
    0x20D7: 84,
    0x20D8: 84,
    0x20D9: 84,
    0x20DA: 84,
    0x20DB: 84,
    0x20DC: 84,
    0x20DD: 84,
    0x20DE: 84,
    0x20DF: 84,
    0x20E0: 84,
    0x20E1: 84,
    0x20E2: 84,
    0x20E3: 84,
    0x20E4: 84,
    0x20E5: 84,
    0x20E6: 84,
    0x20E7: 84,
    0x20E8: 84,
    0x20E9: 84,
    0x20EA: 84,
    0x20EB: 84,
    0x20EC: 84,
    0x20ED: 84,
    0x20EE: 84,
    0x20EF: 84,
    0x20F0: 84,
    0x2CEF: 84,
    0x2CF0: 84,
    0x2CF1: 84,
    0x2D7F: 84,
    0x2DE0: 84,
    0x2DE1: 84,
    0x2DE2: 84,
    0x2DE3: 84,
    0x2DE4: 84,
    0x2DE5: 84,
    0x2DE6: 84,
    0x2DE7: 84,
    0x2DE8: 84,
    0x2DE9: 84,
    0x2DEA: 84,
    0x2DEB: 84,
    0x2DEC: 84,
    0x2DED: 84,
    0x2DEE: 84,
    0x2DEF: 84,
    0x2DF0: 84,
    0x2DF1: 84,
    0x2DF2: 84,
    0x2DF3: 84,
    0x2DF4: 84,
    0x2DF5: 84,
    0x2DF6: 84,
    0x2DF7: 84,
    0x2DF8: 84,
    0x2DF9: 84,
    0x2DFA: 84,
    0x2DFB: 84,
    0x2DFC: 84,
    0x2DFD: 84,
    0x2DFE: 84,
    0x2DFF: 84,
    0x302A: 84,
    0x302B: 84,
    0x302C: 84,
    0x302D: 84,
    0x3099: 84,
    0x309A: 84,
    0xA66F: 84,
    0xA670: 84,
    0xA671: 84,
    0xA672: 84,
    0xA674: 84,
    0xA675: 84,
    0xA676: 84,
    0xA677: 84,
    0xA678: 84,
    0xA679: 84,
    0xA67A: 84,
    0xA67B: 84,
    0xA67C: 84,
    0xA67D: 84,
    0xA69E: 84,
    0xA69F: 84,
    0xA6F0: 84,
    0xA6F1: 84,
    0xA802: 84,
    0xA806: 84,
    0xA80B: 84,
    0xA825: 84,
    0xA826: 84,
    0xA82C: 84,
    0xA840: 68,
    0xA841: 68,
    0xA842: 68,
    0xA843: 68,
    0xA844: 68,
    0xA845: 68,
    0xA846: 68,
    0xA847: 68,
    0xA848: 68,
    0xA849: 68,
    0xA84A: 68,
    0xA84B: 68,
    0xA84C: 68,
    0xA84D: 68,
    0xA84E: 68,
    0xA84F: 68,
    0xA850: 68,
    0xA851: 68,
    0xA852: 68,
    0xA853: 68,
    0xA854: 68,
    0xA855: 68,
    0xA856: 68,
    0xA857: 68,
    0xA858: 68,
    0xA859: 68,
    0xA85A: 68,
    0xA85B: 68,
    0xA85C: 68,
    0xA85D: 68,
    0xA85E: 68,
    0xA85F: 68,
    0xA860: 68,
    0xA861: 68,
    0xA862: 68,
    0xA863: 68,
    0xA864: 68,
    0xA865: 68,
    0xA866: 68,
    0xA867: 68,
    0xA868: 68,
    0xA869: 68,
    0xA86A: 68,
    0xA86B: 68,
    0xA86C: 68,
    0xA86D: 68,
    0xA86E: 68,
    0xA86F: 68,
    0xA870: 68,
    0xA871: 68,
    0xA872: 76,
    0xA8C4: 84,
    0xA8C5: 84,
    0xA8E0: 84,
    0xA8E1: 84,
    0xA8E2: 84,
    0xA8E3: 84,
    0xA8E4: 84,
    0xA8E5: 84,
    0xA8E6: 84,
    0xA8E7: 84,
    0xA8E8: 84,
    0xA8E9: 84,
    0xA8EA: 84,
    0xA8EB: 84,
    0xA8EC: 84,
    0xA8ED: 84,
    0xA8EE: 84,
    0xA8EF: 84,
    0xA8F0: 84,
    0xA8F1: 84,
    0xA8FF: 84,
    0xA926: 84,
    0xA927: 84,
    0xA928: 84,
    0xA929: 84,
    0xA92A: 84,
    0xA92B: 84,
    0xA92C: 84,
    0xA92D: 84,
    0xA947: 84,
    0xA948: 84,
    0xA949: 84,
    0xA94A: 84,
    0xA94B: 84,
    0xA94C: 84,
    0xA94D: 84,
    0xA94E: 84,
    0xA94F: 84,
    0xA950: 84,
    0xA951: 84,
    0xA980: 84,
    0xA981: 84,
    0xA982: 84,
    0xA9B3: 84,
    0xA9B6: 84,
    0xA9B7: 84,
    0xA9B8: 84,
    0xA9B9: 84,
    0xA9BC: 84,
    0xA9BD: 84,
    0xA9E5: 84,
    0xAA29: 84,
    0xAA2A: 84,
    0xAA2B: 84,
    0xAA2C: 84,
    0xAA2D: 84,
    0xAA2E: 84,
    0xAA31: 84,
    0xAA32: 84,
    0xAA35: 84,
    0xAA36: 84,
    0xAA43: 84,
    0xAA4C: 84,
    0xAA7C: 84,
    0xAAB0: 84,
    0xAAB2: 84,
    0xAAB3: 84,
    0xAAB4: 84,
    0xAAB7: 84,
    0xAAB8: 84,
    0xAABE: 84,
    0xAABF: 84,
    0xAAC1: 84,
    0xAAEC: 84,
    0xAAED: 84,
    0xAAF6: 84,
    0xABE5: 84,
    0xABE8: 84,
    0xABED: 84,
    0xFB1E: 84,
    0xFE00: 84,
    0xFE01: 84,
    0xFE02: 84,
    0xFE03: 84,
    0xFE04: 84,
    0xFE05: 84,
    0xFE06: 84,
    0xFE07: 84,
    0xFE08: 84,
    0xFE09: 84,
    0xFE0A: 84,
    0xFE0B: 84,
    0xFE0C: 84,
    0xFE0D: 84,
    0xFE0E: 84,
    0xFE0F: 84,
    0xFE20: 84,
    0xFE21: 84,
    0xFE22: 84,
    0xFE23: 84,
    0xFE24: 84,
    0xFE25: 84,
    0xFE26: 84,
    0xFE27: 84,
    0xFE28: 84,
    0xFE29: 84,
    0xFE2A: 84,
    0xFE2B: 84,
    0xFE2C: 84,
    0xFE2D: 84,
    0xFE2E: 84,
    0xFE2F: 84,
    0xFEFF: 84,
    0xFFF9: 84,
    0xFFFA: 84,
    0xFFFB: 84,
    0x101FD: 84,
    0x102E0: 84,
    0x10376: 84,
    0x10377: 84,
    0x10378: 84,
    0x10379: 84,
    0x1037A: 84,
    0x10A01: 84,
    0x10A02: 84,
    0x10A03: 84,
    0x10A05: 84,
    0x10A06: 84,
    0x10A0C: 84,
    0x10A0D: 84,
    0x10A0E: 84,
    0x10A0F: 84,
    0x10A38: 84,
    0x10A39: 84,
    0x10A3A: 84,
    0x10A3F: 84,
    0x10AC0: 68,
    0x10AC1: 68,
    0x10AC2: 68,
    0x10AC3: 68,
    0x10AC4: 68,
    0x10AC5: 82,
    0x10AC7: 82,
    0x10AC9: 82,
    0x10ACA: 82,
    0x10ACD: 76,
    0x10ACE: 82,
    0x10ACF: 82,
    0x10AD0: 82,
    0x10AD1: 82,
    0x10AD2: 82,
    0x10AD3: 68,
    0x10AD4: 68,
    0x10AD5: 68,
    0x10AD6: 68,
    0x10AD7: 76,
    0x10AD8: 68,
    0x10AD9: 68,
    0x10ADA: 68,
    0x10ADB: 68,
    0x10ADC: 68,
    0x10ADD: 82,
    0x10ADE: 68,
    0x10ADF: 68,
    0x10AE0: 68,
    0x10AE1: 82,
    0x10AE4: 82,
    0x10AE5: 84,
    0x10AE6: 84,
    0x10AEB: 68,
    0x10AEC: 68,
    0x10AED: 68,
    0x10AEE: 68,
    0x10AEF: 82,
    0x10B80: 68,
    0x10B81: 82,
    0x10B82: 68,
    0x10B83: 82,
    0x10B84: 82,
    0x10B85: 82,
    0x10B86: 68,
    0x10B87: 68,
    0x10B88: 68,
    0x10B89: 82,
    0x10B8A: 68,
    0x10B8B: 68,
    0x10B8C: 82,
    0x10B8D: 68,
    0x10B8E: 82,
    0x10B8F: 82,
    0x10B90: 68,
    0x10B91: 82,
    0x10BA9: 82,
    0x10BAA: 82,
    0x10BAB: 82,
    0x10BAC: 82,
    0x10BAD: 68,
    0x10BAE: 68,
    0x10D00: 76,
    0x10D01: 68,
    0x10D02: 68,
    0x10D03: 68,
    0x10D04: 68,
    0x10D05: 68,
    0x10D06: 68,
    0x10D07: 68,
    0x10D08: 68,
    0x10D09: 68,
    0x10D0A: 68,
    0x10D0B: 68,
    0x10D0C: 68,
    0x10D0D: 68,
    0x10D0E: 68,
    0x10D0F: 68,
    0x10D10: 68,
    0x10D11: 68,
    0x10D12: 68,
    0x10D13: 68,
    0x10D14: 68,
    0x10D15: 68,
    0x10D16: 68,
    0x10D17: 68,
    0x10D18: 68,
    0x10D19: 68,
    0x10D1A: 68,
    0x10D1B: 68,
    0x10D1C: 68,
    0x10D1D: 68,
    0x10D1E: 68,
    0x10D1F: 68,
    0x10D20: 68,
    0x10D21: 68,
    0x10D22: 82,
    0x10D23: 68,
    0x10D24: 84,
    0x10D25: 84,
    0x10D26: 84,
    0x10D27: 84,
    0x10EAB: 84,
    0x10EAC: 84,
    0x10EFD: 84,
    0x10EFE: 84,
    0x10EFF: 84,
    0x10F30: 68,
    0x10F31: 68,
    0x10F32: 68,
    0x10F33: 82,
    0x10F34: 68,
    0x10F35: 68,
    0x10F36: 68,
    0x10F37: 68,
    0x10F38: 68,
    0x10F39: 68,
    0x10F3A: 68,
    0x10F3B: 68,
    0x10F3C: 68,
    0x10F3D: 68,
    0x10F3E: 68,
    0x10F3F: 68,
    0x10F40: 68,
    0x10F41: 68,
    0x10F42: 68,
    0x10F43: 68,
    0x10F44: 68,
    0x10F46: 84,
    0x10F47: 84,
    0x10F48: 84,
    0x10F49: 84,
    0x10F4A: 84,
    0x10F4B: 84,
    0x10F4C: 84,
    0x10F4D: 84,
    0x10F4E: 84,
    0x10F4F: 84,
    0x10F50: 84,
    0x10F51: 68,
    0x10F52: 68,
    0x10F53: 68,
    0x10F54: 82,
    0x10F70: 68,
    0x10F71: 68,
    0x10F72: 68,
    0x10F73: 68,
    0x10F74: 82,
    0x10F75: 82,
    0x10F76: 68,
    0x10F77: 68,
    0x10F78: 68,
    0x10F79: 68,
    0x10F7A: 68,
    0x10F7B: 68,
    0x10F7C: 68,
    0x10F7D: 68,
    0x10F7E: 68,
    0x10F7F: 68,
    0x10F80: 68,
    0x10F81: 68,
    0x10F82: 84,
    0x10F83: 84,
    0x10F84: 84,
    0x10F85: 84,
    0x10FB0: 68,
    0x10FB2: 68,
    0x10FB3: 68,
    0x10FB4: 82,
    0x10FB5: 82,
    0x10FB6: 82,
    0x10FB8: 68,
    0x10FB9: 82,
    0x10FBA: 82,
    0x10FBB: 68,
    0x10FBC: 68,
    0x10FBD: 82,
    0x10FBE: 68,
    0x10FBF: 68,
    0x10FC1: 68,
    0x10FC2: 82,
    0x10FC3: 82,
    0x10FC4: 68,
    0x10FC9: 82,
    0x10FCA: 68,
    0x10FCB: 76,
    0x11001: 84,
    0x11038: 84,
    0x11039: 84,
    0x1103A: 84,
    0x1103B: 84,
    0x1103C: 84,
    0x1103D: 84,
    0x1103E: 84,
    0x1103F: 84,
    0x11040: 84,
    0x11041: 84,
    0x11042: 84,
    0x11043: 84,
    0x11044: 84,
    0x11045: 84,
    0x11046: 84,
    0x11070: 84,
    0x11073: 84,
    0x11074: 84,
    0x1107F: 84,
    0x11080: 84,
    0x11081: 84,
    0x110B3: 84,
    0x110B4: 84,
    0x110B5: 84,
    0x110B6: 84,
    0x110B9: 84,
    0x110BA: 84,
    0x110C2: 84,
    0x11100: 84,
    0x11101: 84,
    0x11102: 84,
    0x11127: 84,
    0x11128: 84,
    0x11129: 84,
    0x1112A: 84,
    0x1112B: 84,
    0x1112D: 84,
    0x1112E: 84,
    0x1112F: 84,
    0x11130: 84,
    0x11131: 84,
    0x11132: 84,
    0x11133: 84,
    0x11134: 84,
    0x11173: 84,
    0x11180: 84,
    0x11181: 84,
    0x111B6: 84,
    0x111B7: 84,
    0x111B8: 84,
    0x111B9: 84,
    0x111BA: 84,
    0x111BB: 84,
    0x111BC: 84,
    0x111BD: 84,
    0x111BE: 84,
    0x111C9: 84,
    0x111CA: 84,
    0x111CB: 84,
    0x111CC: 84,
    0x111CF: 84,
    0x1122F: 84,
    0x11230: 84,
    0x11231: 84,
    0x11234: 84,
    0x11236: 84,
    0x11237: 84,
    0x1123E: 84,
    0x11241: 84,
    0x112DF: 84,
    0x112E3: 84,
    0x112E4: 84,
    0x112E5: 84,
    0x112E6: 84,
    0x112E7: 84,
    0x112E8: 84,
    0x112E9: 84,
    0x112EA: 84,
    0x11300: 84,
    0x11301: 84,
    0x1133B: 84,
    0x1133C: 84,
    0x11340: 84,
    0x11366: 84,
    0x11367: 84,
    0x11368: 84,
    0x11369: 84,
    0x1136A: 84,
    0x1136B: 84,
    0x1136C: 84,
    0x11370: 84,
    0x11371: 84,
    0x11372: 84,
    0x11373: 84,
    0x11374: 84,
    0x11438: 84,
    0x11439: 84,
    0x1143A: 84,
    0x1143B: 84,
    0x1143C: 84,
    0x1143D: 84,
    0x1143E: 84,
    0x1143F: 84,
    0x11442: 84,
    0x11443: 84,
    0x11444: 84,
    0x11446: 84,
    0x1145E: 84,
    0x114B3: 84,
    0x114B4: 84,
    0x114B5: 84,
    0x114B6: 84,
    0x114B7: 84,
    0x114B8: 84,
    0x114BA: 84,
    0x114BF: 84,
    0x114C0: 84,
    0x114C2: 84,
    0x114C3: 84,
    0x115B2: 84,
    0x115B3: 84,
    0x115B4: 84,
    0x115B5: 84,
    0x115BC: 84,
    0x115BD: 84,
    0x115BF: 84,
    0x115C0: 84,
    0x115DC: 84,
    0x115DD: 84,
    0x11633: 84,
    0x11634: 84,
    0x11635: 84,
    0x11636: 84,
    0x11637: 84,
    0x11638: 84,
    0x11639: 84,
    0x1163A: 84,
    0x1163D: 84,
    0x1163F: 84,
    0x11640: 84,
    0x116AB: 84,
    0x116AD: 84,
    0x116B0: 84,
    0x116B1: 84,
    0x116B2: 84,
    0x116B3: 84,
    0x116B4: 84,
    0x116B5: 84,
    0x116B7: 84,
    0x1171D: 84,
    0x1171E: 84,
    0x1171F: 84,
    0x11722: 84,
    0x11723: 84,
    0x11724: 84,
    0x11725: 84,
    0x11727: 84,
    0x11728: 84,
    0x11729: 84,
    0x1172A: 84,
    0x1172B: 84,
    0x1182F: 84,
    0x11830: 84,
    0x11831: 84,
    0x11832: 84,
    0x11833: 84,
    0x11834: 84,
    0x11835: 84,
    0x11836: 84,
    0x11837: 84,
    0x11839: 84,
    0x1183A: 84,
    0x1193B: 84,
    0x1193C: 84,
    0x1193E: 84,
    0x11943: 84,
    0x119D4: 84,
    0x119D5: 84,
    0x119D6: 84,
    0x119D7: 84,
    0x119DA: 84,
    0x119DB: 84,
    0x119E0: 84,
    0x11A01: 84,
    0x11A02: 84,
    0x11A03: 84,
    0x11A04: 84,
    0x11A05: 84,
    0x11A06: 84,
    0x11A07: 84,
    0x11A08: 84,
    0x11A09: 84,
    0x11A0A: 84,
    0x11A33: 84,
    0x11A34: 84,
    0x11A35: 84,
    0x11A36: 84,
    0x11A37: 84,
    0x11A38: 84,
    0x11A3B: 84,
    0x11A3C: 84,
    0x11A3D: 84,
    0x11A3E: 84,
    0x11A47: 84,
    0x11A51: 84,
    0x11A52: 84,
    0x11A53: 84,
    0x11A54: 84,
    0x11A55: 84,
    0x11A56: 84,
    0x11A59: 84,
    0x11A5A: 84,
    0x11A5B: 84,
    0x11A8A: 84,
    0x11A8B: 84,
    0x11A8C: 84,
    0x11A8D: 84,
    0x11A8E: 84,
    0x11A8F: 84,
    0x11A90: 84,
    0x11A91: 84,
    0x11A92: 84,
    0x11A93: 84,
    0x11A94: 84,
    0x11A95: 84,
    0x11A96: 84,
    0x11A98: 84,
    0x11A99: 84,
    0x11C30: 84,
    0x11C31: 84,
    0x11C32: 84,
    0x11C33: 84,
    0x11C34: 84,
    0x11C35: 84,
    0x11C36: 84,
    0x11C38: 84,
    0x11C39: 84,
    0x11C3A: 84,
    0x11C3B: 84,
    0x11C3C: 84,
    0x11C3D: 84,
    0x11C3F: 84,
    0x11C92: 84,
    0x11C93: 84,
    0x11C94: 84,
    0x11C95: 84,
    0x11C96: 84,
    0x11C97: 84,
    0x11C98: 84,
    0x11C99: 84,
    0x11C9A: 84,
    0x11C9B: 84,
    0x11C9C: 84,
    0x11C9D: 84,
    0x11C9E: 84,
    0x11C9F: 84,
    0x11CA0: 84,
    0x11CA1: 84,
    0x11CA2: 84,
    0x11CA3: 84,
    0x11CA4: 84,
    0x11CA5: 84,
    0x11CA6: 84,
    0x11CA7: 84,
    0x11CAA: 84,
    0x11CAB: 84,
    0x11CAC: 84,
    0x11CAD: 84,
    0x11CAE: 84,
    0x11CAF: 84,
    0x11CB0: 84,
    0x11CB2: 84,
    0x11CB3: 84,
    0x11CB5: 84,
    0x11CB6: 84,
    0x11D31: 84,
    0x11D32: 84,
    0x11D33: 84,
    0x11D34: 84,
    0x11D35: 84,
    0x11D36: 84,
    0x11D3A: 84,
    0x11D3C: 84,
    0x11D3D: 84,
    0x11D3F: 84,
    0x11D40: 84,
    0x11D41: 84,
    0x11D42: 84,
    0x11D43: 84,
    0x11D44: 84,
    0x11D45: 84,
    0x11D47: 84,
    0x11D90: 84,
    0x11D91: 84,
    0x11D95: 84,
    0x11D97: 84,
    0x11EF3: 84,
    0x11EF4: 84,
    0x11F00: 84,
    0x11F01: 84,
    0x11F36: 84,
    0x11F37: 84,
    0x11F38: 84,
    0x11F39: 84,
    0x11F3A: 84,
    0x11F40: 84,
    0x11F42: 84,
    0x13430: 84,
    0x13431: 84,
    0x13432: 84,
    0x13433: 84,
    0x13434: 84,
    0x13435: 84,
    0x13436: 84,
    0x13437: 84,
    0x13438: 84,
    0x13439: 84,
    0x1343A: 84,
    0x1343B: 84,
    0x1343C: 84,
    0x1343D: 84,
    0x1343E: 84,
    0x1343F: 84,
    0x13440: 84,
    0x13447: 84,
    0x13448: 84,
    0x13449: 84,
    0x1344A: 84,
    0x1344B: 84,
    0x1344C: 84,
    0x1344D: 84,
    0x1344E: 84,
    0x1344F: 84,
    0x13450: 84,
    0x13451: 84,
    0x13452: 84,
    0x13453: 84,
    0x13454: 84,
    0x13455: 84,
    0x16AF0: 84,
    0x16AF1: 84,
    0x16AF2: 84,
    0x16AF3: 84,
    0x16AF4: 84,
    0x16B30: 84,
    0x16B31: 84,
    0x16B32: 84,
    0x16B33: 84,
    0x16B34: 84,
    0x16B35: 84,
    0x16B36: 84,
    0x16F4F: 84,
    0x16F8F: 84,
    0x16F90: 84,
    0x16F91: 84,
    0x16F92: 84,
    0x16FE4: 84,
    0x1BC9D: 84,
    0x1BC9E: 84,
    0x1BCA0: 84,
    0x1BCA1: 84,
    0x1BCA2: 84,
    0x1BCA3: 84,
    0x1CF00: 84,
    0x1CF01: 84,
    0x1CF02: 84,
    0x1CF03: 84,
    0x1CF04: 84,
    0x1CF05: 84,
    0x1CF06: 84,
    0x1CF07: 84,
    0x1CF08: 84,
    0x1CF09: 84,
    0x1CF0A: 84,
    0x1CF0B: 84,
    0x1CF0C: 84,
    0x1CF0D: 84,
    0x1CF0E: 84,
    0x1CF0F: 84,
    0x1CF10: 84,
    0x1CF11: 84,
    0x1CF12: 84,
    0x1CF13: 84,
    0x1CF14: 84,
    0x1CF15: 84,
    0x1CF16: 84,
    0x1CF17: 84,
    0x1CF18: 84,
    0x1CF19: 84,
    0x1CF1A: 84,
    0x1CF1B: 84,
    0x1CF1C: 84,
    0x1CF1D: 84,
    0x1CF1E: 84,
    0x1CF1F: 84,
    0x1CF20: 84,
    0x1CF21: 84,
    0x1CF22: 84,
    0x1CF23: 84,
    0x1CF24: 84,
    0x1CF25: 84,
    0x1CF26: 84,
    0x1CF27: 84,
    0x1CF28: 84,
    0x1CF29: 84,
    0x1CF2A: 84,
    0x1CF2B: 84,
    0x1CF2C: 84,
    0x1CF2D: 84,
    0x1CF30: 84,
    0x1CF31: 84,
    0x1CF32: 84,
    0x1CF33: 84,
    0x1CF34: 84,
    0x1CF35: 84,
    0x1CF36: 84,
    0x1CF37: 84,
    0x1CF38: 84,
    0x1CF39: 84,
    0x1CF3A: 84,
    0x1CF3B: 84,
    0x1CF3C: 84,
    0x1CF3D: 84,
    0x1CF3E: 84,
    0x1CF3F: 84,
    0x1CF40: 84,
    0x1CF41: 84,
    0x1CF42: 84,
    0x1CF43: 84,
    0x1CF44: 84,
    0x1CF45: 84,
    0x1CF46: 84,
    0x1D167: 84,
    0x1D168: 84,
    0x1D169: 84,
    0x1D173: 84,
    0x1D174: 84,
    0x1D175: 84,
    0x1D176: 84,
    0x1D177: 84,
    0x1D178: 84,
    0x1D179: 84,
    0x1D17A: 84,
    0x1D17B: 84,
    0x1D17C: 84,
    0x1D17D: 84,
    0x1D17E: 84,
    0x1D17F: 84,
    0x1D180: 84,
    0x1D181: 84,
    0x1D182: 84,
    0x1D185: 84,
    0x1D186: 84,
    0x1D187: 84,
    0x1D188: 84,
    0x1D189: 84,
    0x1D18A: 84,
    0x1D18B: 84,
    0x1D1AA: 84,
    0x1D1AB: 84,
    0x1D1AC: 84,
    0x1D1AD: 84,
    0x1D242: 84,
    0x1D243: 84,
    0x1D244: 84,
    0x1DA00: 84,
    0x1DA01: 84,
    0x1DA02: 84,
    0x1DA03: 84,
    0x1DA04: 84,
    0x1DA05: 84,
    0x1DA06: 84,
    0x1DA07: 84,
    0x1DA08: 84,
    0x1DA09: 84,
    0x1DA0A: 84,
    0x1DA0B: 84,
    0x1DA0C: 84,
    0x1DA0D: 84,
    0x1DA0E: 84,
    0x1DA0F: 84,
    0x1DA10: 84,
    0x1DA11: 84,
    0x1DA12: 84,
    0x1DA13: 84,
    0x1DA14: 84,
    0x1DA15: 84,
    0x1DA16: 84,
    0x1DA17: 84,
    0x1DA18: 84,
    0x1DA19: 84,
    0x1DA1A: 84,
    0x1DA1B: 84,
    0x1DA1C: 84,
    0x1DA1D: 84,
    0x1DA1E: 84,
    0x1DA1F: 84,
    0x1DA20: 84,
    0x1DA21: 84,
    0x1DA22: 84,
    0x1DA23: 84,
    0x1DA24: 84,
    0x1DA25: 84,
    0x1DA26: 84,
    0x1DA27: 84,
    0x1DA28: 84,
    0x1DA29: 84,
    0x1DA2A: 84,
    0x1DA2B: 84,
    0x1DA2C: 84,
    0x1DA2D: 84,
    0x1DA2E: 84,
    0x1DA2F: 84,
    0x1DA30: 84,
    0x1DA31: 84,
    0x1DA32: 84,
    0x1DA33: 84,
    0x1DA34: 84,
    0x1DA35: 84,
    0x1DA36: 84,
    0x1DA3B: 84,
    0x1DA3C: 84,
    0x1DA3D: 84,
    0x1DA3E: 84,
    0x1DA3F: 84,
    0x1DA40: 84,
    0x1DA41: 84,
    0x1DA42: 84,
    0x1DA43: 84,
    0x1DA44: 84,
    0x1DA45: 84,
    0x1DA46: 84,
    0x1DA47: 84,
    0x1DA48: 84,
    0x1DA49: 84,
    0x1DA4A: 84,
    0x1DA4B: 84,
    0x1DA4C: 84,
    0x1DA4D: 84,
    0x1DA4E: 84,
    0x1DA4F: 84,
    0x1DA50: 84,
    0x1DA51: 84,
    0x1DA52: 84,
    0x1DA53: 84,
    0x1DA54: 84,
    0x1DA55: 84,
    0x1DA56: 84,
    0x1DA57: 84,
    0x1DA58: 84,
    0x1DA59: 84,
    0x1DA5A: 84,
    0x1DA5B: 84,
    0x1DA5C: 84,
    0x1DA5D: 84,
    0x1DA5E: 84,
    0x1DA5F: 84,
    0x1DA60: 84,
    0x1DA61: 84,
    0x1DA62: 84,
    0x1DA63: 84,
    0x1DA64: 84,
    0x1DA65: 84,
    0x1DA66: 84,
    0x1DA67: 84,
    0x1DA68: 84,
    0x1DA69: 84,
    0x1DA6A: 84,
    0x1DA6B: 84,
    0x1DA6C: 84,
    0x1DA75: 84,
    0x1DA84: 84,
    0x1DA9B: 84,
    0x1DA9C: 84,
    0x1DA9D: 84,
    0x1DA9E: 84,
    0x1DA9F: 84,
    0x1DAA1: 84,
    0x1DAA2: 84,
    0x1DAA3: 84,
    0x1DAA4: 84,
    0x1DAA5: 84,
    0x1DAA6: 84,
    0x1DAA7: 84,
    0x1DAA8: 84,
    0x1DAA9: 84,
    0x1DAAA: 84,
    0x1DAAB: 84,
    0x1DAAC: 84,
    0x1DAAD: 84,
    0x1DAAE: 84,
    0x1DAAF: 84,
    0x1E000: 84,
    0x1E001: 84,
    0x1E002: 84,
    0x1E003: 84,
    0x1E004: 84,
    0x1E005: 84,
    0x1E006: 84,
    0x1E008: 84,
    0x1E009: 84,
    0x1E00A: 84,
    0x1E00B: 84,
    0x1E00C: 84,
    0x1E00D: 84,
    0x1E00E: 84,
    0x1E00F: 84,
    0x1E010: 84,
    0x1E011: 84,
    0x1E012: 84,
    0x1E013: 84,
    0x1E014: 84,
    0x1E015: 84,
    0x1E016: 84,
    0x1E017: 84,
    0x1E018: 84,
    0x1E01B: 84,
    0x1E01C: 84,
    0x1E01D: 84,
    0x1E01E: 84,
    0x1E01F: 84,
    0x1E020: 84,
    0x1E021: 84,
    0x1E023: 84,
    0x1E024: 84,
    0x1E026: 84,
    0x1E027: 84,
    0x1E028: 84,
    0x1E029: 84,
    0x1E02A: 84,
    0x1E08F: 84,
    0x1E130: 84,
    0x1E131: 84,
    0x1E132: 84,
    0x1E133: 84,
    0x1E134: 84,
    0x1E135: 84,
    0x1E136: 84,
    0x1E2AE: 84,
    0x1E2EC: 84,
    0x1E2ED: 84,
    0x1E2EE: 84,
    0x1E2EF: 84,
    0x1E4EC: 84,
    0x1E4ED: 84,
    0x1E4EE: 84,
    0x1E4EF: 84,
    0x1E8D0: 84,
    0x1E8D1: 84,
    0x1E8D2: 84,
    0x1E8D3: 84,
    0x1E8D4: 84,
    0x1E8D5: 84,
    0x1E8D6: 84,
    0x1E900: 68,
    0x1E901: 68,
    0x1E902: 68,
    0x1E903: 68,
    0x1E904: 68,
    0x1E905: 68,
    0x1E906: 68,
    0x1E907: 68,
    0x1E908: 68,
    0x1E909: 68,
    0x1E90A: 68,
    0x1E90B: 68,
    0x1E90C: 68,
    0x1E90D: 68,
    0x1E90E: 68,
    0x1E90F: 68,
    0x1E910: 68,
    0x1E911: 68,
    0x1E912: 68,
    0x1E913: 68,
    0x1E914: 68,
    0x1E915: 68,
    0x1E916: 68,
    0x1E917: 68,
    0x1E918: 68,
    0x1E919: 68,
    0x1E91A: 68,
    0x1E91B: 68,
    0x1E91C: 68,
    0x1E91D: 68,
    0x1E91E: 68,
    0x1E91F: 68,
    0x1E920: 68,
    0x1E921: 68,
    0x1E922: 68,
    0x1E923: 68,
    0x1E924: 68,
    0x1E925: 68,
    0x1E926: 68,
    0x1E927: 68,
    0x1E928: 68,
    0x1E929: 68,
    0x1E92A: 68,
    0x1E92B: 68,
    0x1E92C: 68,
    0x1E92D: 68,
    0x1E92E: 68,
    0x1E92F: 68,
    0x1E930: 68,
    0x1E931: 68,
    0x1E932: 68,
    0x1E933: 68,
    0x1E934: 68,
    0x1E935: 68,
    0x1E936: 68,
    0x1E937: 68,
    0x1E938: 68,
    0x1E939: 68,
    0x1E93A: 68,
    0x1E93B: 68,
    0x1E93C: 68,
    0x1E93D: 68,
    0x1E93E: 68,
    0x1E93F: 68,
    0x1E940: 68,
    0x1E941: 68,
    0x1E942: 68,
    0x1E943: 68,
    0x1E944: 84,
    0x1E945: 84,
    0x1E946: 84,
    0x1E947: 84,
    0x1E948: 84,
    0x1E949: 84,
    0x1E94A: 84,
    0x1E94B: 84,
    0xE0001: 84,
    0xE0020: 84,
    0xE0021: 84,
    0xE0022: 84,
    0xE0023: 84,
    0xE0024: 84,
    0xE0025: 84,
    0xE0026: 84,
    0xE0027: 84,
    0xE0028: 84,
    0xE0029: 84,
    0xE002A: 84,
    0xE002B: 84,
    0xE002C: 84,
    0xE002D: 84,
    0xE002E: 84,
    0xE002F: 84,
    0xE0030: 84,
    0xE0031: 84,
    0xE0032: 84,
    0xE0033: 84,
    0xE0034: 84,
    0xE0035: 84,
    0xE0036: 84,
    0xE0037: 84,
    0xE0038: 84,
    0xE0039: 84,
    0xE003A: 84,
    0xE003B: 84,
    0xE003C: 84,
    0xE003D: 84,
    0xE003E: 84,
    0xE003F: 84,
    0xE0040: 84,
    0xE0041: 84,
    0xE0042: 84,
    0xE0043: 84,
    0xE0044: 84,
    0xE0045: 84,
    0xE0046: 84,
    0xE0047: 84,
    0xE0048: 84,
    0xE0049: 84,
    0xE004A: 84,
    0xE004B: 84,
    0xE004C: 84,
    0xE004D: 84,
    0xE004E: 84,
    0xE004F: 84,
    0xE0050: 84,
    0xE0051: 84,
    0xE0052: 84,
    0xE0053: 84,
    0xE0054: 84,
    0xE0055: 84,
    0xE0056: 84,
    0xE0057: 84,
    0xE0058: 84,
    0xE0059: 84,
    0xE005A: 84,
    0xE005B: 84,
    0xE005C: 84,
    0xE005D: 84,
    0xE005E: 84,
    0xE005F: 84,
    0xE0060: 84,
    0xE0061: 84,
    0xE0062: 84,
    0xE0063: 84,
    0xE0064: 84,
    0xE0065: 84,
    0xE0066: 84,
    0xE0067: 84,
    0xE0068: 84,
    0xE0069: 84,
    0xE006A: 84,
    0xE006B: 84,
    0xE006C: 84,
    0xE006D: 84,
    0xE006E: 84,
    0xE006F: 84,
    0xE0070: 84,
    0xE0071: 84,
    0xE0072: 84,
    0xE0073: 84,
    0xE0074: 84,
    0xE0075: 84,
    0xE0076: 84,
    0xE0077: 84,
    0xE0078: 84,
    0xE0079: 84,
    0xE007A: 84,
    0xE007B: 84,
    0xE007C: 84,
    0xE007D: 84,
    0xE007E: 84,
    0xE007F: 84,
    0xE0100: 84,
    0xE0101: 84,
    0xE0102: 84,
    0xE0103: 84,
    0xE0104: 84,
    0xE0105: 84,
    0xE0106: 84,
    0xE0107: 84,
    0xE0108: 84,
    0xE0109: 84,
    0xE010A: 84,
    0xE010B: 84,
    0xE010C: 84,
    0xE010D: 84,
    0xE010E: 84,
    0xE010F: 84,
    0xE0110: 84,
    0xE0111: 84,
    0xE0112: 84,
    0xE0113: 84,
    0xE0114: 84,
    0xE0115: 84,
    0xE0116: 84,
    0xE0117: 84,
    0xE0118: 84,
    0xE0119: 84,
    0xE011A: 84,
    0xE011B: 84,
    0xE011C: 84,
    0xE011D: 84,
    0xE011E: 84,
    0xE011F: 84,
    0xE0120: 84,
    0xE0121: 84,
    0xE0122: 84,
    0xE0123: 84,
    0xE0124: 84,
    0xE0125: 84,
    0xE0126: 84,
    0xE0127: 84,
    0xE0128: 84,
    0xE0129: 84,
    0xE012A: 84,
    0xE012B: 84,
    0xE012C: 84,
    0xE012D: 84,
    0xE012E: 84,
    0xE012F: 84,
    0xE0130: 84,
    0xE0131: 84,
    0xE0132: 84,
    0xE0133: 84,
    0xE0134: 84,
    0xE0135: 84,
    0xE0136: 84,
    0xE0137: 84,
    0xE0138: 84,
    0xE0139: 84,
    0xE013A: 84,
    0xE013B: 84,
    0xE013C: 84,
    0xE013D: 84,
    0xE013E: 84,
    0xE013F: 84,
    0xE0140: 84,
    0xE0141: 84,
    0xE0142: 84,
    0xE0143: 84,
    0xE0144: 84,
    0xE0145: 84,
    0xE0146: 84,
    0xE0147: 84,
    0xE0148: 84,
    0xE0149: 84,
    0xE014A: 84,
    0xE014B: 84,
    0xE014C: 84,
    0xE014D: 84,
    0xE014E: 84,
    0xE014F: 84,
    0xE0150: 84,
    0xE0151: 84,
    0xE0152: 84,
    0xE0153: 84,
    0xE0154: 84,
    0xE0155: 84,
    0xE0156: 84,
    0xE0157: 84,
    0xE0158: 84,
    0xE0159: 84,
    0xE015A: 84,
    0xE015B: 84,
    0xE015C: 84,
    0xE015D: 84,
    0xE015E: 84,
    0xE015F: 84,
    0xE0160: 84,
    0xE0161: 84,
    0xE0162: 84,
    0xE0163: 84,
    0xE0164: 84,
    0xE0165: 84,
    0xE0166: 84,
    0xE0167: 84,
    0xE0168: 84,
    0xE0169: 84,
    0xE016A: 84,
    0xE016B: 84,
    0xE016C: 84,
    0xE016D: 84,
    0xE016E: 84,
    0xE016F: 84,
    0xE0170: 84,
    0xE0171: 84,
    0xE0172: 84,
    0xE0173: 84,
    0xE0174: 84,
    0xE0175: 84,
    0xE0176: 84,
    0xE0177: 84,
    0xE0178: 84,
    0xE0179: 84,
    0xE017A: 84,
    0xE017B: 84,
    0xE017C: 84,
    0xE017D: 84,
    0xE017E: 84,
    0xE017F: 84,
    0xE0180: 84,
    0xE0181: 84,
    0xE0182: 84,
    0xE0183: 84,
    0xE0184: 84,
    0xE0185: 84,
    0xE0186: 84,
    0xE0187: 84,
    0xE0188: 84,
    0xE0189: 84,
    0xE018A: 84,
    0xE018B: 84,
    0xE018C: 84,
    0xE018D: 84,
    0xE018E: 84,
    0xE018F: 84,
    0xE0190: 84,
    0xE0191: 84,
    0xE0192: 84,
    0xE0193: 84,
    0xE0194: 84,
    0xE0195: 84,
    0xE0196: 84,
    0xE0197: 84,
    0xE0198: 84,
    0xE0199: 84,
    0xE019A: 84,
    0xE019B: 84,
    0xE019C: 84,
    0xE019D: 84,
    0xE019E: 84,
    0xE019F: 84,
    0xE01A0: 84,
    0xE01A1: 84,
    0xE01A2: 84,
    0xE01A3: 84,
    0xE01A4: 84,
    0xE01A5: 84,
    0xE01A6: 84,
    0xE01A7: 84,
    0xE01A8: 84,
    0xE01A9: 84,
    0xE01AA: 84,
    0xE01AB: 84,
    0xE01AC: 84,
    0xE01AD: 84,
    0xE01AE: 84,
    0xE01AF: 84,
    0xE01B0: 84,
    0xE01B1: 84,
    0xE01B2: 84,
    0xE01B3: 84,
    0xE01B4: 84,
    0xE01B5: 84,
    0xE01B6: 84,
    0xE01B7: 84,
    0xE01B8: 84,
    0xE01B9: 84,
    0xE01BA: 84,
    0xE01BB: 84,
    0xE01BC: 84,
    0xE01BD: 84,
    0xE01BE: 84,
    0xE01BF: 84,
    0xE01C0: 84,
    0xE01C1: 84,
    0xE01C2: 84,
    0xE01C3: 84,
    0xE01C4: 84,
    0xE01C5: 84,
    0xE01C6: 84,
    0xE01C7: 84,
    0xE01C8: 84,
    0xE01C9: 84,
    0xE01CA: 84,
    0xE01CB: 84,
    0xE01CC: 84,
    0xE01CD: 84,
    0xE01CE: 84,
    0xE01CF: 84,
    0xE01D0: 84,
    0xE01D1: 84,
    0xE01D2: 84,
    0xE01D3: 84,
    0xE01D4: 84,
    0xE01D5: 84,
    0xE01D6: 84,
    0xE01D7: 84,
    0xE01D8: 84,
    0xE01D9: 84,
    0xE01DA: 84,
    0xE01DB: 84,
    0xE01DC: 84,
    0xE01DD: 84,
    0xE01DE: 84,
    0xE01DF: 84,
    0xE01E0: 84,
    0xE01E1: 84,
    0xE01E2: 84,
    0xE01E3: 84,
    0xE01E4: 84,
    0xE01E5: 84,
    0xE01E6: 84,
    0xE01E7: 84,
    0xE01E8: 84,
    0xE01E9: 84,
    0xE01EA: 84,
    0xE01EB: 84,
    0xE01EC: 84,
    0xE01ED: 84,
    0xE01EE: 84,
    0xE01EF: 84,
}
codepoint_classes = {
    "PVALID": (
        0x2D0000002E,
        0x300000003A,
        0x610000007B,
        0xDF000000F7,
        0xF800000100,
        0x10100000102,
        0x10300000104,
        0x10500000106,
        0x10700000108,
        0x1090000010A,
        0x10B0000010C,
        0x10D0000010E,
        0x10F00000110,
        0x11100000112,
        0x11300000114,
        0x11500000116,
        0x11700000118,
        0x1190000011A,
        0x11B0000011C,
        0x11D0000011E,
        0x11F00000120,
        0x12100000122,
        0x12300000124,
        0x12500000126,
        0x12700000128,
        0x1290000012A,
        0x12B0000012C,
        0x12D0000012E,
        0x12F00000130,
        0x13100000132,
        0x13500000136,
        0x13700000139,
        0x13A0000013B,
        0x13C0000013D,
        0x13E0000013F,
        0x14200000143,
        0x14400000145,
        0x14600000147,
        0x14800000149,
        0x14B0000014C,
        0x14D0000014E,
        0x14F00000150,
        0x15100000152,
        0x15300000154,
        0x15500000156,
        0x15700000158,
        0x1590000015A,
        0x15B0000015C,
        0x15D0000015E,
        0x15F00000160,
        0x16100000162,
        0x16300000164,
        0x16500000166,
        0x16700000168,
        0x1690000016A,
        0x16B0000016C,
        0x16D0000016E,
        0x16F00000170,
        0x17100000172,
        0x17300000174,
        0x17500000176,
        0x17700000178,
        0x17A0000017B,
        0x17C0000017D,
        0x17E0000017F,
        0x18000000181,
        0x18300000184,
        0x18500000186,
        0x18800000189,
        0x18C0000018E,
        0x19200000193,
        0x19500000196,
        0x1990000019C,
        0x19E0000019F,
        0x1A1000001A2,
        0x1A3000001A4,
        0x1A5000001A6,
        0x1A8000001A9,
        0x1AA000001AC,
        0x1AD000001AE,
        0x1B0000001B1,
        0x1B4000001B5,
        0x1B6000001B7,
        0x1B9000001BC,
        0x1BD000001C4,
        0x1CE000001CF,
        0x1D0000001D1,
        0x1D2000001D3,
        0x1D4000001D5,
        0x1D6000001D7,
        0x1D8000001D9,
        0x1DA000001DB,
        0x1DC000001DE,
        0x1DF000001E0,
        0x1E1000001E2,
        0x1E3000001E4,
        0x1E5000001E6,
        0x1E7000001E8,
        0x1E9000001EA,
        0x1EB000001EC,
        0x1ED000001EE,
        0x1EF000001F1,
        0x1F5000001F6,
        0x1F9000001FA,
        0x1FB000001FC,
        0x1FD000001FE,
        0x1FF00000200,
        0x20100000202,
        0x20300000204,
        0x20500000206,
        0x20700000208,
        0x2090000020A,
        0x20B0000020C,
        0x20D0000020E,
        0x20F00000210,
        0x21100000212,
        0x21300000214,
        0x21500000216,
        0x21700000218,
        0x2190000021A,
        0x21B0000021C,
        0x21D0000021E,
        0x21F00000220,
        0x22100000222,
        0x22300000224,
        0x22500000226,
        0x22700000228,
        0x2290000022A,
        0x22B0000022C,
        0x22D0000022E,
        0x22F00000230,
        0x23100000232,
        0x2330000023A,
        0x23C0000023D,
        0x23F00000241,
        0x24200000243,
        0x24700000248,
        0x2490000024A,
        0x24B0000024C,
        0x24D0000024E,
        0x24F000002B0,
        0x2B9000002C2,
        0x2C6000002D2,
        0x2EC000002ED,
        0x2EE000002EF,
        0x30000000340,
        0x34200000343,
        0x3460000034F,
        0x35000000370,
        0x37100000372,
        0x37300000374,
        0x37700000378,
        0x37B0000037E,
        0x39000000391,
        0x3AC000003CF,
        0x3D7000003D8,
        0x3D9000003DA,
        0x3DB000003DC,
        0x3DD000003DE,
        0x3DF000003E0,
        0x3E1000003E2,
        0x3E3000003E4,
        0x3E5000003E6,
        0x3E7000003E8,
        0x3E9000003EA,
        0x3EB000003EC,
        0x3ED000003EE,
        0x3EF000003F0,
        0x3F3000003F4,
        0x3F8000003F9,
        0x3FB000003FD,
        0x43000000460,
        0x46100000462,
        0x46300000464,
        0x46500000466,
        0x46700000468,
        0x4690000046A,
        0x46B0000046C,
        0x46D0000046E,
        0x46F00000470,
        0x47100000472,
        0x47300000474,
        0x47500000476,
        0x47700000478,
        0x4790000047A,
        0x47B0000047C,
        0x47D0000047E,
        0x47F00000480,
        0x48100000482,
        0x48300000488,
        0x48B0000048C,
        0x48D0000048E,
        0x48F00000490,
        0x49100000492,
        0x49300000494,
        0x49500000496,
        0x49700000498,
        0x4990000049A,
        0x49B0000049C,
        0x49D0000049E,
        0x49F000004A0,
        0x4A1000004A2,
        0x4A3000004A4,
        0x4A5000004A6,
        0x4A7000004A8,
        0x4A9000004AA,
        0x4AB000004AC,
        0x4AD000004AE,
        0x4AF000004B0,
        0x4B1000004B2,
        0x4B3000004B4,
        0x4B5000004B6,
        0x4B7000004B8,
        0x4B9000004BA,
        0x4BB000004BC,
        0x4BD000004BE,
        0x4BF000004C0,
        0x4C2000004C3,
        0x4C4000004C5,
        0x4C6000004C7,
        0x4C8000004C9,
        0x4CA000004CB,
        0x4CC000004CD,
        0x4CE000004D0,
        0x4D1000004D2,
        0x4D3000004D4,
        0x4D5000004D6,
        0x4D7000004D8,
        0x4D9000004DA,
        0x4DB000004DC,
        0x4DD000004DE,
        0x4DF000004E0,
        0x4E1000004E2,
        0x4E3000004E4,
        0x4E5000004E6,
        0x4E7000004E8,
        0x4E9000004EA,
        0x4EB000004EC,
        0x4ED000004EE,
        0x4EF000004F0,
        0x4F1000004F2,
        0x4F3000004F4,
        0x4F5000004F6,
        0x4F7000004F8,
        0x4F9000004FA,
        0x4FB000004FC,
        0x4FD000004FE,
        0x4FF00000500,
        0x50100000502,
        0x50300000504,
        0x50500000506,
        0x50700000508,
        0x5090000050A,
        0x50B0000050C,
        0x50D0000050E,
        0x50F00000510,
        0x51100000512,
        0x51300000514,
        0x51500000516,
        0x51700000518,
        0x5190000051A,
        0x51B0000051C,
        0x51D0000051E,
        0x51F00000520,
        0x52100000522,
        0x52300000524,
        0x52500000526,
        0x52700000528,
        0x5290000052A,
        0x52B0000052C,
        0x52D0000052E,
        0x52F00000530,
        0x5590000055A,
        0x56000000587,
        0x58800000589,
        0x591000005BE,
        0x5BF000005C0,
        0x5C1000005C3,
        0x5C4000005C6,
        0x5C7000005C8,
        0x5D0000005EB,
        0x5EF000005F3,
        0x6100000061B,
        0x62000000640,
        0x64100000660,
        0x66E00000675,
        0x679000006D4,
        0x6D5000006DD,
        0x6DF000006E9,
        0x6EA000006F0,
        0x6FA00000700,
        0x7100000074B,
        0x74D000007B2,
        0x7C0000007F6,
        0x7FD000007FE,
        0x8000000082E,
        0x8400000085C,
        0x8600000086B,
        0x87000000888,
        0x8890000088F,
        0x898000008E2,
        0x8E300000958,
        0x96000000964,
        0x96600000970,
        0x97100000984,
        0x9850000098D,
        0x98F00000991,
        0x993000009A9,
        0x9AA000009B1,
        0x9B2000009B3,
        0x9B6000009BA,
        0x9BC000009C5,
        0x9C7000009C9,
        0x9CB000009CF,
        0x9D7000009D8,
        0x9E0000009E4,
        0x9E6000009F2,
        0x9FC000009FD,
        0x9FE000009FF,
        0xA0100000A04,
        0xA0500000A0B,
        0xA0F00000A11,
        0xA1300000A29,
        0xA2A00000A31,
        0xA3200000A33,
        0xA3500000A36,
        0xA3800000A3A,
        0xA3C00000A3D,
        0xA3E00000A43,
        0xA4700000A49,
        0xA4B00000A4E,
        0xA5100000A52,
        0xA5C00000A5D,
        0xA6600000A76,
        0xA8100000A84,
        0xA8500000A8E,
        0xA8F00000A92,
        0xA9300000AA9,
        0xAAA00000AB1,
        0xAB200000AB4,
        0xAB500000ABA,
        0xABC00000AC6,
        0xAC700000ACA,
        0xACB00000ACE,
        0xAD000000AD1,
        0xAE000000AE4,
        0xAE600000AF0,
        0xAF900000B00,
        0xB0100000B04,
        0xB0500000B0D,
        0xB0F00000B11,
        0xB1300000B29,
        0xB2A00000B31,
        0xB3200000B34,
        0xB3500000B3A,
        0xB3C00000B45,
        0xB4700000B49,
        0xB4B00000B4E,
        0xB5500000B58,
        0xB5F00000B64,
        0xB6600000B70,
        0xB7100000B72,
        0xB8200000B84,
        0xB8500000B8B,
        0xB8E00000B91,
        0xB9200000B96,
        0xB9900000B9B,
        0xB9C00000B9D,
        0xB9E00000BA0,
        0xBA300000BA5,
        0xBA800000BAB,
        0xBAE00000BBA,
        0xBBE00000BC3,
        0xBC600000BC9,
        0xBCA00000BCE,
        0xBD000000BD1,
        0xBD700000BD8,
        0xBE600000BF0,
        0xC0000000C0D,
        0xC0E00000C11,
        0xC1200000C29,
        0xC2A00000C3A,
        0xC3C00000C45,
        0xC4600000C49,
        0xC4A00000C4E,
        0xC5500000C57,
        0xC5800000C5B,
        0xC5D00000C5E,
        0xC6000000C64,
        0xC6600000C70,
        0xC8000000C84,
        0xC8500000C8D,
        0xC8E00000C91,
        0xC9200000CA9,
        0xCAA00000CB4,
        0xCB500000CBA,
        0xCBC00000CC5,
        0xCC600000CC9,
        0xCCA00000CCE,
        0xCD500000CD7,
        0xCDD00000CDF,
        0xCE000000CE4,
        0xCE600000CF0,
        0xCF100000CF4,
        0xD0000000D0D,
        0xD0E00000D11,
        0xD1200000D45,
        0xD4600000D49,
        0xD4A00000D4F,
        0xD5400000D58,
        0xD5F00000D64,
        0xD6600000D70,
        0xD7A00000D80,
        0xD8100000D84,
        0xD8500000D97,
        0xD9A00000DB2,
        0xDB300000DBC,
        0xDBD00000DBE,
        0xDC000000DC7,
        0xDCA00000DCB,
        0xDCF00000DD5,
        0xDD600000DD7,
        0xDD800000DE0,
        0xDE600000DF0,
        0xDF200000DF4,
        0xE0100000E33,
        0xE3400000E3B,
        0xE4000000E4F,
        0xE5000000E5A,
        0xE8100000E83,
        0xE8400000E85,
        0xE8600000E8B,
        0xE8C00000EA4,
        0xEA500000EA6,
        0xEA700000EB3,
        0xEB400000EBE,
        0xEC000000EC5,
        0xEC600000EC7,
        0xEC800000ECF,
        0xED000000EDA,
        0xEDE00000EE0,
        0xF0000000F01,
        0xF0B00000F0C,
        0xF1800000F1A,
        0xF2000000F2A,
        0xF3500000F36,
        0xF3700000F38,
        0xF3900000F3A,
        0xF3E00000F43,
        0xF4400000F48,
        0xF4900000F4D,
        0xF4E00000F52,
        0xF5300000F57,
        0xF5800000F5C,
        0xF5D00000F69,
        0xF6A00000F6D,
        0xF7100000F73,
        0xF7400000F75,
        0xF7A00000F81,
        0xF8200000F85,
        0xF8600000F93,
        0xF9400000F98,
        0xF9900000F9D,
        0xF9E00000FA2,
        0xFA300000FA7,
        0xFA800000FAC,
        0xFAD00000FB9,
        0xFBA00000FBD,
        0xFC600000FC7,
        0x10000000104A,
        0x10500000109E,
        0x10D0000010FB,
        0x10FD00001100,
        0x120000001249,
        0x124A0000124E,
        0x125000001257,
        0x125800001259,
        0x125A0000125E,
        0x126000001289,
        0x128A0000128E,
        0x1290000012B1,
        0x12B2000012B6,
        0x12B8000012BF,
        0x12C0000012C1,
        0x12C2000012C6,
        0x12C8000012D7,
        0x12D800001311,
        0x131200001316,
        0x13180000135B,
        0x135D00001360,
        0x138000001390,
        0x13A0000013F6,
        0x14010000166D,
        0x166F00001680,
        0x16810000169B,
        0x16A0000016EB,
        0x16F1000016F9,
        0x170000001716,
        0x171F00001735,
        0x174000001754,
        0x17600000176D,
        0x176E00001771,
        0x177200001774,
        0x1780000017B4,
        0x17B6000017D4,
        0x17D7000017D8,
        0x17DC000017DE,
        0x17E0000017EA,
        0x18100000181A,
        0x182000001879,
        0x1880000018AB,
        0x18B0000018F6,
        0x19000000191F,
        0x19200000192C,
        0x19300000193C,
        0x19460000196E,
        0x197000001975,
        0x1980000019AC,
        0x19B0000019CA,
        0x19D0000019DA,
        0x1A0000001A1C,
        0x1A2000001A5F,
        0x1A6000001A7D,
        0x1A7F00001A8A,
        0x1A9000001A9A,
        0x1AA700001AA8,
        0x1AB000001ABE,
        0x1ABF00001ACF,
        0x1B0000001B4D,
        0x1B5000001B5A,
        0x1B6B00001B74,
        0x1B8000001BF4,
        0x1C0000001C38,
        0x1C4000001C4A,
        0x1C4D00001C7E,
        0x1CD000001CD3,
        0x1CD400001CFB,
        0x1D0000001D2C,
        0x1D2F00001D30,
        0x1D3B00001D3C,
        0x1D4E00001D4F,
        0x1D6B00001D78,
        0x1D7900001D9B,
        0x1DC000001E00,
        0x1E0100001E02,
        0x1E0300001E04,
        0x1E0500001E06,
        0x1E0700001E08,
        0x1E0900001E0A,
        0x1E0B00001E0C,
        0x1E0D00001E0E,
        0x1E0F00001E10,
        0x1E1100001E12,
        0x1E1300001E14,
        0x1E1500001E16,
        0x1E1700001E18,
        0x1E1900001E1A,
        0x1E1B00001E1C,
        0x1E1D00001E1E,
        0x1E1F00001E20,
        0x1E2100001E22,
        0x1E2300001E24,
        0x1E2500001E26,
        0x1E2700001E28,
        0x1E2900001E2A,
        0x1E2B00001E2C,
        0x1E2D00001E2E,
        0x1E2F00001E30,
        0x1E3100001E32,
        0x1E3300001E34,
        0x1E3500001E36,
        0x1E3700001E38,
        0x1E3900001E3A,
        0x1E3B00001E3C,
        0x1E3D00001E3E,
        0x1E3F00001E40,
        0x1E4100001E42,
        0x1E4300001E44,
        0x1E4500001E46,
        0x1E4700001E48,
        0x1E4900001E4A,
        0x1E4B00001E4C,
        0x1E4D00001E4E,
        0x1E4F00001E50,
        0x1E5100001E52,
        0x1E5300001E54,
        0x1E5500001E56,
        0x1E5700001E58,
        0x1E5900001E5A,
        0x1E5B00001E5C,
        0x1E5D00001E5E,
        0x1E5F00001E60,
        0x1E6100001E62,
        0x1E6300001E64,
        0x1E6500001E66,
        0x1E6700001E68,
        0x1E6900001E6A,
        0x1E6B00001E6C,
        0x1E6D00001E6E,
        0x1E6F00001E70,
        0x1E7100001E72,
        0x1E7300001E74,
        0x1E7500001E76,
        0x1E7700001E78,
        0x1E7900001E7A,
        0x1E7B00001E7C,
        0x1E7D00001E7E,
        0x1E7F00001E80,
        0x1E8100001E82,
        0x1E8300001E84,
        0x1E8500001E86,
        0x1E8700001E88,
        0x1E8900001E8A,
        0x1E8B00001E8C,
        0x1E8D00001E8E,
        0x1E8F00001E90,
        0x1E9100001E92,
        0x1E9300001E94,
        0x1E9500001E9A,
        0x1E9C00001E9E,
        0x1E9F00001EA0,
        0x1EA100001EA2,
        0x1EA300001EA4,
        0x1EA500001EA6,
        0x1EA700001EA8,
        0x1EA900001EAA,
        0x1EAB00001EAC,
        0x1EAD00001EAE,
        0x1EAF00001EB0,
        0x1EB100001EB2,
        0x1EB300001EB4,
        0x1EB500001EB6,
        0x1EB700001EB8,
        0x1EB900001EBA,
        0x1EBB00001EBC,
        0x1EBD00001EBE,
        0x1EBF00001EC0,
        0x1EC100001EC2,
        0x1EC300001EC4,
        0x1EC500001EC6,
        0x1EC700001EC8,
        0x1EC900001ECA,
        0x1ECB00001ECC,
        0x1ECD00001ECE,
        0x1ECF00001ED0,
        0x1ED100001ED2,
        0x1ED300001ED4,
        0x1ED500001ED6,
        0x1ED700001ED8,
        0x1ED900001EDA,
        0x1EDB00001EDC,
        0x1EDD00001EDE,
        0x1EDF00001EE0,
        0x1EE100001EE2,
        0x1EE300001EE4,
        0x1EE500001EE6,
        0x1EE700001EE8,
        0x1EE900001EEA,
        0x1EEB00001EEC,
        0x1EED00001EEE,
        0x1EEF00001EF0,
        0x1EF100001EF2,
        0x1EF300001EF4,
        0x1EF500001EF6,
        0x1EF700001EF8,
        0x1EF900001EFA,
        0x1EFB00001EFC,
        0x1EFD00001EFE,
        0x1EFF00001F08,
        0x1F1000001F16,
        0x1F2000001F28,
        0x1F3000001F38,
        0x1F4000001F46,
        0x1F5000001F58,
        0x1F6000001F68,
        0x1F7000001F71,
        0x1F7200001F73,
        0x1F7400001F75,
        0x1F7600001F77,
        0x1F7800001F79,
        0x1F7A00001F7B,
        0x1F7C00001F7D,
        0x1FB000001FB2,
        0x1FB600001FB7,
        0x1FC600001FC7,
        0x1FD000001FD3,
        0x1FD600001FD8,
        0x1FE000001FE3,
        0x1FE400001FE8,
        0x1FF600001FF7,
        0x214E0000214F,
        0x218400002185,
        0x2C3000002C60,
        0x2C6100002C62,
        0x2C6500002C67,
        0x2C6800002C69,
        0x2C6A00002C6B,
        0x2C6C00002C6D,
        0x2C7100002C72,
        0x2C7300002C75,
        0x2C7600002C7C,
        0x2C8100002C82,
        0x2C8300002C84,
        0x2C8500002C86,
        0x2C8700002C88,
        0x2C8900002C8A,
        0x2C8B00002C8C,
        0x2C8D00002C8E,
        0x2C8F00002C90,
        0x2C9100002C92,
        0x2C9300002C94,
        0x2C9500002C96,
        0x2C9700002C98,
        0x2C9900002C9A,
        0x2C9B00002C9C,
        0x2C9D00002C9E,
        0x2C9F00002CA0,
        0x2CA100002CA2,
        0x2CA300002CA4,
        0x2CA500002CA6,
        0x2CA700002CA8,
        0x2CA900002CAA,
        0x2CAB00002CAC,
        0x2CAD00002CAE,
        0x2CAF00002CB0,
        0x2CB100002CB2,
        0x2CB300002CB4,
        0x2CB500002CB6,
        0x2CB700002CB8,
        0x2CB900002CBA,
        0x2CBB00002CBC,
        0x2CBD00002CBE,
        0x2CBF00002CC0,
        0x2CC100002CC2,
        0x2CC300002CC4,
        0x2CC500002CC6,
        0x2CC700002CC8,
        0x2CC900002CCA,
        0x2CCB00002CCC,
        0x2CCD00002CCE,
        0x2CCF00002CD0,
        0x2CD100002CD2,
        0x2CD300002CD4,
        0x2CD500002CD6,
        0x2CD700002CD8,
        0x2CD900002CDA,
        0x2CDB00002CDC,
        0x2CDD00002CDE,
        0x2CDF00002CE0,
        0x2CE100002CE2,
        0x2CE300002CE5,
        0x2CEC00002CED,
        0x2CEE00002CF2,
        0x2CF300002CF4,
        0x2D0000002D26,
        0x2D2700002D28,
        0x2D2D00002D2E,
        0x2D3000002D68,
        0x2D7F00002D97,
        0x2DA000002DA7,
        0x2DA800002DAF,
        0x2DB000002DB7,
        0x2DB800002DBF,
        0x2DC000002DC7,
        0x2DC800002DCF,
        0x2DD000002DD7,
        0x2DD800002DDF,
        0x2DE000002E00,
        0x2E2F00002E30,
        0x300500003008,
        0x302A0000302E,
        0x303C0000303D,
        0x304100003097,
        0x30990000309B,
        0x309D0000309F,
        0x30A1000030FB,
        0x30FC000030FF,
        0x310500003130,
        0x31A0000031C0,
        0x31F000003200,
        0x340000004DC0,
        0x4E000000A48D,
        0xA4D00000A4FE,
        0xA5000000A60D,
        0xA6100000A62C,
        0xA6410000A642,
        0xA6430000A644,
        0xA6450000A646,
        0xA6470000A648,
        0xA6490000A64A,
        0xA64B0000A64C,
        0xA64D0000A64E,
        0xA64F0000A650,
        0xA6510000A652,
        0xA6530000A654,
        0xA6550000A656,
        0xA6570000A658,
        0xA6590000A65A,
        0xA65B0000A65C,
        0xA65D0000A65E,
        0xA65F0000A660,
        0xA6610000A662,
        0xA6630000A664,
        0xA6650000A666,
        0xA6670000A668,
        0xA6690000A66A,
        0xA66B0000A66C,
        0xA66D0000A670,
        0xA6740000A67E,
        0xA67F0000A680,
        0xA6810000A682,
        0xA6830000A684,
        0xA6850000A686,
        0xA6870000A688,
        0xA6890000A68A,
        0xA68B0000A68C,
        0xA68D0000A68E,
        0xA68F0000A690,
        0xA6910000A692,
        0xA6930000A694,
        0xA6950000A696,
        0xA6970000A698,
        0xA6990000A69A,
        0xA69B0000A69C,
        0xA69E0000A6E6,
        0xA6F00000A6F2,
        0xA7170000A720,
        0xA7230000A724,
        0xA7250000A726,
        0xA7270000A728,
        0xA7290000A72A,
        0xA72B0000A72C,
        0xA72D0000A72E,
        0xA72F0000A732,
        0xA7330000A734,
        0xA7350000A736,
        0xA7370000A738,
        0xA7390000A73A,
        0xA73B0000A73C,
        0xA73D0000A73E,
        0xA73F0000A740,
        0xA7410000A742,
        0xA7430000A744,
        0xA7450000A746,
        0xA7470000A748,
        0xA7490000A74A,
        0xA74B0000A74C,
        0xA74D0000A74E,
        0xA74F0000A750,
        0xA7510000A752,
        0xA7530000A754,
        0xA7550000A756,
        0xA7570000A758,
        0xA7590000A75A,
        0xA75B0000A75C,
        0xA75D0000A75E,
        0xA75F0000A760,
        0xA7610000A762,
        0xA7630000A764,
        0xA7650000A766,
        0xA7670000A768,
        0xA7690000A76A,
        0xA76B0000A76C,
        0xA76D0000A76E,
        0xA76F0000A770,
        0xA7710000A779,
        0xA77A0000A77B,
        0xA77C0000A77D,
        0xA77F0000A780,
        0xA7810000A782,
        0xA7830000A784,
        0xA7850000A786,
        0xA7870000A789,
        0xA78C0000A78D,
        0xA78E0000A790,
        0xA7910000A792,
        0xA7930000A796,
        0xA7970000A798,
        0xA7990000A79A,
        0xA79B0000A79C,
        0xA79D0000A79E,
        0xA79F0000A7A0,
        0xA7A10000A7A2,
        0xA7A30000A7A4,
        0xA7A50000A7A6,
        0xA7A70000A7A8,
        0xA7A90000A7AA,
        0xA7AF0000A7B0,
        0xA7B50000A7B6,
        0xA7B70000A7B8,
        0xA7B90000A7BA,
        0xA7BB0000A7BC,
        0xA7BD0000A7BE,
        0xA7BF0000A7C0,
        0xA7C10000A7C2,
        0xA7C30000A7C4,
        0xA7C80000A7C9,
        0xA7CA0000A7CB,
        0xA7D10000A7D2,
        0xA7D30000A7D4,
        0xA7D50000A7D6,
        0xA7D70000A7D8,
        0xA7D90000A7DA,
        0xA7F60000A7F8,
        0xA7FA0000A828,
        0xA82C0000A82D,
        0xA8400000A874,
        0xA8800000A8C6,
        0xA8D00000A8DA,
        0xA8E00000A8F8,
        0xA8FB0000A8FC,
        0xA8FD0000A92E,
        0xA9300000A954,
        0xA9800000A9C1,
        0xA9CF0000A9DA,
        0xA9E00000A9FF,
        0xAA000000AA37,
        0xAA400000AA4E,
        0xAA500000AA5A,
        0xAA600000AA77,
        0xAA7A0000AAC3,
        0xAADB0000AADE,
        0xAAE00000AAF0,
        0xAAF20000AAF7,
        0xAB010000AB07,
        0xAB090000AB0F,
        0xAB110000AB17,
        0xAB200000AB27,
        0xAB280000AB2F,
        0xAB300000AB5B,
        0xAB600000AB69,
        0xABC00000ABEB,
        0xABEC0000ABEE,
        0xABF00000ABFA,
        0xAC000000D7A4,
        0xFA0E0000FA10,
        0xFA110000FA12,
        0xFA130000FA15,
        0xFA1F0000FA20,
        0xFA210000FA22,
        0xFA230000FA25,
        0xFA270000FA2A,
        0xFB1E0000FB1F,
        0xFE200000FE30,
        0xFE730000FE74,
        0x100000001000C,
        0x1000D00010027,
        0x100280001003B,
        0x1003C0001003E,
        0x1003F0001004E,
        0x100500001005E,
        0x10080000100FB,
        0x101FD000101FE,
        0x102800001029D,
        0x102A0000102D1,
        0x102E0000102E1,
        0x1030000010320,
        0x1032D00010341,
        0x103420001034A,
        0x103500001037B,
        0x103800001039E,
        0x103A0000103C4,
        0x103C8000103D0,
        0x104280001049E,
        0x104A0000104AA,
        0x104D8000104FC,
        0x1050000010528,
        0x1053000010564,
        0x10597000105A2,
        0x105A3000105B2,
        0x105B3000105BA,
        0x105BB000105BD,
        0x1060000010737,
        0x1074000010756,
        0x1076000010768,
        0x1078000010781,
        0x1080000010806,
        0x1080800010809,
        0x1080A00010836,
        0x1083700010839,
        0x1083C0001083D,
        0x1083F00010856,
        0x1086000010877,
        0x108800001089F,
        0x108E0000108F3,
        0x108F4000108F6,
        0x1090000010916,
        0x109200001093A,
        0x10980000109B8,
        0x109BE000109C0,
        0x10A0000010A04,
        0x10A0500010A07,
        0x10A0C00010A14,
        0x10A1500010A18,
        0x10A1900010A36,
        0x10A3800010A3B,
        0x10A3F00010A40,
        0x10A6000010A7D,
        0x10A8000010A9D,
        0x10AC000010AC8,
        0x10AC900010AE7,
        0x10B0000010B36,
        0x10B4000010B56,
        0x10B6000010B73,
        0x10B8000010B92,
        0x10C0000010C49,
        0x10CC000010CF3,
        0x10D0000010D28,
        0x10D3000010D3A,
        0x10E8000010EAA,
        0x10EAB00010EAD,
        0x10EB000010EB2,
        0x10EFD00010F1D,
        0x10F2700010F28,
        0x10F3000010F51,
        0x10F7000010F86,
        0x10FB000010FC5,
        0x10FE000010FF7,
        0x1100000011047,
        0x1106600011076,
        0x1107F000110BB,
        0x110C2000110C3,
        0x110D0000110E9,
        0x110F0000110FA,
        0x1110000011135,
        0x1113600011140,
        0x1114400011148,
        0x1115000011174,
        0x1117600011177,
        0x11180000111C5,
        0x111C9000111CD,
        0x111CE000111DB,
        0x111DC000111DD,
        0x1120000011212,
        0x1121300011238,
        0x1123E00011242,
        0x1128000011287,
        0x1128800011289,
        0x1128A0001128E,
        0x1128F0001129E,
        0x1129F000112A9,
        0x112B0000112EB,
        0x112F0000112FA,
        0x1130000011304,
        0x113050001130D,
        0x1130F00011311,
        0x1131300011329,
        0x1132A00011331,
        0x1133200011334,
        0x113350001133A,
        0x1133B00011345,
        0x1134700011349,
        0x1134B0001134E,
        0x1135000011351,
        0x1135700011358,
        0x1135D00011364,
        0x113660001136D,
        0x1137000011375,
        0x114000001144B,
        0x114500001145A,
        0x1145E00011462,
        0x11480000114C6,
        0x114C7000114C8,
        0x114D0000114DA,
        0x11580000115B6,
        0x115B8000115C1,
        0x115D8000115DE,
        0x1160000011641,
        0x1164400011645,
        0x116500001165A,
        0x11680000116B9,
        0x116C0000116CA,
        0x117000001171B,
        0x1171D0001172C,
        0x117300001173A,
        0x1174000011747,
        0x118000001183B,
        0x118C0000118EA,
        0x118FF00011907,
        0x119090001190A,
        0x1190C00011914,
        0x1191500011917,
        0x1191800011936,
        0x1193700011939,
        0x1193B00011944,
        0x119500001195A,
        0x119A0000119A8,
        0x119AA000119D8,
        0x119DA000119E2,
        0x119E3000119E5,
        0x11A0000011A3F,
        0x11A4700011A48,
        0x11A5000011A9A,
        0x11A9D00011A9E,
        0x11AB000011AF9,
        0x11C0000011C09,
        0x11C0A00011C37,
        0x11C3800011C41,
        0x11C5000011C5A,
        0x11C7200011C90,
        0x11C9200011CA8,
        0x11CA900011CB7,
        0x11D0000011D07,
        0x11D0800011D0A,
        0x11D0B00011D37,
        0x11D3A00011D3B,
        0x11D3C00011D3E,
        0x11D3F00011D48,
        0x11D5000011D5A,
        0x11D6000011D66,
        0x11D6700011D69,
        0x11D6A00011D8F,
        0x11D9000011D92,
        0x11D9300011D99,
        0x11DA000011DAA,
        0x11EE000011EF7,
        0x11F0000011F11,
        0x11F1200011F3B,
        0x11F3E00011F43,
        0x11F5000011F5A,
        0x11FB000011FB1,
        0x120000001239A,
        0x1248000012544,
        0x12F9000012FF1,
        0x1300000013430,
        0x1344000013456,
        0x1440000014647,
        0x1680000016A39,
        0x16A4000016A5F,
        0x16A6000016A6A,
        0x16A7000016ABF,
        0x16AC000016ACA,
        0x16AD000016AEE,
        0x16AF000016AF5,
        0x16B0000016B37,
        0x16B4000016B44,
        0x16B5000016B5A,
        0x16B6300016B78,
        0x16B7D00016B90,
        0x16E6000016E80,
        0x16F0000016F4B,
        0x16F4F00016F88,
        0x16F8F00016FA0,
        0x16FE000016FE2,
        0x16FE300016FE5,
        0x16FF000016FF2,
        0x17000000187F8,
        0x1880000018CD6,
        0x18D0000018D09,
        0x1AFF00001AFF4,
        0x1AFF50001AFFC,
        0x1AFFD0001AFFF,
        0x1B0000001B123,
        0x1B1320001B133,
        0x1B1500001B153,
        0x1B1550001B156,
        0x1B1640001B168,
        0x1B1700001B2FC,
        0x1BC000001BC6B,
        0x1BC700001BC7D,
        0x1BC800001BC89,
        0x1BC900001BC9A,
        0x1BC9D0001BC9F,
        0x1CF000001CF2E,
        0x1CF300001CF47,
        0x1DA000001DA37,
        0x1DA3B0001DA6D,
        0x1DA750001DA76,
        0x1DA840001DA85,
        0x1DA9B0001DAA0,
        0x1DAA10001DAB0,
        0x1DF000001DF1F,
        0x1DF250001DF2B,
        0x1E0000001E007,
        0x1E0080001E019,
        0x1E01B0001E022,
        0x1E0230001E025,
        0x1E0260001E02B,
        0x1E08F0001E090,
        0x1E1000001E12D,
        0x1E1300001E13E,
        0x1E1400001E14A,
        0x1E14E0001E14F,
        0x1E2900001E2AF,
        0x1E2C00001E2FA,
        0x1E4D00001E4FA,
        0x1E7E00001E7E7,
        0x1E7E80001E7EC,
        0x1E7ED0001E7EF,
        0x1E7F00001E7FF,
        0x1E8000001E8C5,
        0x1E8D00001E8D7,
        0x1E9220001E94C,
        0x1E9500001E95A,
        0x200000002A6E0,
        0x2A7000002B73A,
        0x2B7400002B81E,
        0x2B8200002CEA2,
        0x2CEB00002EBE1,
        0x2EBF00002EE5E,
        0x300000003134B,
        0x31350000323B0,
    ),
    "CONTEXTJ": (0x200C0000200E,),
    "CONTEXTO": (
        0xB7000000B8,
        0x37500000376,
        0x5F3000005F5,
        0x6600000066A,
        0x6F0000006FA,
        0x30FB000030FC,
    ),
}
