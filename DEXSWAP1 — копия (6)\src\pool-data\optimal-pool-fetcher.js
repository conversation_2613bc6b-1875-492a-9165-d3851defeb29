/**
 * 🚀 ОПТИМАЛЬНЫЙ ПОЛУЧАТЕЛЬ ДАННЫХ ПУЛОВ
 * Комбинирует RPC + кеширование + fallback для максимальной производительности
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const axios = require('axios');

class OptimalPoolFetcher {
  constructor(connections = {}) {
    // ✅ МНОЖЕСТВЕННЫЕ RPC ПОДКЛЮЧЕНИЯ
    this.connections = {
      primary: connections.primary || new Connection('https://api.mainnet-beta.solana.com'),
      secondary: connections.secondary || new Connection('https://solana-api.projectserum.com'),
      backup: connections.backup || new Connection('https://rpc.ankr.com/solana')
    };

    // ✅ UNIFIED CACHE MANAGER
    this.cacheManager = require('../utils/unified-cache-manager');

    // 📊 СТАТИСТИКА
    this.stats = {
      rpcHits: 0,
      cacheHits: 0,
      apiHits: 0,
      errors: 0,
      avgResponseTime: 0
    };

    console.log('🚀 OptimalPoolFetcher инициализирован с множественными RPC');
  }

  /**
   * 🎯 ОСНОВНОЙ МЕТОД - ПОЛУЧЕНИЕ ДАННЫХ ПУЛА
   */
  async getPoolData(poolAddress, options = {}) {
    const startTime = Date.now();
    const {
      useCache = true,
      cacheTTL = 500, // 500ms для арбитража
      fallbackToAPI = true
    } = options;

    try {
      // 1. 💾 ПРОВЕРЯЕМ КЭШ
      if (useCache) {
        const cached = this.cacheManager.getPoolData(poolAddress);
        if (cached) {
          this.stats.cacheHits++;
          console.log(`💾 Pool data из кеша: ${poolAddress.slice(0,8)}...`);
          return cached;
        }
      }

      // 2. 🚀 RPC ЗАПРОС (ПАРАЛЛЕЛЬНО)
      try {
        const poolData = await this.getPoolDataRPC(poolAddress);

        // Кешируем результат
        if (useCache) {
          this.cacheManager.setPoolData(poolAddress, poolData, cacheTTL);
        }

        this.stats.rpcHits++;
        this.updateResponseTime(startTime);
        return poolData;

      } catch (rpcError) {
        console.log(`⚠️ RPC ошибка: ${rpcError.message}`);

        // 🚫 FALLBACK НА API ЗАПРЕЩЕН!
        console.log('🚫 FALLBACK НА API ЗАПРЕЩЕН!');
        console.log('💡 Причина: Система должна работать только через RPC');

        throw new Error(`RPC failed: ${rpcError.message}. API fallback запрещен!`);
      }

    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Ошибка получения данных пула ${poolAddress}: ${error.message}`);
      throw error;
    }
  }

  /**
   * ⚡ RPC ЗАПРОС С ПАРАЛЛЕЛЬНЫМИ ПОДКЛЮЧЕНИЯМИ
   */
  async getPoolDataRPC(poolAddress) {
    const publicKey = new PublicKey(poolAddress);

    // Создаем промисы для всех подключений
    const promises = Object.entries(this.connections).map(([name, connection]) =>
      this.fetchFromRPC(connection, publicKey, name)
    );

    // Используем Promise.race для получения первого успешного ответа
    try {
      const result = await Promise.race(promises);
      console.log(`🚀 RPC данные получены от ${result.source}: ${poolAddress.slice(0,8)}...`);
      return result.data;
    } catch (error) {
      // Если все RPC провалились, пробуем Promise.allSettled
      const results = await Promise.allSettled(promises);
      const successful = results.find(r => r.status === 'fulfilled');

      if (successful) {
        console.log(`🚀 RPC данные получены (backup): ${poolAddress.slice(0,8)}...`);
        return successful.value.data;
      }

      throw new Error('Все RPC подключения провалились');
    }
  }

  /**
   * 🔗 ЗАПРОС К ОДНОМУ RPC
   */
  async fetchFromRPC(connection, publicKey, sourceName) {
    try {
      const accountInfo = await connection.getAccountInfo(publicKey, 'confirmed');

      if (!accountInfo || !accountInfo.data) {
        throw new Error('Pool account не найден');
      }

      // Парсим данные пула (упрощенная версия)
      const poolData = this.parsePoolData(accountInfo.data);

      return {
        data: poolData,
        source: sourceName
      };

    } catch (error) {
      throw new Error(`${sourceName}: ${error.message}`);
    }
  }

  /**
   * 🌐 API FALLBACK
   */
  async getPoolDataAPI(poolAddress) {
    const apis = [
      {
        name: 'Raydium',
        url: 'https://api.raydium.io/v2/main/pairs',
        parser: (data) => data.find(pool => pool.ammId === poolAddress)
      },
      {
        name: 'Jupiter',
        url: `https://quote-api.jup.ag/v6/quote?inputMint=${poolAddress}&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=1000000`,
        parser: (data) => data
      }
    ];

    for (const api of apis) {
      try {
        console.log(`🌐 Пробуем API: ${api.name}`);

        const response = await axios.get(api.url, {
          timeout: 3000,
          headers: {
            'User-Agent': 'OptimalPoolFetcher/1.0'
          }
        });

        if (response.data) {
          const poolData = api.parser(response.data);
          if (poolData) {
            console.log(`✅ Данные получены от ${api.name}`);
            return this.normalizeAPIData(poolData);
          }
        }

      } catch (error) {
        console.log(`⚠️ ${api.name} API ошибка: ${error.message}`);
        continue;
      }
    }

    throw new Error('Все API недоступны');
  }

  /**
   * 🔧 ПАРСИНГ ДАННЫХ ПУЛА (УПРОЩЕННАЯ ВЕРСИЯ)
   */
  parsePoolData(buffer) {
    // Это упрощенная версия - в реальности нужен специфичный парсер для каждого DEX
    try {
      // Для демонстрации возвращаем базовую структуру
      return {
        poolAddress: 'parsed_from_buffer',
        tokenA: {
          mint: 'token_a_mint',
          amount: 'token_a_amount'
        },
        tokenB: {
          mint: 'token_b_mint',
          amount: 'token_b_amount'
        },
        fee: 'pool_fee',
        timestamp: Date.now(),
        source: 'RPC'
      };
    } catch (error) {
      throw new Error(`Ошибка парсинга данных пула: ${error.message}`);
    }
  }

  /**
   * 🔄 НОРМАЛИЗАЦИЯ API ДАННЫХ
   */
  normalizeAPIData(apiData) {
    return {
      poolAddress: apiData.ammId || apiData.id,
      tokenA: {
        mint: apiData.baseMint,
        amount: apiData.baseReserve
      },
      tokenB: {
        mint: apiData.quoteMint,
        amount: apiData.quoteReserve
      },
      fee: apiData.fee,
      timestamp: Date.now(),
      source: 'API'
    };
  }

  /**
   * ⚡ МАССОВОЕ ПОЛУЧЕНИЕ ДАННЫХ ПУЛОВ
   */
  async getMultiplePoolsData(poolAddresses, options = {}) {
    console.log(`🚀 Получение данных для ${poolAddresses.length} пулов...`);

    const promises = poolAddresses.map(address =>
      this.getPoolData(address, options).catch(error => ({
        error: error.message,
        address
      }))
    );

    const results = await Promise.allSettled(promises);

    const successful = results
      .filter(r => r.status === 'fulfilled' && !r.value.error)
      .map(r => r.value);

    const failed = results
      .filter(r => r.status === 'rejected' || r.value?.error)
      .map(r => r.reason || r.value);

    console.log(`✅ Успешно: ${successful.length}, ❌ Ошибки: ${failed.length}`);

    return {
      successful,
      failed,
      total: poolAddresses.length
    };
  }

  /**
   * 📊 ОБНОВЛЕНИЕ СТАТИСТИКИ
   */
  updateResponseTime(startTime) {
    const responseTime = Date.now() - startTime;
    this.stats.avgResponseTime = (this.stats.avgResponseTime + responseTime) / 2;
  }

  /**
   * 📈 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    const total = this.stats.rpcHits + this.stats.cacheHits + this.stats.apiHits;

    return {
      ...this.stats,
      total,
      cacheHitRate: total > 0 ? `${((this.stats.cacheHits / total) * 100).toFixed(1)}%` : '0%',
      rpcSuccessRate: total > 0 ? `${((this.stats.rpcHits / total) * 100).toFixed(1)}%` : '0%'
    };
  }

  /**
   * 🧹 ОЧИСТКА РЕСУРСОВ
   */
  cleanup() {
    // Очищаем интервалы если есть
    console.log('🧹 OptimalPoolFetcher очищен');
  }
}

module.exports = OptimalPoolFetcher;
