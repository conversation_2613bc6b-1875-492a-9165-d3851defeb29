#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА РЕАЛЬНОГО БАЛАНСА КОШЕЛЬКА
 * Проверяем откуда взялись 45.18 SOL в ошибке транзакции
 */

console.log('\n🔍 ПРОВЕРКА РЕАЛЬНОГО БАЛАНСА КОШЕЛЬКА');
console.log('================================================================================');

async function checkRealWalletBalance() {
  try {
    require('dotenv').config({ path: '.env.solana' });
    const { Connection, PublicKey, LAMPORTS_PER_SOL } = require('@solana/web3.js');
    
    // 🔗 ПОДКЛЮЧЕНИЕ К QUICKNODE
    const rpcUrl = process.env.QUICKNODE2_RPC_URL || process.env.QUICKNODE_RPC_URL;
    const connection = new Connection(rpcUrl, 'confirmed');
    console.log(`🔗 RPC: ${rpcUrl.substring(0, 50)}...`);
    
    // 🔑 КОШЕЛЕК ИЗ ТРАНЗАКЦИИ
    const walletAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    const walletPubkey = new PublicKey(walletAddress);
    console.log(`🔑 Кошелек: ${walletAddress}`);
    
    // 💰 ПРОВЕРЯЕМ РЕАЛЬНЫЙ БАЛАНС
    console.log('\n💰 ПРОВЕРКА РЕАЛЬНОГО БАЛАНСА...');
    const balance = await connection.getBalance(walletPubkey);
    const balanceSOL = balance / LAMPORTS_PER_SOL;
    
    console.log(`💰 Реальный баланс: ${balance} lamports`);
    console.log(`💰 Реальный баланс: ${balanceSOL.toFixed(9)} SOL`);
    
    // 🔍 СРАВНЕНИЕ С ОШИБКОЙ
    console.log('\n🔍 СРАВНЕНИЕ С ОШИБКОЙ ТРАНЗАКЦИИ...');
    const errorBalance = 45184927; // из ошибки "insufficient lamports 45184927"
    const errorBalanceSOL = errorBalance / LAMPORTS_PER_SOL;
    
    console.log(`❌ Баланс в ошибке: ${errorBalance} lamports`);
    console.log(`❌ Баланс в ошибке: ${errorBalanceSOL.toFixed(9)} SOL`);
    
    const difference = balance - errorBalance;
    const differenceSOL = difference / LAMPORTS_PER_SOL;
    
    console.log(`🔄 Разница: ${difference} lamports`);
    console.log(`🔄 Разница: ${differenceSOL.toFixed(9)} SOL`);
    
    if (Math.abs(difference) < 1000000) { // Менее 0.001 SOL разницы
      console.log('✅ БАЛАНСЫ СОВПАДАЮТ (с учетом комиссий)');
    } else {
      console.log('❌ БАЛАНСЫ НЕ СОВПАДАЮТ!');
    }
    
    // 🧪 ПРОВЕРЯЕМ WSOL АККАУНТ
    console.log('\n🧪 ПРОВЕРКА WSOL АККАУНТА...');
    const wsolMint = new PublicKey('So11111111111111111111111111111111111111112');
    
    // Получаем все token аккаунты кошелька
    const tokenAccounts = await connection.getTokenAccountsByOwner(walletPubkey, {
      mint: wsolMint
    });
    
    console.log(`🔍 WSOL аккаунтов найдено: ${tokenAccounts.value.length}`);
    
    let totalWSOL = 0;
    for (let i = 0; i < tokenAccounts.value.length; i++) {
      const account = tokenAccounts.value[i];
      const accountInfo = await connection.getTokenAccountBalance(account.pubkey);
      const wsolBalance = parseFloat(accountInfo.value.amount) / LAMPORTS_PER_SOL;
      
      console.log(`   WSOL аккаунт ${i + 1}: ${account.pubkey.toString()}`);
      console.log(`   Баланс: ${accountInfo.value.amount} lamports (${wsolBalance.toFixed(9)} WSOL)`);
      
      totalWSOL += wsolBalance;
    }
    
    console.log(`💰 Общий WSOL баланс: ${totalWSOL.toFixed(9)} WSOL`);
    
    // 🔍 ОБЩИЙ АНАЛИЗ
    console.log('\n🔍 ОБЩИЙ АНАЛИЗ...');
    const totalSOLEquivalent = balanceSOL + totalWSOL;
    console.log(`💰 Общий SOL эквивалент: ${totalSOLEquivalent.toFixed(9)} SOL`);
    
    // 🎯 ТРЕБУЕМАЯ СУММА ИЗ ОШИБКИ
    const requiredAmount = ************; // из ошибки "need ************"
    const requiredSOL = requiredAmount / LAMPORTS_PER_SOL;
    console.log(`🎯 Требуется для транзакции: ${requiredAmount} lamports (${requiredSOL.toFixed(9)} SOL)`);
    
    const shortage = requiredAmount - balance;
    const shortageSOL = shortage / LAMPORTS_PER_SOL;
    console.log(`❌ Нехватка: ${shortage} lamports (${shortageSOL.toFixed(9)} SOL)`);
    
    // 🤔 АНАЛИЗ ПРОБЛЕМЫ
    console.log('\n🤔 АНАЛИЗ ПРОБЛЕМЫ...');
    console.log('Возможные причины:');
    console.log('1. 🔄 Транзакция пытается перевести SOL который должен прийти из WSOL closeAccount');
    console.log('2. 🚫 closeAccount инструкция отсутствует или выполняется после System transfer');
    console.log('3. 🔀 Неправильный порядок инструкций в транзакции');
    console.log('4. 🎭 Jupiter создает WSOL аккаунт но не закрывает его вовремя');
    
    return {
      realBalance: balanceSOL,
      errorBalance: errorBalanceSOL,
      wsolBalance: totalWSOL,
      requiredAmount: requiredSOL,
      shortage: shortageSOL
    };
    
  } catch (error) {
    console.error('❌ ОШИБКА ПРОВЕРКИ БАЛАНСА:', error.message);
    return null;
  }
}

// 🚀 ЗАПУСК ПРОВЕРКИ
checkRealWalletBalance()
  .then(result => {
    if (result) {
      console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА!');
      console.log(`💰 Реальный баланс: ${result.realBalance.toFixed(9)} SOL`);
      console.log(`❌ Баланс в ошибке: ${result.errorBalance.toFixed(9)} SOL`);
      console.log(`🔄 WSOL баланс: ${result.wsolBalance.toFixed(9)} WSOL`);
    } else {
      console.log('\n❌ ПРОВЕРКА НЕ УДАЛАСЬ');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('\n💥 КРИТИЧЕСКАЯ ОШИБКА:', error);
    process.exit(1);
  });
