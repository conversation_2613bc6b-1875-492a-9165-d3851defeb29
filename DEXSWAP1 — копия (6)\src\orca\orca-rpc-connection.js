/**
 * 🌊 ORCA RPC CONNECTION - ПОЛУЧЕНИЕ ЦЕН ЧЕРЕЗ SOLANA RPC
 *
 * Аналогично Jupiter RPC модулю, но для Orca Whirlpools
 * Использует официальный @orca-so/whirlpools SDK через Solana RPC
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class OrcaRPCConnection {
  constructor(wallet = null) {
    console.log('🌊 Инициализация Orca RPC Connection...');
    console.log('🔍 Начало конструктора Orca RPC...');

    // 🔑 СОХРАНЯЕМ WALLET
    this.wallet = wallet;

    // 🔗 RPC подключения (аналогично Jupiter)
    this.connections = {
      trading: null,
      data: null
    };

    // 🌊 Orca SDK компоненты
    this.orcaSDK = null;
    this.whirlpoolContext = null;
    this.whirlpoolClient = null;

    // 🚨 БЛОКИРОВКА ПРОБЛЕМНЫХ ПУЛОВ ДЛЯ ПРЕДОТВРАЩЕНИЯ ЗАЦИКЛИВАНИЯ
    this.blockedPools = new Set([
      'FwewVm8u6tFPGewAyHmWAqad9hmF7mvqxK4mJ7iNqqGC' // SOL/USDT pool - вызывает зацикливание
    ]);
    this.errorCounts = new Map();

    // 📊 Статистика
    this.stats = {
      tradingRequests: 0,
      dataRequests: 0,
      errors: 0,
      avgResponseTime: 0,
      customRPC: {
        trading: false,
        data: false
      }
    };

    // 🎯 Известные пулы SOL/USDC и SOL/USDT (ПРАВИЛЬНЫЕ АДРЕСА ИЗ ОСНОВНОГО ФАЙЛА!)
    this.knownPools = {
      'SOL/USDC': {
        address: 'Czfq3xZZDmsdGdUyrNLtRhGc47cXcZtLG4crryfu44zE', // ✅ ПРАВИЛЬНЫЙ Orca SOL/USDC pool
        tokenA: 'So11111111111111111111111111111111111111112', // SOL
        tokenB: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  // USDC
      },
      'SOL/USDT': {
        address: 'FwewVm8u6tFPGewAyHmWAqad9hmF7mvqxK4mJ7iNqqGC', // ✅ ПРАВИЛЬНЫЙ Orca SOL/USDT pool
        tokenA: 'So11111111111111111111111111111111111111112', // SOL
        tokenB: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'  // USDT
      }
    };

    console.log('🔍 Вызываем initializeConnections...');
    this.initializeConnections();
    console.log('🔍 Вызываем initializeOrcaSDK...');
    this.initializeOrcaSDK();
  }

  /**
   * 🔗 ИНИЦИАЛИЗАЦИЯ RPC ПОДКЛЮЧЕНИЙ - ТОЛЬКО SOLANA RPC! (ТОЛЬКО 1 РАЗ!)
   */
  initializeConnections() {
    try {
      // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРОВЕРЯЕМ ЕСТЬ ЛИ УЖЕ ИНИЦИАЛИЗИРОВАННЫЕ CONNECTIONS
      if (this.connections.trading && this.connections.data && this.connectionsInitialized) {
        console.log('✅ Orca RPC connections уже инициализированы - ПРОПУСКАЕМ дублирующую инициализацию!');
        console.log(`   📊 Trading connection: ${this.connections.trading ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log(`   📊 Data connection: ${this.connections.data ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log('🔒 Orca connections инициализация заблокирована от дублирования');
        return;
      }

      console.log('🔗 Инициализация RPC подключений (ЕДИНСТВЕННЫЙ РАЗ)...');

      // 🌐 ИСПОЛЬЗУЕМ ТОЛЬКО SOLANA RPC ДЛЯ ВСЕХ ОПЕРАЦИЙ!
      const solanaRPC = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';

      this.connections.trading = new Connection(solanaRPC, {
        commitment: 'confirmed',
        confirmTransactionInitialTimeout: 60000
      });

      this.connections.data = new Connection(solanaRPC, {
        commitment: 'confirmed'
      });

      // ✅ ПОМЕЧАЕМ КАК ИНИЦИАЛИЗИРОВАННЫЕ
      this.connectionsInitialized = true;

      console.log('🌊 Orca RPC Connection инициализирован');
      console.log(`   🌐 Trading: Solana RPC`);
      console.log(`   🌐 Data: Solana RPC`);
      console.log(`   🔗 RPC URL: ${solanaRPC}`);
      console.log('🔒 Orca connections инициализация заблокирована от дублирования');

    } catch (error) {
      console.error('❌ Ошибка инициализации Orca RPC:', error.message);
      console.error(`🔍 Error type: ${error.constructor.name}`);
      this.connectionsInitialized = false; // Сбрасываем флаг при ошибке
      throw error;
    }
  }

  /**
   * 🌊 ИНИЦИАЛИЗАЦИЯ ORCA SDK (ТОЛЬКО 1 РАЗ!)
   */
  async initializeOrcaSDK() {
    try {
      // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРОВЕРЯЕМ ЕСТЬ ЛИ УЖЕ ИНИЦИАЛИЗИРОВАННЫЙ ORCA SDK
      if (this.orcaSDK && this.orcaSDKInitialized) {
        console.log('✅ Orca SDK уже инициализирован - ПРОПУСКАЕМ дублирующую инициализацию!');
        console.log(`   📊 Orca SDK: ${this.orcaSDK ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log('🔒 Orca SDK инициализация заблокирована от дублирования');
        return;
      }

      console.log('🔍 Начало initializeOrcaSDK (ЕДИНСТВЕННЫЙ РАЗ)...');
      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ ORCA SDK!
      console.log('🔍 Импортируем Orca SDK...');
      const { WhirlpoolContext, buildWhirlpoolClient, ORCA_WHIRLPOOL_PROGRAM_ID, PDAUtil, swapQuoteByInputToken } = require('@orca-so/whirlpools-sdk');
      const { AnchorProvider, Wallet } = require('@coral-xyz/anchor');
      const { Keypair } = require('@solana/web3.js');

      // 🚫 ИСПОЛЬЗУЕМ ТОЛЬКО РЕАЛЬНЫЙ WALLET!
      let wallet = this.wallet;
      if (!wallet) {
        console.log('❌ Wallet не предоставлен! Dummy wallets запрещены!');
        throw new Error('Реальный wallet обязателен! Dummy wallets запрещены!');
      }

      console.log(`✅ Используем реальный wallet: ${wallet.publicKey.toString()}`);

      // Создаем provider
      const provider = new AnchorProvider(this.connections.data, wallet, {
        commitment: 'confirmed'
      });

      // Создаем Whirlpool контекст
      this.whirlpoolContext = WhirlpoolContext.withProvider(provider, ORCA_WHIRLPOOL_PROGRAM_ID);

      // Создаем клиент
      this.whirlpoolClient = buildWhirlpoolClient(this.whirlpoolContext);

      // Сохраняем SDK функции
      this.orcaSDK = {
        WhirlpoolContext,
        buildWhirlpoolClient,
        ORCA_WHIRLPOOL_PROGRAM_ID,
        PDAUtil,
        swapQuoteByInputToken
      };

      // ✅ ПОМЕЧАЕМ КАК ИНИЦИАЛИЗИРОВАННЫЙ
      this.orcaSDKInitialized = true;

      console.log('✅ Orca Whirlpools SDK инициализирован через RPC');
      console.log('🔒 Orca SDK инициализация заблокирована от дублирования');

    } catch (error) {
      console.error('❌ Ошибка инициализации Orca SDK:', error.message);
      console.error(`🔍 Error type: ${error.constructor.name}`);
      // Продолжаем работу без SDK, используем API fallback
      this.orcaSDK = null;
      this.whirlpoolClient = null;
      this.orcaSDKInitialized = false; // Сбрасываем флаг при ошибке
    }
  }

  /**
   * 🌊 ПОЛУЧЕНИЕ ЦЕНЫ ORCA ЧЕРЕЗ RPC
   */
  async getOrcaQuote(params) {
    const startTime = Date.now();

    try {
      const { inputMint, outputMint, amount, slippageBps = 50 } = params;

      // 🔇 УБРАЛИ МУСОРНЫЙ ЛОГ

      // 🔥 КЕШИРОВАНИЕ ОТКЛЮЧЕНО ДЛЯ РЕАЛЬНОГО ВРЕМЕНИ!
      // Всегда получаем свежие цены каждые 200-400ms

      let quote;

      // 🔥 ИСПОЛЬЗУЕМ ТОЛЬКО API - ОТКЛЮЧАЕМ SPL TOKEN LAYOUT!
      // SPL Token Layout дает ошибки, API работает стабильно
      quote = await this.getQuoteViaAPI(inputMint, outputMint, amount);

      const responseTime = Date.now() - startTime;

      // 📊 Обновляем статистику
      this.stats.dataRequests++;
      this.stats.avgResponseTime = (this.stats.avgResponseTime + responseTime) / 2;

      console.log(`✅ Orca quote получен за ${responseTime}ms`);

      // 🔥 КЕШИРОВАНИЕ ОТКЛЮЧЕНО - НЕ СОХРАНЯЕМ В КЕШ!

      return {
        ...quote,
        responseTime
      };

    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Ошибка получения Orca quote: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🌊 ПОЛУЧЕНИЕ QUOTE ЧЕРЕЗ ORCA SDK (ОФИЦИАЛЬНЫЙ МЕТОД)
   */
  async getQuoteViaSDK(inputMint, outputMint, amount, slippageBps) {
    try {
      const { PublicKey } = require('@solana/web3.js');
      const BN = require('bn.js'); // 🔥 ИМПОРТИРУЕМ BN!

      // Определяем пул для пары
      const poolInfo = this.findPoolForPair(inputMint, outputMint);
      if (!poolInfo) {
        throw new Error(`Pool not found for ${inputMint} → ${outputMint}`);
      }

      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ ORCA SDK ДЛЯ ПОЛУЧЕНИЯ QUOTE!
      const poolPubkey = new PublicKey(poolInfo.address);

      // Получаем данные пула через RPC
      const whirlpoolData = await this.whirlpoolClient.getPool(poolPubkey);

      if (!whirlpoolData) {
        throw new Error(`Whirlpool ${poolInfo.address} not found`);
      }

      // 🌊 КОНВЕРТИРУЕМ AMOUNT В BN ОБЪЕКТ!
      const inputTokenMint = new PublicKey(inputMint);
      const outputTokenMint = new PublicKey(outputMint);
      const amountBN = new BN(amount.toString()); // ✅ ПРАВИЛЬНО: BN объект!

      // 🚀 ИСПОЛЬЗУЕМ НОВЫЙ SPARSESWAP ДЛЯ РЕШЕНИЯ TICK ARRAY ПРОБЛЕМ!
      const quote = await this.orcaSDK.swapQuoteByInputToken(
        whirlpoolData,
        inputTokenMint,
        amountBN, // ✅ ИСПОЛЬЗУЕМ BN!
        slippageBps,
        this.whirlpoolContext.program.programId,
        this.whirlpoolContext.fetcher,
        undefined, // opts
        'Situational' // 🔥 ВКЛЮЧАЕМ SPARSESWAP FALLBACK TICK ARRAYS!
      );

      // 🚀 ЛОГИРУЕМ SPARSESWAP ИНФОРМАЦИЮ
      console.log(`🌊 Orca SparseSwap котировка получена: ${quote.estimatedAmountOut.toString()} ${outputMint.slice(0,8)}...`);
      console.log(`   💸 Комиссия: ${quote.feeAmount?.toString() || '0'}`);
      console.log(`   📊 Price Impact: ${((quote.priceImpactPct || 0) * 100).toFixed(4)}%`);
      console.log(`   🚀 SparseSwap: Автоматическое решение tick array проблем!`);

      // 🔍 ДИАГНОСТИКА SPARSESWAP
      if (quote.tickArray2 && !quote.tickArray2.equals(quote.tickArray1)) {
        console.log(`   ✅ SparseSwap использует дополнительные tick arrays`);
      }
      if (quote.supplementalTickArrays && quote.supplementalTickArrays.length > 0) {
        console.log(`   ✅ SparseSwap использует ${quote.supplementalTickArrays.length} дополнительных tick arrays`);
      }

      return {
        inputMint,
        outputMint,
        inAmount: amount.toString(),
        outAmount: quote.estimatedAmountOut.toString(),
        otherAmountThreshold: quote.otherAmountThreshold.toString(),
        swapMode: 'ExactIn',
        slippageBps,
        platformFee: null,
        priceImpactPct: quote.priceImpactPct || 0,
        routePlan: [{
          swapInfo: {
            ammKey: poolInfo.address,
            label: 'Orca Whirlpool (SparseSwap)',
            inputMint,
            outputMint,
            inAmount: amount.toString(),
            outAmount: quote.estimatedAmountOut.toString(),
            feeAmount: quote.feeAmount?.toString() || '0',
            feeMint: inputMint
          }
        }]
      };

    } catch (error) {
      console.error('❌ Ошибка SDK quote:', error.message);
      throw error;
    }
  }

  /**
   * 🔍 ПОИСК ПУЛА ДЛЯ ПАРЫ ТОКЕНОВ
   */
  findPoolForPair(inputMint, outputMint) {
    // 🌊 ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ АДРЕСА ПУЛОВ ИЗ ОСНОВНОГО ФАЙЛА!
    const pools = [
      {
        address: '7qbRF6YsyGuLUVs6Y1q64bdVrfe4ZcUUz1JRdoVNUJnm', // SOL/USDC
        tokenA: 'So11111111111111111111111111111111111111112', // SOL
        tokenB: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        name: 'SOL/USDC'
      },
      {
        address: 'HJPjoWUrhoZzkNfRpHuieeFk9WcZWjwy6PBjZ81ngndJ', // SOL/USDT
        tokenA: 'So11111111111111111111111111111111111111112', // SOL
        tokenB: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
        name: 'SOL/USDT'
      }
    ];

    return pools.find(pool =>
      (pool.tokenA === inputMint && pool.tokenB === outputMint) ||
      (pool.tokenA === outputMint && pool.tokenB === inputMint)
    );
  }

  /**
   * 🔍 АНАЛИЗ РЕАЛЬНЫХ SDK ПАРАМЕТРОВ - НАЙДЕМ ПРАВИЛЬНУЮ ЦЕНУ $138.18!
   */
  async getQuoteViaAPI(inputMint, outputMint, amount) {
    try {
      // 🔇 УБРАЛИ МУСОРНЫЕ ЛОГИ

      // 🔥 ПЫТАЕМСЯ ПОЛУЧИТЬ РЕАЛЬНЫЕ ДАННЫЕ ИЗ SDK!
      try {
        const poolInfo = this.findPoolForPair(inputMint, outputMint);
        if (poolInfo && this.whirlpoolClient) {
          // 🔇 УБРАЛИ МУСОРНЫЙ ЛОГ

          // Получаем реальный пул через SDK
          const poolPubkey = new (require('@solana/web3.js').PublicKey)(poolInfo.address);
          const whirlpool = await this.whirlpoolClient.getPool(poolPubkey);

          if (whirlpool) {
            // Пытаемся получить данные пула
            let poolData;
            try {
              poolData = whirlpool.getData();

              if (poolData) {
                // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНУЮ ЦЕНУ ИЗ sqrtPrice!
                if (poolData.sqrtPrice) {
                  const sqrtPriceBN = poolData.sqrtPrice;

                  // 🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ БАЛАНСЫ ПУЛА!
                  try {
                    const connection = this.connections.data;
                    const vaultAInfo = await connection.getAccountInfo(poolData.tokenVaultA);
                    const vaultBInfo = await connection.getAccountInfo(poolData.tokenVaultB);

                    if (vaultAInfo && vaultBInfo) {
                      // 🔥 ИСПОЛЬЗУЕМ ТОЛЬКО SPL TOKEN LAYOUT!
                      try {
                        const { AccountLayout } = require('@solana/spl-token');

                        const vaultAData = AccountLayout.decode(vaultAInfo.data);
                        const vaultBData = AccountLayout.decode(vaultBInfo.data);

                        // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЕ БАЛАНСЫ ИЗ SPL TOKEN LAYOUT!
                        const vaultAAmount = vaultAData.amount;
                        const vaultBAmount = vaultBData.amount;

                        // 🔥 ПРАВИЛЬНАЯ ФОРМУЛА ИЗ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ UNISWAP V3!
                        const SOL_DECIMALS = **********;
                        const USDC_USDT_DECIMALS = 1000000;
                        const inputSol = amount / SOL_DECIMALS;

                        // 🧮 ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ БАЛАНСЫ ИЗ SPL TOKEN LAYOUT
                        const solBalance = parseFloat(vaultAAmount.toString()) / SOL_DECIMALS;
                        const usdBalance = parseFloat(vaultBAmount.toString()) / USDC_USDT_DECIMALS;

                        // 🔥 ПРАВИЛЬНАЯ ФОРМУЛА ИЗ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ:
                        // price = (sqrtPriceX96 / 2^96)^2 / (10^(decimalsB - decimalsA))
                        // Для Orca: price = (sqrtPrice / 2^64)^2 * 10^(decimalsA - decimalsB)

                        const sqrtPriceBN = poolData.sqrtPrice;
                        const sqrtPriceFloat = parseFloat(sqrtPriceBN.toString());

                        // 🔥 ОФИЦИАЛЬНАЯ ФОРМУЛА UNISWAP V3 ДЛЯ ORCA:
                        const Q64 = Math.pow(2, 64);
                        const sqrtPrice = sqrtPriceFloat / Q64;
                        const price = Math.pow(sqrtPrice, 2);

                        // Корректируем на decimals: SOL(9) - USDC(6) = 3
                        const decimalsA = 9; // SOL
                        const decimalsB = 6; // USDC
                        const adjustedPrice = price * Math.pow(10, decimalsA - decimalsB);

                        // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНУЮ ЦЕНУ ИЗ SQRTPRICE!
                        const realPrice = adjustedPrice;

                        const outputUsd = inputSol * realPrice;
                        const outputAmount = Math.floor(outputUsd * USDC_USDT_DECIMALS);

                        return {
                          inputMint,
                          outputMint,
                          inAmount: amount.toString(),
                          outAmount: outputAmount.toString(),
                          otherAmountThreshold: Math.floor(outputAmount * 0.99).toString(),
                          swapMode: 'ExactIn',
                          slippageBps: 50,
                          platformFee: null,
                          priceImpactPct: 0.3,
                          routePlan: [{
                            swapInfo: {
                              ammKey: poolInfo.address,
                              label: 'Orca Official Formula Price',
                              inputMint,
                              outputMint,
                              inAmount: amount.toString(),
                              outAmount: outputAmount.toString(),
                              feeAmount: '0',
                              feeMint: inputMint
                            }
                          }]
                        };

                      } catch (splError) {
                        console.log(`❌ Ошибка SPL Token Layout:`, splError.message);
                      }
                    } else {
                      console.log(`❌ Не удалось получить данные vault аккаунтов`);
                    }
                  } catch (balanceError) {
                    console.log(`❌ Ошибка получения балансов:`, balanceError.message);
                  }

                  // 🔥 НЕТ FALLBACK! ТОЛЬКО SPL TOKEN LAYOUT!
                }

                // 🔇 УБРАЛИ МУСОРНЫЕ ЛОГИ
              }
            } catch (dataError) {
              console.log(`❌ Ошибка получения данных пула:`, dataError.message);
            }

            // 🔇 УБРАЛИ МУСОРНЫЕ ЛОГИ SDK
          }
        }
      } catch (sdkError) {
        console.log(`❌ Ошибка SDK:`, sdkError.message);
      }

      // 🔥 ВОЗВРАЩАЕМ ЗАГЛУШКУ ЕСЛИ SPL TOKEN LAYOUT НЕ РАБОТАЕТ!
      console.log('⚠️ SPL Token Layout не сработал, возвращаем заглушку...');
      return {
        inputMint,
        outputMint,
        inAmount: amount.toString(),
        outAmount: '0',
        otherAmountThreshold: '0',
        swapMode: 'ExactIn',
        slippageBps: 50,
        platformFee: null,
        priceImpactPct: '0',
        routePlan: [],
        contextSlot: 0,
        timeTaken: 0,
        error: 'SPL Token Layout failed - using fallback'
      };

    } catch (error) {
      console.error('❌ Ошибка анализа SDK:', error.message);
      throw error;
    }
  }

  /**
   * 🚀 ПОЛУЧЕНИЕ ОБЕИХ ORCA ЦЕН ОДНОВРЕМЕННО (SOL/USDC + SOL/USDT)
   */
  async getBothOrcaQuotes(amount = 100000000) {
    const SOL_MINT = 'So11111111111111111111111111111111111111112';
    const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    const USDT_MINT = 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB';

    try {
      // 🔇 УБРАЛИ МУСОРНЫЙ ЛОГ

      // 🔥 ПАРАЛЛЕЛЬНЫЕ ЗАПРОСЫ ДЛЯ МАКСИМАЛЬНОЙ СКОРОСТИ!
      const [usdcQuote, usdtQuote] = await Promise.all([
        this.getOrcaQuote({
          inputMint: SOL_MINT,
          outputMint: USDC_MINT,
          amount: amount,
          slippageBps: 50
        }),
        this.getOrcaQuote({
          inputMint: SOL_MINT,
          outputMint: USDT_MINT,
          amount: amount,
          slippageBps: 50
        })
      ]);

      // 🔥 ПРАВИЛЬНАЯ ФОРМУЛА РАСЧЕТА ЦЕНЫ!
      const SOL_DECIMALS = **********; // 9 decimals
      const USDC_USDT_DECIMALS = 1000000; // 6 decimals

      // Цена = (outAmount в USD) / (inAmount в SOL)
      const inputSol = amount / SOL_DECIMALS; // 0.1 SOL
      const usdcOut = parseFloat(usdcQuote.outAmount) / USDC_USDT_DECIMALS; // USD
      const usdtOut = parseFloat(usdtQuote.outAmount) / USDC_USDT_DECIMALS; // USD

      const usdcPrice = usdcOut / inputSol; // USD per SOL
      const usdtPrice = usdtOut / inputSol; // USD per SOL

      // 🔇 УБРАЛИ СТАРЫЕ ЛОГИ - показываются в RPC Interface

      return {
        'SOL/USDC': {
          price: usdcPrice,
          quote: usdcQuote
        },
        'SOL/USDT': {
          price: usdtPrice,
          quote: usdtQuote
        }
      };

    } catch (error) {
      console.error(`❌ Ошибка получения обеих Orca цен: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🎯 ПОИСК ПУЛА ДЛЯ ПАРЫ ТОКЕНОВ
   */
  findPoolForPair(inputMint, outputMint) {
    // Проверяем известные пулы
    for (const [pair, poolInfo] of Object.entries(this.knownPools)) {
      if ((poolInfo.tokenA === inputMint && poolInfo.tokenB === outputMint) ||
          (poolInfo.tokenA === outputMint && poolInfo.tokenB === inputMint)) {
        return poolInfo;
      }
    }

    return null;
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 🧹 ОЧИСТКА РЕСУРСОВ
   */
  cleanup() {
    console.log('🧹 Очистка Orca RPC Connection...');
    // Закрываем соединения если нужно
  }
}

module.exports = OrcaRPCConnection;
