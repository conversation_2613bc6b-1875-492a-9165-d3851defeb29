/**
 * 🎯 СТРАТЕГИЯ АРБИТРАЖА
 */

class ArbitrageStrategy {
  constructor() {
    this.minProfitThreshold = 0.001; // Минимальная прибыль в SOL
    this.maxSlippage = 0.02; // Максимальный slippage 2%
    this.maxAmount = 10000; // Максимальная сумма для арбитража
  }

  /**
   * 📊 АНАЛИЗ ВОЗМОЖНОСТИ АРБИТРАЖА
   */
  analyzeOpportunity(jupiterPrice, orcaPrice, amount) {
    const priceDifference = Math.abs(jupiterPrice - orcaPrice);
    const percentageDiff = (priceDifference / Math.min(jupiterPrice, orcaPrice)) * 100;
    
    const estimatedProfit = priceDifference * amount;
    const estimatedFees = this.calculateFees(amount);
    const netProfit = estimatedProfit - estimatedFees;
    
    const profitable = netProfit > this.minProfitThreshold;
    const direction = jupiterPrice > orcaPrice ? 'orca_to_jupiter' : 'jupiter_to_orca';
    
    return {
      profitable,
      direction,
      priceDifference,
      percentageDiff,
      estimatedProfit,
      estimatedFees,
      netProfit,
      roi: (netProfit / amount) * 100
    };
  }

  /**
   * 💰 РАССЧИТАТЬ КОМИССИИ
   */
  calculateFees(amount) {
    const jupiterFee = amount * 0.003; // 0.3%
    const orcaFee = amount * 0.003; // 0.3%
    const marginfiFlashLoanFee = amount * 0.001; // 0.1%
    const solanaTransactionFee = 0.000005; // ~5000 lamports
    
    return jupiterFee + orcaFee + marginfiFlashLoanFee + solanaTransactionFee;
  }

  /**
   * 📈 ОПТИМИЗИРОВАТЬ РАЗМЕР ПОЗИЦИИ
   */
  optimizePositionSize(opportunity, maxAmount) {
    if (!opportunity.profitable) {
      return 0;
    }
    
    // Учитываем price impact
    const optimalSize = Math.min(
      maxAmount,
      this.maxAmount,
      // Размер, при котором ROI остается выше порога
      opportunity.netProfit / this.minProfitThreshold
    );
    
    return Math.max(0, optimalSize);
  }

  /**
   * ⚡ СОЗДАТЬ ПЛАН ВЫПОЛНЕНИЯ
   */
  createExecutionPlan(opportunity, amount) {
    if (!opportunity.profitable) {
      return null;
    }
    
    const steps = [];
    
    if (opportunity.direction === 'orca_to_jupiter') {
      steps.push({
        action: 'flash_loan',
        platform: 'marginfi',
        token: 'USDC',
        amount
      });
      steps.push({
        action: 'buy',
        platform: 'orca',
        tokenIn: 'USDC',
        tokenOut: 'SOL',
        amount
      });
      steps.push({
        action: 'sell',
        platform: 'jupiter',
        tokenIn: 'SOL',
        tokenOut: 'USDC',
        amount: 'received_from_orca'
      });
      steps.push({
        action: 'repay_loan',
        platform: 'marginfi',
        token: 'USDC',
        amount: 'original_amount_plus_fee'
      });
    } else {
      // jupiter_to_orca
      steps.push({
        action: 'flash_loan',
        platform: 'marginfi',
        token: 'USDC',
        amount
      });
      steps.push({
        action: 'buy',
        platform: 'jupiter',
        tokenIn: 'USDC',
        tokenOut: 'SOL',
        amount
      });
      steps.push({
        action: 'sell',
        platform: 'orca',
        tokenIn: 'SOL',
        tokenOut: 'USDC',
        amount: 'received_from_jupiter'
      });
      steps.push({
        action: 'repay_loan',
        platform: 'marginfi',
        token: 'USDC',
        amount: 'original_amount_plus_fee'
      });
    }
    
    return {
      steps,
      estimatedProfit: opportunity.netProfit,
      estimatedDuration: '10-30 seconds',
      riskLevel: this.assessRisk(opportunity)
    };
  }

  /**
   * ⚠️ ОЦЕНИТЬ РИСК
   */
  assessRisk(opportunity) {
    let risk = 'low';
    
    if (opportunity.percentageDiff < 1) risk = 'high';
    else if (opportunity.percentageDiff < 2) risk = 'medium';
    
    return risk;
  }
}

module.exports = ArbitrageStrategy;
