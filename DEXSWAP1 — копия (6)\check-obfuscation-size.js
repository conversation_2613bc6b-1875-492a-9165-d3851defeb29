/**
 * 🔍 ПРОВЕРКА РАЗМЕРА ОБФУСКАЦИИ
 */

const { Connection, PublicKey, Keypair, SystemProgram, ComputeBudgetProgram, VersionedTransaction, TransactionMessage } = require('@solana/web3.js');
const fs = require('fs');

async function checkObfuscationSize() {
  console.log('🔍 ПРОВЕРКА РАЗМЕРА ОБФУСКАЦИИ');

  try {
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
    const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
    const wallet = { publicKey: keypair.publicKey, keypair: keypair };

    // Базовые инструкции для теста
    const baseInstructions = [
      ComputeBudgetProgram.setComputeUnitLimit({ units: 200000 }),
      ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 1000 }),
      SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: wallet.publicKey,
        lamports: 1
      })
    ];

    console.log(`📊 Базовых инструкций: ${baseInstructions.length}`);

    // Загружаем ALT
    const altAddresses = [
      'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC',
      'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'
    ];

    const altAccounts = [];
    for (const address of altAddresses) {
      try {
        const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
        if (altAccount.value) {
          altAccounts.push(altAccount.value);
        }
      } catch (e) {
        // Пропускаем
      }
    }

    console.log(`📊 ALT таблиц загружено: ${altAccounts.length}`);

    const { blockhash } = await connection.getLatestBlockhash();

    // ТЕСТ 1: БЕЗ ОБФУСКАЦИИ
    console.log(`\n🧪 ТЕСТ 1: БЕЗ ОБФУСКАЦИИ`);
    
    const messageWithoutObfuscation = new TransactionMessage({
      payerKey: wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: baseInstructions
    }).compileToV0Message(altAccounts);

    const txWithoutObfuscation = new VersionedTransaction(messageWithoutObfuscation);
    txWithoutObfuscation.sign([wallet.keypair]);
    
    const sizeWithoutObfuscation = txWithoutObfuscation.serialize().length;
    console.log(`   📏 Размер БЕЗ обфускации: ${sizeWithoutObfuscation} байт`);

    // ТЕСТ 2: С ОБФУСКАЦИЕЙ (добавляем dummy инструкции)
    console.log(`\n🎭 ТЕСТ 2: С ОБФУСКАЦИЕЙ`);
    
    // Создаем обфускационные инструкции (dummy)
    const obfuscationInstructions = [
      // Dummy System transfers
      SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: wallet.publicKey,
        lamports: 0
      }),
      SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: wallet.publicKey,
        lamports: 0
      }),
      // Dummy ComputeBudget
      ComputeBudgetProgram.setComputeUnitLimit({ units: 1 }),
      // Dummy создание аккаунта
      SystemProgram.createAccount({
        fromPubkey: wallet.publicKey,
        newAccountPubkey: Keypair.generate().publicKey,
        lamports: 0,
        space: 0,
        programId: new PublicKey('********************************')
      })
    ];

    const instructionsWithObfuscation = [
      ...baseInstructions,
      ...obfuscationInstructions
    ];

    console.log(`   📊 Обфускационных инструкций: ${obfuscationInstructions.length}`);
    console.log(`   📊 Всего инструкций: ${instructionsWithObfuscation.length}`);

    const messageWithObfuscation = new TransactionMessage({
      payerKey: wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: instructionsWithObfuscation
    }).compileToV0Message(altAccounts);

    const txWithObfuscation = new VersionedTransaction(messageWithObfuscation);
    txWithObfuscation.sign([wallet.keypair]);
    
    const sizeWithObfuscation = txWithObfuscation.serialize().length;
    console.log(`   📏 Размер С обфускацией: ${sizeWithObfuscation} байт`);

    // ТЕСТ 3: МАКСИМАЛЬНАЯ ОБФУСКАЦИЯ
    console.log(`\n🔥 ТЕСТ 3: МАКСИМАЛЬНАЯ ОБФУСКАЦИЯ`);
    
    const maxObfuscationInstructions = [];
    
    // Добавляем много dummy инструкций до лимита
    for (let i = 0; i < 10; i++) {
      maxObfuscationInstructions.push(
        SystemProgram.transfer({
          fromPubkey: wallet.publicKey,
          toPubkey: wallet.publicKey,
          lamports: 0
        })
      );
    }

    const instructionsWithMaxObfuscation = [
      ...baseInstructions,
      ...maxObfuscationInstructions
    ];

    console.log(`   📊 Максимальных обфускационных инструкций: ${maxObfuscationInstructions.length}`);
    console.log(`   📊 Всего инструкций: ${instructionsWithMaxObfuscation.length}`);

    const messageWithMaxObfuscation = new TransactionMessage({
      payerKey: wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: instructionsWithMaxObfuscation
    }).compileToV0Message(altAccounts);

    const txWithMaxObfuscation = new VersionedTransaction(messageWithMaxObfuscation);
    txWithMaxObfuscation.sign([wallet.keypair]);
    
    const sizeWithMaxObfuscation = txWithMaxObfuscation.serialize().length;
    console.log(`   📏 Размер с МАКСИМАЛЬНОЙ обфускацией: ${sizeWithMaxObfuscation} байт`);

    // АНАЛИЗ РЕЗУЛЬТАТОВ
    console.log(`\n📊 АНАЛИЗ РАЗМЕРОВ ОБФУСКАЦИИ:`);
    console.log(`   🔹 Без обфускации: ${sizeWithoutObfuscation} байт`);
    console.log(`   🔹 С обфускацией: ${sizeWithObfuscation} байт (+${sizeWithObfuscation - sizeWithoutObfuscation} байт)`);
    console.log(`   🔹 Максимальная: ${sizeWithMaxObfuscation} байт (+${sizeWithMaxObfuscation - sizeWithoutObfuscation} байт)`);
    console.log(`   🔹 Лимит Solana: 1232 байт`);
    console.log(`   🔹 Запас без обфускации: ${1232 - sizeWithoutObfuscation} байт`);
    console.log(`   🔹 Запас с обфускацией: ${1232 - sizeWithObfuscation} байт`);
    console.log(`   🔹 Запас максимальной: ${1232 - sizeWithMaxObfuscation} байт`);

    // ПРОВЕРЯЕМ MASTER CONTROLLER
    console.log(`\n🎯 ПРОВЕРКА MASTER CONTROLLER:`);
    
    try {
      const MasterTransactionController = require('./master-transaction-controller.js');
      const masterController = new MasterTransactionController(connection, wallet);
      
      console.log(`   🔧 Master Controller инициализирован`);
      console.log(`   🎭 Обфускация включена: ${masterController.config?.enableObfuscation || 'неизвестно'}`);
      console.log(`   🎯 Целевой размер: ${masterController.config?.targetSize || 'неизвестно'} байт`);
      console.log(`   🔥 Динамическая обфускация: ${masterController.config?.dynamicObfuscation || 'неизвестно'}`);
      
    } catch (e) {
      console.log(`   ❌ Ошибка загрузки Master Controller: ${e.message}`);
    }

    // ИТОГОВЫЙ ВЕРДИКТ
    console.log(`\n🎯 ИТОГОВЫЙ ВЕРДИКТ:`);
    
    const obfuscationOverhead = sizeWithObfuscation - sizeWithoutObfuscation;
    const maxObfuscationOverhead = sizeWithMaxObfuscation - sizeWithoutObfuscation;
    
    if (obfuscationOverhead > 0) {
      console.log(`   ✅ Обфускация ВКЛЮЧЕНА и добавляет ${obfuscationOverhead} байт`);
      console.log(`   📊 Максимальная обфускация добавляет ${maxObfuscationOverhead} байт`);
      console.log(`   💡 В реальной транзакции 1170 байт обфускация УЖЕ ВКЛЮЧЕНА`);
    } else {
      console.log(`   ❌ Обфускация НЕ ВКЛЮЧЕНА или не добавляет размер`);
    }

    return {
      withoutObfuscation: sizeWithoutObfuscation,
      withObfuscation: sizeWithObfuscation,
      withMaxObfuscation: sizeWithMaxObfuscation,
      obfuscationOverhead: obfuscationOverhead,
      maxObfuscationOverhead: maxObfuscationOverhead
    };

  } catch (error) {
    console.error(`❌ Ошибка: ${error.message}`);
    return false;
  }
}

if (require.main === module) {
  checkObfuscationSize()
    .then(result => {
      if (result) {
        console.log(`\n🎯 РЕЗУЛЬТАТ: Обфускация добавляет ${result.obfuscationOverhead} байт к размеру транзакции`);
      }
    });
}

module.exports = { checkObfuscationSize };
