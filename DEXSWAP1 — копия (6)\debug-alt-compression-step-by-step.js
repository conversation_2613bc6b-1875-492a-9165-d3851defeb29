#!/usr/bin/env node

/**
 * 🔍 ГЛУБОКАЯ ДИАГНОСТИКА ALT СЖАТИЯ - ПОЭТАПНЫЙ АНАЛИЗ
 * Изучаем каждый этап создания VersionedTransaction и почему не происходит сжатие
 */

const { Connection, PublicKey, TransactionMessage, VersionedTransaction, SystemProgram, AddressLookupTableAccount, Keypair } = require('@solana/web3.js');
const { createAssociatedTokenAccountIdempotentInstruction } = require('@solana/spl-token');
const fs = require('fs');

// Импортируем complete-flash-loan-structure для получения реальных инструкций
const CompleteFlashLoanStructure = require('./complete-flash-loan-structure.js');

// 🔥 ФУНКЦИЯ ДЛЯ СОЗДАНИЯ ИНСТРУКЦИЙ ТОЛЬКО С 3 ПРОБЛЕМНЫМИ АДРЕСАМИ
async function createSpecificInstructions(connection, wallet) {
    console.log('🔥 СОЗДАНИЕ ИНСТРУКЦИЙ ТОЛЬКО С 3 ПРОБЛЕМНЫМИ АДРЕСАМИ...');

    // 🔥 ТОЛЬКО 3 АДРЕСА, КОТОРЫЕ ДОЛЖНЫ СЖИМАТЬСЯ, НО НЕ СЖИМАЮТСЯ!
    const problematicAddresses = [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // METEORA_DLMM_PROGRAM ✅ в ALT[1][44]
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program ✅ в ALT[0][2] и ALT[1][1]
        'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'  // MarginFi Program ✅ в ALT[0][0] и ALT[1][6]
    ];

    console.log('🔍 ТЕСТИРУЕМ ТОЛЬКО 3 ПРОБЛЕМНЫХ АДРЕСА:');
    problematicAddresses.forEach((addr, index) => {
        console.log(`   ${index + 1}. ${addr.slice(0, 8)}... - ДОЛЖЕН СЖИМАТЬСЯ!`);
    });

    try {
        const instructions = [];

        // 🔥 СОЗДАЕМ ИНСТРУКЦИИ ТОЛЬКО С ЭТИМИ 3 АДРЕСАМИ
        // Каждый адрес используется как programId И как account в keys
        problematicAddresses.forEach((addr, index) => {
            const instruction = {
                programId: new PublicKey(addr), // Используем как programId
                keys: [
                    { pubkey: wallet.publicKey, isSigner: true, isWritable: true }, // Payer (не сжимается)
                    { pubkey: new PublicKey(problematicAddresses[0]), isSigner: false, isWritable: false }, // METEORA_DLMM_PROGRAM
                    { pubkey: new PublicKey(problematicAddresses[1]), isSigner: false, isWritable: false }, // Token Program
                    { pubkey: new PublicKey(problematicAddresses[2]), isSigner: false, isWritable: false }  // MarginFi Program
                ],
                data: Buffer.from([index, 0, 0, 0])
            };
            instructions.push(instruction);
        });

        console.log(`✅ СОЗДАНО ${instructions.length} ИНСТРУКЦИЙ ТОЛЬКО С 3 ПРОБЛЕМНЫМИ АДРЕСАМИ!`);
        console.log(`🔍 Каждый из 3 адресов используется как programId И в keys - ДОЛЖНЫ СЖИМАТЬСЯ!`);
        return instructions;

    } catch (error) {
        console.log(`❌ ОШИБКА СОЗДАНИЯ ИНСТРУКЦИЙ: ${error.message}`);
        return [];
    }
}

// 🔥 ФУНКЦИЯ ДЛЯ СОЗДАНИЯ РЕАЛЬНЫХ ИНСТРУКЦИЙ С АДРЕСАМИ ИЗ ALT
async function createRealInstructions(connection, wallet, customAltData) {
    console.log('🔥 СОЗДАНИЕ РЕАЛЬНЫХ ИНСТРУКЦИЙ С АДРЕСАМИ ИЗ ALT ТАБЛИЦ...');

    try {
        const instructions = [];

        // Получаем реальные адреса из ALT таблиц
        const realAddresses = [];
        Object.values(customAltData.tables).forEach(table => {
            realAddresses.push(...table.addresses);
        });

        console.log(`🔍 Найдено ${realAddresses.length} реальных адресов в ALT таблицах`);

        // Создаем инструкции с реальными адресами из ALT
        // 1. ATA создание (USDC)
        if (realAddresses.includes('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v')) {
            const createUSDCATAIx = createAssociatedTokenAccountIdempotentInstruction(
                wallet.publicKey,
                new PublicKey('6W2uQjo5vBKSAh8r6ofqbzCNNdmZTsu6vWHgQUEAE8Ri'), // USDC ATA
                wallet.publicKey,
                new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') // USDC mint
            );
            instructions.push(createUSDCATAIx);
        }

        // 2. ATA создание (SOL)
        if (realAddresses.includes('So11111111111111111111111111111111111111112')) {
            const createSOLATAIx = createAssociatedTokenAccountIdempotentInstruction(
                wallet.publicKey,
                new PublicKey('BnncknPtDaP1tRBTZrD3ow8hTJaVriTVZj7ir2CWNoyD'), // SOL ATA
                wallet.publicKey,
                new PublicKey('So11111111111111111111111111111111111111112') // WSOL mint
            );
            instructions.push(createSOLATAIx);
        }

        // 3. Добавляем инструкции с адресами из ALT
        const testInstructions = realAddresses.slice(0, 15).map((addr, index) => {
            return {
                programId: new PublicKey(addr),
                keys: [
                    { pubkey: wallet.publicKey, isSigner: true, isWritable: true },
                    { pubkey: new PublicKey(realAddresses[(index + 1) % realAddresses.length]), isSigner: false, isWritable: false },
                    { pubkey: new PublicKey(realAddresses[(index + 2) % realAddresses.length]), isSigner: false, isWritable: true }
                ],
                data: Buffer.from([index, 0, 0, 0])
            };
        });

        instructions.push(...testInstructions);

        console.log(`✅ СОЗДАНО ${instructions.length} РЕАЛЬНЫХ ИНСТРУКЦИЙ С АДРЕСАМИ ИЗ ALT!`);
        return instructions;

    } catch (error) {
        console.log(`❌ ОШИБКА СОЗДАНИЯ РЕАЛЬНЫХ ИНСТРУКЦИЙ: ${error.message}`);
        return [];
    }
}

// 🔥 10 ВАРИАНТОВ ТЕСТИРОВАНИЯ ALT СЖАТИЯ
async function test10ALTCompressionVariants(connection, wallet, blockhash, originalAltTables, customAltData) {
    console.log('\n🔥 НАЧИНАЕМ 10 ВАРИАНТОВ ТЕСТИРОВАНИЯ ALT СЖАТИЯ С РЕАЛЬНЫМИ ИНСТРУКЦИЯМИ');
    console.log('=' .repeat(80));

    // 🔥 СОЗДАЕМ ИНСТРУКЦИИ С КОНКРЕТНЫМИ 10 АДРЕСАМИ!
    console.log('🔥 ПОЛУЧАЕМ ИНСТРУКЦИИ С КОНКРЕТНЫМИ 10 АДРЕСАМИ, КОТОРЫЕ НЕ СЖИМАЮТСЯ...');
    const specificInstructions = await createSpecificInstructions(connection, wallet);

    if (specificInstructions.length === 0) {
        console.log('❌ НЕ УДАЛОСЬ ПОЛУЧИТЬ КОНКРЕТНЫЕ ИНСТРУКЦИИ! Используем общие...');
        const realInstructions = await createRealInstructions(connection, wallet, customAltData);
        console.log(`✅ ПОЛУЧЕНО ${realInstructions.length} ОБЩИХ ИНСТРУКЦИЙ ДЛЯ ТЕСТИРОВАНИЯ!`);
    } else {
        console.log(`✅ ПОЛУЧЕНО ${specificInstructions.length} КОНКРЕТНЫХ ИНСТРУКЦИЙ ДЛЯ ТЕСТИРОВАНИЯ!`);
    }

    // Используем конкретные инструкции для тестирования
    const testInstructions = specificInstructions.length > 0 ? specificInstructions : await createRealInstructions(connection, wallet, customAltData);

    // ВАРИАНТ 1: Простой объект ALT (как сейчас)
    console.log('\n🧪 ВАРИАНТ 1: Простой объект ALT (текущий способ)');
    console.log('-' .repeat(50));

    const variant1ALT = originalAltTables;
    const variant1Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant1ALT, testInstructions, 'Простой объект');

    // ВАРИАНТ 2: AddressLookupTableAccount с правильными полями
    console.log('\n🧪 ВАРИАНТ 2: AddressLookupTableAccount с правильными полями');
    console.log('-' .repeat(50));

    let variant2Result;
    try {
        const variant2ALT = Object.values(customAltData.tables).map(tableData => {
            return new AddressLookupTableAccount({
                key: new PublicKey(tableData.address),
                state: {
                    addresses: tableData.addresses.map(addr => new PublicKey(addr)),
                    deactivationSlot: BigInt('18446744073709551615'),
                    lastExtendedSlot: 0,
                    lastExtendedSlotStartIndex: 0,
                    authority: null
                }
            });
        });
        variant2Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant2ALT, testInstructions, 'AddressLookupTableAccount правильный');
    } catch (error) {
        console.log(`   ❌ Ошибка создания AddressLookupTableAccount: ${error.message}`);
        variant2Result = { variantName: 'AddressLookupTableAccount правильный', success: false, error: error.message };
    }

    // ВАРИАНТ 3: Загрузка через connection.getAddressLookupTable
    console.log('\n🧪 ВАРИАНТ 3: Загрузка через connection.getAddressLookupTable');
    console.log('-' .repeat(50));

    const variant3ALT = [];
    const tableEntries = Object.entries(customAltData.tables).slice(0, 2); // Берем только первые 2 для скорости
    for (const [tableName, tableData] of tableEntries) {
        try {
            const lookupResponse = await connection.getAddressLookupTable(new PublicKey(tableData.address));
            if (lookupResponse.value) {
                variant3ALT.push(lookupResponse.value);
            }
        } catch (error) {
            console.log(`   ❌ Ошибка загрузки ALT ${tableData.address.slice(0,8)}...: ${error.message}`);
        }
    }
    const variant3Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant3ALT, testInstructions, 'connection.getAddressLookupTable');

    // ВАРИАНТ 4: Обратный порядок ALT таблиц
    console.log('\n🧪 ВАРИАНТ 4: Обратный порядок ALT таблиц');
    console.log('-' .repeat(50));

    const variant4ALT = [...originalAltTables].reverse();
    const variant4Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant4ALT, testInstructions, 'Обратный порядок ALT');

    // ВАРИАНТ 5: Только первая ALT таблица
    console.log('\n🧪 ВАРИАНТ 5: Только первая ALT таблица');
    console.log('-' .repeat(50));

    const variant5ALT = originalAltTables.slice(0, 1);
    const variant5Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant5ALT, testInstructions, 'Только первая ALT');

    // ВАРИАНТ 6: Только вторая ALT таблица
    console.log('\n🧪 ВАРИАНТ 6: Только вторая ALT таблица');
    console.log('-' .repeat(50));

    const variant6ALT = originalAltTables.slice(1, 2);
    const variant6Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant6ALT, testInstructions, 'Только вторая ALT');

    // ВАРИАНТ 7: ALT с минимальным state
    console.log('\n🧪 ВАРИАНТ 7: ALT с минимальным state');
    console.log('-' .repeat(50));

    const variant7ALT = Object.values(customAltData.tables).map(tableData => ({
        key: new PublicKey(tableData.address),
        state: {
            addresses: tableData.addresses.map(addr => new PublicKey(addr))
            // Только addresses, без других полей
        }
    }));
    const variant7Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant7ALT, testInstructions, 'ALT минимальный state');

    // ВАРИАНТ 11: ALT через создание объекта напрямую
    console.log('\n🧪 ВАРИАНТ 11: ALT через создание объекта напрямую');
    console.log('-' .repeat(50));

    const variant11ALT = Object.values(customAltData.tables).map(tableData => {
        // Создаем объект напрямую без new AddressLookupTableAccount
        return {
            key: new PublicKey(tableData.address),
            state: {
                addresses: tableData.addresses.map(addr => new PublicKey(addr)),
                deactivationSlot: BigInt('18446744073709551615'),
                lastExtendedSlot: 0,
                authority: null
            }
        };
    });
    const variant11Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant11ALT, testInstructions, 'ALT объект напрямую');

    // ВАРИАНТ 12: ALT с минимальным набором адресов (только 3 проблемных)
    console.log('\n🧪 ВАРИАНТ 12: ALT с минимальным набором адресов');
    console.log('-' .repeat(50));

    const problematicAddresses = [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // METEORA_DLMM_PROGRAM
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
        'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'  // MarginFi Program
    ];

    const variant12ALT = [{
        key: new PublicKey('HGmknUTUJhKKLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL'), // Фейковый ключ
        state: {
            addresses: problematicAddresses.map(addr => new PublicKey(addr)),
            deactivationSlot: BigInt('18446744073709551615'),
            lastExtendedSlot: 0,
            authority: null
        }
    }];
    const variant12Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant12ALT, testInstructions, 'ALT минимальный набор');

    // ВАРИАНТ 8: Дублированные ALT таблицы
    console.log('\n🧪 ВАРИАНТ 8: Дублированные ALT таблицы');
    console.log('-' .repeat(50));

    const variant8ALT = [...originalAltTables, ...originalAltTables];
    const variant8Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant8ALT, testInstructions, 'Дублированные ALT');

    // ВАРИАНТ 9: Пустые ALT таблицы
    console.log('\n🧪 ВАРИАНТ 9: Пустые ALT таблицы');
    console.log('-' .repeat(50));

    const variant9ALT = [];
    const variant9Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant9ALT, testInstructions, 'Пустые ALT');

    // ВАРИАНТ 10: Смешанный формат ALT
    console.log('\n🧪 ВАРИАНТ 10: Смешанный формат ALT');
    console.log('-' .repeat(50));

    const variant10ALT = [
        originalAltTables[0], // Простой объект
        variant2ALT[0] // AddressLookupTableAccount
    ].filter(Boolean);
    const variant10Result = await testALTCompressionWithRealInstructions(connection, wallet, blockhash, variant10ALT, testInstructions, 'Смешанный формат');

    // ИТОГОВЫЙ ОТЧЕТ ПО ВСЕМ ВАРИАНТАМ
    console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ ПО ВСЕМ 12 ВАРИАНТАМ');
    console.log('=' .repeat(80));

    const allResults = [
        variant1Result, variant2Result, variant3Result, variant4Result, variant5Result,
        variant6Result, variant7Result, variant8Result, variant9Result, variant10Result,
        variant11Result, variant12Result
    ];

    allResults.forEach((result, index) => {
        if (result.success) {
            console.log(`${index + 1}. ${result.variantName}: ${result.compression}% сжатие, ${result.altLookups} lookups`);
        } else {
            console.log(`${index + 1}. ${result.variantName}: ОШИБКА - ${result.error}`);
        }
    });

    // Находим лучший результат
    const successfulResults = allResults.filter(r => r.success);
    if (successfulResults.length > 0) {
        const bestResult = successfulResults.reduce((best, current) =>
            current.compression > best.compression ? current : best
        );

        console.log(`\n🏆 ЛУЧШИЙ РЕЗУЛЬТАТ: ${bestResult.variantName}`);
        console.log(`   🗜️ Сжатие: ${bestResult.compression}%`);
        console.log(`   📊 ALT lookups: ${bestResult.altLookups}`);
        console.log(`   📋 Размер: ${bestResult.sizeWithALT} байт`);
    }

    return { allResults, bestResult: successfulResults.length > 0 ? successfulResults.reduce((best, current) =>
        current.compression > best.compression ? current : best
    ) : null };
}

// Функция тестирования ALT сжатия с реальными инструкциями
async function testALTCompressionWithRealInstructions(connection, wallet, blockhash, altTables, realInstructions, variantName) {
    try {
        // Используем РЕАЛЬНЫЕ инструкции из основного кода
        const instructions = realInstructions.length > 0 ? realInstructions : [];

        if (instructions.length === 0) {
            console.log(`   ❌ ${variantName}: НЕТ ИНСТРУКЦИЙ ДЛЯ ТЕСТИРОВАНИЯ`);
            return { variantName, success: false, error: 'Нет инструкций' };
        }

        console.log(`   🔍 ${variantName}: Тестируем с ${instructions.length} конкретными инструкциями`);

        // 🔥 ДЕТАЛЬНЫЙ АНАЛИЗ КАЖДОГО АДРЕСА В ИНСТРУКЦИЯХ
        console.log(`   🔍 АНАЛИЗ АДРЕСОВ В ИНСТРУКЦИЯХ:`);
        const allAddresses = new Set();
        instructions.forEach((ix, ixIndex) => {
            allAddresses.add(ix.programId.toString());
            ix.keys.forEach(key => {
                allAddresses.add(key.pubkey.toString());
            });
        });

        console.log(`   📊 Уникальных адресов в инструкциях: ${allAddresses.size}`);

        // 🔥 ДЕТАЛЬНАЯ ПРОВЕРКА КАЖДОГО ИЗ 3 ПРОБЛЕМНЫХ АДРЕСОВ
        let foundInALT = 0;
        let notFoundInALT = 0;

        console.log(`   🔥 ДЕТАЛЬНАЯ ПРОВЕРКА 3 ПРОБЛЕМНЫХ АДРЕСОВ:`);

        allAddresses.forEach(addr => {
            let found = false;
            let foundPositions = [];

            altTables.forEach((alt, altIndex) => {
                const addressIndex = alt.state.addresses.findIndex(altAddr => altAddr.toString() === addr);
                if (addressIndex !== -1) {
                    foundPositions.push(`ALT[${altIndex}][${addressIndex}]`);
                    found = true;
                    foundInALT++;
                }
            });

            if (found) {
                console.log(`   ✅ ${addr.slice(0,8)}... найден в ${foundPositions.join(', ')}`);

                // 🔥 СПЕЦИАЛЬНАЯ ПРОВЕРКА ДЛЯ 3 ПРОБЛЕМНЫХ АДРЕСОВ
                if (addr === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
                    console.log(`      🔥 METEORA_DLMM_PROGRAM - ДОЛЖЕН СЖИМАТЬСЯ!`);
                } else if (addr === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                    console.log(`      🔥 TOKEN_PROGRAM - ДОЛЖЕН СЖИМАТЬСЯ!`);
                } else if (addr === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                    console.log(`      🔥 MARGINFI_PROGRAM - ДОЛЖЕН СЖИМАТЬСЯ!`);
                }
            } else {
                console.log(`   ❌ ${addr.slice(0,8)}... НЕ НАЙДЕН в ALT`);
                notFoundInALT++;
            }
        });

        console.log(`   📊 ИТОГО: ${foundInALT} найдено, ${notFoundInALT} не найдено в ALT`);

        // Компилируем без ALT
        const messageWithoutALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: instructions
        }).compileToLegacyMessage();

        const txWithoutALT = new VersionedTransaction(messageWithoutALT);
        const sizeWithoutALT = txWithoutALT.serialize().length;

        // Компилируем с ALT
        const messageWithALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: instructions
        }).compileToV0Message(altTables);

        const txWithALT = new VersionedTransaction(messageWithALT);
        const sizeWithALT = txWithALT.serialize().length;

        const compression = ((sizeWithoutALT - sizeWithALT) / sizeWithoutALT * 100).toFixed(2);

        console.log(`   📊 ${variantName}:`);
        console.log(`      Размер без ALT: ${sizeWithoutALT} байт`);
        console.log(`      Размер с ALT: ${sizeWithALT} байт`);
        console.log(`      Сжатие: ${compression}%`);
        console.log(`      ALT lookups: ${messageWithALT.addressTableLookups.length}`);
        console.log(`      ALT таблиц передано: ${altTables.length}`);

        // 🔥 КРИТИЧЕСКАЯ ДИАГНОСТИКА: ПОЧЕМУ ALT LOOKUPS = 0?
        if (messageWithALT.addressTableLookups.length === 0) {
            console.log(`   🔥 КРИТИЧЕСКАЯ ПРОБЛЕМА: ALT LOOKUPS = 0!`);
            console.log(`      📊 Static account keys: ${messageWithALT.staticAccountKeys.length}`);
            console.log(`      🔍 Static keys:`);
            messageWithALT.staticAccountKeys.forEach((key, index) => {
                console.log(`         [${index}] ${key.toString().slice(0,8)}...`);
            });
            console.log(`   🔥 ВСЕ АДРЕСА ОСТАЛИСЬ В STATIC KEYS - compileToV0Message() НЕ РАБОТАЕТ!`);
        }

        return {
            variantName,
            sizeWithoutALT,
            sizeWithALT,
            compression: parseFloat(compression),
            altLookups: messageWithALT.addressTableLookups.length,
            altTablesCount: altTables.length,
            success: true
        };

    } catch (error) {
        console.log(`   ❌ ${variantName}: ОШИБКА - ${error.message}`);
        return {
            variantName,
            success: false,
            error: error.message
        };
    }
}

// Функция тестирования ALT сжатия (старая версия для fallback)
async function testALTCompression(connection, wallet, blockhash, altTables, testAddresses, variantName) {
    try {
        // Создаем инструкции с тестовыми адресами
        const instructions = testAddresses.slice(0, 10).map((addr, index) => {
            return {
                programId: new PublicKey(addr),
                keys: [
                    { pubkey: wallet.publicKey, isSigner: true, isWritable: true },
                    { pubkey: new PublicKey(testAddresses[(index + 1) % testAddresses.length]), isSigner: false, isWritable: false }
                ],
                data: Buffer.from([index])
            };
        });

        // Компилируем без ALT
        const messageWithoutALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: instructions
        }).compileToLegacyMessage();

        const txWithoutALT = new VersionedTransaction(messageWithoutALT);
        const sizeWithoutALT = txWithoutALT.serialize().length;

        // Компилируем с ALT
        const messageWithALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: instructions
        }).compileToV0Message(altTables);

        const txWithALT = new VersionedTransaction(messageWithALT);
        const sizeWithALT = txWithALT.serialize().length;

        const compression = ((sizeWithoutALT - sizeWithALT) / sizeWithoutALT * 100).toFixed(2);

        console.log(`   📊 ${variantName}:`);
        console.log(`      Размер без ALT: ${sizeWithoutALT} байт`);
        console.log(`      Размер с ALT: ${sizeWithALT} байт`);
        console.log(`      Сжатие: ${compression}%`);
        console.log(`      ALT lookups: ${messageWithALT.addressTableLookups.length}`);
        console.log(`      ALT таблиц передано: ${altTables.length}`);

        return {
            variantName,
            sizeWithoutALT,
            sizeWithALT,
            compression: parseFloat(compression),
            altLookups: messageWithALT.addressTableLookups.length,
            altTablesCount: altTables.length,
            success: true
        };

    } catch (error) {
        console.log(`   ❌ ${variantName}: ОШИБКА - ${error.message}`);
        return {
            variantName,
            success: false,
            error: error.message
        };
    }
}

async function debugALTCompressionStepByStep() {
    console.log('🔍 ГЛУБОКАЯ ДИАГНОСТИКА ALT СЖАТИЯ - ПОЭТАПНЫЙ АНАЛИЗ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к Solana
        console.log('🔗 ШАГ 1: Подключение к Solana RPC...');
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        const wallet = Keypair.generate();
        
        const { blockhash } = await connection.getLatestBlockhash();
        console.log(`✅ Подключение успешно! Блок: ${blockhash.slice(0, 8)}...`);

        // 2. Загружаем локальные ALT данные из custom-alt-data.json
        console.log('\n📁 ШАГ 2: Загрузка локальных ALT данных из custom-alt-data.json...');

        const customAltFile = './custom-alt-data.json';
        if (!fs.existsSync(customAltFile)) {
            throw new Error(`❌ Custom ALT файл не найден: ${customAltFile}`);
        }

        const customAltData = JSON.parse(fs.readFileSync(customAltFile, 'utf8'));
        console.log(`✅ Custom ALT данные загружены: ${customAltData.totalTables} таблиц`);

        // 3. Создаем ALT таблицы из custom-alt-data.json
        console.log('\n🔄 ШАГ 3: Создание ALT таблиц из custom-alt-data.json...');
        const altTables = [];
        let totalAccounts = 0;

        // Загружаем ALT таблицы из custom-alt-data.json
        for (const [tableName, tableData] of Object.entries(customAltData.tables)) {
            const altAccount = {
                key: new PublicKey(tableData.address),
                state: {
                    addresses: tableData.addresses.map(addr => new PublicKey(addr)),
                    deactivationSlot: BigInt('18446744073709551615'), // MAX_U64
                    lastExtendedSlot: 0,
                    authority: null
                }
            };

            altTables.push(altAccount);
            totalAccounts += tableData.addresses.length;
            console.log(`✅ Custom ALT (${tableName}): ${tableData.address.slice(0, 8)}... (${tableData.addresses.length} аккаунтов)`);
        }

        console.log(`\n📊 ИТОГО ALT таблиц создано: ${altTables.length}`);
        console.log(`📊 Всего аккаунтов: ${totalAccounts}`);

        // 4. Создаем тестовые инструкции разной сложности
        console.log('\n🧪 ШАГ 4: Создание тестовых инструкций...');
        
        // Простая инструкция
        const simpleInstruction = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: wallet.publicKey,
            lamports: 1000
        });

        // Сложные инструкции (много аккаунтов)
        const complexInstructions = [];
        for (let i = 0; i < 10; i++) {
            complexInstructions.push(SystemProgram.transfer({
                fromPubkey: wallet.publicKey,
                toPubkey: Keypair.generate().publicKey,
                lamports: 1000 + i
            }));
        }

        console.log(`✅ Простых инструкций: 1`);
        console.log(`✅ Сложных инструкций: ${complexInstructions.length}`);

        // 5. ТЕСТ 1: Простая транзакция БЕЗ ALT
        console.log('\n🔍 ШАГ 5: ТЕСТ 1 - Простая транзакция БЕЗ ALT');
        console.log('-' .repeat(50));
        
        const messageWithoutALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [simpleInstruction]
        }).compileToLegacyMessage();

        const txWithoutALT = new VersionedTransaction(messageWithoutALT);
        const sizeWithoutALT = txWithoutALT.serialize().length;
        
        console.log(`📏 Размер БЕЗ ALT: ${sizeWithoutALT} байт`);
        console.log(`📋 Версия: ${txWithoutALT.version}`);

        // 6. ТЕСТ 2: Простая транзакция С ALT
        console.log('\n🔍 ШАГ 6: ТЕСТ 2 - Простая транзакция С ALT');
        console.log('-' .repeat(50));
        
        const messageWithALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [simpleInstruction]
        }).compileToV0Message(altTables);

        const txWithALT = new VersionedTransaction(messageWithALT);
        const sizeWithALT = txWithALT.serialize().length;
        
        console.log(`📏 Размер С ALT: ${sizeWithALT} байт`);
        console.log(`📋 Версия: ${txWithALT.version}`);
        console.log(`🗜️ ALT lookups: ${messageWithALT.addressTableLookups.length}`);

        // Анализ простого сжатия
        const simpleCompression = ((sizeWithoutALT - sizeWithALT) / sizeWithoutALT * 100).toFixed(2);
        console.log(`📊 Простое сжатие: ${simpleCompression}%`);

        // 7. ТЕСТ 3: Сложная транзакция БЕЗ ALT
        console.log('\n🔍 ШАГ 7: ТЕСТ 3 - Сложная транзакция БЕЗ ALT');
        console.log('-' .repeat(50));
        
        const complexMessageWithoutALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: complexInstructions
        }).compileToLegacyMessage();

        const complexTxWithoutALT = new VersionedTransaction(complexMessageWithoutALT);
        const complexSizeWithoutALT = complexTxWithoutALT.serialize().length;
        
        console.log(`📏 Размер БЕЗ ALT: ${complexSizeWithoutALT} байт`);
        console.log(`📋 Инструкций: ${complexInstructions.length}`);

        // 8. ТЕСТ 4: Сложная транзакция С ALT
        console.log('\n🔍 ШАГ 8: ТЕСТ 4 - Сложная транзакция С ALT');
        console.log('-' .repeat(50));
        
        const complexMessageWithALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: complexInstructions
        }).compileToV0Message(altTables);

        const complexTxWithALT = new VersionedTransaction(complexMessageWithALT);
        const complexSizeWithALT = complexTxWithALT.serialize().length;
        
        console.log(`📏 Размер С ALT: ${complexSizeWithALT} байт`);
        console.log(`📋 Версия: ${complexTxWithALT.version}`);
        console.log(`🗜️ ALT lookups: ${complexMessageWithALT.addressTableLookups.length}`);

        // Анализ сложного сжатия
        const complexCompression = ((complexSizeWithoutALT - complexSizeWithALT) / complexSizeWithoutALT * 100).toFixed(2);
        console.log(`📊 Сложное сжатие: ${complexCompression}%`);

        // 9. ДЕТАЛЬНЫЙ АНАЛИЗ ALT LOOKUPS
        console.log('\n🔍 ШАГ 9: ДЕТАЛЬНЫЙ АНАЛИЗ ALT LOOKUPS');
        console.log('-' .repeat(50));
        
        console.log(`📊 ALT таблиц передано: ${altTables.length}`);
        console.log(`📊 ALT lookups в простой транзакции: ${messageWithALT.addressTableLookups.length}`);
        console.log(`📊 ALT lookups в сложной транзакции: ${complexMessageWithALT.addressTableLookups.length}`);

        // Анализируем каждый lookup
        complexMessageWithALT.addressTableLookups.forEach((lookup, index) => {
            console.log(`   ALT ${index + 1}:`);
            console.log(`      Адрес: ${lookup.accountKey.toString().slice(0, 8)}...`);
            console.log(`      Writable indexes: ${lookup.writableIndexes.length}`);
            console.log(`      Readonly indexes: ${lookup.readonlyIndexes.length}`);
        });

        // 10. ПРОВЕРКА ПОКРЫТИЯ АККАУНТОВ
        console.log('\n🔍 ШАГ 10: ПРОВЕРКА ПОКРЫТИЯ АККАУНТОВ');
        console.log('-' .repeat(50));
        
        // Собираем все аккаунты из инструкций
        const allAccountsInInstructions = new Set();
        complexInstructions.forEach(ix => {
            ix.keys.forEach(key => {
                allAccountsInInstructions.add(key.pubkey.toString());
            });
            allAccountsInInstructions.add(ix.programId.toString());
        });
        
        // Собираем все аккаунты из ALT таблиц
        const allAccountsInALT = new Set();
        altTables.forEach(alt => {
            alt.state.addresses.forEach(addr => {
                allAccountsInALT.add(addr.toString());
            });
        });
        
        console.log(`📊 Аккаунтов в инструкциях: ${allAccountsInInstructions.size}`);
        console.log(`📊 Аккаунтов в ALT таблицах: ${allAccountsInALT.size}`);
        
        // Проверяем пересечения
        const coveredAccounts = [];
        const uncoveredAccounts = [];
        
        allAccountsInInstructions.forEach(account => {
            if (allAccountsInALT.has(account)) {
                coveredAccounts.push(account);
            } else {
                uncoveredAccounts.push(account);
            }
        });
        
        console.log(`✅ Покрытых аккаунтов: ${coveredAccounts.length}`);
        console.log(`❌ Непокрытых аккаунтов: ${uncoveredAccounts.length}`);
        
        if (uncoveredAccounts.length > 0) {
            console.log(`⚠️ Непокрытые аккаунты:`);
            uncoveredAccounts.slice(0, 5).forEach(account => {
                console.log(`   ${account.slice(0, 8)}...`);
            });
            if (uncoveredAccounts.length > 5) {
                console.log(`   ... и еще ${uncoveredAccounts.length - 5}`);
            }
        }

        // 11. ИТОГОВЫЙ ДИАГНОЗ
        console.log('\n🎯 ШАГ 11: ИТОГОВЫЙ ДИАГНОЗ');
        console.log('=' .repeat(50));
        
        console.log(`📊 РЕЗУЛЬТАТЫ СЖАТИЯ:`);
        console.log(`   Простая транзакция: ${simpleCompression}%`);
        console.log(`   Сложная транзакция: ${complexCompression}%`);
        console.log(`   Размер сложной транзакции: ${complexSizeWithALT} байт`);
        console.log(`   Лимит Solana: 1232 байт`);
        
        if (complexSizeWithALT > 1232) {
            console.log(`❌ ПРОБЛЕМА: Транзакция превышает лимит на ${complexSizeWithALT - 1232} байт`);
        } else {
            console.log(`✅ Транзакция помещается в лимит`);
        }
        
        console.log(`\n🔍 АНАЛИЗ ALT ЭФФЕКТИВНОСТИ:`);
        console.log(`   ALT таблиц доступно: ${altTables.length}`);
        console.log(`   ALT таблиц используется: ${complexMessageWithALT.addressTableLookups.length}`);
        console.log(`   Покрытие аккаунтов: ${(coveredAccounts.length / allAccountsInInstructions.size * 100).toFixed(1)}%`);
        
        if (complexMessageWithALT.addressTableLookups.length === 0) {
            console.log(`❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: ALT таблицы НЕ ИСПОЛЬЗУЮТСЯ!`);
            console.log(`💡 ПРИЧИНА: Аккаунты в инструкциях не совпадают с ALT таблицами`);
        } else if (complexCompression < 10) {
            console.log(`⚠️ ПРОБЛЕМА: ALT сжатие слишком слабое (${complexCompression}%)`);
            console.log(`💡 ПРИЧИНА: Мало аккаунтов покрыто ALT таблицами`);
        } else {
            console.log(`✅ ALT сжатие работает эффективно`);
        }

        // 12. ЗАПУСК 10 ВАРИАНТОВ ТЕСТИРОВАНИЯ
        console.log(`\n🔥 ШАГ 12: ЗАПУСК 10 ВАРИАНТОВ ТЕСТИРОВАНИЯ ALT СЖАТИЯ`);
        console.log('=' .repeat(80));

        await test10ALTCompressionVariants(connection, wallet, blockhash, altTables, customAltData);

        // 13. РЕКОМЕНДАЦИИ
        console.log(`\n💡 РЕКОМЕНДАЦИИ:`);

        if (complexMessageWithALT.addressTableLookups.length === 0) {
            console.log(`   1. Проверить что аккаунты в инструкциях есть в ALT таблицах`);
            console.log(`   2. Убедиться что ALT таблицы правильно загружены`);
            console.log(`   3. Проверить формат AddressLookupTableAccount объектов`);
        }

        if (uncoveredAccounts.length > 0) {
            console.log(`   4. Добавить недостающие аккаунты в ALT таблицы`);
            console.log(`   5. Создать специализированную ALT таблицу для этого типа транзакций`);
        }

        if (complexSizeWithALT > 1232) {
            console.log(`   6. Уменьшить количество инструкций в транзакции`);
            console.log(`   7. Разбить транзакцию на несколько частей`);
            console.log(`   8. Оптимизировать размер данных в инструкциях`);
        }

    } catch (error) {
        console.error('❌ Ошибка диагностики:', error.message);
        console.error(error.stack);
    }
}

// Запуск диагностики
if (require.main === module) {
    debugALTCompressionStepByStep().catch(console.error);
}

module.exports = { debugALTCompressionStepByStep };
