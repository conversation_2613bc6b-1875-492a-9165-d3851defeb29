#!/usr/bin/env python3
"""
Immunefi Mass Scanner CLI
Интерфейс командной строки для управления массовым тестированием
"""

import asyncio
import click
import json
import sys
from pathlib import Path
from datetime import datetime
from immunefi_mass_tester import ImmunefiBountyMassTester
from immunefi_prioritizer import ImmunefiBountyPrioritizer

@click.group()
@click.version_option(version='1.0.0')
def cli():
    """Immunefi Mass Bug Bounty Scanner - CLI интерфейс для массового тестирования программ Immunefi"""
    pass

@cli.command()
@click.option('--programs', '-p', default=0, help='Количество программ для тестирования (0 = все)')
@click.option('--time-limit', '-t', default=0.0, help='Лимит времени в часах (0 = без лимита)')
@click.option('--concurrent', '-c', default=5, help='Количество параллельных тестов')
@click.option('--delay', '-d', default=1.0, help='Задержка между запросами (сек)')
@click.option('--output', '-o', default='', help='Директория для сохранения результатов')
def scan(programs, time_limit, concurrent, delay, output):
    """Запуск массового сканирования программ Immunefi"""
    click.echo(f"🚀 Запуск массового сканирования Immunefi...")
    click.echo(f"📊 Параметры:")
    click.echo(f"   - Программы: {'все' if programs == 0 else programs}")
    click.echo(f"   - Лимит времени: {'без лимита' if time_limit == 0 else f'{time_limit} часов'}")
    click.echo(f"   - Параллельные потоки: {concurrent}")
    click.echo(f"   - Задержка: {delay} сек")
    
    if output:
        output_dir = Path(output)
        output_dir.mkdir(exist_ok=True)
        click.echo(f"   - Результаты: {output_dir.absolute()}")
    
    async def run_scan():
        try:
            async with ImmunefiBountyMassTester(
                max_concurrent=concurrent,
                rate_limit_delay=delay
            ) as mass_tester:
                
                # Запуск тестирования
                session = await mass_tester.start_mass_testing(
                    target_programs=programs if programs > 0 else 0,
                    time_limit_hours=time_limit if time_limit > 0 else 0
                )
                
                # Генерация отчета
                report = mass_tester.generate_final_report()
                
                # Сохранение результатов
                if output:
                    report_file = output_dir / f"report_{session.session_id}.md"
                    with open(report_file, 'w', encoding='utf-8') as f:
                        f.write(report)
                    click.echo(f"📄 Отчет сохранен: {report_file}")
                
                # Вывод статистики
                click.echo(f"\n✅ Сканирование завершено!")
                click.echo(f"🎯 Результаты:")
                click.echo(f"   - Протестировано программ: {session.tested_programs}")
                click.echo(f"   - Найдено уязвимостей: {session.vulnerabilities_found}")
                click.echo(f"   - Критических: {session.critical_vulns}")
                click.echo(f"   - Высокой серьезности: {session.high_severity_vulns}")
                
        except KeyboardInterrupt:
            click.echo("\n⚠️ Сканирование прервано пользователем")
        except Exception as e:
            click.echo(f"\n❌ Ошибка сканирования: {e}")
            sys.exit(1)
    
    asyncio.run(run_scan())

@cli.command()
@click.option('--input', '-i', required=True, help='JSON файл с программами')
@click.option('--output', '-o', default='prioritized_programs.json', help='Файл для сохранения результатов')
@click.option('--top', '-t', default=20, help='Количество топ программ для вывода')
def prioritize(input, output, top):
    """Приоритизация программ bug bounty"""
    click.echo(f"📊 Приоритизация программ из {input}...")
    
    try:
        # Загрузка программ
        with open(input, 'r', encoding='utf-8') as f:
            programs_data = json.load(f)
        
        click.echo(f"📋 Загружено {len(programs_data)} программ")
        
        # Приоритизация
        prioritizer = ImmunefiBountyPrioritizer()
        prioritizer.load_programs(programs_data)
        scored_programs = prioritizer.calculate_priority_scores()
        
        # Сохранение результатов
        prioritizer.save_prioritized_programs(output)
        
        # Вывод топ программ
        top_programs = prioritizer.get_top_programs(top)
        
        click.echo(f"\n🏆 Топ-{top} приоритетных программ:")
        click.echo("-" * 80)
        
        for i, program in enumerate(top_programs, 1):
            click.echo(f"{i:2d}. {program.program_name}")
            click.echo(f"    Оценка: {program.total_score:.3f} | "
                      f"Награда: {program.reward_score:.3f} | "
                      f"Сложность: {program.complexity_score:.3f}")
            click.echo(f"    Вероятность успеха: {program.success_probability:.1%} | "
                      f"Время: {program.estimated_time_hours:.1f}ч")
            click.echo()
        
        click.echo(f"✅ Результаты сохранены в {output}")
        
    except FileNotFoundError:
        click.echo(f"❌ Файл {input} не найден")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Ошибка приоритизации: {e}")
        sys.exit(1)

@cli.command()
@click.option('--session', '-s', help='ID сессии для анализа')
@click.option('--vulnerabilities', '-v', help='JSON файл с уязвимостями')
def analyze(session, vulnerabilities):
    """Анализ результатов тестирования"""
    click.echo("📈 Анализ результатов тестирования...")
    
    try:
        # Поиск файлов результатов
        if session:
            vuln_file = f"vulnerabilities_{session}.json"
            session_file = f"session_{session}.json"
        elif vulnerabilities:
            vuln_file = vulnerabilities
            session_file = None
        else:
            # Поиск последних файлов
            vuln_files = list(Path('.').glob('vulnerabilities_*.json'))
            if not vuln_files:
                click.echo("❌ Файлы с уязвимостями не найдены")
                sys.exit(1)
            vuln_file = max(vuln_files, key=lambda x: x.stat().st_mtime)
            session_file = None
        
        # Загрузка уязвимостей
        with open(vuln_file, 'r', encoding='utf-8') as f:
            vulnerabilities_data = json.load(f)
        
        click.echo(f"📋 Загружено {len(vulnerabilities_data)} уязвимостей")
        
        # Анализ по серьезности
        severity_counts = {}
        program_counts = {}
        
        for vuln in vulnerabilities_data:
            severity = vuln.get('severity', 'Unknown')
            program = vuln.get('program_name', 'Unknown')
            
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            program_counts[program] = program_counts.get(program, 0) + 1
        
        # Вывод статистики
        click.echo(f"\n📊 Распределение по серьезности:")
        for severity, count in sorted(severity_counts.items(), 
                                    key=lambda x: ['Critical', 'High', 'Medium', 'Low'].index(x[0]) 
                                    if x[0] in ['Critical', 'High', 'Medium', 'Low'] else 999):
            click.echo(f"   {severity}: {count}")
        
        click.echo(f"\n🎯 Топ программы с уязвимостями:")
        sorted_programs = sorted(program_counts.items(), key=lambda x: x[1], reverse=True)
        for i, (program, count) in enumerate(sorted_programs[:10], 1):
            click.echo(f"   {i:2d}. {program}: {count} уязвимостей")
        
        # Загрузка данных сессии если доступны
        if session_file and Path(session_file).exists():
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            click.echo(f"\n⏱️ Информация о сессии:")
            click.echo(f"   Статус: {session_data.get('status', 'Unknown')}")
            click.echo(f"   Протестировано программ: {session_data.get('tested_programs', 0)}")
            click.echo(f"   Всего программ: {session_data.get('total_programs', 0)}")
            
            if 'stats' in session_data:
                stats = session_data['stats']
                click.echo(f"   Программ в час: {stats.get('programs_per_hour', 0):.1f}")
                click.echo(f"   Уязвимостей в час: {stats.get('vulnerabilities_per_hour', 0):.1f}")
        
    except FileNotFoundError as e:
        click.echo(f"❌ Файл не найден: {e}")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Ошибка анализа: {e}")
        sys.exit(1)

@cli.command()
@click.option('--min-reward', default=0.0, help='Минимальная оценка награды (0-1)')
@click.option('--max-time', default=40.0, help='Максимальное время тестирования (часы)')
@click.option('--min-success', default=0.3, help='Минимальная вероятность успеха (0-1)')
@click.option('--input', '-i', default='prioritized_programs.json', help='Файл с приоритизированными программами')
def filter(min_reward, max_time, min_success, input):
    """Фильтрация программ по критериям"""
    click.echo(f"🔍 Фильтрация программ по критериям...")
    click.echo(f"   Минимальная награда: {min_reward}")
    click.echo(f"   Максимальное время: {max_time} часов")
    click.echo(f"   Минимальная вероятность успеха: {min_success}")
    
    try:
        # Загрузка приоритизированных программ
        with open(input, 'r', encoding='utf-8') as f:
            programs_data = json.load(f)
        
        # Фильтрация
        filtered_programs = []
        for program in programs_data:
            if (program.get('reward_score', 0) >= min_reward and
                program.get('estimated_time_hours', 999) <= max_time and
                program.get('success_probability', 0) >= min_success):
                filtered_programs.append(program)
        
        click.echo(f"\n📋 Найдено {len(filtered_programs)} программ, соответствующих критериям:")
        click.echo("-" * 80)
        
        for i, program in enumerate(filtered_programs[:20], 1):  # Показываем первые 20
            click.echo(f"{i:2d}. {program.get('program_name', 'Unknown')}")
            click.echo(f"    Награда: {program.get('reward_score', 0):.3f} | "
                      f"Время: {program.get('estimated_time_hours', 0):.1f}ч | "
                      f"Успех: {program.get('success_probability', 0):.1%}")
        
        if len(filtered_programs) > 20:
            click.echo(f"... и еще {len(filtered_programs) - 20} программ")
        
        # Сохранение отфильтрованного списка
        output_file = f"filtered_programs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_programs, f, indent=2, ensure_ascii=False)
        
        click.echo(f"\n✅ Отфильтрованный список сохранен в {output_file}")
        
    except FileNotFoundError:
        click.echo(f"❌ Файл {input} не найден")
        sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Ошибка фильтрации: {e}")
        sys.exit(1)

@cli.command()
def status():
    """Показать статус последней сессии"""
    click.echo("📊 Статус последней сессии тестирования...")
    
    try:
        # Поиск последних файлов сессии
        session_files = list(Path('.').glob('session_*.json'))
        if not session_files:
            click.echo("❌ Файлы сессий не найдены")
            return
        
        latest_session = max(session_files, key=lambda x: x.stat().st_mtime)
        
        with open(latest_session, 'r', encoding='utf-8') as f:
            session_data = json.load(f)
        
        click.echo(f"\n📋 Сессия: {session_data.get('session_id', 'Unknown')}")
        click.echo(f"🕐 Начало: {session_data.get('start_time', 'Unknown')}")
        click.echo(f"🏁 Конец: {session_data.get('end_time', 'В процессе')}")
        click.echo(f"📊 Статус: {session_data.get('status', 'Unknown')}")
        click.echo(f"🎯 Прогресс: {session_data.get('tested_programs', 0)}/{session_data.get('total_programs', 0)}")
        click.echo(f"🐛 Уязвимости: {session_data.get('vulnerabilities_found', 0)}")
        click.echo(f"🔴 Критические: {session_data.get('critical_vulns', 0)}")
        click.echo(f"🟠 Высокие: {session_data.get('high_severity_vulns', 0)}")
        
        if 'stats' in session_data:
            stats = session_data['stats']
            click.echo(f"\n⚡ Производительность:")
            click.echo(f"   Программ/час: {stats.get('programs_per_hour', 0):.1f}")
            click.echo(f"   Уязвимостей/час: {stats.get('vulnerabilities_per_hour', 0):.1f}")
            click.echo(f"   Успешность: {stats.get('success_rate', 0):.1%}")
        
    except Exception as e:
        click.echo(f"❌ Ошибка получения статуса: {e}")

@cli.command()
def list_files():
    """Показать список файлов результатов"""
    click.echo("📁 Файлы результатов в текущей директории:")
    
    file_patterns = [
        ('programs_*.json', 'Списки программ'),
        ('prioritized_programs_*.json', 'Приоритизированные программы'),
        ('vulnerabilities_*.json', 'Найденные уязвимости'),
        ('session_*.json', 'Данные сессий'),
        ('final_report_*.md', 'Финальные отчеты'),
        ('*.log', 'Лог файлы'),
    ]
    
    for pattern, description in file_patterns:
        files = list(Path('.').glob(pattern))
        if files:
            click.echo(f"\n{description}:")
            for file in sorted(files, key=lambda x: x.stat().st_mtime, reverse=True):
                size = file.stat().st_size
                mtime = datetime.fromtimestamp(file.stat().st_mtime)
                click.echo(f"  📄 {file.name} ({size:,} байт, {mtime.strftime('%Y-%m-%d %H:%M')})")

if __name__ == '__main__':
    cli()
