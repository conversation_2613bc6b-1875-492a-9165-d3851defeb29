/**
 * 🚫 ГЛОБАЛЬНЫЙ МЕНЕДЖЕР ДЕДУПЛИКАЦИИ ИНСТРУКЦИЙ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Полное устранение дублированных инструкций
 * 🚨 ПРОБЛЕМА: Transaction contains a duplicate instruction (8) that is not allowed
 * ✅ РЕШЕНИЕ: Агрессивная дедупликация на всех уровнях
 */

const crypto = require('crypto');

class GlobalDeduplicationManager {
  constructor() {
    this.globalInstructionHashes = new Set();
    this.instructionCounter = 0;
    console.log('🚫 Глобальный менеджер дедупликации инициализирован');
  }

  /**
   * 🔍 СОЗДАНИЕ ДЕТАЛЬНОГО ХЕША ИНСТРУКЦИИ
   */
  createDetailedInstructionHash(instruction) {
    try {
      // Получаем все компоненты инструкции
      const programId = instruction.programId ? instruction.programId.toString() : 'unknown';
      
      // Обрабатываем data
      let dataHash = '';
      if (instruction.data) {
        if (Buffer.isBuffer(instruction.data)) {
          dataHash = instruction.data.toString('hex');
        } else if (Array.isArray(instruction.data)) {
          dataHash = Buffer.from(instruction.data).toString('hex');
        } else {
          dataHash = instruction.data.toString();
        }
      }
      
      // Обрабатываем keys
      let keysHash = '';
      if (instruction.keys && Array.isArray(instruction.keys)) {
        keysHash = instruction.keys.map(key => {
          const pubkey = key.pubkey ? key.pubkey.toString() : 'unknown';
          const isSigner = key.isSigner ? '1' : '0';
          const isWritable = key.isWritable ? '1' : '0';
          return `${pubkey}:${isSigner}:${isWritable}`;
        }).join('|');
      }
      
      // Создаем составной хеш
      const compositeString = `${programId}:${dataHash}:${keysHash}`;
      const hash = crypto.createHash('sha256').update(compositeString).digest('hex');
      
      return hash;
      
    } catch (error) {
      console.error(`❌ Ошибка создания хеша инструкции: ${error.message}`);
      return `fallback_${Math.random().toString(36)}`;
    }
  }

  /**
   * 🔍 СПЕЦИАЛЬНАЯ ОБРАБОТКА COMPUTE BUDGET ИНСТРУКЦИЙ
   */
  getComputeBudgetType(instruction) {
    try {
      if (!instruction.data) return 'unknown';
      
      let dataLength = 0;
      if (Buffer.isBuffer(instruction.data)) {
        dataLength = instruction.data.length;
      } else if (Array.isArray(instruction.data)) {
        dataLength = instruction.data.length;
      }
      
      // Определяем тип по размеру data согласно официальной документации
      switch (dataLength) {
        case 5: return 'SetComputeUnitLimit';
        case 9: return 'SetComputeUnitPrice';
        case 4: return 'RequestHeapFrame';
        default: return `Unknown_${dataLength}`;
      }
      
    } catch (error) {
      console.error(`❌ Ошибка определения типа ComputeBudget: ${error.message}`);
      return 'error';
    }
  }

  /**
   * 🚫 АГРЕССИВНАЯ ДЕДУПЛИКАЦИЯ ИНСТРУКЦИЙ
   */
  deduplicateInstructions(instructions, context = 'UNKNOWN') {
    try {
      console.log(`\n🚫 ===== ГЛОБАЛЬНАЯ ДЕДУПЛИКАЦИЯ ${context} =====`);

      // 🔍 ПРОВЕРКА ТИПА ИНСТРУКЦИЙ
      if (!instructions || !Array.isArray(instructions)) {
        console.log(`❌ Ошибка: instructions не является массивом (тип: ${typeof instructions})`);
        console.log(`📋 Возвращаем пустой массив`);
        return [];
      }

      if (instructions.length === 0) {
        console.log(`ℹ️ Пустой массив инструкций, возвращаем пустой массив`);
        return [];
      }

      console.log(`📋 Исходных инструкций: ${instructions.length}`);

      const uniqueInstructions = [];
      const localHashes = new Set();
      const computeBudgetTypes = new Set();

      let duplicatesRemoved = 0;
      let computeBudgetDuplicates = 0;

      instructions.forEach((instruction, index) => {
        const programId = instruction.programId ? instruction.programId.toString() : 'unknown';
        
        // 🚨 СПЕЦИАЛЬНАЯ ОБРАБОТКА COMPUTE BUDGET PROGRAM
        if (programId === 'ComputeBudget111111111111111111111111111111') {
          const computeType = this.getComputeBudgetType(instruction);
          const computeKey = `ComputeBudget_${computeType}`;

          if (!computeBudgetTypes.has(computeKey)) {
            computeBudgetTypes.add(computeKey);
            uniqueInstructions.push(instruction);
            console.log(`✅ ComputeBudget #${index}: ${computeType} (первая)`);
          } else {
            computeBudgetDuplicates++;
            console.log(`🚫 ComputeBudget #${index}: ${computeType} (дубликат удален)`);
          }
          return;
        }

        // 🚨 СПЕЦИАЛЬНАЯ ОБРАБОТКА METEORA DLMM PROGRAM
        if (programId.startsWith('LBUZKhRx')) {
          // Meteora swap инструкции НИКОГДА не являются дубликатами
          // Каждая swap инструкция уникальна по своей природе
          uniqueInstructions.push(instruction);
          console.log(`✅ Meteora #${index}: ${programId.slice(0, 8)}... (${instruction.keys?.length || 0} аккаунтов) - ВСЕГДА УНИКАЛЬНА`);
          return;
        }
        
        // 🔍 ОБЫЧНАЯ ДЕДУПЛИКАЦИЯ ДЛЯ ОСТАЛЬНЫХ ИНСТРУКЦИЙ
        const hash = this.createDetailedInstructionHash(instruction);
        
        if (!localHashes.has(hash) && !this.globalInstructionHashes.has(hash)) {
          localHashes.add(hash);
          this.globalInstructionHashes.add(hash);
          uniqueInstructions.push(instruction);
          this.instructionCounter++;
          console.log(`✅ Инструкция #${index}: ${programId.slice(0, 8)}... (уникальная)`);
        } else {
          duplicatesRemoved++;
          console.log(`🚫 Инструкция #${index}: ${programId.slice(0, 8)}... (дубликат удален)`);
        }
      });
      
      console.log(`\n📊 РЕЗУЛЬТАТЫ ДЕДУПЛИКАЦИИ ${context}:`);
      console.log(`   ✅ Уникальных инструкций: ${uniqueInstructions.length}`);
      console.log(`   🚫 Удалено дубликатов: ${duplicatesRemoved}`);
      console.log(`   🚫 ComputeBudget дубликатов: ${computeBudgetDuplicates}`);
      console.log(`   📊 Всего удалено: ${duplicatesRemoved + computeBudgetDuplicates}`);
      console.log(`   🎯 Эффективность: ${((duplicatesRemoved + computeBudgetDuplicates) / instructions.length * 100).toFixed(1)}%`);
      
      // 🔥 ВОЗВРАЩАЕМ ТОЛЬКО МАССИВ ИНСТРУКЦИЙ ДЛЯ СОВМЕСТИМОСТИ
      console.log(`\n✅ Возвращаем ${uniqueInstructions.length} дедуплицированных инструкций`);
      return uniqueInstructions;
      
    } catch (error) {
      console.error(`❌ Ошибка глобальной дедупликации: ${error.message}`);
      console.log(`🔄 Возвращаем исходные инструкции без дедупликации`);
      return instructions || [];
    }
  }

  /**
   * 🔍 ФИНАЛЬНАЯ ПРОВЕРКА НА ДУБЛИКАТЫ
   */
  validateNoDuplicates(instructions, context = 'FINAL') {
    try {
      console.log(`\n🔍 ===== ФИНАЛЬНАЯ ПРОВЕРКА ДУБЛИКАТОВ ${context} =====`);

      // 🔍 ПРОВЕРКА ТИПА ИНСТРУКЦИЙ
      if (!instructions || !Array.isArray(instructions)) {
        console.log(`❌ Ошибка: instructions не является массивом (тип: ${typeof instructions})`);
        return [];
      }

      if (instructions.length === 0) {
        console.log(`ℹ️ Пустой массив инструкций, нечего проверять`);
        return [];
      }

      console.log(`📋 Проверяем ${instructions.length} инструкций на дубликаты...`);

      const hashes = new Set();
      const computeBudgetTypes = new Set();
      const duplicates = [];

      instructions.forEach((instruction, index) => {
        const programId = instruction.programId ? instruction.programId.toString() : 'unknown';
        
        if (programId === 'ComputeBudget111111111111111111111111111111') {
          const computeType = this.getComputeBudgetType(instruction);
          if (computeBudgetTypes.has(computeType)) {
            duplicates.push({
              index: index,
              type: 'ComputeBudget',
              subtype: computeType,
              programId: programId
            });
          } else {
            computeBudgetTypes.add(computeType);
          }
        } else {
          const hash = this.createDetailedInstructionHash(instruction);
          if (hashes.has(hash)) {
            duplicates.push({
              index: index,
              type: 'Regular',
              hash: hash.slice(0, 16),
              programId: programId
            });
          } else {
            hashes.add(hash);
          }
        }
      });
      
      console.log(`📊 РЕЗУЛЬТАТЫ ПРОВЕРКИ:`);
      console.log(`   📋 Всего инструкций: ${instructions.length}`);
      console.log(`   🔍 Уникальных хешей: ${hashes.size}`);
      console.log(`   🔍 ComputeBudget типов: ${computeBudgetTypes.size}`);
      console.log(`   🚫 Найдено дубликатов: ${duplicates.length}`);
      
      if (duplicates.length > 0) {
        console.log(`\n🚨 НАЙДЕНЫ ДУБЛИКАТЫ:`);
        duplicates.forEach(dup => {
          console.log(`   🚫 #${dup.index}: ${dup.type} (${dup.subtype || dup.hash}) - ${dup.programId.slice(0, 8)}...`);
        });
        return false;
      } else {
        console.log(`✅ ВСЕ ИНСТРУКЦИИ УНИКАЛЬНЫ!`);
        return true;
      }
      
    } catch (error) {
      console.error(`❌ Ошибка проверки дубликатов: ${error.message}`);
      return false;
    }
  }

  /**
   * 🧹 ОЧИСТКА ГЛОБАЛЬНОГО КЭША
   */
  clearGlobalCache() {
    this.globalInstructionHashes.clear();
    this.instructionCounter = 0;
    console.log('🧹 Глобальный кэш инструкций очищен');
  }

  /**
   * 📊 СТАТИСТИКА ГЛОБАЛЬНОГО КЭША
   */
  getGlobalStats() {
    return {
      totalUniqueInstructions: this.globalInstructionHashes.size,
      instructionCounter: this.instructionCounter
    };
  }

  /**
   * 🚫 ГЛАВНЫЙ МЕТОД: ПОЛНАЯ ДЕДУПЛИКАЦИЯ
   */
  performFullDeduplication(instructions, context = 'MAIN') {
    try {
      console.log(`\n🚫 ===== ПОЛНАЯ ДЕДУПЛИКАЦИЯ ${context} =====`);
      
      // 1. Агрессивная дедупликация
      const deduplicationResult = this.deduplicateInstructions(instructions, context);
      
      // 2. Финальная проверка
      const isValid = this.validateNoDuplicates(deduplicationResult.instructions, context);
      
      // 3. Если все еще есть дубликаты - применяем экстренные меры
      if (!isValid) {
        console.log(`🚨 ЭКСТРЕННЫЕ МЕРЫ: Применяем дополнительную очистку...`);
        
        const emergencyResult = this.emergencyDeduplication(deduplicationResult.instructions);
        const emergencyValid = this.validateNoDuplicates(emergencyResult.instructions, `${context}_EMERGENCY`);
        
        return {
          instructions: emergencyResult.instructions,
          isValid: emergencyValid,
          stats: {
            ...deduplicationResult.stats,
            emergencyApplied: true,
            emergencyRemoved: emergencyResult.stats.totalRemoved
          }
        };
      }
      
      return {
        instructions: deduplicationResult.instructions,
        isValid: isValid,
        stats: deduplicationResult.stats
      };
      
    } catch (error) {
      console.error(`❌ Ошибка полной дедупликации: ${error.message}`);
      return {
        instructions: instructions,
        isValid: false,
        stats: { error: error.message }
      };
    }
  }

  /**
   * 🚨 ЭКСТРЕННАЯ ДЕДУПЛИКАЦИЯ
   */
  emergencyDeduplication(instructions) {
    try {
      console.log(`🚨 ЭКСТРЕННАЯ ДЕДУПЛИКАЦИЯ: ${instructions.length} инструкций`);
      
      const emergencyUnique = [];
      const seenSignatures = new Set();
      let removed = 0;
      
      instructions.forEach((instruction, index) => {
        // Создаем упрощенную сигнатуру инструкции
        const programId = instruction.programId ? instruction.programId.toString() : 'unknown';
        const dataLength = instruction.data ? instruction.data.length : 0;
        const keysCount = instruction.keys ? instruction.keys.length : 0;
        
        const signature = `${programId}:${dataLength}:${keysCount}`;
        
        if (!seenSignatures.has(signature)) {
          seenSignatures.add(signature);
          emergencyUnique.push(instruction);
        } else {
          removed++;
          console.log(`🚨 ЭКСТРЕННО УДАЛЕН #${index}: ${programId.slice(0, 8)}...`);
        }
      });
      
      console.log(`🚨 ЭКСТРЕННАЯ ДЕДУПЛИКАЦИЯ ЗАВЕРШЕНА: ${emergencyUnique.length} уникальных, ${removed} удалено`);
      
      return {
        instructions: emergencyUnique,
        stats: {
          original: instructions.length,
          unique: emergencyUnique.length,
          totalRemoved: removed
        }
      };
      
    } catch (error) {
      console.error(`❌ Ошибка экстренной дедупликации: ${error.message}`);
      return {
        instructions: instructions,
        stats: { error: error.message }
      };
    }
  }
}

module.exports = GlobalDeduplicationManager;
