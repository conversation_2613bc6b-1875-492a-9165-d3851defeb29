{"timestamp": "2025-07-20T18:46:38.489Z", "summary": {"totalReal": 13, "coveredReal": 10, "uncoveredReal": 3, "uncoveredRealAddresses": ["izePositionAndAddLiquidityByStrategy", "AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"], "totalStatic": 34, "coveredStatic": 25, "uncoveredStatic": 9, "uncoveredStaticAddresses": ["LBUZKhRxUjRp3gR2luWZ8jQECnNK1qhNRNkTmhdzNmx", "Dbw8mACQKJULjhVzr6McbWCo9doWaVYPeFPbNJVE5B8s", "3WMYy9V9Lp7Fh8Qj2KxGvN5RtEuP4CzXsA6BdMnHkL9w", "HZzNfgApKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "8A4Crui8VnBpKjUvQr5WxE2YtRpLm3CzXsA6BdMnHkL9", "2mGnsXcGKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "GBDuzqBgKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "7xmtz8hDKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF"], "totalDynamic": 4, "altTableSize": 587, "realAddresses": ["bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV", "3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU", "izePositionAndAddLiquidityByStrategy", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto", "DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H", "4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb", "ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj", "EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o", "CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz", "59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li"]}, "uncoveredRealAddresses": ["izePositionAndAddLiquidityByStrategy", "AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"], "uncoveredStaticAddresses": ["LBUZKhRxUjRp3gR2luWZ8jQECnNK1qhNRNkTmhdzNmx", "Dbw8mACQKJULjhVzr6McbWCo9doWaVYPeFPbNJVE5B8s", "3WMYy9V9Lp7Fh8Qj2KxGvN5RtEuP4CzXsA6BdMnHkL9w", "HZzNfgApKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "8A4Crui8VnBpKjUvQr5WxE2YtRpLm3CzXsA6BdMnHkL9", "2mGnsXcGKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "GBDuzqBgKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "7xmtz8hDKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w", "6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF"], "allRealAddresses": ["bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV", "3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU", "izePositionAndAddLiquidityByStrategy", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto", "DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H", "4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb", "ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj", "EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o", "CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz", "59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li"], "compressionPotential": {"addressCount": 3, "byteSavings": 96, "kbSavings": "0.1"}}