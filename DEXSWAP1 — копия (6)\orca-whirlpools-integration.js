#!/usr/bin/env node

/**
 * 🌊 ORCA WHIRLPOOLS ИНТЕГРАЦИЯ
 * ═══════════════════════════════════════════════════════════════
 * 🔥 ЦЕЛЬ: Интеграция с Orca Whirlpools (концентрированная ликвидность)
 * 🚀 SDK: @orca-so/whirlpools-sdk
 * 🎯 ФУНКЦИИ: Получение цен, создание swap инструкций, управление позициями
 */

const { PublicKey, Connection } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const colors = require('colors');

// 🔥 ИМПОРТ ORCA WHIRLPOOLS SDK
let WhirlpoolContext, buildWhirlpoolClient, ORCA_WHIRLPOOL_PROGRAM_ID, 
    PDAUtil, swapQuoteByInputToken, TickUtil, PriceMath, AnchorProvider, Wallet;

try {
    const orcaSDK = require('@orca-so/whirlpools-sdk');
    WhirlpoolContext = orcaSDK.WhirlpoolContext;
    buildWhirlpoolClient = orcaSDK.buildWhirlpoolClient;
    ORCA_WHIRLPOOL_PROGRAM_ID = orcaSDK.ORCA_WHIRLPOOL_PROGRAM_ID;
    PDAUtil = orcaSDK.PDAUtil;
    swapQuoteByInputToken = orcaSDK.swapQuoteByInputToken;
    TickUtil = orcaSDK.TickUtil;
    PriceMath = orcaSDK.PriceMath;

    const anchor = require('@coral-xyz/anchor');
    AnchorProvider = anchor.AnchorProvider;
    Wallet = anchor.Wallet;

    console.log('✅ Orca Whirlpools SDK импортирован успешно');
} catch (error) {
    console.log('⚠️ Orca Whirlpools SDK не найден, используем fallback режим');
    console.log(`   Ошибка: ${error.message}`);
}

// 🔥 ИМПОРТ КОНФИГУРАЦИИ
const {
    TRADING_CONFIG,
    getTokenMint,
    getTechnicalLimit
} = require('./trading-config.js');

// 🔥 ИМПОРТ КОНВЕРТЕРА СУММ
const { convertUsdToNativeAmount, convertNativeToUsdAmount } = require('./centralized-amount-converter.js');

/**
 * 🌊 ORCA WHIRLPOOLS ИНТЕГРАЦИЯ
 */
class OrcaWhirlpoolsIntegration {
    constructor(connection, wallet, universalCacheManager = null) {
        this.connection = connection;
        this.wallet = wallet;
        this.universalCacheManager = universalCacheManager;
        this.whirlpoolClient = null;
        this.whirlpoolContext = null;
        this.initialized = false;

        // 📊 ИЗВЕСТНЫЕ ORCA WHIRLPOOLS SOL/USDC
        this.knownWhirlpools = new Map([
            ['SOL/USDC_WP_1', {
                address: 'HJPjoWUrhoZzkNfRpHuieeFk9WcZWjwy6PBjZ81ngndJ', // Пример адреса
                tokenA: getTokenMint('SOL'),
                tokenB: getTokenMint('USDC'),
                tickSpacing: 64,
                fee: 0.003 // 0.3%
            }],
            ['SOL/USDC_WP_2', {
                address: '7qbRF6YsyGuLUVs6Y1q64bdVrfe4ZcUUz1JRdoVNUJnm', // Пример адреса
                tokenA: getTokenMint('SOL'),
                tokenB: getTokenMint('USDC'),
                tickSpacing: 128,
                fee: 0.01 // 1%
            }]
        ]);

        // 📊 СТАТИСТИКА
        this.stats = {
            priceQueries: 0,
            swapInstructions: 0,
            errors: 0,
            lastUpdate: null
        };

        console.log('🌊 Orca Whirlpools интеграция создана');
        console.log(`   Известных whirlpools: ${this.knownWhirlpools.size}`);
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ ORCA WHIRLPOOLS SDK
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация Orca Whirlpools...'.yellow);

            if (!WhirlpoolContext) {
                console.log('⚠️ Orca Whirlpools SDK недоступен, используем API fallback');
                this.initialized = false;
                return false;
            }

            // 1. Создаем AnchorProvider
            console.log('🔗 Создание AnchorProvider...');
            const provider = new AnchorProvider(
                this.connection,
                new Wallet(this.wallet),
                {
                    commitment: 'confirmed',
                    preflightCommitment: 'confirmed'
                }
            );

            // 2. Создаем WhirlpoolContext
            console.log('🌊 Создание WhirlpoolContext...');
            this.whirlpoolContext = WhirlpoolContext.withProvider(
                provider,
                ORCA_WHIRLPOOL_PROGRAM_ID
            );

            // 3. Создаем WhirlpoolClient
            console.log('🏊 Создание WhirlpoolClient...');
            this.whirlpoolClient = buildWhirlpoolClient(this.whirlpoolContext);

            console.log('✅ Orca Whirlpools SDK инициализирован');

            // 4. Загружаем информацию о whirlpools
            await this.loadWhirlpoolsInfo();

            this.initialized = true;
            console.log('✅ Orca Whirlpools интеграция готова к работе!'.green);

            return true;

        } catch (error) {
            console.error('❌ Ошибка инициализации Orca Whirlpools:', error.message);
            this.stats.errors++;
            this.initialized = false;
            return false;
        }
    }

    /**
     * 📊 ЗАГРУЗКА ИНФОРМАЦИИ О WHIRLPOOLS
     */
    async loadWhirlpoolsInfo() {
        try {
            console.log('📊 Загрузка информации о Orca Whirlpools...');

            if (!this.whirlpoolClient) {
                throw new Error('WhirlpoolClient не инициализирован');
            }

            // Загружаем данные для каждого whirlpool
            for (const [key, whirlpoolData] of this.knownWhirlpools.entries()) {
                try {
                    const whirlpool = await this.whirlpoolClient.getPool(
                        new PublicKey(whirlpoolData.address)
                    );
                    
                    whirlpoolData.poolData = whirlpool.getData();
                    console.log(`   ${key}: загружен`);
                } catch (error) {
                    console.log(`   ❌ ${key}: ${error.message}`);
                }
            }

            console.log('✅ Информация о whirlpools загружена');

        } catch (error) {
            console.log(`⚠️ Ошибка загрузки информации о whirlpools: ${error.message}`);
        }
    }

    /**
     * 💰 ПОЛУЧЕНИЕ ЦЕН СО ВСЕХ ORCA WHIRLPOOLS
     */
    async getAllPrices() {
        try {
            console.log('💰 Получение цен Orca Whirlpools...');

            if (!this.initialized) {
                console.log('⚠️ Orca Whirlpools не инициализирован, используем API fallback');
                return await this.getPricesViaAPI();
            }

            const prices = new Map();

            for (const [whirlpoolKey, whirlpoolData] of this.knownWhirlpools.entries()) {
                try {
                    const price = await this.getWhirlpoolPrice(whirlpoolData.address);
                    if (price) {
                        prices.set(`${whirlpoolKey}_${whirlpoolData.address}`, price);
                        console.log(`   ${whirlpoolKey}: $${price.toFixed(4)}`);
                    }
                } catch (error) {
                    console.log(`   ❌ ${whirlpoolKey}: ${error.message}`);
                }
            }

            this.stats.priceQueries++;
            this.stats.lastUpdate = Date.now();

            console.log(`✅ Orca Whirlpools: получено ${prices.size} цен`);
            return prices;

        } catch (error) {
            console.error('❌ Ошибка получения цен Orca Whirlpools:', error.message);
            this.stats.errors++;
            return new Map();
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ ЦЕНЫ КОНКРЕТНОГО WHIRLPOOL
     */
    async getWhirlpoolPrice(whirlpoolAddress) {
        try {
            if (!this.whirlpoolClient) {
                throw new Error('WhirlpoolClient не инициализирован');
            }

            // Получаем whirlpool
            const whirlpool = await this.whirlpoolClient.getPool(
                new PublicKey(whirlpoolAddress)
            );

            const whirlpoolData = whirlpool.getData();

            // Рассчитываем цену из sqrtPrice
            const price = this.calculatePriceFromSqrtPrice(whirlpoolData.sqrtPrice);

            return price;

        } catch (error) {
            console.log(`⚠️ Ошибка получения цены whirlpool ${whirlpoolAddress}: ${error.message}`);
            return null;
        }
    }

    /**
     * 🧮 РАСЧЕТ ЦЕНЫ ИЗ SQRT_PRICE
     */
    calculatePriceFromSqrtPrice(sqrtPrice) {
        try {
            if (!PriceMath) {
                // Fallback расчет
                const sqrtPriceFloat = sqrtPrice.toNumber() / Math.pow(2, 64);
                const price = Math.pow(sqrtPriceFloat, 2);
                
                // Корректируем на разность decimals (SOL=9, USDC=6)
                return price * Math.pow(10, 9 - 6);
            }

            // Используем официальный PriceMath
            const price = PriceMath.sqrtPriceX64ToPrice(sqrtPrice, 9, 6);
            return price.toNumber();

        } catch (error) {
            console.log(`⚠️ Ошибка расчета цены из sqrtPrice: ${error.message}`);
            return null;
        }
    }

    /**
     * 🌐 ПОЛУЧЕНИЕ ЦЕН ЧЕРЕЗ API (FALLBACK)
     */
    async getPricesViaAPI() {
        try {
            console.log('🌐 Получение цен Orca через API...');

            // Используем публичный API Orca
            const response = await fetch('https://api.orca.so/v1/whirlpool/list', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'Orca-Integration/1.0'
                },
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`API ответил с кодом ${response.status}`);
            }

            const data = await response.json();
            const prices = new Map();

            // Ищем SOL/USDC whirlpools
            if (data.whirlpools) {
                for (const whirlpool of data.whirlpools) {
                    if (this.isSolUsdcWhirlpool(whirlpool)) {
                        const price = this.extractPriceFromAPI(whirlpool);
                        if (price > 0) {
                            prices.set(`SOL/USDC_${whirlpool.address}`, price);
                        }
                    }
                }
            }

            console.log(`✅ API Orca: получено ${prices.size} цен`);
            return prices;

        } catch (error) {
            console.log(`⚠️ Ошибка API Orca: ${error.message}`);
            return new Map();
        }
    }

    /**
     * 🔍 ПРОВЕРКА, ЯВЛЯЕТСЯ ЛИ WHIRLPOOL SOL/USDC
     */
    isSolUsdcWhirlpool(whirlpool) {
        const solMint = getTokenMint('SOL');
        const usdcMint = getTokenMint('USDC');

        return (
            (whirlpool.tokenA?.mint === solMint && whirlpool.tokenB?.mint === usdcMint) ||
            (whirlpool.tokenA?.mint === usdcMint && whirlpool.tokenB?.mint === solMint)
        );
    }

    /**
     * 📊 ИЗВЛЕЧЕНИЕ ЦЕНЫ ИЗ API ДАННЫХ
     */
    extractPriceFromAPI(whirlpool) {
        try {
            if (whirlpool.price) {
                return parseFloat(whirlpool.price);
            }

            if (whirlpool.sqrtPrice) {
                return this.calculatePriceFromSqrtPrice(new BN(whirlpool.sqrtPrice));
            }

            return 0;
        } catch (error) {
            console.log(`⚠️ Ошибка извлечения цены из API: ${error.message}`);
            return 0;
        }
    }

    /**
     * 🚀 СОЗДАНИЕ SWAP ИНСТРУКЦИИ
     */
    async createSwapInstruction(whirlpoolAddress, amountIn, tokenA, tokenB, slippage = 50) {
        try {
            console.log(`🚀 Создание Orca Whirlpool swap инструкции...`);
            console.log(`   Whirlpool: ${whirlpoolAddress}`);
            console.log(`   Сумма: ${amountIn}`);
            console.log(`   Направление: ${tokenA} → ${tokenB}`);

            if (!this.initialized || !this.whirlpoolClient) {
                throw new Error('Orca Whirlpools SDK не инициализирован');
            }

            // Получаем whirlpool
            const whirlpool = await this.whirlpoolClient.getPool(
                new PublicKey(whirlpoolAddress)
            );

            // Получаем quote для swap
            const quote = await swapQuoteByInputToken(
                whirlpool,
                new PublicKey(tokenA),
                new BN(amountIn),
                slippage, // slippage в basis points (50 = 0.5%)
                this.whirlpoolContext.program.programId,
                this.whirlpoolContext.fetcher,
                undefined, // opts
                'Situational' // refresh mode для решения tick array проблем
            );

            // Создаем swap инструкцию
            const swapInstruction = await whirlpool.swap(quote);

            this.stats.swapInstructions++;

            console.log('✅ Orca Whirlpool swap инструкция создана');

            return {
                success: true,
                instruction: swapInstruction,
                quote: quote,
                whirlpoolAddress: whirlpoolAddress,
                amountIn: amountIn,
                estimatedAmountOut: quote.estimatedAmountOut.toString(),
                dex: 'orca'
            };

        } catch (error) {
            console.error(`❌ Ошибка создания Orca swap инструкции: ${error.message}`);
            this.stats.errors++;
            
            return {
                success: false,
                error: error.message,
                dex: 'orca'
            };
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            ...this.stats,
            initialized: this.initialized,
            knownWhirlpools: this.knownWhirlpools.size,
            sdkAvailable: !!WhirlpoolContext
        };
    }

    /**
     * 🔄 СБРОС СТАТИСТИКИ
     */
    resetStats() {
        this.stats = {
            priceQueries: 0,
            swapInstructions: 0,
            errors: 0,
            lastUpdate: null
        };
        console.log('📊 Статистика Orca сброшена');
    }
}

module.exports = OrcaWhirlpoolsIntegration;
