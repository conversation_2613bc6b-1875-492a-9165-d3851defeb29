#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА БЕЗОПАСНОСТИ FLASH LOAN СИСТЕМЫ
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Убедиться что система использует ТОЛЬКО правильные flash loans
 * 🚨 ПРОВЕРКА: Нет прямых вызовов makeBorrowIx/makeRepayIx
 * ✅ ГАРАНТИЯ: Только buildFlashLoanTx для атомарных операций
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const fs = require('fs');
const path = require('path');

class FlashLoanSafetyVerifier {
  constructor() {
    this.dangerousPatterns = [
      // Прямые вызовы, которые создают реальные долги
      /await\s+.*\.makeBorrowIx\s*\(/g,
      /await\s+.*\.makeRepayIx\s*\(/g,
      /processTransaction\s*\(\s*borrowIx/g,
      /processTransaction\s*\(\s*repayIx/g,
      /\.borrow\s*\(/g,
      /\.repay\s*\(/g,
      
      // Опасные комбинации
      /makeBorrowIx.*processTransaction/g,
      /makeRepayIx.*processTransaction/g,
    ];
    
    this.safePatterns = [
      // Безопасные паттерны (создание инструкций для buildFlashLoanTx)
      /const\s+borrowIx\s*=\s*await.*\.makeBorrowIx/g,
      /const\s+repayIx\s*=\s*await.*\.makeRepayIx/g,
      /buildFlashLoanTx\s*\(/g,
    ];
    
    this.results = {
      filesChecked: 0,
      dangerousFiles: [],
      safeFiles: [],
      errors: []
    };
  }

  /**
   * 🔍 ПРОВЕРКА ФАЙЛА НА ОПАСНЫЕ ПАТТЕРНЫ
   */
  checkFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(process.cwd(), filePath);
      
      this.results.filesChecked++;
      
      const dangerousMatches = [];
      const safeMatches = [];
      
      // Проверяем опасные паттерны
      this.dangerousPatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
          dangerousMatches.push({
            pattern: pattern.toString(),
            matches: matches,
            type: 'DANGEROUS'
          });
        }
      });
      
      // Проверяем безопасные паттерны
      this.safePatterns.forEach((pattern, index) => {
        const matches = content.match(pattern);
        if (matches) {
          safeMatches.push({
            pattern: pattern.toString(),
            matches: matches,
            type: 'SAFE'
          });
        }
      });
      
      // Анализируем результаты
      if (dangerousMatches.length > 0) {
        this.results.dangerousFiles.push({
          file: relativePath,
          dangerous: dangerousMatches,
          safe: safeMatches
        });
        return 'DANGEROUS';
      } else if (safeMatches.length > 0) {
        this.results.safeFiles.push({
          file: relativePath,
          safe: safeMatches
        });
        return 'SAFE';
      }
      
      return 'CLEAN';
      
    } catch (error) {
      this.results.errors.push({
        file: filePath,
        error: error.message
      });
      return 'ERROR';
    }
  }

  /**
   * 🔍 РЕКУРСИВНАЯ ПРОВЕРКА ДИРЕКТОРИИ
   */
  checkDirectory(dirPath, extensions = ['.js', '.ts']) {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Пропускаем node_modules и другие служебные папки
          if (!['node_modules', '.git', 'dist', 'build', 'ARCHIVED', 'OLDC'].includes(item)) {
            this.checkDirectory(fullPath, extensions);
          }
        } else if (stat.isFile()) {
          const ext = path.extname(fullPath);
          if (extensions.includes(ext)) {
            this.checkFile(fullPath);
          }
        }
      }
    } catch (error) {
      this.results.errors.push({
        directory: dirPath,
        error: error.message
      });
    }
  }

  /**
   * 📊 ГЕНЕРАЦИЯ ОТЧЕТА
   */
  generateReport() {
    console.log('🔍 ОТЧЕТ ПРОВЕРКИ БЕЗОПАСНОСТИ FLASH LOAN СИСТЕМЫ');
    console.log('═══════════════════════════════════════════════════════════════');
    
    console.log(`📊 Статистика проверки:`);
    console.log(`   Файлов проверено: ${this.results.filesChecked}`);
    console.log(`   Опасных файлов: ${this.results.dangerousFiles.length}`);
    console.log(`   Безопасных файлов: ${this.results.safeFiles.length}`);
    console.log(`   Ошибок: ${this.results.errors.length}`);
    
    // Опасные файлы
    if (this.results.dangerousFiles.length > 0) {
      console.log('\n🚨 ОПАСНЫЕ ФАЙЛЫ (МОГУТ СОЗДАВАТЬ РЕАЛЬНЫЕ ДОЛГИ):');
      console.log('═══════════════════════════════════════════════════════════════');
      
      this.results.dangerousFiles.forEach((file, index) => {
        console.log(`\n❌ ${index + 1}. ${file.file}`);
        
        file.dangerous.forEach(match => {
          console.log(`   🚨 ОПАСНЫЙ ПАТТЕРН: ${match.pattern}`);
          match.matches.forEach(m => {
            console.log(`      "${m}"`);
          });
        });
        
        if (file.safe.length > 0) {
          console.log(`   ✅ Также найдены безопасные паттерны: ${file.safe.length}`);
        }
      });
    }
    
    // Безопасные файлы
    if (this.results.safeFiles.length > 0) {
      console.log('\n✅ БЕЗОПАСНЫЕ ФАЙЛЫ (ИСПОЛЬЗУЮТ buildFlashLoanTx):');
      console.log('═══════════════════════════════════════════════════════════════');
      
      this.results.safeFiles.forEach((file, index) => {
        console.log(`\n✅ ${index + 1}. ${file.file}`);
        
        file.safe.forEach(match => {
          console.log(`   ✅ БЕЗОПАСНЫЙ ПАТТЕРН: ${match.matches.length} использований`);
        });
      });
    }
    
    // Ошибки
    if (this.results.errors.length > 0) {
      console.log('\n⚠️ ОШИБКИ ПРОВЕРКИ:');
      console.log('═══════════════════════════════════════════════════════════════');
      
      this.results.errors.forEach((error, index) => {
        console.log(`\n⚠️ ${index + 1}. ${error.file || error.directory}`);
        console.log(`   Ошибка: ${error.error}`);
      });
    }
    
    // Итоговая оценка
    console.log('\n🎯 ИТОГОВАЯ ОЦЕНКА БЕЗОПАСНОСТИ:');
    console.log('═══════════════════════════════════════════════════════════════');
    
    if (this.results.dangerousFiles.length === 0) {
      console.log('🎉 СИСТЕМА ПОЛНОСТЬЮ БЕЗОПАСНА!');
      console.log('✅ Не найдено опасных паттернов создания реальных долгов');
      console.log('✅ Все flash loans используют правильную архитектуру');
      console.log('✅ buildFlashLoanTx обеспечивает атомарность операций');
      console.log('✅ IN_FLASHLOAN_FLAG корректно управляет health checks');
      
      return true;
    } else {
      console.log('🚨 ОБНАРУЖЕНЫ ПОТЕНЦИАЛЬНЫЕ РИСКИ!');
      console.log(`❌ Найдено ${this.results.dangerousFiles.length} файлов с опасными паттернами`);
      console.log('🔧 РЕКОМЕНДАЦИИ:');
      console.log('   1. Удалите прямые вызовы makeBorrowIx/makeRepayIx');
      console.log('   2. Используйте только buildFlashLoanTx для flash loans');
      console.log('   3. Убедитесь что repayAll = true везде');
      console.log('   4. Проверьте что нет processTransaction(borrowIx)');
      
      return false;
    }
  }

  /**
   * 🚀 ЗАПУСК ПОЛНОЙ ПРОВЕРКИ
   */
  async runFullCheck() {
    console.log('🚀 ЗАПУСК ПРОВЕРКИ БЕЗОПАСНОСТИ FLASH LOAN СИСТЕМЫ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Цель: Убедиться что система НЕ создает реальные долги');
    console.log('🔍 Проверяем: Отсутствие прямых вызовов makeBorrowIx/makeRepayIx');
    console.log('✅ Ожидаем: Только buildFlashLoanTx для атомарных операций');
    
    // Проверяем основные директории
    const directoriesToCheck = [
      '.',
      'solana-flash-loans',
      'src'
    ];
    
    for (const dir of directoriesToCheck) {
      if (fs.existsSync(dir)) {
        console.log(`\n🔍 Проверяем директорию: ${dir}`);
        this.checkDirectory(dir);
      }
    }
    
    // Генерируем отчет
    const isSafe = this.generateReport();
    
    // Сохраняем детальный отчет
    const reportPath = 'flash-loan-safety-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Детальный отчет сохранен: ${reportPath}`);
    
    return isSafe;
  }
}

// Запуск проверки
if (require.main === module) {
  const verifier = new FlashLoanSafetyVerifier();
  verifier.runFullCheck()
    .then(isSafe => {
      console.log(`\n🏁 ПРОВЕРКА ЗАВЕРШЕНА: ${isSafe ? 'СИСТЕМА БЕЗОПАСНА' : 'ТРЕБУЮТСЯ ИСПРАВЛЕНИЯ'}`);
      process.exit(isSafe ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ КРИТИЧЕСКАЯ ОШИБКА ПРОВЕРКИ:', error.message);
      process.exit(1);
    });
}

module.exports = FlashLoanSafetyVerifier;
