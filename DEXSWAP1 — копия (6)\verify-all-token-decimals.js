#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА ТОЧНЫХ DECIMALS ВСЕХ НАШИХ ТОКЕНОВ
 * ═══════════════════════════════════════════════════════════════
 * 📚 ОСНОВАНО НА ОФИЦИАЛЬНЫХ ИСТОЧНИКАХ SOLANA И MARGINFI
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class TokenDecimalsVerifier {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    
    // НАШИ ТОКЕНЫ С ОФИЦИАЛЬНЫМИ MINT ADDRESSES
    this.TOKENS = {
      'USDC': {
        mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        expectedDecimals: 6,
        source: 'Официальная документация Solana + MarginFi'
      },
      'USDT': {
        mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
        expectedDecimals: 6,
        source: 'Tatum.io + Solscan'
      },
      'SOL': {
        mint: '11111111111111111111111111111111', // System Program (native SOL)
        expectedDecimals: 9,
        source: 'Официальная документация Solana (1 SOL = 1,000,000,000 lamports)'
      },
      'WSOL': {
        mint: 'So11111111111111111111111111111112', // Wrapped SOL
        expectedDecimals: 9,
        source: 'Стандарт Wrapped SOL (такие же decimals как SOL)'
      }
    };
  }

  // ПРОВЕРКА DECIMALS ЧЕРЕЗ RPC
  async verifyTokenDecimals(symbol, tokenInfo) {
    console.log(`\n🔍 ПРОВЕРКА ${symbol}:`);
    console.log('─────────────────────────────────────────────────────────────');
    console.log(`📍 Mint: ${tokenInfo.mint}`);
    console.log(`📊 Ожидаемые decimals: ${tokenInfo.expectedDecimals}`);
    console.log(`📚 Источник: ${tokenInfo.source}`);

    try {
      if (symbol === 'SOL') {
        // SOL - нативный токен, не имеет mint account
        console.log(`✅ SOL (нативный): 9 decimals (1 SOL = 1,000,000,000 lamports)`);
        return { symbol, verified: true, actualDecimals: 9, expectedDecimals: 9 };
      }

      // Получаем информацию о mint
      const mintPubkey = new PublicKey(tokenInfo.mint);
      const mintInfo = await this.connection.getParsedAccountInfo(mintPubkey);

      if (!mintInfo.value) {
        throw new Error(`Mint account не найден: ${tokenInfo.mint}`);
      }

      const parsedData = mintInfo.value.data.parsed;
      const actualDecimals = parsedData.info.decimals;

      console.log(`🔢 Фактические decimals: ${actualDecimals}`);

      if (actualDecimals === tokenInfo.expectedDecimals) {
        console.log(`✅ ПРОВЕРКА ПРОЙДЕНА! Decimals совпадают: ${actualDecimals}`);
        return { symbol, verified: true, actualDecimals, expectedDecimals: tokenInfo.expectedDecimals };
      } else {
        console.log(`❌ ОШИБКА! Decimals не совпадают:`);
        console.log(`   Ожидалось: ${tokenInfo.expectedDecimals}`);
        console.log(`   Фактически: ${actualDecimals}`);
        return { symbol, verified: false, actualDecimals, expectedDecimals: tokenInfo.expectedDecimals };
      }

    } catch (error) {
      console.log(`❌ ОШИБКА ПРОВЕРКИ: ${error.message}`);
      return { symbol, verified: false, error: error.message };
    }
  }

  // ТЕСТ КОНВЕРТАЦИЙ С ПРАВИЛЬНЫМИ DECIMALS
  testConversions(symbol, decimals) {
    console.log(`\n🧪 ТЕСТ КОНВЕРТАЦИЙ ДЛЯ ${symbol} (${decimals} decimals):`);
    console.log('─────────────────────────────────────────────────────────────');

    const testCases = [
      { usd: 50000, description: 'Flash loan сумма' },
      { usd: 1000, description: 'Средняя сумма' },
      { usd: 1, description: 'Минимальная сумма' }
    ];

    testCases.forEach(({ usd, description }) => {
      let nativeAmount;
      
      if (symbol === 'USDC' || symbol === 'USDT') {
        // Для стейблкоинов: 1 USD = 1 токен
        nativeAmount = usd * Math.pow(10, decimals);
      } else if (symbol === 'SOL' || symbol === 'WSOL') {
        // Для SOL: нужна цена (примерно $150)
        const solPrice = 150;
        const solAmount = usd / solPrice;
        nativeAmount = Math.floor(solAmount * Math.pow(10, decimals));
      }

      console.log(`💰 $${usd} ${symbol} (${description}):`);
      console.log(`   → ${nativeAmount.toLocaleString()} native units`);
      
      // Проверяем разумность суммы
      const maxReasonable = 1000000 * Math.pow(10, decimals); // $1M максимум
      if (nativeAmount > maxReasonable) {
        console.log(`   ⚠️ ВНИМАНИЕ: Сумма кажется слишком большой!`);
      } else {
        console.log(`   ✅ Сумма в разумных пределах`);
      }
    });
  }

  // СОЗДАНИЕ ПРАВИЛЬНОЙ КОНФИГУРАЦИИ
  generateCorrectConfig(verificationResults) {
    console.log(`\n📋 ПРАВИЛЬНАЯ КОНФИГУРАЦИЯ ТОКЕНОВ:`);
    console.log('═══════════════════════════════════════════════════════════════');

    const config = {};
    
    verificationResults.forEach(result => {
      if (result.verified) {
        config[result.symbol] = {
          decimals: result.actualDecimals,
          mint: this.TOKENS[result.symbol].mint
        };
      }
    });

    console.log('```javascript');
    console.log('// 🔧 ПРАВИЛЬНАЯ КОНФИГУРАЦИЯ ТОКЕНОВ (ПРОВЕРЕНО)');
    console.log('const TOKEN_CONFIG = {');
    
    Object.entries(config).forEach(([symbol, info]) => {
      console.log(`  '${symbol}': {`);
      console.log(`    decimals: ${info.decimals}, // Проверено через RPC`);
      console.log(`    mint: '${info.mint}'`);
      console.log(`  },`);
    });
    
    console.log('};');
    console.log('```');

    return config;
  }

  // ПРОВЕРКА ВСЕХ ТОКЕНОВ
  async verifyAllTokens() {
    console.log('🔍 ПРОВЕРКА DECIMALS ВСЕХ НАШИХ ТОКЕНОВ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Токены: USDC, USDT, SOL, WSOL');
    console.log('📚 Источники: Официальная документация Solana + MarginFi');
    console.log('');

    const results = [];

    // Проверяем каждый токен
    for (const [symbol, tokenInfo] of Object.entries(this.TOKENS)) {
      const result = await this.verifyTokenDecimals(symbol, tokenInfo);
      results.push(result);
      
      if (result.verified) {
        this.testConversions(symbol, result.actualDecimals);
      }
      
      // Пауза между запросами
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Генерируем правильную конфигурацию
    const correctConfig = this.generateCorrectConfig(results);

    // Итоговый отчет
    console.log(`\n🎯 ИТОГОВЫЙ ОТЧЕТ:`);
    console.log('═══════════════════════════════════════════════════════════════');

    const verified = results.filter(r => r.verified);
    const failed = results.filter(r => !r.verified);

    console.log(`✅ Проверено успешно: ${verified.length}/${results.length}`);
    verified.forEach(r => {
      console.log(`   ${r.symbol}: ${r.actualDecimals} decimals`);
    });

    if (failed.length > 0) {
      console.log(`❌ Ошибки проверки: ${failed.length}`);
      failed.forEach(r => {
        console.log(`   ${r.symbol}: ${r.error || 'Decimals не совпадают'}`);
      });
    }

    console.log('');
    console.log('🔧 КЛЮЧЕВЫЕ ПРАВИЛА КОНВЕРТАЦИИ:');
    console.log('   • USDC/USDT: $1 = 1 токен × 10^6 = 1,000,000 микро-единиц');
    console.log('   • SOL/WSOL: $150 = 1 SOL × 10^9 = 1,000,000,000 lamports');
    console.log('   • НЕ умножать дважды на decimals!');
    console.log('   • Использовать Math.floor() для целых чисел');

    return { results, correctConfig };
  }
}

async function main() {
  const verifier = new TokenDecimalsVerifier();
  
  try {
    const { results, correctConfig } = await verifier.verifyAllTokens();
    
    console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА!');
    console.log('📁 Используйте correctConfig в своем коде');
    
  } catch (error) {
    console.error('❌ Ошибка проверки:', error.message);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = TokenDecimalsVerifier;
