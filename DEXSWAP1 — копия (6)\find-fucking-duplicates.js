#!/usr/bin/env node

/**
 * 🔥 НАЙТИ ЕБУЧЕЕ ДУБЛИРОВАНИЕ В MARGINFI ТРАНЗАКЦИИ
 * Детальный анализ каждого аккаунта в buildFlashLoanTx
 */

const { Connection, PublicKey, Keypair, VersionedTransaction } = require('@solana/web3.js');
const fs = require('fs');

async function findFuckingDuplicates() {
    console.log('🔥 НАЙТИ ЕБУЧЕЕ ДУБЛИРОВАНИЕ В MARGINFI ТРАНЗАКЦИИ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
        
        console.log(`✅ Кошелек: ${wallet.publicKey.toString()}`);
        
        // 2. Загружаем ALT таблицы
        console.log('\n📁 Загрузка ALT таблиц...');
        
        const altTables = [];
        
        // Meteora ALT
        if (fs.existsSync('./meteora-alt-cache.json')) {
            const meteora = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
            
            for (const result of meteora.validationResults?.slice(0, 2) || []) {
                if (result.valid && result.address) {
                    try {
                        const altAccount = await connection.getAddressLookupTable(new PublicKey(result.address));
                        if (altAccount?.value) {
                            altTables.push(altAccount.value);
                            console.log(`✅ Meteora ALT: ${result.address.slice(0, 8)}... (${altAccount.value.state.addresses.length} аккаунтов)`);
                        }
                    } catch (error) {
                        console.log(`❌ Ошибка загрузки Meteora ALT: ${error.message}`);
                    }
                }
            }
        }
        
        // MarginFi ALT
        if (fs.existsSync('./marginfi-alt-cache.json')) {
            const marginfi = JSON.parse(fs.readFileSync('./marginfi-alt-cache.json', 'utf8'));
            
            for (const result of marginfi.validationResults?.slice(0, 1) || []) {
                if (result.valid && result.address) {
                    try {
                        const altAccount = await connection.getAddressLookupTable(new PublicKey(result.address));
                        if (altAccount?.value) {
                            altTables.push(altAccount.value);
                            console.log(`✅ MarginFi ALT: ${result.address.slice(0, 8)}... (${altAccount.value.state.addresses.length} аккаунтов)`);
                        }
                    } catch (error) {
                        console.log(`❌ Ошибка загрузки MarginFi ALT: ${error.message}`);
                    }
                }
            }
        }
        
        console.log(`📊 Загружено ALT таблиц: ${altTables.length}`);
        
        // 3. Анализируем ГОТОВУЮ транзакцию из основного кода
        console.log('\n🔥 АНАЛИЗ ГОТОВОЙ ТРАНЗАКЦИИ ИЗ ОСНОВНОГО КОДА...');

        // Читаем последнюю созданную транзакцию из логов
        console.log('💡 Используем данные из последнего запуска BMETEORA.js');
        console.log('📊 Анализируем структуру VersionedTransaction с 13 инструкциями');

        // 4. Создаем тестовую транзакцию с известными дублирующимися аккаунтами
        console.log('\n🔧 СОЗДАНИЕ ТЕСТОВОЙ ТРАНЗАКЦИИ С ДУБЛИРУЮЩИМИСЯ АККАУНТАМИ...');

        const { SystemProgram, TransactionMessage } = require('@solana/web3.js');

        // Создаем инструкции которые используют одни аккаунты
        const instruction1 = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: wallet.publicKey,
            lamports: 1000
        });

        const instruction2 = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: wallet.publicKey,
            lamports: 2000
        });

        console.log('✅ Тестовые инструкции созданы');

        // 5. Создаем транзакцию с ALT таблицами
        console.log('\n🔥 СОЗДАНИЕ ТРАНЗАКЦИИ С ALT ТАБЛИЦАМИ ДЛЯ АНАЛИЗА...');

        try {
            const { blockhash } = await connection.getLatestBlockhash();

            const messageV0 = new TransactionMessage({
                payerKey: wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: [instruction1, instruction2],
            }).compileToV0Message(altTables);

            const testTx = new VersionedTransaction(messageV0);

            console.log('✅ Тестовая транзакция создана');
            console.log(`📊 Тип: ${testTx.constructor.name}`);
            
            // 6. ДЕТАЛЬНЫЙ АНАЛИЗ ТРАНЗАКЦИИ
            console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ТЕСТОВОЙ ТРАНЗАКЦИИ...');

            if (testTx instanceof VersionedTransaction) {
                const message = testTx.message;
                
                console.log(`📊 Версия транзакции: ${message.version}`);
                console.log(`📊 Static аккаунтов: ${message.staticAccountKeys.length}`);
                console.log(`📊 ALT lookups: ${message.addressTableLookups.length}`);
                
                // Анализируем static аккаунты
                console.log('\n🔍 АНАЛИЗ STATIC АККАУНТОВ:');
                
                const staticAccountsMap = new Map();
                const duplicateStatic = [];
                
                message.staticAccountKeys.forEach((account, index) => {
                    const accountStr = account.toString();
                    
                    if (staticAccountsMap.has(accountStr)) {
                        duplicateStatic.push({
                            account: accountStr,
                            indices: [staticAccountsMap.get(accountStr), index]
                        });
                        console.log(`❌ ДУБЛИРОВАНИЕ STATIC: ${accountStr.slice(0, 8)}...${accountStr.slice(-8)} (индексы ${staticAccountsMap.get(accountStr)}, ${index})`);
                    } else {
                        staticAccountsMap.set(accountStr, index);
                        console.log(`✅ Static ${index}: ${accountStr.slice(0, 8)}...${accountStr.slice(-8)}`);
                    }
                });
                
                // Анализируем ALT lookups
                console.log('\n🔍 АНАЛИЗ ALT LOOKUPS:');
                
                const altAccountsMap = new Map();
                const duplicateALT = [];
                
                message.addressTableLookups.forEach((lookup, lookupIndex) => {
                    console.log(`\n📋 ALT Lookup ${lookupIndex}: ${lookup.accountKey.toString()}`);
                    console.log(`   Readonly индексы: [${lookup.readonlyIndexes.join(', ')}]`);
                    console.log(`   Writable индексы: [${lookup.writableIndexes.join(', ')}]`);
                    
                    // Находим соответствующую ALT таблицу
                    const altTable = altTables.find(alt => alt.key.equals(lookup.accountKey));
                    
                    if (altTable) {
                        // Проверяем readonly индексы
                        lookup.readonlyIndexes.forEach(index => {
                            if (index < altTable.state.addresses.length) {
                                const account = altTable.state.addresses[index].toString();
                                const key = `${lookupIndex}-readonly-${index}`;
                                
                                if (altAccountsMap.has(account)) {
                                    duplicateALT.push({
                                        account,
                                        locations: [altAccountsMap.get(account), key]
                                    });
                                    console.log(`❌ ДУБЛИРОВАНИЕ ALT: ${account.slice(0, 8)}...${account.slice(-8)} (${altAccountsMap.get(account)} и ${key})`);
                                } else {
                                    altAccountsMap.set(account, key);
                                    console.log(`✅ ALT readonly ${index}: ${account.slice(0, 8)}...${account.slice(-8)}`);
                                }
                            }
                        });
                        
                        // Проверяем writable индексы
                        lookup.writableIndexes.forEach(index => {
                            if (index < altTable.state.addresses.length) {
                                const account = altTable.state.addresses[index].toString();
                                const key = `${lookupIndex}-writable-${index}`;
                                
                                if (altAccountsMap.has(account)) {
                                    duplicateALT.push({
                                        account,
                                        locations: [altAccountsMap.get(account), key]
                                    });
                                    console.log(`❌ ДУБЛИРОВАНИЕ ALT: ${account.slice(0, 8)}...${account.slice(-8)} (${altAccountsMap.get(account)} и ${key})`);
                                } else {
                                    altAccountsMap.set(account, key);
                                    console.log(`✅ ALT writable ${index}: ${account.slice(0, 8)}...${account.slice(-8)}`);
                                }
                            }
                        });
                    }
                });
                
                // Проверяем пересечения между static и ALT
                console.log('\n🔍 ПРОВЕРКА ПЕРЕСЕЧЕНИЙ STATIC ↔ ALT:');
                
                const crossDuplicates = [];
                
                staticAccountsMap.forEach((staticIndex, account) => {
                    if (altAccountsMap.has(account)) {
                        crossDuplicates.push({
                            account,
                            staticIndex,
                            altLocation: altAccountsMap.get(account)
                        });
                        console.log(`❌ ПЕРЕСЕЧЕНИЕ: ${account.slice(0, 8)}...${account.slice(-8)} (static ${staticIndex} и ALT ${altAccountsMap.get(account)})`);
                    }
                });
                
                // 7. ИТОГОВЫЙ РЕЗУЛЬТАТ
                console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ АНАЛИЗА:');
                console.log('=' .repeat(60));
                
                console.log(`📊 Static дублирований: ${duplicateStatic.length}`);
                console.log(`📊 ALT дублирований: ${duplicateALT.length}`);
                console.log(`📊 Static ↔ ALT пересечений: ${crossDuplicates.length}`);
                
                const totalDuplicates = duplicateStatic.length + duplicateALT.length + crossDuplicates.length;
                
                if (totalDuplicates > 0) {
                    console.log(`\n🚨 НАЙДЕНО ${totalDuplicates} ДУБЛИРОВАНИЙ!`);
                    
                    if (duplicateStatic.length > 0) {
                        console.log(`\n❌ STATIC ДУБЛИРОВАНИЯ:`);
                        duplicateStatic.forEach((dup, index) => {
                            console.log(`   ${index + 1}. ${dup.account.slice(0, 8)}...${dup.account.slice(-8)} (индексы ${dup.indices.join(', ')})`);
                        });
                    }
                    
                    if (duplicateALT.length > 0) {
                        console.log(`\n❌ ALT ДУБЛИРОВАНИЯ:`);
                        duplicateALT.forEach((dup, index) => {
                            console.log(`   ${index + 1}. ${dup.account.slice(0, 8)}...${dup.account.slice(-8)} (${dup.locations.join(' и ')})`);
                        });
                    }
                    
                    if (crossDuplicates.length > 0) {
                        console.log(`\n❌ STATIC ↔ ALT ПЕРЕСЕЧЕНИЯ:`);
                        crossDuplicates.forEach((dup, index) => {
                            console.log(`   ${index + 1}. ${dup.account.slice(0, 8)}...${dup.account.slice(-8)} (static ${dup.staticIndex} и ALT ${dup.altLocation})`);
                        });
                    }
                    
                    console.log(`\n💡 ЭТО ИСТОЧНИК ОШИБКИ "AccountLoadedTwice"!`);
                } else {
                    console.log(`\n✅ ДУБЛИРОВАНИЙ НЕ НАЙДЕНО`);
                    console.log(`💡 Проблема может быть в другом месте`);
                }
                
                // 8. Тестируем симуляцию
                console.log('\n🧪 ТЕСТИРОВАНИЕ СИМУЛЯЦИИ...');

                try {
                    testTx.sign([wallet]);
                    const simulation = await connection.simulateTransaction(testTx);
                    
                    if (simulation.value.err) {
                        const errorStr = JSON.stringify(simulation.value.err);
                        console.log(`❌ ОШИБКА СИМУЛЯЦИИ: ${errorStr}`);
                        
                        if (errorStr.includes('AccountLoadedTwice')) {
                            console.log('🚨 ПОДТВЕРЖДЕНО: Ошибка "AccountLoadedTwice"!');
                        }
                    } else {
                        console.log('✅ Симуляция прошла успешно');
                    }
                } catch (error) {
                    console.log(`❌ Ошибка симуляции: ${error.message}`);
                    
                    if (error.message.includes('AccountLoadedTwice')) {
                        console.log('🚨 ПОДТВЕРЖДЕНО: Ошибка "AccountLoadedTwice"!');
                    }
                }
                
            } else {
                console.log('❌ Неожиданный тип транзакции');
            }
            
        } catch (error) {
            console.error(`❌ Ошибка создания тестовой транзакции: ${error.message}`);
            console.error(error.stack);
        }

    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        console.error(error.stack);
    }
}

// Запуск анализа
if (require.main === module) {
    findFuckingDuplicates().catch(console.error);
}

module.exports = { findFuckingDuplicates };
