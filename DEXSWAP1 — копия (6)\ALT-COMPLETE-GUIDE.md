# 🔥 ПОЛНОЕ РУКОВОДСТВО ПО ADDRESS LOOKUP TABLES (ALT)

## 📋 **ОГЛАВЛЕНИЕ**
1. [Что такое ALT](#что-такое-alt)
2. [Зачем нужны ALT](#зачем-нужны-alt)
3. [Создание ALT таблицы](#создание-alt-таблицы)
4. [Добавление адресов в ALT](#добавление-адресов-в-alt)
5. [Загрузка ALT из сети](#загрузка-alt-из-сети)
6. [Использование ALT в транзакциях](#использование-alt-в-транзакциях)
7. [Локальное кэширование ALT](#локальное-кэширование-alt)
8. [Готовые скрипты](#готовые-скрипты)
9. [Диагностика проблем](#диагностика-проблем)

---

## 🎯 **ЧТО ТАКОЕ ALT**

**Address Lookup Tables (ALT)** - это таблицы адресов в Solana, которые позволяют:
- **Сжимать адреса** с 32 байт до 1 байта (индекс в таблице)
- **Увеличить лимит адресов** в транзакции с 32 до 64
- **Уменьшить размер транзакции** на 31 байт за каждый сжатый адрес

---

## 🔥 **ЗАЧЕМ НУЖНЫ ALT**

### ❌ **БЕЗ ALT:**
- Лимит: **32 адреса** в транзакции
- Размер: **32 байта** на каждый адрес
- Проблема: **Большие транзакции** превышают лимит 1232 байт

### ✅ **С ALT:**
- Лимит: **64 адреса** в транзакции
- Размер: **1 байт** на каждый сжатый адрес
- Экономия: **31 байт** на каждый адрес

---

## 🛠️ **СОЗДАНИЕ ALT ТАБЛИЦЫ**

### **1. Создание новой ALT таблицы:**

```javascript
const { Connection, AddressLookupTableProgram } = require('@solana/web3.js');

// 1. Подключение к сети
const connection = new Connection(process.env.QUICKNODE_RPC_URL);

// 2. Получение текущего slot
const slot = await connection.getSlot();

// 3. Создание инструкции для создания ALT
const [lookupTableInst, lookupTableAddress] = 
    AddressLookupTableProgram.createLookupTable({
        authority: payer.publicKey,  // Владелец таблицы
        payer: payer.publicKey,      // Плательщик
        recentSlot: slot,            // Текущий slot
    });

console.log("ALT адрес:", lookupTableAddress.toBase58());

// 4. Отправка транзакции создания
const transaction = new Transaction().add(lookupTableInst);
const signature = await sendAndConfirmTransaction(connection, transaction, [payer]);
```

---

## ➕ **ДОБАВЛЕНИЕ АДРЕСОВ В ALT**

### **2. Расширение ALT таблицы (extend):**

```javascript
// Список адресов для добавления
const addressesToAdd = [
    new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), // Token Program
    new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'), // Associated Token
    new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'), // MarginFi
    // ... до 20 адресов за раз
];

// Создание инструкции расширения
const extendInstruction = AddressLookupTableProgram.extendLookupTable({
    payer: payer.publicKey,
    authority: payer.publicKey,
    lookupTable: lookupTableAddress,
    addresses: addressesToAdd,
});

// Отправка транзакции расширения
const extendTransaction = new Transaction().add(extendInstruction);
const extendSignature = await sendAndConfirmTransaction(connection, extendTransaction, [payer]);
```

### **⚠️ ВАЖНО:**
- **Лимит:** Максимум ~20 адресов за одну транзакцию
- **Батчинг:** Для большего количества нужно несколько транзакций
- **Стоимость:** ~0.00133 SOL за 6 адресов

---

## 📥 **ЗАГРУЗКА ALT ИЗ СЕТИ**

### **3. Получение ALT таблицы из блокчейна:**

```javascript
// Загрузка ALT таблицы
const lookupTableAccount = await connection.getAddressLookupTable(lookupTableAddress);

if (lookupTableAccount.value) {
    const altAccount = lookupTableAccount.value;
    
    console.log("ALT адрес:", altAccount.key.toBase58());
    console.log("Количество адресов:", altAccount.state.addresses.length);
    
    // Вывод всех адресов
    altAccount.state.addresses.forEach((address, index) => {
        console.log(`${index}: ${address.toBase58()}`);
    });
}
```

---

## 🚀 **ИСПОЛЬЗОВАНИЕ ALT В ТРАНЗАКЦИЯХ**

### **4. Создание V0 транзакции с ALT:**

```javascript
// 1. Создание инструкций (обычным способом)
const instructions = [
    // Ваши инструкции здесь
];

// 2. Загрузка ALT таблиц
const lookupTableAccounts = [
    (await connection.getAddressLookupTable(altAddress1)).value,
    (await connection.getAddressLookupTable(altAddress2)).value,
    // ... другие ALT таблицы
].filter(Boolean);

// 3. Получение blockhash
const { blockhash } = await connection.getLatestBlockhash();

// 4. Создание V0 сообщения с ALT
const messageV0 = new TransactionMessage({
    payerKey: payer.publicKey,
    recentBlockhash: blockhash,
    instructions: instructions,
}).compileToV0Message(lookupTableAccounts); // 🔥 ЗДЕСЬ ALT!

// 5. Создание V0 транзакции
const transactionV0 = new VersionedTransaction(messageV0);

// 6. Подписание ПЕРЕД отправкой
transactionV0.sign([payer]);

// 7. Отправка V0 транзакции
const signature = await sendAndConfirmTransaction(connection, transactionV0);
```

### **⚠️ КРИТИЧЕСКИ ВАЖНО:**
- **Только V0 транзакции** поддерживают ALT
- **Подписывать ПЕРЕД** `sendAndConfirmTransaction`
- **НЕ передавать signers** в `sendAndConfirmTransaction`

---

## 💾 **ЛОКАЛЬНОЕ КЭШИРОВАНИЕ ALT**

### **5. Сохранение ALT в локальный файл:**

```javascript
// Структура кэша ALT
const altCache = {
    lastUpdated: new Date().toISOString(),
    totalTables: 2,
    tables: {
        "mainnet": {
            address: "HGmknUTUhJKG4CxgqArBGXe...",
            accountCount: 86,
            addresses: [
                "11111111111111111111111111111111",
                "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
                // ... все адреса
            ],
            valid: true
        },
        "custom": {
            address: "FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe",
            accountCount: 100,
            addresses: [
                // ... ваши кастомные адреса
            ],
            valid: true
        }
    }
};

// Сохранение в файл
const fs = require('fs');
fs.writeFileSync('correct-alt-tables-cache.json', JSON.stringify(altCache, null, 2));
```

### **6. Загрузка ALT из локального кэша:**

```javascript
function loadALTTablesFromCache() {
    try {
        const fs = require('fs');
        const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
        
        const altTables = [];
        
        Object.entries(fileData.tables).forEach(([name, table]) => {
            if (table.valid && table.addresses) {
                altTables.push({
                    key: new PublicKey(table.address),
                    state: {
                        addresses: table.addresses.map(addr => new PublicKey(addr))
                    }
                });
            }
        });
        
        console.log(`✅ Загружено ${altTables.length} ALT таблиц из кэша`);
        return altTables;
        
    } catch (error) {
        console.log(`❌ Ошибка загрузки ALT кэша: ${error.message}`);
        return [];
    }
}
```

---

## 📜 **ГОТОВЫЕ СКРИПТЫ**

### **7. Скрипт создания ALT (`create-alt.js`):**

```javascript
const { Connection, Keypair, Transaction, sendAndConfirmTransaction, AddressLookupTableProgram } = require('@solana/web3.js');

async function createALT() {
    const connection = new Connection(process.env.QUICKNODE_RPC_URL);
    const payer = Keypair.fromSecretKey(/* ваш приватный ключ */);
    
    const slot = await connection.getSlot();
    const [lookupTableInst, lookupTableAddress] = 
        AddressLookupTableProgram.createLookupTable({
            authority: payer.publicKey,
            payer: payer.publicKey,
            recentSlot: slot,
        });
    
    const transaction = new Transaction().add(lookupTableInst);
    const signature = await sendAndConfirmTransaction(connection, transaction, [payer]);
    
    console.log(`✅ ALT создана: ${lookupTableAddress.toBase58()}`);
    console.log(`🔗 Транзакция: ${signature}`);
    
    return lookupTableAddress;
}

createALT().catch(console.error);
```

### **8. Скрипт добавления адресов (`add-to-alt.js`):**

```javascript
const { Connection, Keypair, PublicKey, Transaction, sendAndConfirmTransaction, AddressLookupTableProgram } = require('@solana/web3.js');

async function addAddressesToALT() {
    const connection = new Connection(process.env.QUICKNODE_RPC_URL);
    const payer = Keypair.fromSecretKey(/* ваш приватный ключ */);
    const lookupTableAddress = new PublicKey('YOUR_ALT_ADDRESS');
    
    const addressesToAdd = [
        new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
        new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'),
        // ... ваши адреса
    ];
    
    const extendInstruction = AddressLookupTableProgram.extendLookupTable({
        payer: payer.publicKey,
        authority: payer.publicKey,
        lookupTable: lookupTableAddress,
        addresses: addressesToAdd,
    });
    
    const transaction = new Transaction().add(extendInstruction);
    const signature = await sendAndConfirmTransaction(connection, transaction, [payer]);
    
    console.log(`✅ Добавлено ${addressesToAdd.length} адресов`);
    console.log(`🔗 Транзакция: ${signature}`);
}

addAddressesToALT().catch(console.error);
```

---

## 🔧 **ДИАГНОСТИКА ПРОБЛЕМ**

### **9. Проверка размера транзакции:**

```javascript
function analyzeTransactionSize(transaction) {
    const serialized = transaction.serialize();
    const size = serialized.length;
    
    console.log(`📊 Размер транзакции: ${size} байт`);
    console.log(`📊 Лимит: 1232 байт`);
    console.log(`📊 Превышение: ${size > 1232 ? size - 1232 : 0} байт`);
    
    if (size > 1232) {
        console.log(`❌ ТРАНЗАКЦИЯ СЛИШКОМ БОЛЬШАЯ!`);
        console.log(`🔥 НУЖНО ALT СЖАТИЕ!`);
    } else {
        console.log(`✅ Размер в пределах лимита`);
    }
}
```

### **10. Проверка ALT сжатия:**

```javascript
function checkALTCompression(messageV0) {
    const staticKeys = messageV0.staticAccountKeys.length;
    const writableIndexes = messageV0.addressTableLookups.reduce((sum, alt) => 
        sum + alt.writableIndexes.length, 0);
    const readonlyIndexes = messageV0.addressTableLookups.reduce((sum, alt) => 
        sum + alt.readonlyIndexes.length, 0);
    
    console.log(`📊 Static keys: ${staticKeys} (должно быть <= 15)`);
    console.log(`📊 ALT writable: ${writableIndexes}`);
    console.log(`📊 ALT readonly: ${readonlyIndexes}`);
    console.log(`📊 Всего адресов: ${staticKeys + writableIndexes + readonlyIndexes}`);
    
    if (staticKeys <= 15) {
        console.log(`✅ ALT СЖАТИЕ РАБОТАЕТ!`);
    } else {
        console.log(`❌ ALT СЖАТИЕ НЕ РАБОТАЕТ!`);
        console.log(`🔥 НУЖНО ДОБАВИТЬ БОЛЬШЕ АДРЕСОВ В ALT!`);
    }
}
```

---

## 🚨 **ЧАСТЫЕ ОШИБКИ И РЕШЕНИЯ**

### **Ошибка: "Transaction too large"**
- **Причина:** Размер > 1232 байт
- **Решение:** Добавить адреса в ALT таблицы

### **Ошибка: "Invalid transaction"**
- **Причина:** Использование legacy транзакций с ALT
- **Решение:** Использовать только V0 транзакции

### **Ошибка: "Address not found in lookup table"**
- **Причина:** Адрес не добавлен в ALT
- **Решение:** Добавить адрес через extend

### **Ошибка: "Insufficient lamports"**
- **Причина:** Недостаточно SOL для создания/расширения ALT
- **Решение:** Пополнить кошелек на ~0.003 SOL

---

## 📚 **ПОЛЕЗНЫЕ ССЫЛКИ**

- [Официальная документация Solana ALT](https://solana.com/developers/guides/advanced/lookup-tables)
- [Web3.js AddressLookupTableProgram](https://solana-labs.github.io/solana-web3.js/v1.x/classes/AddressLookupTableProgram.html)
- [Versioned Transactions](https://solana.com/developers/guides/advanced/versions)

---

## 🔒 **ВАЖНЫЕ ЗАМЕТКИ**

1. **ALT таблицы** можно создавать legacy транзакциями, но **использовать только в V0**
2. **Максимум 256 адресов** в одной ALT таблице
3. **Экономия 31 байт** на каждый сжатый адрес
4. **Стоимость создания** ~0.001 SOL + rent
5. **Стоимость расширения** ~0.00133 SOL за 6 адресов

---

**🔥 ЭТОТ ФАЙЛ - ТВОЯ БИБЛИЯ ПО ALT! СОХРАНИ И ИСПОЛЬЗУЙ КАЖДЫЙ РАЗ!**
