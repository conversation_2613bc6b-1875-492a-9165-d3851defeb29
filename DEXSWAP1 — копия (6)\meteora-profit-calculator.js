/**
 * 🚀 КАЛЬКУЛЯТОР ПРИБЫЛИ ДЛЯ METEORA ПУЛОВ
 * 
 * Точный расчет по твоей формуле:
 * 1. <PERSON> <PERSON><PERSON> → Покупка в маленьком пуле → Повышение цены
 * 2. Продажа в большом пуле по повышенной цене
 * 3. Возврат займа + прибыль
 */

class MeteoraArbitrageCalculator {
    constructor() {
        this.FLASH_LOAN_FEE = 0.0009; // 0.09% MarginFi
        this.TRANSACTION_FEE = 0.01; // $0.01 за транзакцию
        
        console.log('🚀 MeteoraArbitrageCalculator инициализирован');
        console.log('💡 Алгоритм: Flash Loan → Покупка (поднимаем цену) → Продажа → Прибыль');
    }

    /**
     * 🧮 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ ПРИ ПОКУПКЕ
     */
    calculatePriceIncrease(buyAmount, poolLiquidity) {
        // Процент от ликвидности
        const buyPercent = (buyAmount / poolLiquidity) * 100;
        
        // Новая ликвидность после покупки
        const newLiquidity = poolLiquidity + buyAmount;
        
        // Повышение цены (твоя формула: примерно 15-16% при $500K в $3M пуле)
        const priceIncreasePercent = buyPercent * 0.8; // Коэффициент 0.8 для реалистичности
        
        return {
            buyAmount,
            poolLiquidity,
            newLiquidity,
            buyPercent: buyPercent.toFixed(2),
            priceIncreasePercent: priceIncreasePercent.toFixed(2),
            newPrice: 1 + (priceIncreasePercent / 100)
        };
    }

    /**
     * 💰 РАСЧЕТ ПРОДАЖИ В БОЛЬШОМ ПУЛЕ
     */
    calculateSale(sellAmount, bigPoolLiquidity, priceIncrease) {
        // Процент от большого пула
        const sellPercent = (sellAmount / bigPoolLiquidity) * 100;
        
        // Slippage при продаже (квадратичная функция)
        const slippagePercent = Math.pow(sellPercent / 100, 1.5) * 100;
        
        // Доход с учетом повышенной цены
        const baseRevenue = sellAmount;
        const priceBonus = sellAmount * (parseFloat(priceIncrease) / 100);
        const slippageLoss = sellAmount * (slippagePercent / 100);
        
        const netRevenue = baseRevenue + priceBonus - slippageLoss;
        
        return {
            sellAmount,
            bigPoolLiquidity,
            sellPercent: sellPercent.toFixed(4),
            slippagePercent: slippagePercent.toFixed(4),
            baseRevenue: Math.round(baseRevenue),
            priceBonus: Math.round(priceBonus),
            slippageLoss: Math.round(slippageLoss),
            netRevenue: Math.round(netRevenue)
        };
    }

    /**
     * 🎯 ПОЛНЫЙ РАСЧЕТ АРБИТРАЖА
     */
    calculateArbitrage(flashLoanAmount, sellAmount, smallPool, bigPool) {
        // 1. Покупка в маленьком пуле
        const buyResult = this.calculatePriceIncrease(flashLoanAmount, smallPool);
        
        // 2. Продажа в большом пуле
        const sellResult = this.calculateSale(sellAmount, bigPool, buyResult.priceIncreasePercent);
        
        // 3. Расчет затрат
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalCosts = flashLoanAmount + flashLoanFee + this.TRANSACTION_FEE;
        
        // 4. Итоговая прибыль
        const grossProfit = sellResult.netRevenue - flashLoanAmount;
        const netProfit = sellResult.netRevenue - totalCosts;
        const roi = (netProfit / flashLoanAmount) * 100;
        
        return {
            inputs: {
                flashLoanAmount,
                sellAmount,
                smallPool,
                bigPool
            },
            buy: buyResult,
            sell: sellResult,
            costs: {
                flashLoanAmount,
                flashLoanFee: Math.round(flashLoanFee),
                transactionFee: this.TRANSACTION_FEE,
                totalCosts: Math.round(totalCosts)
            },
            profit: {
                grossProfit: Math.round(grossProfit),
                netProfit: Math.round(netProfit),
                roi: roi.toFixed(4),
                profitable: netProfit > 0
            }
        };
    }

    /**
     * 🔍 ПОИСК ОПТИМАЛЬНОЙ СУММЫ ДЛЯ ПУЛОВ $3M И $7M
     */
    findOptimalAmount() {
        console.log('\n🔍 ПОИСК ОПТИМАЛЬНОЙ СУММЫ ДЛЯ METEORA ПУЛОВ');
        console.log('=' .repeat(80));
        console.log('🌊 Маленький пул: $3,000,000');
        console.log('🐋 Большой пул: $7,000,000');
        
        const smallPool = 3000000; // $3M
        const bigPool = 7000000;   // $7M
        
        // Тестируем разные суммы Flash Loan
        const flashLoanAmounts = [
            100000, 200000, 300000, 400000, 500000, 
            600000, 700000, 800000, 900000, 1000000
        ];
        
        // Коэффициенты продажи (продаем больше чем покупаем)
        const sellMultipliers = [0.6, 0.7, 0.8, 0.9, 1.0, 1.2, 1.5];
        
        let bestStrategy = { profit: { netProfit: -Infinity } };
        const results = [];
        
        console.log('\n📊 АНАЛИЗ РАЗЛИЧНЫХ СТРАТЕГИЙ:\n');
        
        flashLoanAmounts.forEach(flashLoanAmount => {
            console.log(`💰 FLASH LOAN: $${flashLoanAmount.toLocaleString()}`);
            
            sellMultipliers.forEach(multiplier => {
                const sellAmount = Math.round(flashLoanAmount * multiplier);
                
                const result = this.calculateArbitrage(
                    flashLoanAmount, 
                    sellAmount, 
                    smallPool, 
                    bigPool
                );
                
                results.push(result);
                
                if (result.profit.netProfit > bestStrategy.profit.netProfit) {
                    bestStrategy = result;
                }
                
                const status = result.profit.profitable ? '✅' : '❌';
                const emoji = result.profit.profitable ? '💚' : '🔴';
                
                console.log(`   ${status} Продажа ${(multiplier*100)}% ($${sellAmount.toLocaleString()}):`);
                console.log(`      📈 Повышение цены: ${result.buy.priceIncreasePercent}%`);
                console.log(`      💸 Slippage продажи: ${result.sell.slippagePercent}%`);
                console.log(`      ${emoji} Прибыль: $${result.profit.netProfit.toLocaleString()} (ROI: ${result.profit.roi}%)`);
            });
            console.log('');
        });
        
        return { bestStrategy, allResults: results };
    }

    /**
     * 📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕЙ СТРАТЕГИИ
     */
    analyzeBestStrategy(strategy) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ОПТИМАЛЬНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        const inputs = strategy.inputs;
        const buy = strategy.buy;
        const sell = strategy.sell;
        const costs = strategy.costs;
        const profit = strategy.profit;
        
        console.log(`🎯 ПАРАМЕТРЫ ОПЕРАЦИИ:`);
        console.log(`   💰 Flash Loan: $${inputs.flashLoanAmount.toLocaleString()}`);
        console.log(`   💸 Продажа: $${inputs.sellAmount.toLocaleString()}`);
        console.log(`   🌊 Маленький пул: $${inputs.smallPool.toLocaleString()}`);
        console.log(`   🐋 Большой пул: $${inputs.bigPool.toLocaleString()}`);
        
        console.log(`\n🛒 ПОКУПКА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   📊 Процент от пула: ${buy.buyPercent}%`);
        console.log(`   📈 Повышение цены: ${buy.priceIncreasePercent}%`);
        console.log(`   🌊 Новая ликвидность: $${buy.newLiquidity.toLocaleString()}`);
        
        console.log(`\n💰 ПРОДАЖА В БОЛЬШОМ ПУЛЕ:`);
        console.log(`   📊 Процент от пула: ${sell.sellPercent}%`);
        console.log(`   💸 Slippage: ${sell.slippagePercent}%`);
        console.log(`   💎 Базовый доход: $${sell.baseRevenue.toLocaleString()}`);
        console.log(`   📈 Бонус от цены: $${sell.priceBonus.toLocaleString()}`);
        console.log(`   💔 Потери slippage: $${sell.slippageLoss.toLocaleString()}`);
        console.log(`   💚 Чистый доход: $${sell.netRevenue.toLocaleString()}`);
        
        console.log(`\n💸 ЗАТРАТЫ:`);
        console.log(`   🏦 Flash Loan: $${costs.flashLoanAmount.toLocaleString()}`);
        console.log(`   💰 Комиссия FL: $${costs.flashLoanFee.toLocaleString()}`);
        console.log(`   ⚡ Транзакция: $${costs.transactionFee}`);
        console.log(`   📊 Общие затраты: $${costs.totalCosts.toLocaleString()}`);
        
        console.log(`\n🏆 ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
        console.log(`   💚 Чистая прибыль: $${profit.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${profit.roi}%`);
        console.log(`   ✅ Прибыльность: ${profit.profitable ? 'ДА' : 'НЕТ'}`);
        
        // Потенциал масштабирования
        if (profit.profitable) {
            const dailyPotential = profit.netProfit * 20; // 20 операций в день
            const monthlyPotential = dailyPotential * 30;
            
            console.log(`\n💎 ПОТЕНЦИАЛ МАСШТАБИРОВАНИЯ:`);
            console.log(`   📅 Дневной потенциал (20 операций): $${dailyPotential.toLocaleString()}`);
            console.log(`   📆 Месячный потенциал: $${monthlyPotential.toLocaleString()}`);
            console.log(`   🚀 Годовой потенциал: $${(monthlyPotential * 12).toLocaleString()}`);
        }
        
        return strategy;
    }

    /**
     * 📈 СРАВНЕНИЕ С ТВОИМИ РАСЧЕТАМИ
     */
    compareWithUserCalculations() {
        console.log('\n📈 СРАВНЕНИЕ С ТВОИМИ РАСЧЕТАМИ');
        console.log('=' .repeat(80));
        
        // Твой первый пример
        console.log('🔍 ТВОЙ ПРИМЕР 1:');
        const example1 = this.calculateArbitrage(500000, 350000, 3000000, 7000000);
        console.log(`   Flash Loan: $500K → Продажа: $350K`);
        console.log(`   Повышение цены: ${example1.buy.priceIncreasePercent}% (ты: 15-16%)`);
        console.log(`   Slippage продажи: ${example1.sell.slippagePercent}% (ты: 5.25%)`);
        console.log(`   Прибыль: $${example1.profit.netProfit.toLocaleString()}`);
        
        // Твой второй пример
        console.log('\n🔍 ТВОЙ ПРИМЕР 2:');
        const example2 = this.calculateArbitrage(500000, 300000, 3000000, 7000000);
        console.log(`   Flash Loan: $500K → Продажа: $300K`);
        console.log(`   Повышение цены: ${example2.buy.priceIncreasePercent}% (ты: 15-16%)`);
        console.log(`   Slippage продажи: ${example2.sell.slippagePercent}% (ты: 4.58%)`);
        console.log(`   Прибыль: $${example2.profit.netProfit.toLocaleString()}`);
        
        return { example1, example2 };
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🚀 ЗАПУСК КАЛЬКУЛЯТОРА METEORA АРБИТРАЖА...\n');
    
    const calculator = new MeteoraArbitrageCalculator();
    
    try {
        // Поиск оптимальной стратегии
        const { bestStrategy, allResults } = calculator.findOptimalAmount();
        
        // Детальный анализ
        const analysis = calculator.analyzeBestStrategy(bestStrategy);
        
        // Сравнение с твоими расчетами
        const comparison = calculator.compareWithUserCalculations();
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${bestStrategy.profit.netProfit.toLocaleString()}`);
        console.log(`💰 ОПТИМАЛЬНЫЙ FLASH LOAN: $${bestStrategy.inputs.flashLoanAmount.toLocaleString()}`);
        console.log(`📈 ОПТИМАЛЬНАЯ ПРОДАЖА: $${bestStrategy.inputs.sellAmount.toLocaleString()}`);
        console.log(`🚀 ROI: ${bestStrategy.profit.roi}%`);
        
        return { analysis, comparison, bestStrategy };
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { MeteoraArbitrageCalculator };
