/**
 * 🔧 TRANSACTION BUILDER MODULE
 * 
 * Модуль для сборки и оптимизации транзакций:
 * 1. Анализирует все инструкции на дубликаты
 * 2. Находит точные совпадения по programId, keys, data
 * 3. Удаляет дубликаты, оставляя только уникальные
 * 4. Проверяет результат через RPC simulate
 * 5. Может использоваться как модуль или запускаться отдельно
 */

const { 
    Connection, 
    Keypair, 
    VersionedTransaction,
    TransactionMessage
} = require('@solana/web3.js');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ ПОЛНУЮ СИСТЕМУ
const CompleteFlashLoanWithLiquidity = require('./complete-flash-loan-with-liquidity.js');
const TransactionSizeDiagnostic = require('./transaction-size-diagnostic.js');
const MeteoraSwapDiagnostic = require('./meteora-swap-diagnostic.js');

require('dotenv').config();

class TransactionBuilderModule {
    constructor(connection = null, wallet = null) {
        // 🌐 CONNECTION К SOLANA
        this.connection = connection || new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = wallet;
        
        // 🔥 ПОЛНАЯ СИСТЕМА
        this.flashLoanSystem = null;

        // 🔍 ДИАГНОСТИЧЕСКИЕ ИНСТРУМЕНТЫ
        this.diagnostic = new TransactionSizeDiagnostic();
        this.meteoraDiagnostic = new MeteoraSwapDiagnostic();

        console.log('🔧 TRANSACTION BUILDER MODULE ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ TRANSACTION BUILDER...');
        
        if (!this.wallet) {
            const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
            this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        }
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ...');
        this.flashLoanSystem = new CompleteFlashLoanWithLiquidity();
        await this.flashLoanSystem.initialize();
        
        console.log('   ✅ Готов к сборке транзакций');
    }

    /**
     * 🔍 АНАЛИЗ ДУБЛИРУЮЩИХСЯ ИНСТРУКЦИЙ
     */
    analyzeDuplicates(instructions) {
        console.log('\n🔍 АНАЛИЗ ДУБЛИРУЮЩИХСЯ ИНСТРУКЦИЙ...');
        console.log('=' .repeat(60));
        
        const duplicates = [];
        const seen = new Map();
        
        instructions.forEach((instruction, index) => {
            // Создаем уникальный ключ для инструкции
            const programId = instruction.programId.toString();
            const keysStr = instruction.keys?.map(k => 
                `${k.pubkey.toString()}-${k.isSigner}-${k.isWritable}`
            ).join('|') || '';
            const dataStr = instruction.data ? Buffer.from(instruction.data).toString('hex') : '';
            
            const uniqueKey = `${programId}:${keysStr}:${dataStr}`;
            
            if (seen.has(uniqueKey)) {
                const originalIndex = seen.get(uniqueKey);
                duplicates.push({
                    originalIndex,
                    duplicateIndex: index,
                    programId,
                    uniqueKey: uniqueKey.slice(0, 50) + '...'
                });
                
                console.log(`🚨 ДУБЛИКАТ НАЙДЕН:`);
                console.log(`   Оригинал: инструкция ${originalIndex}`);
                console.log(`   Дубликат: инструкция ${index}`);
                console.log(`   Программа: ${this.getProgramName(programId)}`);
            } else {
                seen.set(uniqueKey, index);
            }
        });
        
        return duplicates;
    }

    /**
     * 🧹 УДАЛЕНИЕ ДУБЛИКАТОВ
     */
    removeDuplicates(instructions) {
        console.log('\n🧹 УДАЛЕНИЕ ДУБЛИКАТОВ...');
        
        const duplicates = this.analyzeDuplicates(instructions);
        
        if (duplicates.length === 0) {
            console.log('✅ Дубликатов не найдено');
            return instructions;
        }
        
        // Получаем индексы для удаления (только дубликаты, не оригиналы)
        const indicesToRemove = duplicates.map(d => d.duplicateIndex);
        
        // Удаляем дубликаты (в обратном порядке, чтобы не сбить индексы)
        const cleanInstructions = instructions.filter((_, index) => 
            !indicesToRemove.includes(index)
        );
        
        console.log(`🧹 Удалено ${duplicates.length} дубликатов`);
        console.log(`📊 Осталось ${cleanInstructions.length} уникальных инструкций`);
        
        return cleanInstructions;
    }

    /**
     * 🔧 СБОРКА ОПТИМИЗИРОВАННОЙ ТРАНЗАКЦИИ
     */
    async buildOptimizedTransaction(instructions, addressLookupTables = []) {
        console.log('\n🔧 СБОРКА ОПТИМИЗИРОВАННОЙ ТРАНЗАКЦИИ...');
        
        // 1. Удаляем дубликаты
        const cleanInstructions = this.removeDuplicates(instructions);
        
        // 2. Получаем последний blockhash
        const { blockhash } = await this.connection.getLatestBlockhash();
        
        // 3. Создаем сообщение транзакции
        const messageV0 = new TransactionMessage({
            payerKey: this.wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: cleanInstructions,
        }).compileToV0Message(addressLookupTables);
        
        // 4. Создаем версионированную транзакцию
        const transaction = new VersionedTransaction(messageV0);
        
        // 5. Подписываем
        transaction.sign([this.wallet]);
        
        console.log(`✅ Транзакция собрана: ${cleanInstructions.length} инструкций`);
        
        return transaction;
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ ТРАНЗАКЦИИ
     */
    async testTransaction(transaction) {
        // 🚫 СИМУЛЯЦИЯ ОТКЛЮЧЕНА - ДУБЛИРУЕТ transaction-optimizer-module.js!
        console.log('\n🚫 СИМУЛЯЦИЯ ОТКЛЮЧЕНА В TRANSACTION-BUILDER (ИЗБЕГАЕМ ДУБЛИРОВАНИЯ)');
        console.log('✅ Симуляция будет выполнена в transaction-optimizer-module.js');

        // Возвращаем успех без симуляции
        return true;
    }

    /**
     * 🚀 ПОЛНЫЙ ЦИКЛ: СБОРКА + ТЕСТ + ОТПРАВКА
     */
    async buildTestAndSend(instructions, addressLookupTables = []) {
        console.log('\n🚀 ПОЛНЫЙ ЦИКЛ СБОРКИ ТРАНЗАКЦИИ...');
        
        // 1. Собираем транзакцию
        const transaction = await this.buildOptimizedTransaction(instructions, addressLookupTables);
        
        // 2. Тестируем
        const isValid = await this.testTransaction(transaction);
        
        if (!isValid) {
            console.log('❌ Транзакция не прошла тест');
            return null;
        }
        
        // 3. Отправляем (опционально)
        console.log('🚀 Отправка транзакции...');
        try {
            const signature = await this.connection.sendTransaction(transaction);
            console.log(`✅ Транзакция отправлена: ${signature}`);
            return signature;
        } catch (error) {
            console.log('❌ Ошибка отправки:', error.message);
            return null;
        }
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ ПРОГРАММЫ
     */
    getProgramName(programId) {
        const programs = {
            '11111111111111111111111111111111': 'System Program',
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
            'ComputeBudget111111111111111111111111111111': 'Compute Budget Program',
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi V2',
            'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter V6'
        };
        
        return programs[programId] || `Unknown (${programId.slice(0, 8)}...)`;
    }
}

module.exports = TransactionBuilderModule;
