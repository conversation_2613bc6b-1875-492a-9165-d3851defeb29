#!/usr/bin/env node

/**
 * 🔑 PDA CALCULATOR ДЛЯ METEORA DLMM
 * 
 * Рассчитывает все необходимые Program Derived Addresses
 * для взаимодействия с Meteora DLMM пулами
 */

const { PublicKey } = require('@solana/web3.js');

class MeteoraLBPDACalculator {
    constructor() {
        // 🔑 PROGRAM IDS
        this.METEORA_DLMM_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        
        console.log('🔑 PDA CALCULATOR ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🧮 РАСЧЕТ BIN ARRAY PDA
     */
    calculateBinArrayPDA(lbPairAddress, binArrayIndex) {
        const [binArrayPda, bump] = PublicKey.findProgramAddressSync(
            [
                Buffer.from("bin_array"),
                new PublicKey(lbPairAddress).toBuffer(),
                Buffer.from(binArrayIndex.toString())
            ],
            this.METEORA_DLMM_PROGRAM_ID
        );

        return {
            address: binArrayPda.toString(),
            bump: bump,
            seeds: ["bin_array", lbPairAddress, binArrayIndex.toString()]
        };
    }

    /**
     * 🏦 РАСЧЕТ RESERVE PDA
     */
    calculateReservePDA(lbPairAddress, tokenMint) {
        const [reservePda, bump] = PublicKey.findProgramAddressSync(
            [
                new PublicKey(lbPairAddress).toBuffer(),
                new PublicKey(tokenMint).toBuffer()
            ],
            this.METEORA_DLMM_PROGRAM_ID
        );

        return {
            address: reservePda.toString(),
            bump: bump,
            seeds: [lbPairAddress, tokenMint]
        };
    }

    /**
     * 📊 РАСЧЕТ ORACLE PDA
     */
    calculateOraclePDA(lbPairAddress) {
        const [oraclePda, bump] = PublicKey.findProgramAddressSync(
            [
                Buffer.from("oracle"),
                new PublicKey(lbPairAddress).toBuffer()
            ],
            this.METEORA_DLMM_PROGRAM_ID
        );

        return {
            address: oraclePda.toString(),
            bump: bump,
            seeds: ["oracle", lbPairAddress]
        };
    }

    /**
     * 🎯 РАСЧЕТ ВСЕХ PDA ДЛЯ СТРАТЕГИИ
     */
    calculateAllPDAsForStrategy(poolConfig) {
        console.log('\n🎯 РАСЧЕТ ВСЕХ PDA ДЛЯ СТРАТЕГИИ...');
        console.log(`   Пул: ${poolConfig.lbPairAddress}`);
        console.log(`   Bins: [${poolConfig.bins.join(', ')}]`);

        const results = {
            lbPair: poolConfig.lbPairAddress,
            binArrays: {},
            reserves: {},
            oracle: null
        };

        // 1. Рассчитываем Bin Arrays
        const binArrayIndices = this.getBinArrayIndices(poolConfig.bins);
        console.log(`   Bin Array индексы: [${binArrayIndices.join(', ')}]`);

        binArrayIndices.forEach(index => {
            const binArrayPDA = this.calculateBinArrayPDA(poolConfig.lbPairAddress, index);
            results.binArrays[index] = binArrayPDA;
            console.log(`   📊 Bin Array ${index}: ${binArrayPDA.address}`);
        });

        // 2. Рассчитываем Reserves
        const reserveX = this.calculateReservePDA(poolConfig.lbPairAddress, poolConfig.tokenX);
        const reserveY = this.calculateReservePDA(poolConfig.lbPairAddress, poolConfig.tokenY);
        
        results.reserves.x = reserveX;
        results.reserves.y = reserveY;
        
        console.log(`   🏦 Reserve X: ${reserveX.address}`);
        console.log(`   🏦 Reserve Y: ${reserveY.address}`);

        // 3. Рассчитываем Oracle
        const oracle = this.calculateOraclePDA(poolConfig.lbPairAddress);
        results.oracle = oracle;
        console.log(`   📊 Oracle: ${oracle.address}`);

        console.log('   ✅ Все PDA рассчитаны');
        return results;
    }

    /**
     * 🔢 ПОЛУЧЕНИЕ ИНДЕКСОВ BIN ARRAYS
     */
    getBinArrayIndices(bins) {
        const indices = new Set();
        bins.forEach(binId => {
            const arrayIndex = Math.floor(binId / 70);
            indices.add(arrayIndex);
        });
        return Array.from(indices).sort((a, b) => a - b);
    }

    /**
     * 📋 ГЕНЕРАЦИЯ АККАУНТОВ ДЛЯ ИНСТРУКЦИИ
     */
    generateInstructionAccounts(pdaResults, userWallet, positionKeypair) {
        const binArrays = Object.values(pdaResults.binArrays);
        
        return {
            // Основные аккаунты
            user: userWallet,
            position: positionKeypair,
            lbPair: pdaResults.lbPair,
            
            // Bin Arrays (может быть несколько)
            binArrayLower: binArrays[0]?.address || null,
            binArrayUpper: binArrays[binArrays.length - 1]?.address || binArrays[0]?.address,
            
            // Reserves
            reserveX: pdaResults.reserves.x.address,
            reserveY: pdaResults.reserves.y.address,
            
            // Oracle
            oracle: pdaResults.oracle.address,
            
            // Programs
            tokenProgram: this.TOKEN_PROGRAM_ID.toString(),
            eventAuthority: this.METEORA_DLMM_PROGRAM_ID.toString(),
            program: this.METEORA_DLMM_PROGRAM_ID.toString()
        };
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    const calculator = new MeteoraLBPDACalculator();
    
    // Конфигурация для нашей стратегии
    const poolConfig = {
        lbPairAddress: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Средний пул
        tokenX: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        tokenY: 'So11111111111111111111111111111111111111112',   // SOL
        bins: [-8, -7, -6, -5, -4, -3, -2, -1]
    };
    
    // Рассчитываем все PDA
    const pdaResults = calculator.calculateAllPDAsForStrategy(poolConfig);
    
    // Генерируем аккаунты для инструкции
    const userWallet = 'USER_WALLET_ADDRESS';
    const positionKeypair = 'POSITION_KEYPAIR_ADDRESS';
    
    const accounts = calculator.generateInstructionAccounts(pdaResults, userWallet, positionKeypair);
    
    console.log('\n📋 АККАУНТЫ ДЛЯ ИНСТРУКЦИИ:');
    console.log(JSON.stringify(accounts, null, 2));
    
    console.log('\n🎉 PDA РАСЧЕТ ЗАВЕРШЕН!');
}

module.exports = MeteoraLBPDACalculator;
