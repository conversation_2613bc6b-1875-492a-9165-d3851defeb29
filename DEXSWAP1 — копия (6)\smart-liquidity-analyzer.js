/**
 * 🧠 ЕДИНСТВЕННЫЙ УМНЫЙ АНАЛИЗАТОР ЛИКВИДНОСТИ
 * 
 * Анализирует 3 бина каждого пула (активный + соседние) и рассчитывает:
 * - Максимальную ликвидность в каждом бине
 * - Оптимальные суммы займов
 * - Распределение ликвидности между пулами
 * - Суммы для всех операций (BORROW, ADD LIQUIDITY, SWAP, etc.)
 */

class SmartLiquidityAnalyzer {
    constructor() {
        console.log('🧠 УМНЫЙ АНАЛИЗАТОР ЛИКВИДНОСТИ инициализирован');
        
        // Конфигурация анализатора
        this.config = {
            targetCoverage: 99.0,           // 99% покрытие ликвидности
            liquidityMultiplier: 3,         // Умножаем на 3 (для 3 бинов)
            openPositionPercent: 33,        // 33% для открытия позиции

            // 🔥 МИНИМАЛЬНЫЕ ПОРОГИ (ХАРДКОД)
            minUsdcAmount: 3300000,         // Минимум 3.3M USDC
            minWsolAmount: 17647,           // Минимум 17,647 WSOL (эквивалент 3.3M USDC)
            minOpenPositionAmount: 1089000, // Минимум 1,089,000 для открытия позиции (33%)

            maxLoanAmount: 50000000         // Максимум 50M токенов
        };
    }

    /**
     * 🎯 ГЛАВНАЯ ФУНКЦИЯ: АНАЛИЗ 3 БИНОВ КАЖДОГО ПУЛА
     */
    async analyzeThreeBinsLiquidity(pool1Data, pool2Data) {
        console.log('🧠 АНАЛИЗ 3 БИНОВ КАЖДОГО ПУЛА...');
        console.log(`   Pool 1: ${pool1Data.poolAddress?.slice(0,8)}... (${pool1Data.threeBins?.length || 0} бинов)`);
        console.log(`   Pool 2: ${pool2Data.poolAddress?.slice(0,8)}... (${pool2Data.threeBins?.length || 0} бинов)`);

        try {
            // 🔍 ШАГ 1: АНАЛИЗ ЛИКВИДНОСТИ 3 БИНОВ POOL 1
            const pool1Analysis = this.analyzePoolThreeBins(pool1Data, 'POOL_1');
            
            // 🔍 ШАГ 2: АНАЛИЗ ЛИКВИДНОСТИ 3 БИНОВ POOL 2  
            const pool2Analysis = this.analyzePoolThreeBins(pool2Data, 'POOL_2');

            // 🎯 ШАГ 3: ОПРЕДЕЛЕНИЕ МАКСИМАЛЬНОЙ СУММЫ
            const maxLiquidityNeeded = Math.max(
                pool1Analysis.totalLiquidityNeeded,
                pool2Analysis.totalLiquidityNeeded
            );

            console.log(`\n🎯 СРАВНЕНИЕ ЛИКВИДНОСТИ:`);
            console.log(`   Pool 1 требует: ${pool1Analysis.totalLiquidityNeeded.toLocaleString()} токенов`);
            console.log(`   Pool 2 требует: ${pool2Analysis.totalLiquidityNeeded.toLocaleString()} токенов`);
            console.log(`   Максимум: ${maxLiquidityNeeded.toLocaleString()} токенов`);

            // 🧮 ШАГ 4: РАСЧЕТ ВСЕХ СУММ
            const calculatedAmounts = this.calculateAllAmounts(maxLiquidityNeeded, pool1Analysis, pool2Analysis);

            return {
                pool1Analysis,
                pool2Analysis,
                maxLiquidityNeeded,
                calculatedAmounts,
                success: true
            };

        } catch (error) {
            console.log(`❌ ОШИБКА АНАЛИЗА: ${error.message}`);
            return {
                error: error.message,
                success: false
            };
        }
    }

    /**
     * 🔍 АНАЛИЗ 3 БИНОВ ОДНОГО ПУЛА (ТОЛЬКО НУЖНЫЕ ТОКЕНЫ!)
     */
    analyzePoolThreeBins(poolData, poolName) {
        console.log(`\n🔍 АНАЛИЗ ${poolName}:`);

        if (!poolData.threeBins || poolData.threeBins.length !== 3) {
            throw new Error(`${poolName}: Нет данных о 3 бинах`);
        }

        const bins = poolData.threeBins;
        let maxBinLiquidity = 0;
        let activeBinLiquidity = 0;

        // 🔥 ОПРЕДЕЛЯЕМ КАКОЙ ТОКЕН АНАЛИЗИРОВАТЬ
        const isPool1 = poolName === 'POOL_1';
        const targetToken = isPool1 ? 'WSOL' : 'USDC';

        console.log(`   🎯 АНАЛИЗИРУЕМ ТОЛЬКО ${targetToken} ЛИКВИДНОСТЬ (${isPool1 ? 'для первого свопа' : 'для второго свопа'})`);

        // Анализируем каждый бин
        bins.forEach((bin, index) => {
            const binName = index === 0 ? 'ЛЕВЫЙ' : index === 1 ? 'АКТИВНЫЙ' : 'ПРАВЫЙ';

            // 🔥 БЕРЕМ ТОЛЬКО НУЖНЫЙ ТОКЕН!
            const targetLiquidity = isPool1 ? bin.liquidityX : bin.liquidityY; // Pool1=WSOL(X), Pool2=USDC(Y)
            const totalLiquidity = bin.liquidityX + bin.liquidityY;

            console.log(`   ${binName} бин (${bin.binId}): ${targetLiquidity.toLocaleString()} ${targetToken} (из ${totalLiquidity.toLocaleString()} общей)`);
            console.log(`      X(WSOL): ${bin.liquidityX.toLocaleString()}, Y(USDC): ${bin.liquidityY.toLocaleString()}`);

            if (targetLiquidity > maxBinLiquidity) {
                maxBinLiquidity = targetLiquidity;
            }

            if (bin.isActive) {
                activeBinLiquidity = targetLiquidity;
            }
        });

        // 🎯 РАСЧЕТ НЕОБХОДИМОЙ ЛИКВИДНОСТИ С МИНИМАЛЬНЫМИ ПОРОГАМИ
        // Берем максимальную ликвидность из 3 бинов
        const targetLiquidity = maxBinLiquidity * (this.config.targetCoverage / 100);

        // Умножаем на 3 (для распределения по 3 бинам)
        let totalLiquidityNeeded = targetLiquidity * this.config.liquidityMultiplier;

        // 🔥 ПРИМЕНЯЕМ МИНИМАЛЬНЫЕ ПОРОГИ!
        const minThreshold = isPool1 ? this.config.minWsolAmount : this.config.minUsdcAmount;

        if (totalLiquidityNeeded < minThreshold) {
            console.log(`   ⚠️ Потребность ${totalLiquidityNeeded.toLocaleString()} меньше минимума ${minThreshold.toLocaleString()}`);
            totalLiquidityNeeded = minThreshold;
            console.log(`   🔥 ПРИМЕНЯЕМ МИНИМАЛЬНЫЙ ПОРОГ: ${minThreshold.toLocaleString()} ${isPool1 ? 'WSOL' : 'USDC'}`);
        }

        console.log(`   📊 РЕЗУЛЬТАТ ${poolName}:`);
        console.log(`      Максимальная ${isPool1 ? 'WSOL' : 'USDC'} ликвидность бина: ${maxBinLiquidity.toLocaleString()}`);
        console.log(`      Целевая ликвидность (${this.config.targetCoverage}%): ${targetLiquidity.toLocaleString()}`);
        console.log(`      Общая потребность (x3): ${totalLiquidityNeeded.toLocaleString()}`);
        console.log(`      Минимальный порог: ${minThreshold.toLocaleString()} ${isPool1 ? 'WSOL' : 'USDC'}`);

        return {
            poolName,
            maxBinLiquidity,
            activeBinLiquidity,
            targetLiquidity,
            totalLiquidityNeeded,
            minThreshold,
            bins: bins
        };
    }

    /**
     * 🧮 РАСЧЕТ ВСЕХ СУММ ДЛЯ ОПЕРАЦИЙ С МИНИМАЛЬНЫМИ ПОРОГАМИ
     */
    calculateAllAmounts(maxLiquidityNeeded, pool1Analysis, pool2Analysis) {
        console.log(`\n🧮 РАСЧЕТ ВСЕХ СУММ НА ОСНОВЕ МАКСИМАЛЬНОЙ ПОТРЕБНОСТИ: ${maxLiquidityNeeded.toLocaleString()}`);

        // 🔥 ПРИМЕНЯЕМ МИНИМАЛЬНЫЕ ПОРОГИ ДЛЯ КАЖДОГО ПУЛА ОТДЕЛЬНО!
        const borrowWSOL = Math.max(pool1Analysis.totalLiquidityNeeded, this.config.minWsolAmount);
        const borrowUSDC = Math.max(pool2Analysis.totalLiquidityNeeded, this.config.minUsdcAmount);

        // Ограничиваем максимумом
        const finalBorrowWSOL = Math.min(borrowWSOL, this.config.maxLoanAmount);
        const finalBorrowUSDC = Math.min(borrowUSDC, this.config.maxLoanAmount);

        // 🎯 ПРАВИЛЬНАЯ ЛОГИКА: ЛИКВИДНОСТЬ + ТОРГОВЛЯ = ОТДЕЛЬНЫЕ СУММЫ!
        const pool1LiquidityAmount = finalBorrowWSOL;  // Pool 1 = ВСЕ WSOL (17,647)
        const pool2LiquidityAmount = finalBorrowUSDC;  // Pool 2 = ВСЯ СУММА USDC (3,300,000) - НЕ 67%!

        // 🔥 ТОРГОВАЯ СУММА - ДОПОЛНИТЕЛЬНАЯ К ЛИКВИДНОСТИ!
        let openPositionAmount = Math.floor(pool2LiquidityAmount * (this.config.openPositionPercent / 100));

        // 🧮 ОБЩИЙ ЗАЙМ USDC = ЛИКВИДНОСТЬ + ТОРГОВЛЯ
        const totalBorrowUSDC = pool2LiquidityAmount + openPositionAmount;

        console.log(`🔥 ПРАВИЛЬНЫЕ РАСЧЕТЫ:`);
        console.log(`   Pool 2 ликвидность: ${pool2LiquidityAmount.toLocaleString()} USDC`);
        console.log(`   Торговая сумма: ${openPositionAmount.toLocaleString()} USDC`);
        console.log(`   ОБЩИЙ ЗАЙМ USDC: ${totalBorrowUSDC.toLocaleString()} USDC`);

        // Применяем минимальный порог для торговли
        if (openPositionAmount < this.config.minOpenPositionAmount) {
            console.log(`   ⚠️ Торговая сумма ${openPositionAmount.toLocaleString()} меньше минимума ${this.config.minOpenPositionAmount.toLocaleString()}`);
            openPositionAmount = this.config.minOpenPositionAmount;
            console.log(`   🔥 ПРИМЕНЯЕМ МИНИМАЛЬНЫЙ ПОРОГ ТОРГОВЛИ: ${this.config.minOpenPositionAmount.toLocaleString()}`);
        }

        console.log(`   💰 ЗАЙМЫ (ЭТАП 1) - КЛАДУТ НА АККАУНТЫ:`);
        console.log(`      USDC аккаунт получит: ${totalBorrowUSDC.toLocaleString()} USDC (ликвидность + торговля)`);
        console.log(`      WSOL аккаунт получит: ${finalBorrowWSOL.toLocaleString()} WSOL`);

        console.log(`   🏊 ДОБАВЛЕНИЕ ЛИКВИДНОСТИ (ЭТАП 3) - БЕРУТ С АККАУНТОВ:`);
        console.log(`      Pool 1 (инструкция #6): ${pool1LiquidityAmount.toLocaleString()} WSOL с WSOL аккаунта`);
        console.log(`      Pool 2 (инструкция #7): ${pool2LiquidityAmount.toLocaleString()} USDC с USDC аккаунта`);

        console.log(`   📈 ТОРГОВЛЯ (ЭТАП 4) - ОСТАЕТСЯ НА USDC АККАУНТЕ:`);
        console.log(`      Остается для торговли: ${openPositionAmount.toLocaleString()} USDC`);
        console.log(`      Своп 1: ${openPositionAmount.toLocaleString()} USDC → WSOL (Pool 1)`);
        console.log(`      Своп 2: ВСЯ полученная WSOL → USDC (Pool 2)`);

        return {
            // ЗАЙМЫ
            borrowUSDC: totalBorrowUSDC,  // 🔥 ОБЩИЙ ЗАЙМ = ЛИКВИДНОСТЬ + ТОРГОВЛЯ!
            borrowWSOL: finalBorrowWSOL,

            // ДОБАВЛЕНИЕ ЛИКВИДНОСТИ
            pool1LiquidityAmount,
            pool2LiquidityAmount,

            // ТОРГОВЫЕ ОПЕРАЦИИ
            openPositionAmount,

            // МЕТАДАННЫЕ
            maxLiquidityNeeded: Math.max(finalBorrowWSOL, finalBorrowUSDC),
            openPositionPercent: this.config.openPositionPercent,
            liquidityMultiplier: this.config.liquidityMultiplier,
            targetCoverage: this.config.targetCoverage
        };
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ РЕКОМЕНДАЦИЙ ДЛЯ ИНСТРУКЦИЙ
     */
    getInstructionRecommendations(analysisResult) {
        if (!analysisResult.success) {
            throw new Error(`Анализ не выполнен: ${analysisResult.error}`);
        }

        const amounts = analysisResult.calculatedAmounts;
        
        return {
            // ДЛЯ BORROW ИНСТРУКЦИЙ
            borrowInstructions: {
                usdcAmount: amounts.borrowUSDC,
                wsolAmount: amounts.borrowWSOL
            },
            
            // ДЛЯ ADD LIQUIDITY ИНСТРУКЦИЙ
            liquidityInstructions: {
                pool1Amount: amounts.pool1LiquidityAmount,
                pool2Amount: amounts.pool2LiquidityAmount
            },
            
            // ДЛЯ SWAP ИНСТРУКЦИЙ
            swapInstructions: {
                firstSwapAmount: amounts.openPositionAmount,
                secondSwapAmount: 'ALL_REMAINING' // Все что осталось после первого свопа
            },
            
            // ДЛЯ REMOVE LIQUIDITY И CLAIM FEE
            cleanupInstructions: {
                removeLiquidityAmount: 'ALL',
                claimFeeAmount: 'ALL'
            }
        };
    }
}

module.exports = SmartLiquidityAnalyzer;
