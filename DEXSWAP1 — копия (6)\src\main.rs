use anyhow::Result;
use clap::{Arg, Command};
use log::{info, warn, error};
use std::sync::Arc;
use tokio::sync::Mutex;

mod scanner;
mod fuzzer;
mod exploits;
mod reporter;
mod config;
mod database;

use scanner::VulnerabilityScanner;
use fuzzer::{ReentrancyFuzzer, OverflowFuzzer, ValidationFuzzer, MemoryFuzzer};
use reporter::BugReporter;
use config::Config;
use database::Database;

#[tokio::main]
async fn main() -> Result<()> {
    // Инициализация логирования
    env_logger::init();
    
    // Парсинг аргументов командной строки
    let matches = Command::new("Solana Bug Hunter")
        .version("1.0.0")
        .author("Bug Bounty Hunter")
        .about("🔥 Ultimate Solana Bug Bounty Hunting System")
        .arg(
            Arg::new("mode")
                .short('m')
                .long("mode")
                .value_name("MODE")
                .help("Hunting mode: scan, fuzz, exploit, report")
                .required(true)
        )
        .arg(
            Arg::new("target")
                .short('t')
                .long("target")
                .value_name("PROGRAM_ID")
                .help("Target program ID (optional)")
        )
        .arg(
            Arg::new("threads")
                .short('j')
                .long("threads")
                .value_name("NUM")
                .help("Number of parallel threads")
                .default_value("8")
        )
        .arg(
            Arg::new("output")
                .short('o')
                .long("output")
                .value_name("FILE")
                .help("Output file for results")
                .default_value("bug_report.json")
        )
        .get_matches();

    // Загрузка конфигурации
    let config = Config::load().await?;
    
    // Инициализация базы данных
    let db = Database::new("bug_hunter.db").await?;
    
    println!("🔥 SOLANA BUG BOUNTY HUNTER v1.0.0");
    println!("🎯 Target: Massive bug discovery and $25M+ rewards");
    println!("⚡ Mode: {}", matches.get_one::<String>("mode").unwrap());
    
    match matches.get_one::<String>("mode").unwrap().as_str() {
        "scan" => {
            info!("Starting vulnerability scanning mode");
            run_scanner(&config, &db).await?;
        },
        "fuzz" => {
            info!("Starting fuzzing mode");
            run_fuzzer(&config, &db).await?;
        },
        "exploit" => {
            info!("Starting exploit generation mode");
            run_exploit_generator(&config, &db).await?;
        },
        "report" => {
            info!("Starting bug reporting mode");
            run_reporter(&config, &db).await?;
        },
        "hunt" => {
            info!("Starting full hunting mode - ALL SYSTEMS GO!");
            run_full_hunt(&config, &db).await?;
        },
        _ => {
            error!("Invalid mode. Use: scan, fuzz, exploit, report, or hunt");
            std::process::exit(1);
        }
    }
    
    Ok(())
}

async fn run_scanner(config: &Config, db: &Database) -> Result<()> {
    println!("🔍 STARTING VULNERABILITY SCANNER");
    
    let scanner = VulnerabilityScanner::new(config.clone()).await?;
    let results = scanner.scan_all_programs().await?;
    
    println!("✅ Scan completed. Found {} potential vulnerabilities", results.len());
    
    // Сохраняем результаты в базу данных
    for result in results {
        db.save_vulnerability(&result).await?;
    }
    
    Ok(())
}

async fn run_fuzzer(config: &Config, db: &Database) -> Result<()> {
    println!("🎯 STARTING MASSIVE FUZZING OPERATION");
    println!("📊 Target: 185M test cases across 4 categories");
    
    // Создаем все фаззеры
    let reentrancy_fuzzer = Arc::new(ReentrancyFuzzer::new(config.clone()).await?);
    let overflow_fuzzer = Arc::new(OverflowFuzzer::new(config.clone()).await?);
    let validation_fuzzer = Arc::new(ValidationFuzzer::new(config.clone()).await?);
    let memory_fuzzer = Arc::new(MemoryFuzzer::new(config.clone()).await?);
    
    // Запускаем все фаззеры параллельно
    let tasks = vec![
        {
            let fuzzer = reentrancy_fuzzer.clone();
            let db = db.clone();
            tokio::spawn(async move {
                fuzzer.fuzz_reentrancy_patterns(&db).await
            })
        },
        {
            let fuzzer = overflow_fuzzer.clone();
            let db = db.clone();
            tokio::spawn(async move {
                fuzzer.fuzz_integer_operations(&db).await
            })
        },
        {
            let fuzzer = validation_fuzzer.clone();
            let db = db.clone();
            tokio::spawn(async move {
                fuzzer.fuzz_account_validation(&db).await
            })
        },
        {
            let fuzzer = memory_fuzzer.clone();
            let db = db.clone();
            tokio::spawn(async move {
                fuzzer.fuzz_memory_operations(&db).await
            })
        },
    ];
    
    // Мониторим прогресс
    let progress_task = {
        let reentrancy = reentrancy_fuzzer.clone();
        let overflow = overflow_fuzzer.clone();
        let validation = validation_fuzzer.clone();
        let memory = memory_fuzzer.clone();
        
        tokio::spawn(async move {
            monitor_fuzzing_progress(reentrancy, overflow, validation, memory).await
        })
    };
    
    // Ждем завершения всех фаззеров
    for task in tasks {
        if let Err(e) = task.await? {
            error!("Fuzzer task failed: {}", e);
        }
    }
    
    progress_task.abort();
    
    println!("🎉 FUZZING COMPLETED!");
    
    Ok(())
}

async fn run_exploit_generator(config: &Config, db: &Database) -> Result<()> {
    println!("🔧 GENERATING PROOF-OF-CONCEPT EXPLOITS");
    
    // Получаем все найденные уязвимости из базы данных
    let vulnerabilities = db.get_all_vulnerabilities().await?;
    
    println!("📊 Found {} vulnerabilities to exploit", vulnerabilities.len());
    
    for vuln in vulnerabilities {
        if vuln.severity == "Critical" || vuln.severity == "High" {
            println!("🎯 Generating exploit for: {} (${} reward)", 
                    vuln.program_id, vuln.estimated_reward);
            
            // Генерируем PoC эксплойт
            let exploit = exploits::generate_poc(&vuln).await?;
            
            // Сохраняем эксплойт
            db.save_exploit(&exploit).await?;
            
            println!("✅ Exploit generated and saved");
        }
    }
    
    Ok(())
}

async fn run_reporter(config: &Config, db: &Database) -> Result<()> {
    println!("📤 STARTING BUG REPORTING SYSTEM");
    
    let reporter = BugReporter::new(config.clone()).await?;
    
    // Получаем все готовые эксплойты
    let exploits = db.get_all_exploits().await?;
    
    println!("📊 Found {} exploits ready for submission", exploits.len());
    
    let mut total_potential_rewards = 0u64;
    
    for exploit in &exploits {
        total_potential_rewards += exploit.estimated_reward;
    }
    
    println!("💰 Total potential rewards: ${}", total_potential_rewards);
    
    // Отправляем отчеты в bug bounty программы
    for exploit in exploits {
        println!("📤 Submitting exploit: {} (${} potential)", 
                exploit.title, exploit.estimated_reward);
        
        match reporter.submit_exploit(&exploit).await {
            Ok(submission_id) => {
                println!("✅ Submitted successfully. ID: {}", submission_id);
                db.update_exploit_status(&exploit.id, "submitted", &submission_id).await?;
            },
            Err(e) => {
                error!("Failed to submit exploit: {}", e);
            }
        }
        
        // Задержка между отправками
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
    }
    
    println!("🎉 ALL EXPLOITS SUBMITTED!");
    
    Ok(())
}

async fn run_full_hunt(config: &Config, db: &Database) -> Result<()> {
    println!("🚀 STARTING FULL BUG HUNTING OPERATION");
    println!("🎯 This is the complete end-to-end bug bounty hunting system");
    println!("💰 Target: $25M+ in bug bounty rewards");
    
    // Этап 1: Сканирование
    println!("\n🔍 PHASE 1: VULNERABILITY SCANNING");
    run_scanner(config, db).await?;
    
    // Этап 2: Фаззинг
    println!("\n🎯 PHASE 2: MASSIVE FUZZING");
    run_fuzzer(config, db).await?;
    
    // Этап 3: Генерация эксплойтов
    println!("\n🔧 PHASE 3: EXPLOIT GENERATION");
    run_exploit_generator(config, db).await?;
    
    // Этап 4: Отправка отчетов
    println!("\n📤 PHASE 4: BUG REPORTING");
    run_reporter(config, db).await?;
    
    // Финальная статистика
    let stats = db.get_hunting_statistics().await?;
    
    println!("\n🎉 HUNTING OPERATION COMPLETED!");
    println!("📊 FINAL STATISTICS:");
    println!("  🔍 Programs scanned: {}", stats.programs_scanned);
    println!("  🐛 Vulnerabilities found: {}", stats.vulnerabilities_found);
    println!("  🚨 Critical bugs: {}", stats.critical_bugs);
    println!("  🔧 Exploits generated: {}", stats.exploits_generated);
    println!("  📤 Reports submitted: {}", stats.reports_submitted);
    println!("  💰 Potential rewards: ${}", stats.potential_rewards);
    println!("  ✅ Confirmed rewards: ${}", stats.confirmed_rewards);
    
    Ok(())
}

async fn monitor_fuzzing_progress(
    reentrancy: Arc<ReentrancyFuzzer>,
    overflow: Arc<OverflowFuzzer>, 
    validation: Arc<ValidationFuzzer>,
    memory: Arc<MemoryFuzzer>,
) -> Result<()> {
    use indicatif::{ProgressBar, ProgressStyle};
    
    let pb = ProgressBar::new(185_000_000);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
            .unwrap()
            .progress_chars("#>-"),
    );
    
    loop {
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        let total_tests = 
            reentrancy.get_test_count() +
            overflow.get_test_count() +
            validation.get_test_count() +
            memory.get_test_count();
        
        pb.set_position(total_tests);
        
        if total_tests >= 185_000_000 {
            break;
        }
    }
    
    pb.finish_with_message("🎉 Fuzzing completed!");
    Ok(())
}
