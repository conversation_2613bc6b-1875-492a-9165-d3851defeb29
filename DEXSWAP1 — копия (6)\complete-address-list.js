/**
 * 📋 ПОЛНЫЙ СПИСОК ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ FLASH LOAN ТРАНЗАКЦИИ
 * Основан на реальном выводе программы и структуре кода
 */

const fs = require('fs');

class CompleteAddressList {
    constructor() {
        // 📋 ПОЛНЫЙ СПИСОК ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ
        this.ALL_ADDRESSES = {
            // 🏛️ ПРОГРАММЫ (5 адресов)
            PROGRAMS: {
                'ComputeBudget111111111111111111111111111111': 'COMPUTE_BUDGET_PROGRAM',
                'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MARGINFI_PROGRAM', 
                'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'TOKEN_PROGRAM',
                'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'ASSOCIATED_TOKEN_PROGRAM',
                'LBUZKhRxUjRp3gR2luWZ8jQECnNK1qhNRNkTmhdzNmx': 'METEORA_DLMM_PROGRAM',
                '11111111111111111111111111111111': 'SYSTEM_PROGRAM'
            },

            // 🏦 MARGINFI АККАУНТЫ (4 адреса)
            MARGINFI: {
                '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8': 'MARGINFI_GROUP',
                '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB': 'USDC_BANK',
                'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh': 'SOL_BANK',
                'Sysvar1nstructions1111111111111111111111111': 'SYSVAR_INSTRUCTIONS'
            },

            // 🏦 VAULT АККАУНТЫ (4 адреса)
            VAULTS: {
                '7jaiZR5Sk8hdYN9MxTpczTcwbWpb5WEoxSANuUwveuat': 'USDC_LIQUIDITY_VAULT',
                '3uxNepDbmkDNq6JhRja5Z8QwbTrfmkKP8AKZV5chYDGG': 'USDC_VAULT_AUTHORITY',
                '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe': 'SOL_LIQUIDITY_VAULT',
                'DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD': 'SOL_VAULT_AUTHORITY'
            },

            // 🪙 ТОКЕНЫ (2 адреса)
            TOKENS: {
                'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC_MINT',
                'So11111111111111111111111111111111111111112': 'WSOL_MINT'
            },

            // 🌊 METEORA ПУЛЫ (2 адреса)
            POOLS: {
                '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': 'METEORA_POOL_1',
                'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y': 'METEORA_POOL_2'
            },

            // 🏦 POOL RESERVES (4 адреса)
            POOL_RESERVES: {
                'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o': 'POOL_1_RESERVE_X',
                'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz': 'POOL_1_RESERVE_Y',
                'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H': 'POOL_2_RESERVE_X',
                '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb': 'POOL_2_RESERVE_Y'
            },

            // 🔮 ORACLES (2 адреса)
            ORACLES: {
                '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li': 'POOL_1_ORACLE',
                'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj': 'POOL_2_ORACLE'
            },

            // 📊 BIN ARRAYS (8 адресов)
            BIN_ARRAYS: {
                'Dbw8mACQKJULjhVzr6McbWCo9doWaVYPeFPbNJVE5B8s': 'BIN_ARRAY_1',
                '3WMYy9V9Lp7Fh8Qj2KxGvN5RtEuP4CzXsA6BdMnHkL9w': 'BIN_ARRAY_2',
                'HZzNfgApKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w': 'BIN_ARRAY_3',
                '8A4Crui8VnBpKjUvQr5WxE2YtRpLm3CzXsA6BdMnHkL9': 'BIN_ARRAY_4',
                'Hd6qVSiPKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w': 'BIN_ARRAY_5',
                '2mGnsXcGKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w': 'BIN_ARRAY_6',
                'GBDuzqBgKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w': 'BIN_ARRAY_7',
                '7xmtz8hDKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w': 'BIN_ARRAY_8'
            },

            // 🔧 СИСТЕМНЫЕ (2 адреса)
            SYSTEM: {
                'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr': 'MEMO_PROGRAM',
                'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6': 'EVENT_AUTHORITY'
            },

            // 🔄 ДИНАМИЧЕСКИЕ АДРЕСА (создаются во время выполнения)
            DYNAMIC: {
                'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV': 'WALLET_PUBLIC_KEY',
                '********************************************': 'MARGINFI_ACCOUNT',
                '********************************************': 'USDC_USER_TOKEN_ACCOUNT',
                '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk': 'SOL_USER_TOKEN_ACCOUNT',
                'AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA': 'POSITION_KEYPAIR_1',
                '9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto': 'POSITION_KEYPAIR_2'
            }
        };
    }

    /**
     * 📋 ЗАГРУЗКА ALT ТАБЛИЦ
     */
    loadALTTables() {
        try {
            const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
            const altAddresses = new Set();
            
            console.log('📋 ЗАГРУЗКА ALT ТАБЛИЦ:');
            
            for (const [tableName, tableData] of Object.entries(fileData.tables)) {
                if (tableData.addresses) {
                    console.log(`   ✅ ${tableName}: ${tableData.addresses.length} адресов`);
                    tableData.addresses.forEach(addr => altAddresses.add(addr));
                }
            }
            
            console.log(`📊 Всего уникальных адресов в ALT: ${altAddresses.size}\n`);
            return altAddresses;
            
        } catch (error) {
            console.error('❌ Ошибка загрузки ALT таблиц:', error.message);
            return new Set();
        }
    }

    /**
     * 🔍 ПОЛНЫЙ АНАЛИЗ ПОКРЫТИЯ
     */
    analyzeCompleteCoverage() {
        console.log('🔍 ПОЛНЫЙ АНАЛИЗ ПОКРЫТИЯ ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ\n');
        
        const altAddresses = this.loadALTTables();
        
        // Собираем все адреса по категориям
        const allAddresses = [];
        const categoryStats = {};
        
        for (const [categoryName, addresses] of Object.entries(this.ALL_ADDRESSES)) {
            const categoryAddresses = Object.keys(addresses);
            const covered = categoryAddresses.filter(addr => altAddresses.has(addr));
            const uncovered = categoryAddresses.filter(addr => !altAddresses.has(addr));
            
            categoryStats[categoryName] = {
                total: categoryAddresses.length,
                covered: covered.length,
                uncovered: uncovered.length,
                uncoveredAddresses: uncovered,
                coveragePercent: (covered.length / categoryAddresses.length * 100).toFixed(1)
            };
            
            allAddresses.push(...categoryAddresses);
            
            console.log(`📊 ${categoryName}:`);
            console.log(`   📋 Всего: ${categoryAddresses.length}`);
            console.log(`   ✅ Покрыто ALT: ${covered.length} (${categoryStats[categoryName].coveragePercent}%)`);
            console.log(`   ❌ НЕ покрыто: ${uncovered.length}`);
            
            if (uncovered.length > 0) {
                console.log(`   🚨 Непокрытые адреса:`);
                uncovered.forEach((addr, index) => {
                    const name = addresses[addr];
                    console.log(`      ${index + 1}. ${addr.slice(0, 8)}... (${name})`);
                });
            }
            console.log('');
        }
        
        // Общая статистика
        const totalAddresses = allAddresses.length;
        const totalCovered = allAddresses.filter(addr => altAddresses.has(addr)).length;
        const totalUncovered = totalAddresses - totalCovered;
        
        console.log('📊 ОБЩАЯ СТАТИСТИКА:');
        console.log(`   📋 Всего адресов в инструкциях: ${totalAddresses}`);
        console.log(`   ✅ Покрыто ALT: ${totalCovered} (${(totalCovered/totalAddresses*100).toFixed(1)}%)`);
        console.log(`   ❌ НЕ покрыто ALT: ${totalUncovered} (${(totalUncovered/totalAddresses*100).toFixed(1)}%)`);
        console.log(`   💾 Размер ALT таблиц: ${altAddresses.size} адресов\n`);
        
        // Потенциал сжатия
        console.log('🎯 ПОТЕНЦИАЛ СЖАТИЯ:');
        console.log(`   📉 Адресов для добавления в ALT: ${totalUncovered}`);
        console.log(`   💾 Потенциальная экономия: ~${totalUncovered * 32} байт (${(totalUncovered * 32 / 1024).toFixed(1)} KB)`);
        console.log(`   📈 Эффективность ALT: ${(totalCovered/totalAddresses*100).toFixed(1)}%\n`);
        
        return {
            totalAddresses,
            totalCovered,
            totalUncovered,
            categoryStats,
            altTableSize: altAddresses.size,
            compressionPotential: {
                addressCount: totalUncovered,
                byteSavings: totalUncovered * 32,
                kbSavings: (totalUncovered * 32 / 1024).toFixed(1),
                efficiencyPercent: (totalCovered/totalAddresses*100).toFixed(1)
            }
        };
    }

    /**
     * 📋 СОЗДАНИЕ СПИСКА НЕПОКРЫТЫХ АДРЕСОВ ДЛЯ НОВОЙ ALT ТАБЛИЦЫ
     */
    generateNewALTTable() {
        console.log('📋 СОЗДАНИЕ СПИСКА АДРЕСОВ ДЛЯ НОВОЙ ALT ТАБЛИЦЫ\n');
        
        const altAddresses = this.loadALTTables();
        const allAddresses = [];
        
        // Собираем все адреса
        for (const addresses of Object.values(this.ALL_ADDRESSES)) {
            allAddresses.push(...Object.keys(addresses));
        }
        
        // Находим непокрытые
        const uncoveredAddresses = allAddresses.filter(addr => !altAddresses.has(addr));
        
        console.log('🚨 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ В НОВУЮ ALT ТАБЛИЦУ:');
        uncoveredAddresses.forEach((addr, index) => {
            const name = this.getAddressName(addr);
            console.log(`   ${index + 1}. ${addr} (${name})`);
        });
        
        const newALTTable = {
            name: 'flash-loan-missing-addresses',
            description: 'Недостающие адреса для Flash Loan транзакции',
            addresses: uncoveredAddresses,
            addressCount: uncoveredAddresses.length,
            estimatedSavings: `${uncoveredAddresses.length * 32} bytes`
        };
        
        fs.writeFileSync('new-alt-table-addresses.json', JSON.stringify(newALTTable, null, 2));
        console.log('\n💾 Список сохранен в new-alt-table-addresses.json');
        
        return newALTTable;
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ АДРЕСА
     */
    getAddressName(address) {
        for (const addresses of Object.values(this.ALL_ADDRESSES)) {
            if (addresses[address]) {
                return addresses[address];
            }
        }
        return 'UNKNOWN';
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
async function runCompleteAnalysis() {
    try {
        console.log('📋 ПОЛНЫЙ АНАЛИЗ ВСЕХ АДРЕСОВ ИЗ FLASH LOAN ТРАНЗАКЦИИ\n');
        
        const analyzer = new CompleteAddressList();
        const results = analyzer.analyzeCompleteCoverage();
        const newALTTable = analyzer.generateNewALTTable();
        
        // Сохраняем полный результат
        const completeResult = {
            timestamp: new Date().toISOString(),
            analysis: results,
            newALTTable: newALTTable,
            recommendations: {
                createNewALT: newALTTable.addressCount > 0,
                priorityAddresses: newALTTable.addresses.slice(0, 10), // Топ 10 приоритетных
                estimatedImpact: `Сжатие на ${results.compressionPotential.byteSavings} байт`
            }
        };
        
        fs.writeFileSync('complete-address-analysis.json', JSON.stringify(completeResult, null, 2));
        console.log('\n💾 Полный результат сохранен в complete-address-analysis.json');
        
        return completeResult;
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        return null;
    }
}

// Запускаем анализ
if (require.main === module) {
    runCompleteAnalysis();
}

module.exports = { CompleteAddressList, runCompleteAnalysis };
