#!/usr/bin/env node

/**
 * 🔥 АНАЛИЗ РЕАЛЬНОЙ MARGINFI ТРАНЗАКЦИИ
 * Перехватываем и анализируем buildFlashLoanTx из основного кода
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const fs = require('fs');

// Глобальная переменная для перехвата транзакции
global.INTERCEPTED_TRANSACTION = null;

// Перехватчик для buildFlashLoanTx
function interceptBuildFlashLoanTx() {
    console.log('🔥 УСТАНОВКА ПЕРЕХВАТЧИКА buildFlashLoanTx...');
    
    // Перехватываем MarginFi buildFlashLoanTx
    const originalConsoleLog = console.log;
    
    console.log = function(...args) {
        const message = args.join(' ');
        
        // Ищем момент создания транзакции
        if (message.includes('buildFlashLoanTx вернул готовую VersionedTransaction')) {
            console.log('🚨 ПЕРЕХВАЧЕН МОМЕНТ СОЗДАНИЯ buildFlashLoanTx!');
        }
        
        return originalConsoleLog.apply(console, args);
    };
}

async function analyzeRealTransaction() {
    console.log('🔥 АНАЛИЗ РЕАЛЬНОЙ MARGINFI ТРАНЗАКЦИИ');
    console.log('=' .repeat(70));

    try {
        // 1. Подключение
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
        
        console.log(`✅ Кошелек: ${wallet.publicKey.toString()}`);
        
        // 2. Загружаем ALT таблицы
        console.log('\n📁 Загрузка ALT таблиц...');
        
        const altTables = [];
        
        // Meteora ALT
        if (fs.existsSync('./meteora-alt-cache.json')) {
            const meteora = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
            
            for (const result of meteora.validationResults?.slice(0, 2) || []) {
                if (result.valid && result.address) {
                    try {
                        const altAccount = await connection.getAddressLookupTable(new PublicKey(result.address));
                        if (altAccount?.value) {
                            altTables.push(altAccount.value);
                            console.log(`✅ Meteora ALT: ${result.address.slice(0, 8)}... (${altAccount.value.state.addresses.length} аккаунтов)`);
                        }
                    } catch (error) {
                        console.log(`❌ Ошибка загрузки Meteora ALT: ${error.message}`);
                    }
                }
            }
        }
        
        // MarginFi ALT
        if (fs.existsSync('./marginfi-alt-cache.json')) {
            const marginfi = JSON.parse(fs.readFileSync('./marginfi-alt-cache.json', 'utf8'));
            
            for (const result of marginfi.validationResults?.slice(0, 1) || []) {
                if (result.valid && result.address) {
                    try {
                        const altAccount = await connection.getAddressLookupTable(new PublicKey(result.address));
                        if (altAccount?.value) {
                            altTables.push(altAccount.value);
                            console.log(`✅ MarginFi ALT: ${result.address.slice(0, 8)}... (${altAccount.value.state.addresses.length} аккаунтов)`);
                        }
                    } catch (error) {
                        console.log(`❌ Ошибка загрузки MarginFi ALT: ${error.message}`);
                    }
                }
            }
        }
        
        console.log(`📊 Загружено ALT таблиц: ${altTables.length}`);
        
        // 3. Создаем известные проблемные аккаунты
        console.log('\n🔥 АНАЛИЗ ИЗВЕСТНЫХ ПРОБЛЕМНЫХ АККАУНТОВ...');
        
        const problematicAccounts = [
            'So********************************111111112',   // SOL Token Mint
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',   // USDC Token Mint
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',   // Token Program
            '********************************',               // System Program
            'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',     // User Wallet
            '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',   // User SOL Token Account
            '********************************************',   // User USDC Token Account
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',     // Meteora Program
            'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',     // MarginFi SOL Bank
            '********************************************'      // MarginFi Account
        ];
        
        // Проверяем какие из проблемных аккаунтов есть в ALT таблицах
        console.log('\n🔍 ПРОВЕРКА ПРОБЛЕМНЫХ АККАУНТОВ В ALT ТАБЛИЦАХ:');
        
        const altAccountsSet = new Set();
        altTables.forEach((altTable, tableIndex) => {
            console.log(`\n📋 ALT Таблица ${tableIndex + 1}: ${altTable.key.toString()}`);
            
            altTable.state.addresses.forEach((address, index) => {
                const addressStr = address.toString();
                altAccountsSet.add(addressStr);
                
                if (problematicAccounts.includes(addressStr)) {
                    console.log(`❌ ПРОБЛЕМНЫЙ АККАУНТ В ALT: ${addressStr.slice(0, 8)}...${addressStr.slice(-8)} (индекс ${index})`);
                }
            });
        });
        
        // Подсчитываем сколько проблемных аккаунтов в ALT
        const problematicInALT = problematicAccounts.filter(account => altAccountsSet.has(account));
        
        console.log(`\n📊 РЕЗУЛЬТАТ ПРОВЕРКИ:`);
        console.log(`   Всего проблемных аккаунтов: ${problematicAccounts.length}`);
        console.log(`   В ALT таблицах: ${problematicInALT.length}`);
        console.log(`   НЕ в ALT таблицах: ${problematicAccounts.length - problematicInALT.length}`);
        
        if (problematicInALT.length > 0) {
            console.log(`\n❌ ПРОБЛЕМНЫЕ АККАУНТЫ В ALT:`);
            problematicInALT.forEach((account, index) => {
                console.log(`   ${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
            });
        }
        
        // 4. Анализируем типичную MarginFi Flash Loan структуру
        console.log('\n🔥 АНАЛИЗ ТИПИЧНОЙ MARGINFI FLASH LOAN СТРУКТУРЫ...');
        
        console.log('📋 ТИПИЧНАЯ СТРУКТУРА buildFlashLoanTx:');
        console.log('   1. start_flashloan - использует MarginFi Program, Account, Bank');
        console.log('   2. borrow (3 инструкции) - использует Bank, Vault, Token Program');
        console.log('   3. transfer_out - использует Vault, User Account, Token Program');
        console.log('   4. арбитраж (2 инструкции) - использует User Accounts, Meteora Program');
        console.log('   5. transfer_in - использует User Account, Vault, Token Program');
        console.log('   6. repay (4 инструкции) - использует Bank, Vault, Token Program');
        console.log('   7. end_flashloan - использует MarginFi Program, Account, Bank');
        
        // 5. Ищем потенциальные дублирования
        console.log('\n🔍 ПОТЕНЦИАЛЬНЫЕ ДУБЛИРОВАНИЯ:');
        
        const potentialDuplicates = [
            {
                account: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
                usage: 'Используется в borrow, transfer_out, transfer_in, repay (4+ раза)'
            },
            {
                account: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
                usage: 'Используется как signer во всех инструкциях'
            },
            {
                account: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
                usage: 'Используется в transfer_out, арбитраж, transfer_in'
            },
            {
                account: '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe',
                usage: 'Bank Vault - используется в borrow, transfer_out, transfer_in, repay'
            }
        ];
        
        potentialDuplicates.forEach((dup, index) => {
            const inALT = altAccountsSet.has(dup.account);
            console.log(`${index + 1}. ${dup.account.slice(0, 8)}...${dup.account.slice(-8)}`);
            console.log(`   Использование: ${dup.usage}`);
            console.log(`   В ALT: ${inALT ? '✅ ДА' : '❌ НЕТ'}`);
            
            if (inALT) {
                console.log(`   🚨 ПОТЕНЦИАЛЬНОЕ ДУБЛИРОВАНИЕ: аккаунт в ALT И в инструкциях!`);
            }
        });
        
        // 6. Рекомендации по исправлению
        console.log('\n🔧 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ:');
        console.log('=' .repeat(50));
        
        const duplicatesInALT = potentialDuplicates.filter(dup => altAccountsSet.has(dup.account));
        
        if (duplicatesInALT.length > 0) {
            console.log(`❌ НАЙДЕНО ${duplicatesInALT.length} ПОТЕНЦИАЛЬНЫХ ДУБЛИРОВАНИЙ`);
            console.log('\n💡 ВАРИАНТЫ РЕШЕНИЯ:');
            console.log('   1. Удалить проблемные аккаунты из ALT таблиц');
            console.log('   2. Модифицировать buildFlashLoanTx чтобы не дублировать аккаунты');
            console.log('   3. Использовать разные ALT таблицы для разных инструкций');
            
            console.log('\n🔧 КОНКРЕТНЫЕ ДЕЙСТВИЯ:');
            duplicatesInALT.forEach((dup, index) => {
                console.log(`   ${index + 1}. Удалить ${dup.account.slice(0, 8)}...${dup.account.slice(-8)} из ALT таблиц`);
            });
        } else {
            console.log('✅ Потенциальные дублирования НЕ найдены в ALT таблицах');
            console.log('💡 Проблема может быть в другом месте');
        }
        
        // 7. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        console.log('=' .repeat(40));
        
        if (problematicInALT.length > 0 || duplicatesInALT.length > 0) {
            console.log(`🚨 ИСТОЧНИК ОШИБКИ "AccountLoadedTwice" НАЙДЕН!`);
            console.log(`📊 Проблемных аккаунтов в ALT: ${problematicInALT.length}`);
            console.log(`📊 Потенциальных дублирований: ${duplicatesInALT.length}`);
            console.log(`💡 Эти аккаунты используются И в ALT таблицах, И в инструкциях`);
        } else {
            console.log(`✅ Очевидные дублирования не найдены`);
            console.log(`💡 Нужен более глубокий анализ реальной транзакции`);
        }

    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        console.error(error.stack);
    }
}

// Запуск анализа
if (require.main === module) {
    analyzeRealTransaction().catch(console.error);
}

module.exports = { analyzeRealTransaction, interceptBuildFlashLoanTx };
