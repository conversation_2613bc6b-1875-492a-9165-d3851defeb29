#!/usr/bin/env node

/**
 * 🔍 ДЕТАЛЬНАЯ ПРОВЕРКА БАЛАНСА И ДОЛГОВ MARGINFI
 * Владелец: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV
 */

const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginRequirementType } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

async function main() {
  console.log('🔍 ДЕТАЛЬНАЯ ПРОВЕРКА БАЛАНСА MARGINFI');
  console.log('👤 Владелец: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
  console.log('═'.repeat(70));

  try {
    // 🔗 Подключение к RPC
    console.log('🔗 Подключение к QuickNode RPC...');
    const connection = new Connection(
      'https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/',
      'confirmed'
    );

    // 🔑 Загрузка wallet
    const envContent = fs.readFileSync('.env.solana', 'utf8');
    const privateKeyMatch = envContent.match(/WALLET_PRIVATE_KEY=(.+)/);
    const privateKeyBase58 = privateKeyMatch[1].trim();

    // Декодирование base58
    const decode = (str) => {
      const alphabet = '**********************************************************';
      let decoded = 0n;
      for (let char of str) {
        decoded = decoded * 58n + BigInt(alphabet.indexOf(char));
      }
      const bytes = [];
      while (decoded > 0n) {
        bytes.unshift(Number(decoded % 256n));
        decoded = decoded / 256n;
      }
      return new Uint8Array(bytes);
    };

    const keypair = Keypair.fromSecretKey(decode(privateKeyBase58));
    const wallet = new NodeWallet(keypair);
    console.log(`✅ Wallet: ${wallet.publicKey.toString()}`);

    // 🏦 Инициализация MarginFi
    const config = getConfig('production');
    const client = await MarginfiClient.fetch(config, wallet, connection);

    // 📋 Получение всех аккаунтов
    console.log('\n📋 ПОИСК MARGINFI АККАУНТОВ...');
    const accounts = await client.getMarginfiAccountsForAuthority();
    
    if (accounts.length === 0) {
      console.log('❌ MarginFi аккаунты не найдены');
      return;
    }

    console.log(`✅ Найдено аккаунтов: ${accounts.length}`);

    // 🔍 Проверка каждого аккаунта
    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      console.log(`\n📊 АККАУНТ ${i + 1}: ${account.address.toString()}`);
      
      // Обновление данных
      await account.reload();
      
      const allBalances = account.balances;
      console.log(`💰 Всего балансов: ${allBalances.length}`);
      
      if (allBalances.length === 0) {
        console.log('   📭 Балансов нет');
        continue;
      }

      let totalDebtUSD = 0;
      let totalAssetsUSD = 0;

      for (const balance of allBalances) {
        const bank = client.getBankByPk(balance.bankPk);
        if (!bank) continue;

        const symbol = bank.tokenSymbol || 'UNKNOWN';
        
        // Получаем количества
        const { assets, liabilities } = balance.computeQuantity(bank);
        const { assets: assetsUI, liabilities: liabilitiesUI } = balance.computeQuantityUi(bank);
        
        console.log(`\n   💳 ${symbol}:`);
        console.log(`      📊 Активный: ${balance.active ? 'ДА' : 'НЕТ'}`);
        console.log(`      💰 Активы: ${assetsUI.toFixed(6)} ${symbol}`);
        console.log(`      💸 Долги: ${liabilitiesUI.toFixed(6)} ${symbol}`);
        
        // USD стоимость
        const oraclePrice = client.getOraclePriceByBank(bank.address);
        if (oraclePrice) {
          const { assets: assetsUSD, liabilities: liabilitiesUSD } = balance.computeUsdValue(
            bank, 
            oraclePrice, 
            MarginRequirementType.Equity
          );
          
          console.log(`      💵 Активы USD: $${assetsUSD.toFixed(2)}`);
          console.log(`      💸 Долги USD: $${liabilitiesUSD.toFixed(2)}`);
          
          totalAssetsUSD += assetsUSD.toNumber();
          totalDebtUSD += liabilitiesUSD.toNumber();
          
          if (liabilitiesUSD.gt(0)) {
            console.log(`      🚨 ДОЛГ: $${liabilitiesUSD.toFixed(2)}`);
          }
        }
      }

      // Итоговая сводка
      console.log(`\n📊 ИТОГО ПО АККАУНТУ:`);
      console.log(`   💰 Активы: $${totalAssetsUSD.toFixed(2)}`);
      console.log(`   💸 Долги: $${totalDebtUSD.toFixed(2)}`);
      console.log(`   📈 Чистая стоимость: $${(totalAssetsUSD - totalDebtUSD).toFixed(2)}`);
      
      if (totalDebtUSD > 0) {
        console.log(`   🚨 СТАТУС: ЕСТЬ ДОЛГИ ($${totalDebtUSD.toFixed(2)})`);
        const healthFactor = totalAssetsUSD / totalDebtUSD;
        console.log(`   🏥 Health Factor: ${healthFactor.toFixed(4)}`);
      } else {
        console.log(`   ✅ СТАТУС: ДОЛГОВ НЕТ!`);
      }
    }

    console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА!');

  } catch (error) {
    console.error('❌ ОШИБКА:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
