/**
 * 💰 METEORA POSITION RENT CALCULATOR
 * Рассчитывает точную стоимость rent и комиссий для initialize_position
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
require('dotenv').config();

class MeteoraPositionRentCalculator {
    constructor() {
        // Используем QuickNode из .env файла
        const rpcUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        this.connection = new Connection(rpcUrl, 'confirmed');

        console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}...`);
        
        // Пулы для анализа
        this.POOLS = {
            POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
        };
        
        console.log('💰 Meteora Position Rent Calculator инициализирован');
    }

    /**
     * 🔍 АНАЛИЗ РАЗМЕРА POSITION АККАУНТА
     */
    async analyzePositionAccountSize() {
        console.log('\n🔍 АНАЛИЗ РАЗМЕРА POSITION АККАУНТА...\n');
        
        try {
            // Создаем DLMM instance для получения информации
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(this.POOLS.POOL_1));
            
            // Получаем существующие позиции для анализа размера
            const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(
                new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV') // Наш wallet
            );
            
            if (userPositions.length > 0) {
                const position = userPositions[0];
                const positionAccountInfo = await this.connection.getAccountInfo(position.publicKey);
                
                if (positionAccountInfo) {
                    console.log(`📊 АНАЛИЗ СУЩЕСТВУЮЩЕЙ ПОЗИЦИИ:`);
                    console.log(`   Адрес позиции: ${position.publicKey.toString()}`);
                    console.log(`   Размер аккаунта: ${positionAccountInfo.data.length} байт`);
                    console.log(`   Lamports в аккаунте: ${positionAccountInfo.lamports}`);
                    console.log(`   Rent в SOL: ${(positionAccountInfo.lamports / 1e9).toFixed(9)} SOL`);
                    console.log(`   Rent в USD: $${((positionAccountInfo.lamports / 1e9) * 167).toFixed(6)}`);
                    
                    return {
                        accountSize: positionAccountInfo.data.length,
                        rentLamports: positionAccountInfo.lamports,
                        rentSOL: positionAccountInfo.lamports / 1e9,
                        rentUSD: (positionAccountInfo.lamports / 1e9) * 167
                    };
                }
            }
            
            // Если нет существующих позиций, рассчитываем теоретический размер
            console.log(`⚠️ Нет существующих позиций для анализа`);
            return await this.calculateTheoreticalPositionRent();
            
        } catch (error) {
            console.error(`❌ Ошибка анализа позиции: ${error.message}`);
            return await this.calculateTheoreticalPositionRent();
        }
    }

    /**
     * 📐 РАСЧЕТ ТЕОРЕТИЧЕСКОГО RENT ДЛЯ POSITION
     */
    async calculateTheoreticalPositionRent() {
        console.log('\n📐 РАСЧЕТ ТЕОРЕТИЧЕСКОГО RENT...\n');
        
        // Типичный размер Position аккаунта в Meteora DLMM
        // Основан на структуре Position в Rust коде
        const POSITION_ACCOUNT_SIZE = 8 + // discriminator
                                     32 + // lb_pair (PublicKey)
                                     32 + // owner (PublicKey)
                                     8 +  // liquidity_shares (u64)
                                     4 +  // lower_bin_id (i32)
                                     4 +  // upper_bin_id (i32)
                                     8 +  // last_updated_at (i64)
                                     8 +  // total_claimed_fee_x_amount (u64)
                                     8 +  // total_claimed_fee_y_amount (u64)
                                     8 +  // total_claimed_reward_amount (u64)
                                     1 +  // operator (Option<PublicKey>) - None = 1 byte
                                     32 + // lock_release_point (PublicKey)
                                     1 +  // subjected_to_bootstrap_liquidity_locking (bool)
                                     8 +  // fee_owner (u64)
                                     7;   // padding для выравнивания
        
        console.log(`📊 ТЕОРЕТИЧЕСКИЙ РАЗМЕР POSITION АККАУНТА:`);
        console.log(`   Размер: ${POSITION_ACCOUNT_SIZE} байт`);
        
        // Получаем минимальный rent для этого размера
        const rentLamports = await this.connection.getMinimumBalanceForRentExemption(POSITION_ACCOUNT_SIZE);
        const rentSOL = rentLamports / 1e9;
        const rentUSD = rentSOL * 167; // Примерная цена SOL
        
        console.log(`   Rent в lamports: ${rentLamports}`);
        console.log(`   Rent в SOL: ${rentSOL.toFixed(9)} SOL`);
        console.log(`   Rent в USD: $${rentUSD.toFixed(6)}`);
        
        return {
            accountSize: POSITION_ACCOUNT_SIZE,
            rentLamports: rentLamports,
            rentSOL: rentSOL,
            rentUSD: rentUSD
        };
    }

    /**
     * 💸 РАСЧЕТ КОМИССИЙ ЗА ТРАНЗАКЦИЮ
     */
    async calculateTransactionFees() {
        console.log('\n💸 РАСЧЕТ КОМИССИЙ ЗА ТРАНЗАКЦИЮ...\n');
        
        // Используем стандартную комиссию Solana
        const baseFee = 5000; // 5000 lamports за подпись (стандартная комиссия)
        
        // Для initialize_position нужно:
        // 1. Подпись пользователя
        // 2. Подпись position keypair
        const signatures = 2;
        const transactionFeeLamports = baseFee * signatures;
        const transactionFeeSOL = transactionFeeLamports / 1e9;
        const transactionFeeUSD = transactionFeeSOL * 167;
        
        console.log(`📊 КОМИССИИ ЗА ТРАНЗАКЦИЮ:`);
        console.log(`   Базовая комиссия: ${baseFee} lamports за подпись`);
        console.log(`   Количество подписей: ${signatures}`);
        console.log(`   Общая комиссия: ${transactionFeeLamports} lamports`);
        console.log(`   Комиссия в SOL: ${transactionFeeSOL.toFixed(9)} SOL`);
        console.log(`   Комиссия в USD: $${transactionFeeUSD.toFixed(6)}`);
        
        return {
            baseFee: baseFee,
            signatures: signatures,
            totalFeeLamports: transactionFeeLamports,
            totalFeeSOL: transactionFeeSOL,
            totalFeeUSD: transactionFeeUSD
        };
    }

    /**
     * 🎯 ПОЛНЫЙ РАСЧЕТ СТОИМОСТИ ДЛЯ ОБОИХ ПУЛОВ
     */
    async calculateFullCostForBothPools() {
        console.log('\n🎯 ПОЛНЫЙ РАСЧЕТ СТОИМОСТИ ДЛЯ ОБОИХ ПУЛОВ...\n');
        
        // Получаем данные о rent и комиссиях
        const positionRent = await this.analyzePositionAccountSize();
        const transactionFees = await this.calculateTransactionFees();
        
        // Рассчитываем для двух пулов
        const poolsCount = 2;
        
        console.log(`📊 ИТОГОВАЯ СТОИМОСТЬ:`);
        console.log(`\n💰 ЗА ОДНУ ПОЗИЦИЮ:`);
        console.log(`   Rent: ${positionRent.rentSOL.toFixed(9)} SOL ($${positionRent.rentUSD.toFixed(6)})`);
        console.log(`   Комиссия транзакции: ${transactionFees.totalFeeSOL.toFixed(9)} SOL ($${transactionFees.totalFeeUSD.toFixed(6)})`);
        
        const totalPerPositionSOL = positionRent.rentSOL + transactionFees.totalFeeSOL;
        const totalPerPositionUSD = positionRent.rentUSD + transactionFees.totalFeeUSD;
        
        console.log(`   ИТОГО за позицию: ${totalPerPositionSOL.toFixed(9)} SOL ($${totalPerPositionUSD.toFixed(6)})`);
        
        console.log(`\n💰 ЗА ДВЕ ПОЗИЦИИ (${poolsCount} пула):`);
        const totalForTwoPoolsSOL = totalPerPositionSOL * poolsCount;
        const totalForTwoPoolsUSD = totalPerPositionUSD * poolsCount;
        
        console.log(`   Rent (2 позиции): ${(positionRent.rentSOL * poolsCount).toFixed(9)} SOL ($${(positionRent.rentUSD * poolsCount).toFixed(6)})`);
        console.log(`   Комиссии (2 транзакции): ${(transactionFees.totalFeeSOL * poolsCount).toFixed(9)} SOL ($${(transactionFees.totalFeeUSD * poolsCount).toFixed(6)})`);
        console.log(`   ИТОГО за 2 позиции: ${totalForTwoPoolsSOL.toFixed(9)} SOL ($${totalForTwoPoolsUSD.toFixed(6)})`);
        
        // Дополнительные расходы
        console.log(`\n💸 ДОПОЛНИТЕЛЬНЫЕ РАСХОДЫ:`);
        console.log(`   Compute Units: ~200,000 CU за позицию`);
        console.log(`   Priority Fee: ~0.000001 SOL (опционально)`);
        
        return {
            perPosition: {
                rentSOL: positionRent.rentSOL,
                rentUSD: positionRent.rentUSD,
                feeSOL: transactionFees.totalFeeSOL,
                feeUSD: transactionFees.totalFeeUSD,
                totalSOL: totalPerPositionSOL,
                totalUSD: totalPerPositionUSD
            },
            forTwoPools: {
                totalRentSOL: positionRent.rentSOL * poolsCount,
                totalRentUSD: positionRent.rentUSD * poolsCount,
                totalFeesSOL: transactionFees.totalFeeSOL * poolsCount,
                totalFeesUSD: transactionFees.totalFeeUSD * poolsCount,
                grandTotalSOL: totalForTwoPoolsSOL,
                grandTotalUSD: totalForTwoPoolsUSD
            }
        };
    }

    /**
     * 🔍 АНАЛИЗ КОНКРЕТНЫХ ПУЛОВ
     */
    async analyzeSpecificPools() {
        console.log('\n🔍 АНАЛИЗ КОНКРЕТНЫХ ПУЛОВ...\n');
        
        for (const [poolName, poolAddress] of Object.entries(this.POOLS)) {
            console.log(`📊 ${poolName}: ${poolAddress}`);
            
            try {
                const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress));
                const activeBin = await dlmmPool.getActiveBin();
                
                console.log(`   Активный bin ID: ${activeBin.binId}`);
                console.log(`   Цена активного bin: ${activeBin.price ? activeBin.price.toString() : 'N/A'}`);
                console.log(`   Bin step: ${dlmmPool.lbPair.binStep} basis points`);
                
            } catch (error) {
                console.log(`   ❌ Ошибка анализа: ${error.message}`);
            }
            
            console.log('');
        }
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
async function runRentCalculation() {
    try {
        console.log('💰 METEORA POSITION RENT & FEE CALCULATOR\n');
        
        const calculator = new MeteoraPositionRentCalculator();
        
        // Анализируем конкретные пулы
        await calculator.analyzeSpecificPools();
        
        // Рассчитываем полную стоимость
        const results = await calculator.calculateFullCostForBothPools();
        
        console.log('\n🎯 КРАТКИЙ ИТОГ:');
        console.log(`   💰 Одна позиция: ${results.perPosition.totalSOL.toFixed(9)} SOL ($${results.perPosition.totalUSD.toFixed(6)})`);
        console.log(`   💰 Две позиции: ${results.forTwoPools.grandTotalSOL.toFixed(9)} SOL ($${results.forTwoPools.grandTotalUSD.toFixed(6)})`);
        
        // Сохраняем результат
        const fs = require('fs');
        fs.writeFileSync('meteora-position-costs.json', JSON.stringify({
            timestamp: new Date().toISOString(),
            pools: calculator.POOLS,
            costs: results
        }, null, 2));
        
        console.log('\n💾 Результат сохранен в meteora-position-costs.json');
        
        return results;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error.message);
        return null;
    }
}

// Запускаем расчет
if (require.main === module) {
    runRentCalculation();
}

module.exports = { MeteoraPositionRentCalculator, runRentCalculation };
