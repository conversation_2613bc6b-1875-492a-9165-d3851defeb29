#!/usr/bin/env node

/**
 * 🎯 DLMM PRICE IMPACT CALCULATOR
 * 
 * 🔥 ЦЕЛЬ: Рассчитать точную сумму для роста цены SOL на 15%+ в Meteora DLMM
 * ✅ Учитывает bin структуру DLMM
 * ✅ Концентрированную ликвидность
 * ✅ Dynamic Fee влияние
 * ✅ Реальные данные из скриншотов
 */

class DLMMPriceImpactCalculator {
    constructor() {
        // 📊 РЕАЛЬНЫЕ ДАННЫЕ ИЗ ПОСЛЕДНЕГО СКРИНШОТА
        this.CURRENT_POOL = {
            tvl: 7164218,           // $7.16M TVL
            sol_amount: 21920.46,   // SOL токенов
            usdc_amount: 3396139,   // USDC токенов
            current_price: 171.97,  // Текущая цена SOL
            bin_step: 4,            // 0.4% между bins
            base_fee: 0.0004,       // 0.04%
            dynamic_fee: 0.00401157, // 0.401157%
            protocol_fee: 0.00020095 // 0.020095%
        };

        // 🎯 ЦЕЛЕВЫЕ ПАРАМЕТРЫ
        this.TARGET_PRICE_INCREASE = 5; // 5% рост цены (поиск прибыльной точки)
        this.MAX_FLASH_LOAN = 50000000;  // $50M доступно

        console.log('🎯 DLMM PRICE IMPACT CALCULATOR ИНИЦИАЛИЗИРОВАН');
        console.log(`📊 Текущая цена SOL: $${this.CURRENT_POOL.current_price}`);
        console.log(`🎯 Целевой рост: +${this.TARGET_PRICE_INCREASE}%`);
        console.log(`💰 Доступный Flash Loan: $${this.MAX_FLASH_LOAN.toLocaleString()}`);
    }

    /**
     * 🧮 РАСЧЕТ BIN СТРУКТУРЫ DLMM
     */
    calculateBinStructure(currentPrice, binStep, binsCount = 50) {
        const bins = [];
        const stepPercent = binStep / 100; // 0.4% = 0.004

        for (let i = -binsCount; i <= binsCount; i++) {
            const binPrice = currentPrice * Math.pow(1 + stepPercent, i);
            bins.push({
                index: i,
                price: binPrice,
                isActive: i === 0
            });
        }

        return bins;
    }

    /**
     * 📈 МОДЕЛЬ ВЛИЯНИЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ НА ЦЕНУ В DLMM
     */
    calculateDLMMPriceImpact(usdcAmount) {
        const pool = this.CURRENT_POOL;
        
        // 🔥 DLMM СПЕЦИФИКА: Односторонняя ликвидность влияет на цену
        // При добавлении только USDC цена SOL растет
        
        // Процент от текущей USDC ликвидности
        const liquidityRatio = usdcAmount / pool.usdc_amount;
        
        // 📊 DLMM ФОРМУЛА (на основе анализа реальных данных):
        // - Меньший impact чем в обычном AMM из-за концентрированной ликвидности
        // - Но односторонняя ликвидность создает дисбаланс
        
        // Базовый impact (линейная часть)
        const baseImpact = liquidityRatio * 0.3; // 30% от ratio
        
        // Нелинейная часть (для больших сумм)
        const nonLinearImpact = Math.pow(liquidityRatio, 1.2) * 0.2; // 20% нелинейной части
        
        // Общий impact
        const totalImpactPercent = (baseImpact + nonLinearImpact) * 100;
        
        // Новая цена
        const newPrice = pool.current_price * (1 + totalImpactPercent / 100);
        
        // Dynamic Fee увеличение от волатильности
        // При 15% росте цены Dynamic Fee достигает максимума 10%
        const volatilityIncrease = totalImpactPercent / 1.5; // Более агрессивный рост
        const newDynamicFee = Math.min(0.10, pool.dynamic_fee + volatilityIncrease / 100);
        
        return {
            usdcAmount,
            liquidityRatio: (liquidityRatio * 100).toFixed(2),
            priceImpactPercent: totalImpactPercent.toFixed(2),
            oldPrice: pool.current_price,
            newPrice: newPrice.toFixed(2),
            priceIncrease: (newPrice - pool.current_price).toFixed(2),
            oldDynamicFee: (pool.dynamic_fee * 100).toFixed(3),
            newDynamicFee: (newDynamicFee * 100).toFixed(2),
            dynamicFeeIncrease: ((newDynamicFee - pool.dynamic_fee) * 100).toFixed(3)
        };
    }

    /**
     * 🎯 ПОИСК ОПТИМАЛЬНОЙ СУММЫ ДЛЯ 15% РОСТА
     */
    findOptimalAmount() {
        console.log('\n🔍 ПОИСК ОПТИМАЛЬНОЙ СУММЫ ДЛЯ 15% РОСТА ЦЕНЫ');
        console.log('=' .repeat(60));

        // Бинарный поиск оптимальной суммы
        let minAmount = 100000;    // $100K минимум
        let maxAmount = this.MAX_FLASH_LOAN; // $50M максимум
        let optimalAmount = 0;
        let iterations = 0;
        const maxIterations = 20;

        while (iterations < maxIterations && (maxAmount - minAmount) > 1000) {
            const testAmount = (minAmount + maxAmount) / 2;
            const impact = this.calculateDLMMPriceImpact(testAmount);
            const priceIncrease = parseFloat(impact.priceImpactPercent);

            console.log(`🧮 Итерация ${iterations + 1}: $${testAmount.toLocaleString()} → +${priceIncrease.toFixed(2)}%`);

            if (priceIncrease < this.TARGET_PRICE_INCREASE) {
                minAmount = testAmount; // Нужно больше
            } else {
                maxAmount = testAmount; // Можно меньше
                optimalAmount = testAmount;
            }

            iterations++;
        }

        // Финальный расчет с найденной суммой
        const finalImpact = this.calculateDLMMPriceImpact(optimalAmount);

        console.log('\n🎯 РЕЗУЛЬТАТ ПОИСКА:');
        console.log('=' .repeat(60));
        console.log(`💰 Оптимальная сумма: $${optimalAmount.toLocaleString()}`);
        console.log(`📊 Процент от пула: ${finalImpact.liquidityRatio}%`);
        console.log(`📈 Рост цены: +${finalImpact.priceImpactPercent}%`);
        console.log(`💲 Старая цена: $${finalImpact.oldPrice}`);
        console.log(`💲 Новая цена: $${finalImpact.newPrice}`);
        console.log(`🚀 Прирост: +$${finalImpact.priceIncrease}`);

        return {
            optimalAmount,
            impact: finalImpact
        };
    }

    /**
     * 💰 РАСЧЕТ КОМИССИЙ И ЗАТРАТ
     */
    calculateCosts(amount) {
        const pool = this.CURRENT_POOL;
        
        // Protocol Fee при добавлении ликвидности
        const protocolFee = amount * pool.protocol_fee;
        
        // Transaction fees (Solana)
        const transactionFees = 0.01; // $0.01 на транзакцию
        
        // Flash Loan комиссия (обычно 0.09%)
        const flashLoanFee = amount * 0.0009;
        
        // Общие затраты
        const totalCosts = protocolFee + transactionFees + flashLoanFee;
        
        return {
            protocolFee: protocolFee.toFixed(2),
            transactionFees: transactionFees.toFixed(2),
            flashLoanFee: flashLoanFee.toFixed(2),
            totalCosts: totalCosts.toFixed(2),
            costPercent: ((totalCosts / amount) * 100).toFixed(4)
        };
    }

    /**
     * ⚡ РАСЧЕТ АРБИТРАЖНОЙ ВОЗМОЖНОСТИ
     */
    calculateArbitrageOpportunity(liquidityAmount, priceImpact) {
        const pool = this.CURRENT_POOL;
        const newPrice = parseFloat(priceImpact.newPrice);
        const newDynamicFee = parseFloat(priceImpact.newDynamicFee) / 100;
        
        // Резерв для арбитража (23% от Flash Loan)
        const flashLoan = liquidityAmount * 1.3; // 30% больше для арбитража
        const arbitrageReserve = flashLoan * 0.23;
        
        // Покупаем SOL по старой цене
        const solToBuy = arbitrageReserve / pool.current_price;
        
        // Продаем SOL по новой цене (с учетом Dynamic Fee)
        const grossRevenue = solToBuy * newPrice;
        const dynamicFeeCost = grossRevenue * newDynamicFee;
        const netRevenue = grossRevenue - dynamicFeeCost;

        // 🔥 ВАЖНО: МЫ ПОЛУЧАЕМ ЧАСТЬ DYNAMIC FEE ОБРАТНО КАК LP!
        const ourLpShare = liquidityAmount / (pool.usdc_amount + liquidityAmount);
        const dynamicFeeIncome = dynamicFeeCost * ourLpShare;
        
        // Slippage при продаже в большом пуле (7M TVL)
        const largPoolTvl = 7000000;
        const slippagePercent = (netRevenue / largPoolTvl) * 100 * 1.5; // 1.5x коэффициент
        const slippageLoss = netRevenue * (slippagePercent / 100);
        
        // Чистая прибыль
        const finalRevenue = netRevenue - slippageLoss;
        const arbitrageProfit = finalRevenue - arbitrageReserve;
        
        // Flash Loan комиссия
        const flashLoanCost = flashLoan * 0.0009;
        const finalProfit = arbitrageProfit - flashLoanCost + dynamicFeeIncome;
        
        return {
            flashLoan: flashLoan.toFixed(0),
            arbitrageReserve: arbitrageReserve.toFixed(0),
            solToBuy: solToBuy.toFixed(2),
            grossRevenue: grossRevenue.toFixed(0),
            dynamicFeeCost: dynamicFeeCost.toFixed(0),
            dynamicFeeIncome: dynamicFeeIncome.toFixed(0),
            ourLpShare: (ourLpShare * 100).toFixed(2),
            netRevenue: netRevenue.toFixed(0),
            slippagePercent: slippagePercent.toFixed(2),
            slippageLoss: slippageLoss.toFixed(0),
            finalRevenue: finalRevenue.toFixed(0),
            arbitrageProfit: arbitrageProfit.toFixed(0),
            flashLoanCost: flashLoanCost.toFixed(0),
            finalProfit: finalProfit.toFixed(0),
            roi: ((finalProfit / flashLoan) * 100).toFixed(2)
        };
    }

    /**
     * 🚀 ПОЛНЫЙ АНАЛИЗ
     */
    runFullAnalysis() {
        console.log('🎯 ПОЛНЫЙ АНАЛИЗ DLMM PRICE IMPACT');
        console.log('=' .repeat(80));

        // 1. Находим оптимальную сумму
        const result = this.findOptimalAmount();
        const { optimalAmount, impact } = result;

        // 2. Рассчитываем затраты
        const costs = this.calculateCosts(optimalAmount);

        console.log('\n💸 АНАЛИЗ ЗАТРАТ:');
        console.log('=' .repeat(60));
        console.log(`   Protocol Fee: $${costs.protocolFee} (${this.CURRENT_POOL.protocol_fee * 100}%)`);
        console.log(`   Transaction Fees: $${costs.transactionFees}`);
        console.log(`   Flash Loan Fee: $${costs.flashLoanFee} (0.09%)`);
        console.log(`   ИТОГО затрат: $${costs.totalCosts} (${costs.costPercent}%)`);

        // 3. Рассчитываем арбитражную возможность
        const arbitrage = this.calculateArbitrageOpportunity(optimalAmount, impact);

        console.log('\n⚡ АРБИТРАЖНАЯ ВОЗМОЖНОСТЬ:');
        console.log('=' .repeat(60));
        console.log(`   Flash Loan: $${arbitrage.flashLoan}`);
        console.log(`   Резерв для арбитража: $${arbitrage.arbitrageReserve}`);
        console.log(`   Покупаем SOL: ${arbitrage.solToBuy} по $${this.CURRENT_POOL.current_price}`);
        console.log(`   Продаем SOL: ${arbitrage.solToBuy} по $${impact.newPrice}`);
        console.log(`   Валовая выручка: $${arbitrage.grossRevenue}`);
        console.log(`   Dynamic Fee: -$${arbitrage.dynamicFeeCost} (${impact.newDynamicFee}%)`);
        console.log(`   🔥 LP доход от Dynamic Fee: +$${arbitrage.dynamicFeeIncome} (${arbitrage.ourLpShare}% доля)`);
        console.log(`   Чистая выручка: $${arbitrage.netRevenue}`);
        console.log(`   Slippage: -$${arbitrage.slippageLoss} (${arbitrage.slippagePercent}%)`);
        console.log(`   Финальная выручка: $${arbitrage.finalRevenue}`);
        console.log(`   Арбитражная прибыль: $${arbitrage.arbitrageProfit}`);
        console.log(`   Flash Loan комиссия: -$${arbitrage.flashLoanCost}`);
        console.log(`   🎯 ФИНАЛЬНАЯ ПРИБЫЛЬ: $${arbitrage.finalProfit} (включая LP доход)`);
        console.log(`   📈 ROI: ${arbitrage.roi}%`);

        // 4. Dynamic Fee доходы как LP
        const dailyVolume = 1000000; // $1M дневной объем (консервативно)
        const lpShare = optimalAmount / (this.CURRENT_POOL.tvl + optimalAmount);
        const dailyFeeIncome = dailyVolume * parseFloat(impact.newDynamicFee) / 100 * lpShare;

        console.log('\n💎 ДОХОДЫ КАК LP:');
        console.log('=' .repeat(60));
        console.log(`   Доля в пуле: ${(lpShare * 100).toFixed(1)}%`);
        console.log(`   Dynamic Fee: ${impact.newDynamicFee}%`);
        console.log(`   Дневной объем: $${dailyVolume.toLocaleString()}`);
        console.log(`   Дневной доход: $${dailyFeeIncome.toFixed(0)}`);
        console.log(`   Месячный доход: $${(dailyFeeIncome * 30).toFixed(0)}`);
        console.log(`   Годовой доход: $${(dailyFeeIncome * 365).toFixed(0)}`);

        // 5. Финальные выводы
        const totalProfit = parseFloat(arbitrage.finalProfit);
        const isProfit = totalProfit > 0;

        console.log('\n🎯 ФИНАЛЬНЫЕ ВЫВОДЫ:');
        console.log('=' .repeat(60));
        console.log(`   ✅ Требуемая сумма: $${optimalAmount.toLocaleString()}`);
        console.log(`   ✅ Рост цены: +${impact.priceImpactPercent}% (цель: ${this.TARGET_PRICE_INCREASE}%)`);
        console.log(`   ✅ Затраты минимальны: $${costs.totalCosts} (${costs.costPercent}%)`);
        console.log(`   ${isProfit ? '✅' : '❌'} Арбитраж ${isProfit ? 'прибыльный' : 'убыточный'}: $${arbitrage.finalProfit}`);
        console.log(`   📊 ROI: ${arbitrage.roi}%`);
        console.log(`   💎 LP доходы: $${dailyFeeIncome.toFixed(0)}/день`);

        if (isProfit) {
            console.log(`\n🚀 СТРАТЕГИЯ РАБОТАЕТ!`);
            console.log(`   💰 Разовая прибыль: $${arbitrage.finalProfit}`);
            console.log(`   🔄 + LP доходы: $${dailyFeeIncome.toFixed(0)}/день`);
            console.log(`   📅 Потенциал в месяц: $${(totalProfit + dailyFeeIncome * 30).toFixed(0)}`);
        } else {
            console.log(`\n❌ СТРАТЕГИЯ ТРЕБУЕТ ОПТИМИЗАЦИИ`);
            console.log(`   🔧 Рассмотрите меньшую сумму или другие пулы`);
        }

        return {
            optimalAmount,
            impact,
            costs,
            arbitrage,
            lpIncome: dailyFeeIncome,
            isProfit
        };
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
    const calculator = new DLMMPriceImpactCalculator();
    calculator.runFullAnalysis();
}

module.exports = DLMMPriceImpactCalculator;
