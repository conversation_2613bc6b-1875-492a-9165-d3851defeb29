/**
 * 🔥 ИНТЕГРИРОВАННЫЙ FLASH LOAN + METEORA POSITION
 * ОБЪЕДИНЯЕТ ЛУЧШЕЕ ИЗ ОСНОВНОГО БОТА И SDK ПОДХОДА
 */

const { Connection, Keypair, PublicKey, TransactionMessage, VersionedTransaction, TransactionInstruction, SystemProgram, ComputeBudgetProgram } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const DLMM = require('@meteora-ag/dlmm').default;
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

class IntegratedFlashLoanMeteora {
    constructor() {
        // 🔥 КОНСТАНТЫ ИЗ ОСНОВНОГО БОТА
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        
        // 🧠 ДИНАМИЧЕСКИЕ СУММЫ ЗАЙМОВ ОТ УМНОГО АНАЛИЗАТОРА
        this.BORROW_AMOUNTS = {
            USDC: 2500000, // Базовое значение (будет заменено умным анализатором)
            SOL: 8000 * 1e9  // Базовое значение (будет заменено умным анализатором)
        };

        // 🧠 ИМПОРТ И ИНИЦИАЛИЗАЦИЯ УМНОГО АНАЛИЗАТОРА
        const SmartLiquidityAnalyzer = require('./smart-liquidity-analyzer');
        this.smartAnalyzer = new SmartLiquidityAnalyzer();
        
        // 🔥 БАНКИ ИЗ ОСНОВНОГО БОТА
        this.BANKS = {
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh')
        };

        // 🔥 VAULT АККАУНТЫ ИЗ ОСНОВНОГО БОТА
        this.VAULTS = {
            USDC: {
                liquidityVault: new PublicKey('7jaiZR5Sk8hdYN9MxTpczTcwbWpb5WEoxSANuUwveuat'),
                vaultAuthority: new PublicKey('3uxNepDbmkDNq6JhRja5Z8QwbTrfmkKP8AKZV5chYDGG'),
                userTokenAccount: new PublicKey('3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo')
            },
            SOL: {
                liquidityVault: new PublicKey('2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe'),
                vaultAuthority: new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD'),
                userTokenAccount: new PublicKey('68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk')
            }
        };
        
        // 🔥 ПУЛЫ ИЗ ОСНОВНОГО БОТА
        this.POOLS = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',  // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'   // Pool 3
        ];

        console.log('🔥 INTEGRATED FLASH LOAN METEORA ИНИЦИАЛИЗИРОВАН');
        console.log(`💰 ЗАЙМЫ: ${this.BORROW_AMOUNTS.USDC.toLocaleString()} USDC, ${(this.BORROW_AMOUNTS.SOL / 1e9).toLocaleString()} SOL`);
    }

    /**
     * 🧠 ОБНОВЛЕНИЕ СУММ ЗАЙМОВ ОТ УМНОГО АНАЛИЗАТОРА
     */
    async updateBorrowAmountsFromSmartAnalyzer(pool1Data, pool2Data) {
        console.log('🧠 ОБНОВЛЕНИЕ СУММ ЗАЙМОВ ОТ УМНОГО АНАЛИЗАТОРА...');

        try {
            // Вызываем умный анализатор
            const smartAnalysis = await this.smartAnalyzer.analyzeThreeBinsLiquidity(pool1Data, pool2Data);

            if (smartAnalysis.success) {
                const recommendations = this.smartAnalyzer.getInstructionRecommendations(smartAnalysis);

                // Обновляем суммы займов
                this.BORROW_AMOUNTS.USDC = recommendations.borrowInstructions.usdcAmount;
                this.BORROW_AMOUNTS.SOL = recommendations.borrowInstructions.wsolAmount * 1e9; // Конвертируем в lamports

                console.log('✅ СУММЫ ЗАЙМОВ ОБНОВЛЕНЫ ОТ УМНОГО АНАЛИЗАТОРА:');
                console.log(`   💰 USDC: ${this.BORROW_AMOUNTS.USDC.toLocaleString()}`);
                console.log(`   💰 SOL: ${(this.BORROW_AMOUNTS.SOL / 1e9).toLocaleString()}`);

                return {
                    success: true,
                    smartAnalysis: smartAnalysis,
                    recommendations: recommendations
                };
            } else {
                console.log(`❌ УМНЫЙ АНАЛИЗАТОР ПРОВАЛИЛСЯ: ${smartAnalysis.error}`);
                return { success: false, error: smartAnalysis.error };
            }

        } catch (error) {
            console.log(`❌ ОШИБКА УМНОГО АНАЛИЗАТОРА: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔥 ГЛАВНЫЙ МЕТОД - СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ
     */
    async createCompleteFlashLoanWithPosition() {
        console.log('🔥 СОЗДАНИЕ ИНТЕГРИРОВАННОЙ FLASH LOAN + METEORA POSITION ТРАНЗАКЦИИ...');
        console.log(`💰 ЗАЙМЫ: ${this.BORROW_AMOUNTS.USDC / 1e6} млн USDC + ${this.BORROW_AMOUNTS.SOL / 1e9} SOL`);

        // 🔧 НАСТРОЙКА
        require('dotenv').config();
        const rpcUrl = process.env.QUICKNODE_RPC_URL || 'https://api.mainnet-beta.solana.com';
        const connection = new Connection(rpcUrl, 'confirmed');
        console.log(`🌐 RPC: ${rpcUrl.slice(0, 50)}...`);
        
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);

        // 🔧 СОЗДАНИЕ MARGINFI CLIENT
        const nodeWallet = new NodeWallet(wallet);
        const config = getConfig('production');
        const marginfiClient = await MarginfiClient.fetch(config, nodeWallet, connection);
        console.log(`🏦 MarginFi Client создан`);

        // 🔧 ПОЛУЧЕНИЕ MARGINFI АККАУНТА
        const marginfiAccountAddress = '********************************************';
        const marginfiAccount = await MarginfiAccountWrapper.fetch(marginfiAccountAddress, marginfiClient);
        console.log(`📋 MarginFi аккаунт загружен: ${marginfiAccountAddress}`);

        // 🔧 СОЗДАНИЕ METEORA POSITION (НУЛЕВАЯ ЛИКВИДНОСТЬ)
        console.log('\n🔥 Создание Meteora position с НУЛЕВОЙ ликвидностью...');
        const poolAddress = this.POOLS[0]; // Используем первый пул
        const dlmmPool = await DLMM.create(connection, new PublicKey(poolAddress));
        
        // Генерируем новый position keypair
        const newPosition = Keypair.generate();
        console.log(`🔑 Новый position: ${newPosition.publicKey.toString()}`);

        // Получаем активный bin для стратегии
        const activeBin = await dlmmPool.getActiveBin();
        const minBinId = activeBin.binId - 10;
        const maxBinId = activeBin.binId + 10;

        // Создаем position с НУЛЕВОЙ ликвидностью
        const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
            positionPubKey: newPosition.publicKey,
            user: wallet.publicKey,
            totalXAmount: new BN(0), // 0 SOL - НУЛЕВАЯ ликвидность!
            totalYAmount: new BN(0), // 0 USDC - НУЛЕВАЯ ликвидность!
            strategy: {
                maxBinId,
                minBinId,
                strategyType: 0, // Spot
            },
            userTokenX: this.VAULTS.SOL.userTokenAccount,
            userTokenY: this.VAULTS.USDC.userTokenAccount,
        });

        console.log(`✅ Position создан с ${createPositionTx.instructions.length} инструкциями`);

        // 🔧 СОЗДАНИЕ ВСЕХ ИНСТРУКЦИЙ
        const instructions = [];
        
        // 0-1: COMPUTE BUDGET
        console.log('🔧 0-1: Создание ComputeBudget инструкций...');
        instructions.push(ComputeBudgetProgram.setComputeUnitLimit({ units: 1400000 }));
        instructions.push(ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 0 }));
        
        // 2: START FLASH LOAN
        console.log('🔧 2: Создание START Flash Loan...');
        const endIndex = 17; // Позиция END Flash Loan
        const startFlashLoanIx = this.createStartFlashLoanInstruction(endIndex, wallet, marginfiAccountAddress);
        instructions.push(startFlashLoanIx);
        
        // 3-4: BORROW (2 ТОКЕНА)
        console.log('🔧 3-4: Создание BORROW инструкций...');
        const borrowUsdcIx = this.createBorrowInstruction(this.BORROW_AMOUNTS.USDC, this.BANKS.USDC, wallet, marginfiAccountAddress);
        const borrowWsolIx = this.createBorrowInstruction(this.BORROW_AMOUNTS.SOL, this.BANKS.SOL, wallet, marginfiAccountAddress);
        instructions.push(borrowUsdcIx);
        instructions.push(borrowWsolIx);
        
        // 5: CREATE POSITION (НУЛЕВАЯ ЛИКВИДНОСТЬ)
        console.log('🔧 5: Добавление CREATE POSITION инструкций...');
        // Берем только InitializePosition инструкцию (без AddLiquidity)
        const initPositionIx = createPositionTx.instructions.find(ix => 
            ix.programId.equals(this.METEORA_DLMM_PROGRAM)
        );
        if (initPositionIx) {
            instructions.push(initPositionIx);
            console.log('✅ InitializePosition добавлен');
        }

        // 6-15: METEORA ОПЕРАЦИИ (ИЗ ОСНОВНОГО БОТА)
        console.log('🔧 6-15: Создание METEORA операций...');
        instructions.push(this.createMeteoraAddLiquidityInstruction(1));     // 6: ADD Pool 1
        instructions.push(this.createMeteoraAddLiquidityInstruction(2));     // 7: ADD Pool 2
        instructions.push(await this.createMeteoraSwapInstruction('BUY'));   // 8: BUY Swap
        instructions.push(await this.createMeteoraSwapInstruction('SELL'));  // 9: SELL Swap
        instructions.push(this.createMeteoraRemoveLiquidityInstruction(1));  // 10: REMOVE Pool 1
        instructions.push(this.createMeteoraRemoveLiquidityInstruction(2));  // 11: REMOVE Pool 2
        instructions.push(this.createMeteoraClaimFeeInstruction('USDC'));    // 12: Claim USDC
        instructions.push(this.createMeteoraClaimFeeInstruction('WSOL'));    // 13: Claim WSOL
        instructions.push(this.createMeteoraCloseAccountInstruction('USDC')); // 14: Close USDC
        instructions.push(this.createMeteoraCloseAccountInstruction('WSOL')); // 15: Close WSOL
        
        // 16-17: REPAY (2 ТОКЕНА)
        console.log('🔧 16-17: Создание REPAY инструкций...');
        const repayUsdcIx = this.createRepayInstruction(this.BANKS.USDC, wallet, marginfiAccountAddress);
        const repayWsolIx = this.createRepayInstruction(this.BANKS.SOL, wallet, marginfiAccountAddress);
        instructions.push(repayUsdcIx);
        instructions.push(repayWsolIx);
        
        // 18: END FLASH LOAN
        console.log('🔧 18: Создание END Flash Loan...');
        const endFlashLoanIx = this.createEndFlashLoanInstruction(wallet, marginfiAccountAddress);
        instructions.push(endFlashLoanIx);

        console.log(`✅ СОЗДАНО ${instructions.length} ИНСТРУКЦИЙ`);
        console.log('\n🔄 СТРУКТУРА ИНТЕГРИРОВАННОЙ ТРАНЗАКЦИИ:');
        console.log('   0-1: ComputeBudget (2)');
        console.log('   2: START Flash Loan (1)');
        console.log('   3-4: BORROW USDC + SOL (2)');
        console.log('   5: CREATE Position (нулевая ликвидность) (1)');
        console.log('   6-15: METEORA операции (10)');
        console.log('   16-17: REPAY USDC + SOL (2)');
        console.log('   18: END Flash Loan (1)');
        console.log(`   🎯 ИТОГО: ${instructions.length} инструкций`);

        // 🔧 ЗАГРУЗКА ALT ТАБЛИЦ
        console.log('\n🔧 Загрузка ALT таблиц...');
        const altTables = this.loadALTTablesDirectly();

        return {
            instructions,
            addressLookupTableAccounts: altTables,
            newPosition: newPosition,
            compressionStats: {
                originalInstructions: instructions.length,
                finalInstructions: instructions.length,
                altTables: altTables.length,
                totalAddresses: altTables.reduce((sum, alt) => sum + (alt.state?.addresses?.length || 0), 0)
            }
        };
    }

    /**
     * 🔥 START FLASH LOAN ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createStartFlashLoanInstruction(endIndex, wallet, marginfiAccountAddress) {
        console.log(`🔧 START Flash Loan с endIndex: ${endIndex}`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionData = Buffer.alloc(16);
        const correctDiscriminator = [14, 131, 33, 220, 81, 186, 180, 107]; // 0x0e8321dc51bab46b
        Buffer.from(correctDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(endIndex), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ
        const accounts = [
            { pubkey: new PublicKey(marginfiAccountAddress), isSigner: false, isWritable: true },
            { pubkey: wallet.publicKey, isSigner: true, isWritable: true },
            { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔥 END FLASH LOAN ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createEndFlashLoanInstruction(wallet, marginfiAccountAddress) {
        console.log('🔧 END Flash Loan инструкция');

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const endFlashLoanDiscriminator = [105, 124, 201, 106, 153, 2, 8, 156]; // 0x697cc96a9902089c
        const instructionData = Buffer.from(endFlashLoanDiscriminator);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ
        const accounts = [
            { pubkey: new PublicKey(marginfiAccountAddress), isSigner: false, isWritable: true },
            { pubkey: wallet.publicKey, isSigner: true, isWritable: true }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔥 BORROW ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createBorrowInstruction(amount, bankAddress, wallet, marginfiAccountAddress) {
        console.log(`🔧 BORROW ${amount} от банка ${bankAddress.toString().slice(0,8)}...`);

        // 🔥 ОПРЕДЕЛЯЕМ КАКОЙ БАНК (USDC ИЛИ SOL)
        const isUSDC = bankAddress.equals(this.BANKS.USDC);
        const vaultInfo = isUSDC ? this.VAULTS.USDC : this.VAULTS.SOL;

        console.log(`   💰 Токен: ${isUSDC ? 'USDC' : 'SOL'}`);
        console.log(`   🏦 Vault: ${vaultInfo.liquidityVault.toString().slice(0,8)}...`);
        console.log(`   👤 User Account: ${vaultInfo.userTokenAccount.toString().slice(0,8)}...`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ BORROW
        const borrowDiscriminator = [0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f];

        const instructionData = Buffer.alloc(16);
        Buffer.from(borrowDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ ДЛЯ BORROW
        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: new PublicKey(marginfiAccountAddress), isSigner: false, isWritable: true },
            { pubkey: wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: bankAddress, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔥 REPAY ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createRepayInstruction(bankAddress, wallet, marginfiAccountAddress) {
        console.log(`🔧 REPAY банк ${bankAddress.toString().slice(0,8)}... (repayAll: true)`);

        // 🔥 ОПРЕДЕЛЯЕМ КАКОЙ БАНК (USDC ИЛИ SOL)
        const isUSDC = bankAddress.equals(this.BANKS.USDC);
        const vaultInfo = isUSDC ? this.VAULTS.USDC : this.VAULTS.SOL;

        console.log(`   💰 Токен: ${isUSDC ? 'USDC' : 'SOL'}`);
        console.log(`   🏦 Vault: ${vaultInfo.liquidityVault.toString().slice(0,8)}...`);
        console.log(`   👤 User Account: ${vaultInfo.userTokenAccount.toString().slice(0,8)}...`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ REPAY
        const repayDiscriminator = [79, 209, 172, 177, 222, 51, 173, 151]; // 0x4fd1acb1de33ad97

        // 🔥 ПРАВИЛЬНАЯ СТРУКТУРА ДАННЫХ (18 BYTES!)
        const instructionData = Buffer.alloc(18);
        Buffer.from(repayDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(0), 8); // Amount (0 для repayAll)
        instructionData.writeUInt8(1, 16); // Some variant
        instructionData.writeUInt8(1, 17); // repayAll = true

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ ДЛЯ REPAY
        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: new PublicKey(marginfiAccountAddress), isSigner: false, isWritable: true },
            { pubkey: wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: bankAddress, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 ADD LIQUIDITY ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createMeteoraAddLiquidityInstruction(poolNumber) {
        console.log(`🔧 ADD Liquidity Pool ${poolNumber}`);

        // 🔥 METEORA DLMM ADD LIQUIDITY BY STRATEGY2 DISCRIMINATOR
        const addLiquidityDiscriminator = [3, 221, 149, 218, 111, 141, 118, 213];

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionDataHex = '03dd95da6f8d76d50000000000000000286411420f000000f7eeffff03000000f6eefffff6eeffff060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000********';
        const instructionData = Buffer.from(instructionDataHex, 'hex');

        const poolAddress = this.POOLS[poolNumber - 1];
        const positionAddress = '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A'; // ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ

        const instruction = new TransactionInstruction({
            keys: [
                { pubkey: new PublicKey(positionAddress), isSigner: false, isWritable: true },
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.USDC.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: true, isWritable: true }, // wallet (будет заменен динамически)
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: new PublicKey('4hE4o3aX5BMwkkjX8LUX3NdzJTszUtuJLFUZe2WuDMQF'), isSigner: false, isWritable: true }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });

        return instruction;
    }

    /**
     * 🔧 REMOVE LIQUIDITY ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createMeteoraRemoveLiquidityInstruction(poolNumber) {
        console.log(`🔧 REMOVE Liquidity Pool ${poolNumber}`);

        // 🔥 METEORA DLMM REMOVE LIQUIDITY BY RANGE2 DISCRIMINATOR
        const removeLiquidityDiscriminator = [204, 2, 195, 145, 53, 145, 145, 205];

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionDataHex = 'cc02c391359191cdf5eefffff6eeffff102702000000********';
        const instructionData = Buffer.from(instructionDataHex, 'hex');

        const poolAddress = this.POOLS[poolNumber - 1];

        const instruction = new TransactionInstruction({
            keys: [
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: true, isWritable: true }, // wallet
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.USDC.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });

        return instruction;
    }

    /**
     * 🔧 SWAP ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    async createMeteoraSwapInstruction(direction) {
        console.log(`🔧 ${direction} SOL swap`);

        const poolAddress = this.POOLS[0]; // Используем первый пул

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const swapDataHex = direction === 'BUY' ?
            '414b3f4ceb5b5b889332aa42926a0100000000000000000002000000********' : // BUY данные
            '414b3f4ceb5b5b880200000000000000000000000000000002000000********';   // SELL данные
        const data = Buffer.from(swapDataHex, 'hex');

        const accounts = [
            { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
            { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: new PublicKey('HaLvVwfhWoN3HGcSGbJwLMvq8Kfd9ZkV729paC8RbGpU'), isSigner: false, isWritable: true },
            { pubkey: new PublicKey('2JwJeA5tSAPdnriRRiy6H6qgCzYifpeii4Pehn1ANv8z'), isSigner: false, isWritable: true },
            { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: this.VAULTS.USDC.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: false },
            { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: false },
            { pubkey: new PublicKey('2TY9D9nYg6UjBtZ1av16hDdruYAARDn1CHLD8AJZXEJn'), isSigner: false, isWritable: true },
            { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: true, isWritable: true }, // wallet
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
            { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            keys: accounts,
            programId: this.METEORA_DLMM_PROGRAM,
            data: data
        });
    }

    /**
     * 🔧 CLAIM FEE ИНСТРУКЦИЯ (ИЗ ОСНОВНОГО БОТА)
     */
    createMeteoraClaimFeeInstruction(tokenType) {
        console.log(`🔧 claimFee2 ${tokenType}`);

        // 🔥 METEORA DLMM CLAIM FEE DISCRIMINATOR
        const claimFeeDiscriminator = [112, 191, 101, 171, 28, 144, 127, 187];

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionDataHex = '70bf65ab1c907fbbf5eefffff6eeffff02000000********';
        const instructionData = Buffer.from(instructionDataHex, 'hex');

        const poolAddress = this.POOLS[0]; // Используем первый пул

        const instruction = new TransactionInstruction({
            keys: [
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: true, isWritable: true }, // wallet
                { pubkey: tokenType === 'USDC' ? this.VAULTS.USDC.userTokenAccount : this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: tokenType === 'USDC' ? new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') : new PublicKey('So11111111111111111111111111111111111111112'), isSigner: false, isWritable: false },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });

        return instruction;
    }

    /**
     * 🔧 CLOSE ACCOUNT ИНСТРУКЦИЯ (SPL TOKEN)
     */
    createMeteoraCloseAccountInstruction(tokenType) {
        console.log(`🔧 closeAccount ${tokenType}`);

        // 🔥 SPL TOKEN CLOSE ACCOUNT DISCRIMINATOR
        const instructionData = Buffer.alloc(1);
        instructionData[0] = 9; // CloseAccount instruction

        const instruction = new TransactionInstruction({
            keys: [
                { pubkey: tokenType === 'USDC' ? this.VAULTS.USDC.userTokenAccount : this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: false, isWritable: true }, // wallet
                { pubkey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'), isSigner: true, isWritable: false } // wallet
            ],
            programId: this.TOKEN_PROGRAM,
            data: instructionData
        });

        return instruction;
    }

    /**
     * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ (ИЗ ОСНОВНОГО БОТА)
     */
    loadALTTablesDirectly() {
        try {
            console.log('🔥 ЗАГРУЗКА ALT ТАБЛИЦ НАПРЯМУЮ ИЗ ФАЙЛА...');

            const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
            console.log(`📊 Загружено из файла: ${fileData.totalTables} ALT таблиц`);

            const formattedALTs = [];
            let totalAccounts = 0;
            let index = 0;

            for (const [tableName, tableData] of Object.entries(fileData.tables)) {
                if (tableData.address && tableData.addresses) {
                    const formattedALT = {
                        key: new PublicKey(tableData.address),
                        state: {
                            addresses: tableData.addresses.map(addr => new PublicKey(addr))
                        }
                    };
                    formattedALTs.push(formattedALT);
                    totalAccounts += tableData.addresses.length;
                    console.log(`✅ ALT ${index + 1} (${tableName}): ${tableData.addresses.length} адресов`);
                    index++;
                }
            }

            console.log(`📊 Всего аккаунтов: ${totalAccounts}`);
            return formattedALTs;

        } catch (error) {
            console.log(`❌ Ошибка загрузки ALT таблиц: ${error.message}`);
            return [];
        }
    }
}

/**
 * 🔥 ГЛАВНАЯ ФУНКЦИЯ ТЕСТИРОВАНИЯ
 */
async function testIntegratedFlashLoan() {
    console.log('🔥 ТЕСТИРОВАНИЕ ИНТЕГРИРОВАННОГО FLASH LOAN + METEORA POSITION\n');

    try {
        const integrated = new IntegratedFlashLoanMeteora();
        const result = await integrated.createCompleteFlashLoanWithPosition();

        console.log('\n✅ ИНТЕГРИРОВАННАЯ ТРАНЗАКЦИЯ СОЗДАНА УСПЕШНО!');
        console.log(`📊 Инструкций: ${result.instructions.length}`);
        console.log(`🔑 Position: ${result.newPosition.publicKey.toString()}`);
        console.log(`📋 ALT таблиц: ${result.addressLookupTableAccounts.length}`);
        console.log(`📊 Всего адресов в ALT: ${result.compressionStats.totalAddresses}`);

        // Сохраняем результат
        const resultData = {
            timestamp: new Date().toISOString(),
            instructionCount: result.instructions.length,
            positionAddress: result.newPosition.publicKey.toString(),
            compressionStats: result.compressionStats,
            borrowAmounts: {
                USDC: '2,500,000 USDC',
                SOL: '8,300 SOL'
            }
        };

        fs.writeFileSync('integrated-flash-loan-result.json', JSON.stringify(resultData, null, 2));
        console.log('💾 Результат сохранен в integrated-flash-loan-result.json');

    } catch (error) {
        console.error('❌ Ошибка:', error.message);
        console.error(error.stack);
    }
}

// Запуск тестирования
if (require.main === module) {
    testIntegratedFlashLoan().catch(console.error);
}

module.exports = IntegratedFlashLoanMeteora;
