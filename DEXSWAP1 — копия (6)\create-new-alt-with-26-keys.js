/**
 * 🔥 СОЗДАНИЕ НОВОЙ ALT ТАБЛИЦЫ С 26 НЕПОКРЫТЫМИ КЛЮЧАМИ
 * 
 * Создаём новую Address Lookup Table с 26 ключами для экономии 806 байт
 */

const { Connection, PublicKey, Keypair, TransactionMessage, VersionedTransaction, AddressLookupTableProgram } = require('@solana/web3.js');
const fs = require('fs');

// 🔥 НАСТРОЙКИ
const RPC_URL = 'https://api.devnet.solana.com';

console.log('🔥 СОЗДАНИЕ НОВОЙ ALT ТАБЛИЦЫ С 26 НЕПОКРЫТЫМИ КЛЮЧАМИ');

/**
 * 🚀 ОСНОВНАЯ ФУНКЦИЯ СОЗДАНИЯ ALT С КЛЮЧАМИ
 */
async function createNewALTWith26Keys() {
    try {
        // 1. Подключение к Solana
        console.log('🌐 Подключение к Solana Devnet...');
        const connection = new Connection(RPC_URL, 'confirmed');
        
        // 2. Загрузка кошелька
        console.log('💰 Загрузка кошелька...');
        const secretKey = JSON.parse(fs.readFileSync('guideSecret.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        console.log(`✅ Кошелек загружен: ${wallet.publicKey.toString()}`);

        // 3. Загрузка 26 непокрытых ключей из анализа
        console.log('📋 Загрузка непокрытых ключей...');
        let uncoveredKeysData;
        try {
            uncoveredKeysData = JSON.parse(fs.readFileSync('all-24-instructions-uncovered-keys.json', 'utf8'));
            console.log('✅ Файл с непокрытыми ключами загружен');
        } catch (error) {
            console.log('❌ Не удалось загрузить файл с ключами:', error.message);
            return;
        }

        // 4. Извлекаем 26 уникальных ключей
        const uncoveredKeys = uncoveredKeysData.keysToAdd.map(key => key.address);
        console.log(`📊 Найдено ${uncoveredKeys.length} непокрытых ключей для добавления`);

        // Выводим список ключей по категориям
        console.log('\n📋 КЛЮЧИ ДЛЯ ДОБАВЛЕНИЯ В НОВУЮ ALT:');
        uncoveredKeysData.keysToAdd.forEach((key, index) => {
            console.log(`   ${index + 1}. ${key.address.slice(0,8)}...${key.address.slice(-8)} (${key.category})`);
        });

        // 5. Проверяем баланс кошелька
        console.log('\n💰 ПРОВЕРКА БАЛАНСА...');
        const balance = await connection.getBalance(wallet.publicKey);
        console.log(`💰 Баланс кошелька: ${balance / 1e9} SOL`);
        
        if (balance < 0.01 * 1e9) {
            console.log('❌ Недостаточно SOL для создания ALT таблицы!');
            console.log('   Нужно минимум 0.01 SOL');
            return;
        }

        // 6. Создаем инструкцию для создания ALT таблицы
        console.log('\n🔧 СОЗДАНИЕ НОВОЙ ALT ТАБЛИЦЫ...');

        // Получаем последний blockhash
        const { blockhash } = await connection.getLatestBlockhash('confirmed');
        console.log(`✅ Blockhash получен: ${blockhash.slice(0, 8)}...`);

        // Создаем инструкцию создания ALT
        const [createALTInstruction, altAddress] = AddressLookupTableProgram.createLookupTable({
            authority: wallet.publicKey,
            payer: wallet.publicKey,
            recentSlot: await connection.getSlot('confirmed')
        });

        console.log(`✅ Новый адрес ALT: ${altAddress.toString()}`);

        // 7. Создаем и отправляем транзакцию создания ALT
        console.log('\n🚀 ОТПРАВКА ТРАНЗАКЦИИ СОЗДАНИЯ ALT...');

        const createMessage = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [createALTInstruction]
        }).compileToV0Message();

        const createTransaction = new VersionedTransaction(createMessage);
        createTransaction.sign([wallet]);

        const createSignature = await connection.sendTransaction(createTransaction, {
            maxRetries: 3,
            preflightCommitment: 'confirmed'
        });

        console.log(`✅ Транзакция создания отправлена: ${createSignature}`);

        // Ждем подтверждения
        console.log('⏳ Ожидание подтверждения создания ALT...');
        const createConfirmation = await connection.confirmTransaction(createSignature, 'confirmed');
        
        if (createConfirmation.value.err) {
            throw new Error(`Создание ALT провалилось: ${createConfirmation.value.err}`);
        }

        console.log('✅ ALT таблица создана!');

        // 8. Ждем активации ALT (нужно подождать несколько слотов)
        console.log('\n⏳ ОЖИДАНИЕ АКТИВАЦИИ ALT (10 секунд)...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        // 9. Добавляем ключи в ALT таблицу
        console.log(`\n🔧 ДОБАВЛЕНИЕ ${uncoveredKeys.length} КЛЮЧЕЙ В ALT...`);

        // Конвертируем строки в PublicKey
        const keyPublicKeys = uncoveredKeys.map(key => new PublicKey(key));

        // Создаем инструкцию extend
        const extendInstruction = AddressLookupTableProgram.extendLookupTable({
            payer: wallet.publicKey,
            authority: wallet.publicKey,
            lookupTable: altAddress,
            addresses: keyPublicKeys
        });

        console.log('✅ Инструкция extend создана');

        // 10. Создаем и отправляем транзакцию расширения
        console.log('\n🚀 ОТПРАВКА ТРАНЗАКЦИИ РАСШИРЕНИЯ ALT...');

        // Получаем новый blockhash
        const { blockhash: newBlockhash } = await connection.getLatestBlockhash('confirmed');

        const extendMessage = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: newBlockhash,
            instructions: [extendInstruction]
        }).compileToV0Message();

        const extendTransaction = new VersionedTransaction(extendMessage);
        extendTransaction.sign([wallet]);

        const extendSignature = await connection.sendTransaction(extendTransaction, {
            maxRetries: 3,
            preflightCommitment: 'confirmed'
        });

        console.log(`✅ Транзакция расширения отправлена: ${extendSignature}`);

        // Ждем подтверждения
        console.log('⏳ Ожидание подтверждения расширения...');
        const extendConfirmation = await connection.confirmTransaction(extendSignature, 'confirmed');
        
        if (extendConfirmation.value.err) {
            throw new Error(`Расширение ALT провалилось: ${extendConfirmation.value.err}`);
        }

        console.log('✅ Ключи добавлены в ALT!');

        // 11. Проверяем результат
        console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА...');
        const altAccount = await connection.getAddressLookupTable(altAddress);
        const addresses = altAccount.value.state.addresses.map(addr => addr.toString());

        console.log(`✅ Адресов в новой ALT: ${addresses.length}`);

        // 12. Сохраняем результат
        const result = {
            success: true,
            altAddress: altAddress.toString(),
            createSignature: createSignature,
            extendSignature: extendSignature,
            addedKeys: uncoveredKeys,
            totalAddresses: addresses.length,
            expectedCoverage: 112, // Покроем все 112 вхождений
            byteSavings: uncoveredKeys.length * 31, // Экономия байт
            timestamp: new Date().toISOString(),
            network: 'devnet',
            categories: uncoveredKeysData.keysToAdd.reduce((acc, key) => {
                acc[key.category] = (acc[key.category] || 0) + 1;
                return acc;
            }, {})
        };

        const resultFile = 'new-alt-with-26-keys-result.json';
        fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
        console.log(`✅ Результат сохранен в: ${resultFile}`);

        // Сохраняем адрес ALT в отдельный файл
        fs.writeFileSync('new-alt-address.txt', altAddress.toString());
        console.log(`✅ Адрес ALT сохранен в: new-alt-address.txt`);

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎉 НОВАЯ ALT ТАБЛИЦА С 26 КЛЮЧАМИ СОЗДАНА!');
        console.log('🚀 ТЕПЕРЬ ВСЕ 112 ВХОЖДЕНИЙ БУДУТ ПОКРЫТЫ ALT!');
        console.log(`📊 ALT адрес: ${altAddress.toString()}`);
        console.log(`📊 Создание: ${createSignature}`);
        console.log(`📊 Расширение: ${extendSignature}`);
        console.log(`📊 Добавлено ключей: ${uncoveredKeys.length}`);
        console.log(`📊 Всего адресов в ALT: ${addresses.length}`);
        console.log(`💰 Экономия в транзакции: ${uncoveredKeys.length * 31} байт`);
        console.log(`🔥 Покрытие увеличится с 10% до ~90%!`);
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка создания ALT:', error.message);
        console.error(error.stack);
    }
}

// Запуск скрипта
if (require.main === module) {
    createNewALTWith26Keys();
}

module.exports = { createNewALTWith26Keys };
