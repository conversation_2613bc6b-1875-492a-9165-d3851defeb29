const { Connection } = require('@solana/web3.js');

async function checkSignature() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    const signature = '56jFhNNxFis3awx7k15qLkoRpUbYeqmfSDY7RVfrrHovsxuqiyzhut8VUGhQH7aZbi1RKkAs8H3YEBWwbJsHbrBH';
    
    try {
        console.log(`🔍 Проверяем signature: ${signature}`);
        
        // Проверяем статус транзакции
        const status = await connection.getSignatureStatus(signature);
        console.log('📊 Статус транзакции:', status);
        
        // Пробуем получить транзакцию
        const transaction = await connection.getTransaction(signature);
        console.log('📋 Транзакция:', transaction ? 'НАЙДЕНА' : 'НЕ НАЙДЕНА');
        
        if (transaction) {
            console.log('✅ Транзакция существует в блокчейне');
            console.log(`   Slot: ${transaction.slot}`);
            console.log(`   Block time: ${new Date(transaction.blockTime * 1000)}`);
            console.log(`   Fee: ${transaction.meta.fee} lamports`);
            console.log(`   Status: ${transaction.meta.err ? 'FAILED' : 'SUCCESS'}`);
            if (transaction.meta.err) {
                console.log(`   Error: ${JSON.stringify(transaction.meta.err)}`);
            }
        } else {
            console.log('❌ Транзакция НЕ найдена в блокчейне');
            console.log('💡 Возможные причины:');
            console.log('   1. Транзакция не была отправлена в сеть');
            console.log('   2. Транзакция отклонена валидаторами');
            console.log('   3. Неправильный signature');
            console.log('   4. Транзакция еще обрабатывается');
        }
        
    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

checkSignature();
