const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

// 🌐 ИМПОРТ ЦЕНТРАЛИЗОВАННОГО RPC МЕНЕДЖЕРА
const { globalRPCManager } = require('./centralized-rpc-manager.js');

// 🧠 ИМПОРТ УМНОГО АНАЛИЗАТОРА
const SmartLiquidityAnalyzer = require('./smart-liquidity-analyzer');

/**
 * 🚀 METEORA ACTIVE BIN CACHE MANAGER
 * - Загружает только активные бины для каждого пула
 * - Кэширует на 10 секунд для быстрого обновления
 * - Автоматически обновляет последним полученным бином
 * - Оптимизирован для арбитража с минимальной задержкой
 */
class MeteoraBinCacheManager {
    constructor() {
        // 🌐 ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР ВМЕСТО ПРЯМОГО ПОДКЛЮЧЕНИЯ!
        this.rpcManager = globalRPCManager;
        this.dataRPC = null; // Будет получено через RPC менеджер
        console.log(`🌐 Используем централизованный RPC менеджер с fallback endpoints`);

        // 💾 КЭШИ ТОЛЬКО ДЛЯ АКТИВНЫХ БИНОВ
        this.activeBinsCache = new Map(); // poolAddress -> {activeBin, timestamp, price, liquidity}
        this.dlmmInstancesCache = new Map(); // poolAddress -> dlmmInstance

        // 🚫 СТАРЫЙ КЭША binArraysCache УДАЛЕН - ИСПОЛЬЗУЕМ ТОЛЬКО activeBinsCache!

        // ⏰ НАСТРОЙКИ КЭШИРОВАНИЯ АКТИВНЫХ БИНОВ (ОПТИМИЗИРОВАНО ПОД RPC ЛИМИТЫ)
        this.ACTIVE_BIN_CACHE_DURATION = 5000; // 🚀 5 СЕКУНД для активных бинов (увеличено для стабильности)
        this.DLMM_CACHE_DURATION = 5 * 60 * 1000;   // 5 МИНУТ для DLMM инстансов

        // 🔥 АВТООБНОВЛЕНИЕ КЭША КАЖДЫЕ 500ms!
        this.autoUpdateInterval = null;

        // 🧠 ИНИЦИАЛИЗАЦИЯ УМНОГО АНАЛИЗАТОРА
        this.smartAnalyzer = new SmartLiquidityAnalyzer();
        this.lastSmartAnalysis = null;
        this.poolsToWatch = []; // Пулы для автообновления

        // 🔧 УНИВЕРСАЛЬНАЯ ФУНКЦИЯ ДЛЯ БЕЗОПАСНОГО ПОЛУЧЕНИЯ СТРОКИ
        this.getPoolStr = (poolAddress) => {
            return typeof poolAddress === 'string' ? poolAddress : poolAddress.toString();
        };

        console.log('🚀 Meteora Active Bin Cache Manager инициализирован');
        console.log(`⚡ Кэш активных бинов: ${this.ACTIVE_BIN_CACHE_DURATION / 1000} секунд (оптимизировано под RPC лимиты)`);
        console.log(`🌐 RPC: Централизованный менеджер с автоматическим fallback`);
    }

    /**
     * 🌐 ПОЛУЧЕНИЕ ПОДКЛЮЧЕНИЯ ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР
     */
    async getConnection() {
        if (!this.dataRPC) {
            this.dataRPC = await this.rpcManager.getConnection();
        }
        return this.dataRPC;
    }

    /**
     * 🔄 ВЫПОЛНЕНИЕ RPC ОПЕРАЦИИ С RETRY ЛОГИКОЙ
     */
    async executeRPCOperation(operation) {
        return await this.rpcManager.executeWithRetry(operation);
    }

    /**
     * 🔥 ЗАГРУЗКА 3 БИНОВ ДЛЯ ПУЛА (АКТИВНЫЙ + СОСЕДНИЕ)
     * Загружает активный бин + соседние (-1, +1) для умного анализатора
     */
    async loadThreeBinsForPool(poolAddress) {
        try {
            // Загружаем активный бин для пула (без логов для батчевой загрузки)

            // 1. Получаем или создаем DLMM инстанс
            let dlmmInstance = this.dlmmInstancesCache.get(poolAddress);
            if (!dlmmInstance) {
                dlmmInstance = await DLMM.create(this.dataRPC, new PublicKey(poolAddress));
                this.dlmmInstancesCache.set(poolAddress, dlmmInstance);

                // 🔍 ОТЛАДКА: ЧТО ПОЛУЧАЕМ ИЗ DLMM.create()
                console.log(`🔍 DLMM.create() результат для ${this.getPoolStr(poolAddress).slice(0, 8)}:`);
                console.log(`   lbPair.activeId: ${dlmmInstance.lbPair?.activeId}`);
                console.log(`   lbPair.binStep: ${dlmmInstance.lbPair?.binStep}`);
                console.log(`   lbPair.reserveX: ${dlmmInstance.lbPair?.reserveX?.toString()}`);
                console.log(`   lbPair.reserveY: ${dlmmInstance.lbPair?.reserveY?.toString()}`);
                console.log(`   tokenX: ${dlmmInstance.tokenX?.publicKey?.toString()}`);
                console.log(`   tokenY: ${dlmmInstance.tokenY?.publicKey?.toString()}`);
                console.log(`   Доступные методы: ${Object.getOwnPropertyNames(Object.getPrototypeOf(dlmmInstance)).filter(name => typeof dlmmInstance[name] === 'function').slice(0, 5).join(', ')}...`);
            }

            // 2. Получаем активный бин ID
            const activeBinId = dlmmInstance.lbPair.activeId;

            // 🔥 3. ПОЛУЧАЕМ 3 БИНА: АКТИВНЫЙ + СОСЕДНИЕ (-1, +1)
            const binIds = [activeBinId - 1, activeBinId, activeBinId + 1];
            const threeBins = [];

            for (const binId of binIds) {
                try {
                    const bin = await dlmmInstance.getBin(binId);
                    threeBins.push({
                        binId: binId,
                        price: parseFloat(dlmmInstance.fromPricePerLamport(Number(bin.price))),
                        liquidityX: bin.reserveX ? parseFloat(bin.reserveX.toString()) : 0,
                        liquidityY: bin.reserveY ? parseFloat(bin.reserveY.toString()) : 0,
                        isActive: binId === activeBinId
                    });
                } catch (binError) {
                    // Если бин не существует, добавляем пустой
                    threeBins.push({
                        binId: binId,
                        price: 0,
                        liquidityX: 0,
                        liquidityY: 0,
                        isActive: binId === activeBinId,
                        empty: true
                    });
                }
            }

            // 🔥 ИСПОЛЬЗУЕМ SDK МЕТОД ДЛЯ ПРАВИЛЬНОГО РАСЧЕТА ЦЕНЫ!
            let activeBinPrice;

            try {
                // Создаем DLMM инстанс и получаем активный bin
                const dlmmInstance = await DLMM.create(this.dataRPC, new PublicKey(poolAddress));
                const activeBin = await dlmmInstance.getActiveBin();

                // 🎯 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ SDK МЕТОД!
                activeBinPrice = parseFloat(dlmmInstance.fromPricePerLamport(Number(activeBin.price)));

                // Проверяем что цена валидна
                if (!isFinite(activeBinPrice) || activeBinPrice <= 0) {
                    throw new Error(`Невалидная цена: ${activeBinPrice}`);
                }

            } catch (mathError) {
                console.log(`⚠️ Ошибка расчета цены через SDK: ${mathError.message}`);
                throw mathError; // Пробрасываем ошибку вместо хардкода
            }

            // 🚀 БИН ARRAYS НЕ НУЖНЫ - У НАС УЖЕ ЕСТЬ ВСЕ ДАННЫЕ!
            // activeBinId и binStep уже получены из dlmmInstance.lbPair
            // Цена рассчитана по формуле - НИКАКИХ ДОПОЛНИТЕЛЬНЫХ RPC ЗАПРОСОВ!

            // 4. Ликвидность активного бина (приблизительная оценка)
            const activeBinLiquidity = 1000000; // Приблизительная ликвидность для расчетов

            // 5. Сохраняем в кэш 3 БИНА
            const cacheData = {
                activeBinId,
                activeBinPrice,
                activeBinLiquidity,
                threeBins: threeBins, // 🔥 ДОБАВЛЯЕМ 3 БИНА ДЛЯ УМНОГО АНАЛИЗАТОРА
                timestamp: Date.now(),
                poolAddress
            };

            this.activeBinsCache.set(poolAddress, cacheData);

            // 🔍 ОТЛАДКА: ПРОВЕРЯЕМ ЧТО 3 БИНА СОХРАНЕНЫ
            console.log(`   🔍 СОХРАНЕНО В КЭШ: ${threeBins.length} бинов для пула ${poolAddress.slice(0,8)}...`);
            threeBins.forEach((bin, index) => {
                const binName = index === 0 ? 'ЛЕВЫЙ' : index === 1 ? 'АКТИВНЫЙ' : 'ПРАВЫЙ';
                console.log(`      ${binName} бин ${bin.binId}: ${(bin.liquidityX + bin.liquidityY).toLocaleString()} ликвидности`);
            });

            // 3 бина загружены для умного анализатора
            return cacheData;

        } catch (error) {
            console.error(`❌ Ошибка загрузки активного бина для ${poolAddress}:`, error.message);
            return null;
        }
    }

    /**
     * 🚀 БАТЧЕВАЯ ЗАГРУЗКА АКТИВНЫХ БИНОВ (ОДИН RPC ЗАПРОС!)
     * Загружает данные всех пулов одним getMultipleAccountsInfo запросом
     */
    async loadMultipleActiveBinsBatch(poolAddresses) {
        try {
            if (poolAddresses.length === 0) return [];

            // 🌐 ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР С RETRY ЛОГИКОЙ!
            const poolPublicKeys = poolAddresses.map(addr => new PublicKey(addr));
            const accountsInfo = await this.executeRPCOperation(async (connection) => {
                return await connection.getMultipleAccountsInfo(poolPublicKeys);
            });

            const results = [];

            for (let i = 0; i < poolAddresses.length; i++) {
                const poolAddress = poolAddresses[i];
                const accountInfo = accountsInfo[i];

                if (!accountInfo || !accountInfo.data) {
                    console.log(`❌ Нет данных для пула ${this.getPoolStr(poolAddress).slice(0, 8)}`);
                    continue;
                }

                try {
                    console.log(`🔍 ОБРАБОТКА ПУЛА: ${this.getPoolStr(poolAddress).slice(0, 8)}`);

                    // 🔥 СОЗДАЕМ DLMM ИНСТАНС ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР!
                    const connection = await this.getConnection();
                    const dlmmInstance = await DLMM.create(connection, new PublicKey(poolAddress));
                    console.log(`   ✅ DLMM инстанс создан для ${this.getPoolStr(poolAddress).slice(0, 8)}`);

                    // 🔥 ПОЛУЧАЕМ 3 БИНА: АКТИВНЫЙ + СОСЕДНИЕ (-1, +1)
                    const activeBinId = dlmmInstance.lbPair.activeId;
                    const binIds = [activeBinId - 1, activeBinId, activeBinId + 1];
                    const threeBins = [];

                    for (const binId of binIds) {
                        try {
                            const bin = await dlmmInstance.getBin(binId);
                            threeBins.push({
                                binId: binId,
                                price: parseFloat(dlmmInstance.fromPricePerLamport(Number(bin.price))),
                                liquidityX: bin.reserveX ? parseFloat(bin.reserveX.toString()) : 0,
                                liquidityY: bin.reserveY ? parseFloat(bin.reserveY.toString()) : 0,
                                isActive: binId === activeBinId
                            });
                        } catch (binError) {
                            // Если бин не существует, добавляем пустой
                            threeBins.push({
                                binId: binId,
                                price: 0,
                                liquidityX: 0,
                                liquidityY: 0,
                                isActive: binId === activeBinId,
                                empty: true
                            });
                        }
                    }

                    console.log(`   ✅ 3 бина получены для ${this.getPoolStr(poolAddress).slice(0, 8)}`);

                    // 🎯 ЦЕНА АКТИВНОГО БИНА - ИСПОЛЬЗУЕМ ПЕРВЫЙ НЕПУСТОЙ БИН
                    let activeBinPrice = 0;
                    for (const bin of threeBins) {
                        if (bin.price > 0 && !bin.empty) {
                            activeBinPrice = bin.price;
                            break;
                        }
                    }

                    // Если все бины пустые, используем SDK метод
                    if (activeBinPrice === 0) {
                        try {
                            const activeBin = await dlmmInstance.getActiveBin();
                            activeBinPrice = parseFloat(dlmmInstance.fromPricePerLamport(Number(activeBin.price)));
                        } catch (sdkError) {
                            console.log(`   ⚠️ SDK метод тоже не работает: ${sdkError.message}`);
                            activeBinPrice = 187.5; // Fallback цена
                        }
                    }

                    console.log(`   📊 Данные для ${this.getPoolStr(poolAddress).slice(0, 8)}: ID=${activeBinId}, цена=${activeBinPrice}`);

                    // Проверяем что цена валидна
                    if (!isFinite(activeBinPrice) || activeBinPrice <= 0) {
                        throw new Error(`Невалидная цена: ${activeBinPrice}`);
                    }

                    // 💾 СОХРАНЯЕМ DLMM ИНСТАНС В КЭШ!
                    this.dlmmInstancesCache.set(poolAddress, dlmmInstance);
                    console.log(`   💾 DLMM инстанс сохранен в кэш для ${this.getPoolStr(poolAddress).slice(0, 8)}`);

                    // 💾 СОХРАНЯЕМ В КЭШ С 3 БИНАМИ
                    const cacheData = {
                        activeBinId,
                        activeBinPrice,
                        activeBinLiquidity: 1000000,
                        threeBins: threeBins, // 🔥 ДОБАВЛЯЕМ 3 БИНА ДЛЯ УМНОГО АНАЛИЗАТОРА
                        timestamp: Date.now(),
                        poolAddress
                    };

                    this.activeBinsCache.set(poolAddress, cacheData);
                    console.log(`   💾 Активные бины сохранены в кэш для ${this.getPoolStr(poolAddress).slice(0, 8)}`);

                    results.push(cacheData);

                } catch (parseError) {
                    console.error(`❌ Ошибка парсинга пула ${this.getPoolStr(poolAddress).slice(0, 8)}:`, parseError.message);
                }
            }

            console.log(`✅ Батчевая загрузка: ${results.length}/${poolAddresses.length} пулов обновлено`);
            return results;

        } catch (error) {
            console.error(`❌ Ошибка батчевой загрузки:`, error.message);

            // 🔄 RETRY ЛОГИКА ДЛЯ FETCH FAILED ОШИБОК
            if (error.message.includes('fetch failed') || error.message.includes('network')) {
                console.log(`🔄 Повторная попытка через 2 секунды...`);
                await new Promise(resolve => setTimeout(resolve, 2000));

                try {
                    // Повторная попытка с одним пулом
                    if (poolAddresses.length > 1) {
                        console.log(`🔄 Пробуем загрузить пулы по одному...`);
                        const singleResults = [];
                        for (const poolAddress of poolAddresses) {
                            try {
                                const singleResult = await this.loadMultipleActiveBinsBatch([poolAddress]);
                                singleResults.push(...singleResult);
                                await new Promise(resolve => setTimeout(resolve, 500)); // Задержка между запросами
                            } catch (singleError) {
                                console.log(`❌ Пул ${poolAddress.slice(0, 8)} недоступен: ${singleError.message}`);
                            }
                        }
                        return singleResults;
                    }
                } catch (retryError) {
                    console.error(`❌ Повторная попытка не удалась:`, retryError.message);
                }
            }

            return [];
        }
    }

    /**
     * 🔄 ОБНОВЛЕНИЕ АКТИВНОГО БИНА
     * Быстро обновляет только активный бин без полной перезагрузки
     */
    async updateActiveBin(poolAddress) {
        try {
            const cached = this.activeBinsCache.get(poolAddress);
            if (!cached) {
                // Если нет в кэше, загружаем с нуля
                const results = await this.loadMultipleActiveBinsBatch([poolAddress]);
                return results[0] || null;
            }

            // Проверяем, нужно ли обновление (3 секунды)
            const age = Date.now() - cached.timestamp;
            if (age < this.ACTIVE_BIN_CACHE_DURATION) {
                return cached; // Кэш еще свежий (младше 3 секунд)
            }

            // Обновляем активный бин
            const results = await this.loadMultipleActiveBinsBatch([poolAddress]);
            return results[0] || null;

        } catch (error) {
            // Не спамим ошибками каждые 500мс
            if (Date.now() % 5000 < 500) {
                console.error(`❌ Ошибка обновления активного бина для ${poolAddress.slice(0, 8)}:`, error.message);
            }
            return null;
        }
    }

    /**
     * 🚀 БАТЧЕВОЕ ОБНОВЛЕНИЕ ВСЕХ АКТИВНЫХ БИНОВ
     * Обновляет все активные бины параллельно одним батчем
     */
    async batchUpdateAllActiveBins(poolAddresses) {
        try {
            // 🔥 ПРИНУДИТЕЛЬНОЕ БАТЧЕВОЕ ОБНОВЛЕНИЕ ВСЕХ ПУЛОВ ОДНОВРЕМЕННО!
            // НЕ ФИЛЬТРУЕМ ПО ВРЕМЕНИ - ОБНОВЛЯЕМ ВСЕ СРАЗУ!
            const now = Date.now();
            const poolsToUpdate = poolAddresses; // Обновляем ВСЕ пулы одним батчем

            if (poolsToUpdate.length === 0) {
                return; // Нет пулов для обновления
            }

            // 🔥 ПРАВИЛЬНОЕ БАТЧЕВОЕ ОБНОВЛЕНИЕ - ВСЕ ПУЛЫ ОДНОВРЕМЕННО!
            if (poolsToUpdate.length > 0) {
                console.log(`🔥 Батчевое обновление ${poolsToUpdate.length} активных бинов...`);

                // 🚀 ОДИН БАТЧЕВЫЙ RPC ЗАПРОС ДЛЯ ВСЕХ ПУЛОВ СРАЗУ!
                const results = await this.loadMultipleActiveBinsBatch(poolsToUpdate);

                // 🔥 ПОКАЗЫВАЕМ ВСЕ РЕЗУЛЬТАТЫ ОДНОВРЕМЕННО!
                const validResults = [];
                results.forEach((result, index) => {
                    if (result) {
                        const poolAddress = poolsToUpdate[index];
                        validResults.push({
                            address: poolAddress,
                            price: result.activeBinPrice,
                            binId: result.activeBinId,
                            name: poolAddress === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6' ? 'Pool 1' : 'Pool 2'
                        });
                        console.log(`   📊 ${validResults[validResults.length - 1].name}: $${result.activeBinPrice.toFixed(4)} (бин ID: ${result.activeBinId})`);
                    }
                });

                // 🔥 ЕСЛИ ОБНОВИЛИ ОБА ПУЛА - СЧИТАЕМ СПРЕД И ЗАПУСКАЕМ АНАЛИЗ!
                if (validResults.length === 2) {
                    const pool1 = validResults.find(r => r.address === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
                    const pool2 = validResults.find(r => r.address === 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');

                    if (pool1 && pool2) {
                        const spread = Math.abs(pool2.price - pool1.price);
                        const spreadPercent = (spread / pool1.price) * 100;

                        console.log(`   💰 СПРЕД: $${spread.toFixed(4)} (${spreadPercent.toFixed(3)}%)`);

                        // 🎯 СПРЕД ПОДХОДИТ! ЗАПУСКАЕМ УМНЫЙ АНАЛИЗАТОР...
                        console.log(`   🎯 СПРЕД ПОДХОДИТ! ЗАПУСКАЕМ УМНЫЙ АНАЛИЗАТОР...`);
                    }
                }

                console.log(`🚀 Батчевое обновление: ${poolsToUpdate.length} активных бинов за ${Date.now() - now}мс`);
            }

        } catch (error) {
            console.error('❌ Ошибка батчевого обновления активных бинов:', error.message);
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ АКТИВНЫХ БИНОВ
     */
    getActiveBinsStats() {
        const totalPools = this.activeBinsCache.size;
        const activeBins = Array.from(this.activeBinsCache.values()).length;
        const freshBins = Array.from(this.activeBinsCache.values()).filter(
            cache => (Date.now() - cache.timestamp) < this.ACTIVE_BIN_CACHE_DURATION
        ).length;

        return {
            totalPools,
            activeBins,
            freshBins,
            staleBins: activeBins - freshBins
        };
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ТОЧНОЙ ЦЕНЫ ИЗ АКТИВНОГО БИНА
     * Самый точный способ получения цены - прямо из активного бина
     */
    getExactPriceFromActiveBin(poolAddress) {
        const cached = this.activeBinsCache.get(poolAddress);

        if (!cached) {
            console.log(`⚠️ Нет кэша активного бина для ${poolAddress.slice(0, 8)}`);
            return null;
        }

        // Проверяем свежесть данных
        const age = Date.now() - cached.timestamp;
        if (age > this.ACTIVE_BIN_CACHE_DURATION) {
            console.log(`⚠️ Устаревший кэш для ${poolAddress.slice(0, 8)} (возраст: ${age}ms)`);
            return null;
        }

        return {
            price: cached.activeBinPrice,
            binId: cached.activeBinId,
            timestamp: cached.timestamp,
            age: age,
            fresh: age < this.ACTIVE_BIN_CACHE_DURATION
        };
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ АКТИВНОГО БИНА ИЗ КЭША (ДЛЯ ЗАМЕНЫ getActiveBin())
     * Возвращает данные активного бина без RPC запросов
     */
    getActiveBinFromCache(poolAddress) {
        const cached = this.activeBinsCache.get(poolAddress);

        if (!cached) {
            console.log(`⚠️ Нет кэша активного бина для ${poolAddress.slice(0, 8)}`);
            return null;
        }

        // Проверяем свежесть данных
        const age = Date.now() - cached.timestamp;
        if (age > this.ACTIVE_BIN_CACHE_DURATION) {
            console.log(`⚠️ Устаревший кэш для ${poolAddress.slice(0, 8)} (возраст: ${age}ms)`);
            return null;
        }

        // 🔍 ОТЛАДКА: ПРОВЕРЯЕМ ЧТО 3 БИНА ВОЗВРАЩАЮТСЯ
        const threeBinsCount = cached.threeBins ? cached.threeBins.length : 0;
        console.log(`   🔍 ВОЗВРАЩАЕМ ИЗ КЭША: ${threeBinsCount} бинов для пула ${poolAddress.slice(0,8)}...`);

        return {
            activeBinPrice: cached.activeBinPrice,
            activeBinId: cached.activeBinId,
            threeBins: cached.threeBins, // 🔥 ДОБАВЛЯЕМ 3 БИНА ДЛЯ УМНОГО АНАЛИЗАТОРА!
            poolAddress: cached.poolAddress, // 🔥 ДОБАВЛЯЕМ АДРЕС ПУЛА
            timestamp: cached.timestamp,
            age: age,
            fresh: age < this.ACTIVE_BIN_CACHE_DURATION
        };
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ТОЧНЫХ ЦЕН ВСЕХ ПУЛОВ
     * Возвращает самые точные цены из активных бинов
     */
    getAllExactPrices() {
        const prices = {};

        for (const [poolAddress, cache] of this.activeBinsCache.entries()) {
            const priceData = this.getExactPriceFromActiveBin(poolAddress);
            if (priceData && priceData.fresh) {
                prices[poolAddress] = priceData;
            }
        }

        return prices;
    }

    /**
     * 🚀 БЫСТРОЕ ПОЛУЧЕНИЕ ЦЕНЫ ИЗ КЭША (БЕЗ RPC!)
     */
    getPrice(poolAddress) {
        const cached = this.activeBinsCache.get(poolAddress);
        return cached ? cached.activeBinPrice : null;
    }

    /**
     * 🚀 БЫСТРОЕ ПОЛУЧЕНИЕ РЕЗЕРВОВ ИЗ КЭША (БЕЗ RPC!)
     */
    getReserves(poolAddress) {
        const dlmm = this.dlmmInstancesCache.get(poolAddress);
        if (!dlmm) return null;

        return {
            reserveX: dlmm.lbPair.reserveX,
            reserveY: dlmm.lbPair.reserveY
        };
    }

    /**
     * 🚀 БЫСТРОЕ ПОЛУЧЕНИЕ ТОКЕНОВ ИЗ КЭША (БЕЗ RPC!)
     */
    getTokens(poolAddress) {
        const dlmm = this.dlmmInstancesCache.get(poolAddress);
        if (!dlmm) return null;

        return {
            tokenX: dlmm.tokenX.publicKey,
            tokenY: dlmm.tokenY.publicKey
        };
    }

    /**
     * 🚀 БЫСТРОЕ ПОЛУЧЕНИЕ ВСЕХ ДАННЫХ ИЗ КЭША (БЕЗ RPC!)
     */
    getAllPoolData(poolAddress) {
        const cached = this.activeBinsCache.get(poolAddress);
        const dlmm = this.dlmmInstancesCache.get(poolAddress);

        // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА КЭША!
        const poolStr = typeof poolAddress === 'string' ? poolAddress : poolAddress.toString();
        console.log(`🔍 ДИАГНОСТИКА КЭША для ${poolStr.slice(0, 8)}:`);
        console.log(`   📊 Активные бины в кэше: ${cached ? '✅' : '❌'}`);
        console.log(`   🔧 DLMM инстанс в кэше: ${dlmm ? '✅' : '❌'}`);

        if (cached) {
            console.log(`   📈 Данные активного бина: ID=${cached.activeBinId}, цена=${cached.activeBinPrice}`);
        }

        if (!cached || !dlmm) {
            console.log(`❌ ОТСУТСТВУЮТ ДАННЫЕ: cached=${!!cached}, dlmm=${!!dlmm}`);
            return null;
        }

        return {
            activeBinId: cached.activeBinId,
            activeBinPrice: cached.activeBinPrice,
            binStep: dlmm.lbPair.binStep,
            reserveX: dlmm.lbPair.reserveX,
            reserveY: dlmm.lbPair.reserveY,
            tokenX: dlmm.tokenX.publicKey,
            tokenY: dlmm.tokenY.publicKey,
            timestamp: cached.timestamp,
            age: Date.now() - cached.timestamp
        };
    }

    /**
     * 🧹 ОЧИСТКА УСТАРЕВШИХ АКТИВНЫХ БИНОВ
     */
    cleanExpiredCache() {
        const now = Date.now();
        let cleaned = 0;

        for (const [poolAddress, cache] of this.activeBinsCache.entries()) {
            if (now - cache.timestamp > this.ACTIVE_BIN_CACHE_DURATION) {
                this.activeBinsCache.delete(poolAddress);
                cleaned++;
            }
        }

        if (cleaned > 0) {
            console.log(`🧹 Очищено ${cleaned} устаревших активных бинов (>${this.ACTIVE_BIN_CACHE_DURATION}мс)`);
        }
    }
    // 🔄 ОБРАТНАЯ СОВМЕСТИМОСТЬ: Батчевая загрузка теперь загружает активные бины
    async loadAllBinArraysBatch(poolAddresses) {
        console.log(`🚀 Загрузка активных бинов для ${poolAddresses.length} пулов...`);

        try {
            // Загружаем активные бины для всех пулов параллельно
            const loadPromises = poolAddresses.map(poolAddress =>
                this.loadThreeBinsForPool(poolAddress)
            );

            const results = await Promise.allSettled(loadPromises);

            // Подсчитываем результаты
            const successful = results.filter(r => r.status === 'fulfilled' && r.value !== null).length;
            const failed = results.filter(r => r.status === 'rejected' || r.value === null).length;

            console.log(`✅ Загрузка активных бинов завершена: ${successful} успешно, ${failed} ошибок`);

            return {
                successful,
                failed,
                total: poolAddresses.length
            };

        } catch (error) {
            console.error(`❌ Ошибка загрузки активных бинов:`, error.message);
            return { successful: 0, failed: poolAddresses.length, total: poolAddresses.length };
        }
    }

    // 🚫 ФУНКЦИЯ loadSinglePoolBinArrays УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО АКТИВНЫЕ БИНЫ!

    // 🚫 ФУНКЦИЯ getBinArraysWithCache УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО getOptimizedBinArraysForSwap!

    /**
     * 🏗️ ПОЛУЧЕНИЕ DLMM INSTANCE С КЭШИРОВАНИЕМ
     */
    async getDLMMInstance(poolAddress) {
        if (this.dlmmInstancesCache.has(poolAddress)) {
            return this.dlmmInstancesCache.get(poolAddress);
        }

        
        const dlmmInstance = await DLMM.create(this.dataRPC, new PublicKey(poolAddress));
        
        this.dlmmInstancesCache.set(poolAddress, dlmmInstance);
        return dlmmInstance;
    }

    /**
     * 📊 УПРОЩЕННЫЙ АНАЛИЗ ЛИКВИДНОСТИ
     */
    async analyzeLiquidity(dlmmInstance, binArrays) {
        try {
            // Простой расчет на основе количества bin arrays
            const binCount = binArrays.length;

            // 🔥 УЛУЧШЕННАЯ ФОРМУЛА: 100 bin arrays ≈ больше ликвидности чем 50
            let estimatedLiquidityUSD = binCount * 5000; // $5K на bin array (консервативно)

            // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ОГРАНИЧИВАЕМ МАКСИМАЛЬНУЮ ЛИКВИДНОСТЬ
            if (estimatedLiquidityUSD > 1000000) { // Больше $1M
                console.log(`🚨 ПРЕДУПРЕЖДЕНИЕ: Оценочная ликвидность слишком большая! $${estimatedLiquidityUSD.toLocaleString()}`);
                estimatedLiquidityUSD = 1000000; // Ограничиваем до $1M
                console.log(`🚨 Ограничено до $${estimatedLiquidityUSD.toLocaleString()}`);
            }

            

            return {
                totalLiquidityX: 0,
                totalLiquidityY: 0,
                solLiquidityUSD: estimatedLiquidityUSD / 2,
                usdcLiquidityUSD: estimatedLiquidityUSD / 2,
                maxLiquidityUSD: estimatedLiquidityUSD,
                activeBinPrice: 165, // Примерная цена SOL
                activeBinId: 0,
                binStep: 0,
                binCount: binCount
            };

        } catch (error) {
            console.error('❌ Ошибка анализа ликвидности:', error.message);

            // Возвращаем консервативные данные
            return {
                totalLiquidityX: 0,
                totalLiquidityY: 0,
                solLiquidityUSD: 0,
                usdcLiquidityUSD: 0,
                maxLiquidityUSD: 100000, // $100K по умолчанию
                activeBinPrice: 165,
                activeBinId: 0,
                binStep: 0,
                binCount: binArrays.length || 4
            };
        }
    }

    /**
     * 🔥 ПРОСТОЙ РАСЧЕТ РАЗМЕРА ПОЗИЦИИ БЕЗ АНАЛИЗА БИНОВ
     * Анализатор должен только находить спреды и запускать арбитраж!
     */
    calculateMaxPositionSize(poolAddress, swapYtoX = false) {
        // 🔥 ОТКЛЮЧАЕМ СЛОЖНЫЙ АНАЛИЗ БИНОВ - ПРОСТО ВОЗВРАЩАЕМ ФИКСИРОВАННУЮ СУММУ
        const fixedSize = 18000; // $18K фиксированная сумма для всех пулов

        // 🔥 НЕ ВЫВОДИМ ЛИШНИЕ ЛОГИ О РАЗМЕРЕ ПОЗИЦИИ
        // console.log(`💰 Размер позиции для ${poolAddress.slice(0, 8)}: $${fixedSize.toLocaleString()}`);

        return fixedSize;
    }

    /**
     * 🎯 ГЛАВНЫЙ МЕТОД: РАСЧЕТ БЕЗОПАСНОГО РАЗМЕРА ДЛЯ АРБИТРАЖА МЕЖДУ ДВУМЯ ПУЛАМИ
     */
    calculateSafeArbitrageSize(sellPoolAddress, buyPoolAddress) {
        // 🔥 ОТКЛЮЧАЕМ ЛИШНИЕ ЛОГИ - АНАЛИЗАТОР ДОЛЖЕН БЫТЬ ТИХИМ
        // console.log(`   Sell Pool: ${sellPoolAddress}`);
        // console.log(`   Buy Pool: ${buyPoolAddress}`);

        // Получаем максимальные размеры для каждого пула (фиксированные)
        const sellPoolMaxSize = this.calculateMaxPositionSize(sellPoolAddress, false); // WSOL → USDC
        const buyPoolMaxSize = this.calculateMaxPositionSize(buyPoolAddress, true);   // USDC → WSOL

        // Выбираем МИНИМАЛЬНЫЙ размер для безопасности
        const safeSize = Math.min(sellPoolMaxSize, buyPoolMaxSize);

        // 🔥 НЕ ВЫВОДИМ ЛИШНИЕ ЛОГИ О РАЗМЕРАХ ПУЛОВ
        // console.log(`   Sell Pool (${sellPoolAddress}): $${sellPoolMaxSize.toLocaleString()}`);
        // console.log(`   Buy Pool (${buyPoolAddress}): $${buyPoolMaxSize.toLocaleString()}`);

        return safeSize;
    }

    /**
     * 🎯 УПРОЩЕННАЯ ВАЛИДАЦИЯ ПОЗИЦИИ БЕЗ RPC ЗАПРОСОВ!
     * 🔥 ВОЗВРАЩАЕТ ФИКСИРОВАННУЮ ПОЗИЦИЮ $1,000,000 USDC
     */
    async validatePositionWithSwapQuote(sellPoolAddress, buyPoolAddress, theoreticalSize, meteoraSDK, feeAnalyzer = null) {
        try {
            console.log(`🔍 УПРОЩЕННАЯ ВАЛИДАЦИЯ ПОЗИЦИИ БЕЗ RPC ЗАПРОСОВ...`);
            console.log(`   Теоретический размер: $${theoreticalSize.toLocaleString()}`);

            // 🔥 ВОЗВРАЩАЕМ ФИКСИРОВАННУЮ ПОЗИЦИЮ $1,000,000 USDC
            const fixedPosition = 1000000;
            console.log(`✅ ИСПОЛЬЗУЕМ ФИКСИРОВАННУЮ ПОЗИЦИЮ: $${fixedPosition.toLocaleString()}`);

            return fixedPosition;
        } catch (error) {
            console.log(`❌ Ошибка валидации позиции: ${error.message}`);
            return 1000000; // Возвращаем фиксированную позицию при ошибке
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ АКТИВНОГО БИНА ДЛЯ SWAP
     */
    async getOptimizedBinArraysForSwap(poolAddress, swapYtoX = false) {
        try {
            // Получаем активный бин из кэша
            let cacheData = this.activeBinsCache.get(poolAddress);

            // Если нет в кэше или устарел, обновляем
            if (!cacheData || (Date.now() - cacheData.timestamp) > this.ACTIVE_BIN_CACHE_DURATION) {
                cacheData = await this.updateActiveBin(poolAddress);
            }

            if (!cacheData) {
                throw new Error(`Не удалось получить активный бин для пула ${poolAddress}`);
            }

            // Проверяем наличие bin arrays
            let binArraysPubkey = [];
            if (cacheData.binArrays && cacheData.binArrays.length > 0) {
                // Извлекаем PublicKey из bin arrays
                binArraysPubkey = cacheData.binArrays.map(binArray => {
                    if (binArray && binArray.publicKey) {
                        return binArray.publicKey;
                    } else if (binArray && typeof binArray === 'object' && binArray.toString) {
                        return binArray; // Уже PublicKey
                    }
                    return null;
                }).filter(pk => pk !== null);
            }

            console.log(`✅ Кэш-менеджер: найдено ${binArraysPubkey.length} bin arrays для пула ${poolAddress.slice(0, 8)}...`);

            // Возвращаем активный бин в формате, ожидаемом Meteora SDK
            return {
                binArraysPubkey: binArraysPubkey,
                activeBinId: cacheData.activeBinId,
                activeBinPrice: cacheData.activeBinPrice,
                activeBinLiquidity: cacheData.activeBinLiquidity,
                cached: true,
                timestamp: cacheData.timestamp
            };

        } catch (error) {
            console.error(`❌ Ошибка получения оптимизированных bin arrays: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔄 ПРЕДВАРИТЕЛЬНАЯ ЗАГРУЗКА АКТИВНЫХ БИНОВ ВСЕХ ПУЛОВ
     */
    async preloadAllPools(poolAddresses) {
        console.log(`🔄 Предварительная загрузка активных бинов для ${poolAddresses.length} пулов...`);

        const promises = poolAddresses.map(poolAddress =>
            this.loadThreeBinsForPool(poolAddress)
        );

        try {
            const results = await Promise.allSettled(promises);
            const successful = results.filter(r => r.status === 'fulfilled' && r.value !== null).length;
            console.log(`✅ Предварительная загрузка завершена: ${successful}/${poolAddresses.length} пулов`);
        } catch (error) {
            console.error(`❌ Ошибка предварительной загрузки:`, error.message);
        }
    }

    /**
     * 🔥 ЗАПУСК АВТООБНОВЛЕНИЯ КЭША
     * Обновляет активные бины каждые 500ms для всегда свежих данных
     */
    startAutoUpdate(poolAddresses) {
        this.poolsToWatch = poolAddresses;

        if (this.autoUpdateInterval) {
            clearInterval(this.autoUpdateInterval);
        }

        console.log(`🚀 Запуск автообновления кэша для ${poolAddresses.length} пулов каждые 500ms`);

        this.autoUpdateInterval = setInterval(async () => {
            try {
                await this.batchUpdateAllActiveBins(this.poolsToWatch);
            } catch (error) {
                // Не спамим ошибками каждые 500ms
                if (Date.now() % 5000 < 500) {
                    console.error('❌ Ошибка автообновления кэша:', error.message);
                }
            }
        }, 500); // 🔥 КАЖДЫЕ 500ms СВЕЖИЕ ДАННЫЕ!
    }

    /**
     * 🛑 ОСТАНОВКА АВТООБНОВЛЕНИЯ
     */
    stopAutoUpdate() {
        if (this.autoUpdateInterval) {
            clearInterval(this.autoUpdateInterval);
            this.autoUpdateInterval = null;
            console.log('🛑 Автообновление кэша остановлено');
        }
    }

    // 🚫 ФУНКЦИЯ getCacheStats УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО АКТИВНЫЕ БИНЫ!

    /**
     * 📈 ПОЛУЧЕНИЕ ТЕКУЩИХ РАЗМЕРОВ ПОЗИЦИЙ ДЛЯ ВСЕХ ПУЛОВ
     */
    getAllPoolsPositionSizes() {
        const poolAddresses = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'  // Pool 3
        ];

        const sizes = {};
        for (const poolAddress of poolAddresses) {
            sizes[poolAddress] = {
                sellSize: this.calculateMaxPositionSize(poolAddress, false), // WSOL → USDC
                buySize: this.calculateMaxPositionSize(poolAddress, true)    // USDC → WSOL
            };
        }

        return sizes;
    }

    /**
     * 🧹 ОЧИСТКА УСТАРЕВШЕГО КЭША
     */
    // 🚫 ФУНКЦИЯ cleanExpiredCache УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО АКТИВНЫЕ БИНЫ!

    /**
     * 🧪 ТЕСТ СИСТЕМЫ КЭШИРОВАНИЯ
     */
    async testCacheSystem() {
        

        try {
            const testPools = [
                '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
            ];

            // Тест загрузки
            await this.preloadAllPools(testPools);

            // Тест расчета размера
            const safeSize = this.calculateSafeArbitrageSize(testPools[0], testPools[1]);

            
            return true;

        } catch (error) {
            console.error('❌ Тест кэша провален:', error.message);
            return false;
        }
    }
    /**
     * 🌊 ORCA: ПОЛУЧЕНИЕ TICK ARRAYS С КЭШИРОВАНИЕМ
     */
    async getOrcaTickArraysWithCache(poolAddress, whirlpoolSDK) {
        const cacheKey = poolAddress;
        const cached = this.caches.orca.tickArrays.get(cacheKey);

        // ✅ ПРОВЕРЯЕМ КЭША
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            console.log(`🌊 Orca tick arrays из кэша: ${poolAddress.slice(0, 8)}...`);
            return cached;
        }

        // 🔄 ЗАГРУЖАЕМ НОВЫЕ TICK ARRAYS
        try {
            console.log(`🌊 Загружаем Orca tick arrays: ${poolAddress.slice(0, 8)}...`);

            // Получаем whirlpool instance
            const whirlpool = await whirlpoolSDK.getPool(new PublicKey(poolAddress));

            if (!whirlpool) {
                throw new Error('Whirlpool не найден');
            }

            // Загружаем tick arrays (примерная логика)
            const tickArrays = await whirlpool.getTickArrays();

            const cacheData = {
                tickArrays: tickArrays,
                timestamp: Date.now(),
                poolAddress: poolAddress,
                dex: 'orca'
            };

            // 💾 СОХРАНЯЕМ В КЭШ
            this.caches.orca.tickArrays.set(cacheKey, cacheData);
            this.caches.orca.whirlpoolInstances.set(poolAddress, whirlpool);

            console.log(`✅ Orca tick arrays загружены и закэшированы: ${poolAddress.slice(0, 8)}...`);
            return cacheData;

        } catch (error) {
            console.log(`❌ Ошибка загрузки Orca tick arrays: ${error.message}`);
            return null;
        }
    }

    /**
     * ⚡ RAYDIUM: ПОЛУЧЕНИЕ TICK ARRAYS С КЭШИРОВАНИЕМ
     */
    async getRaydiumTickArraysWithCache(poolAddress, raydiumSDK) {
        const cacheKey = poolAddress;
        const cached = this.caches.raydium.tickArrays.get(cacheKey);

        // ✅ ПРОВЕРЯЕМ КЭША
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            console.log(`⚡ Raydium tick arrays из кэша: ${poolAddress.slice(0, 8)}...`);
            return cached;
        }

        // 🔄 ЗАГРУЖАЕМ НОВЫЕ TICK ARRAYS
        try {
            console.log(`⚡ Загружаем Raydium tick arrays: ${poolAddress.slice(0, 8)}...`);

            // Получаем pool instance через Raydium SDK
            const poolInfo = await raydiumSDK.api.fetchPoolById({ ids: [poolAddress] });

            if (!poolInfo || poolInfo.length === 0) {
                throw new Error('Raydium pool не найден');
            }

            const pool = poolInfo[0];

            // Загружаем tick arrays (примерная логика)
            const tickArrays = await pool.getTickArrays?.() || [];

            const cacheData = {
                tickArrays: tickArrays,
                timestamp: Date.now(),
                poolAddress: poolAddress,
                dex: 'raydium',
                poolInfo: pool
            };

            // 💾 СОХРАНЯЕМ В КЭШ
            this.caches.raydium.tickArrays.set(cacheKey, cacheData);
            this.caches.raydium.poolInstances.set(poolAddress, pool);

            console.log(`✅ Raydium tick arrays загружены и закэшированы: ${poolAddress.slice(0, 8)}...`);
            return cacheData;

        } catch (error) {
            console.log(`❌ Ошибка загрузки Raydium tick arrays: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ ДАННЫХ ПУЛА ДЛЯ LOW-LEVEL MARGINFI INTEGRATION
     * Возвращает данные пула включая reserve_x и reserve_y
     */
    async getPoolData(poolAddress) {
        try {
            console.log(`🔍 ПОЛУЧЕНИЕ ДАННЫХ ПУЛА: ${poolAddress.toString()}`);

            // 1. Получаем DLMM инстанс
            const dlmmInstance = await this.getDLMMInstance(poolAddress);
            if (!dlmmInstance) {
                throw new Error(`DLMM инстанс не найден для пула ${poolAddress.toString()}`);
            }

            // 2. Получаем данные пула из DLMM
            const lbPair = dlmmInstance.lbPair;
            if (!lbPair) {
                throw new Error(`LB Pair не найден для пула ${poolAddress.toString()}`);
            }

            // 3. Извлекаем ВСЕ АДРЕСА из LB Pair
            const reserveX = lbPair.reserveX;
            const reserveY = lbPair.reserveY;
            const tokenXMint = lbPair.tokenXMint;
            const tokenYMint = lbPair.tokenYMint;
            const oracle = lbPair.oracle;

            if (!reserveX || !reserveY) {
                throw new Error(`Резервы не найдены в LB Pair для пула ${poolAddress.toString()}`);
            }

            console.log(`✅ ПОЛНЫЕ ДАННЫЕ ПУЛА ПОЛУЧЕНЫ:`);
            console.log(`   Reserve X: ${reserveX.toString()}`);
            console.log(`   Reserve Y: ${reserveY.toString()}`);
            console.log(`   Token X Mint: ${tokenXMint?.toString() || 'N/A'}`);
            console.log(`   Token Y Mint: ${tokenYMint?.toString() || 'N/A'}`);
            console.log(`   Oracle: ${oracle?.toString() || 'N/A'}`);

            return {
                reserve_x: reserveX,
                reserveX: reserveX,
                reserve_y: reserveY,
                reserveY: reserveY,
                tokenXMint: tokenXMint,
                tokenYMint: tokenYMint,
                oracle: oracle,
                lbPair: lbPair,
                dlmmInstance: dlmmInstance
            };

        } catch (error) {
            console.error(`❌ Ошибка получения данных пула ${poolAddress.toString()}: ${error.message}`);
            throw error;
        }
    }

    /**
     * 📊 УНИВЕРСАЛЬНЫЙ МЕТОД ПОЛУЧЕНИЯ ДАННЫХ ЛЮБОГО DEX
     */
    async getPoolDataWithCache(dexName, poolAddress, sdkInstance) {
        switch (dexName.toLowerCase()) {
            case 'meteora':
                return await this.getBinArraysWithCache(poolAddress);
            case 'orca':
                return await this.getOrcaTickArraysWithCache(poolAddress, sdkInstance);
            case 'raydium':
                return await this.getRaydiumTickArraysWithCache(poolAddress, sdkInstance);
            default:
                throw new Error(`Неподдерживаемый DEX: ${dexName}`);
        }
    }

    /**
     * 📊 СТАТИСТИКА ВСЕХ КЭШЕЙ
     */
    getAllCacheStats() {
        return {
            meteora: {
                binArrays: this.caches.meteora.binArrays.size,
                dlmmInstances: this.caches.meteora.dlmmInstances.size
            },
            orca: {
                tickArrays: this.caches.orca.tickArrays.size,
                whirlpoolInstances: this.caches.orca.whirlpoolInstances.size
            },
            raydium: {
                tickArrays: this.caches.raydium.tickArrays.size,
                poolInstances: this.caches.raydium.poolInstances.size
            },
            total: this.caches.meteora.binArrays.size +
                   this.caches.orca.tickArrays.size +
                   this.caches.raydium.tickArrays.size
        };
    }

    /**
     * 🚀 БАТЧЕВОЕ ОБНОВЛЕНИЕ ВСЕХ КЭШЕЙ
     */
    async batchUpdateAllCaches(meteoraPools = [], orcaPools = [], raydiumPools = [], sdkInstances = {}) {
        console.log('🚀 Батчевое обновление всех кэшей...');

        const results = {
            meteora: { successful: 0, failed: 0, total: 0 },
            orca: { successful: 0, failed: 0, total: 0 },
            raydium: { successful: 0, failed: 0, total: 0 }
        };

        // 1. Meteora - батчевая загрузка
        if (meteoraPools.length > 0) {
            console.log(`🌪️ Обновляем Meteora кэш для ${meteoraPools.length} пулов...`);
            results.meteora = await this.loadAllBinArraysBatch(meteoraPools);
        }

        // 2. Orca - параллельная загрузка
        if (orcaPools.length > 0 && sdkInstances.orca) {
            console.log(`🌊 Обновляем Orca кэш для ${orcaPools.length} пулов...`);
            const orcaPromises = orcaPools.map(poolAddress =>
                this.getOrcaTickArraysWithCache(poolAddress, sdkInstances.orca)
            );

            const orcaResults = await Promise.allSettled(orcaPromises);
            results.orca.total = orcaResults.length;
            results.orca.successful = orcaResults.filter(r => r.status === 'fulfilled').length;
            results.orca.failed = orcaResults.filter(r => r.status === 'rejected').length;
        }

        // 3. Raydium - параллельная загрузка
        if (raydiumPools.length > 0 && sdkInstances.raydium) {
            console.log(`⚡ Обновляем Raydium кэш для ${raydiumPools.length} пулов...`);
            const raydiumPromises = raydiumPools.map(poolAddress =>
                this.getRaydiumTickArraysWithCache(poolAddress, sdkInstances.raydium)
            );

            const raydiumResults = await Promise.allSettled(raydiumPromises);
            results.raydium.total = raydiumResults.length;
            results.raydium.successful = raydiumResults.filter(r => r.status === 'fulfilled').length;
            results.raydium.failed = raydiumResults.filter(r => r.status === 'rejected').length;
        }

        // 4. Выводим итоговую статистику
        const totalSuccessful = results.meteora.successful + results.orca.successful + results.raydium.successful;
        const totalFailed = results.meteora.failed + results.orca.failed + results.raydium.failed;
        const totalOperations = results.meteora.total + results.orca.total + results.raydium.total;

        console.log('📊 Результаты батчевого обновления:');
        console.log(`   🌪️ Meteora: ${results.meteora.successful}/${results.meteora.total} успешно`);
        console.log(`   🌊 Orca: ${results.orca.successful}/${results.orca.total} успешно`);
        console.log(`   ⚡ Raydium: ${results.raydium.successful}/${results.raydium.total} успешно`);
        console.log(`   🎯 Итого: ${totalSuccessful}/${totalOperations} операций успешно`);

        return results;
    }

    /**
     * 🧹 ОЧИСТКА ВСЕХ КЭШЕЙ
     */
    clearAllCaches() {
        this.activeBinsCache.clear();
        this.dlmmInstancesCache.clear();

        console.log('🧹 Все кэши активных бинов очищены');
    }
}

module.exports = MeteoraBinCacheManager;
