const { Connection, PublicKey } = require('@solana/web3.js');
const crypto = require('crypto');

async function findLendingAccountInitDiscriminator() {
    console.log('🔍 ПОИСК ПРАВИЛЬНОГО DISCRIMINATOR ДЛЯ LENDING_ACCOUNT_INIT...\n');
    
    // Anchor discriminator вычисляется как первые 8 байт SHA256 хэша от "global:instruction_name"
    
    const possibleNames = [
        'lending_account_init',
        'init_lending_account', 
        'initialize_lending_account',
        'lending_account_initialize',
        'account_init',
        'init_account'
    ];
    
    console.log('🔧 ВЫЧИСЛЯЕМ ANCHOR DISCRIMINATORS:');
    
    for (const name of possibleNames) {
        const input = `global:${name}`;
        const hash = crypto.createHash('sha256').update(input).digest();
        const discriminator = hash.slice(0, 8);
        
        console.log(`   ${name}:`);
        console.log(`      Input: "${input}"`);
        console.log(`      Discriminator: [${Array.from(discriminator).join(', ')}]`);
        console.log(`      Hex: ${discriminator.toString('hex')}`);
        console.log('');
    }
    
    // Также проверим известные discriminators из других MarginFi инструкций
    console.log('🔧 ИЗВЕСТНЫЕ MARGINFI DISCRIMINATORS:');
    
    const knownInstructions = {
        'lending_account_borrow': [4, 126, 116, 53, 48, 5, 212, 31],
        'lending_account_repay': [234, 103, 67, 82, 208, 234, 219, 166]
    };
    
    for (const [name, discriminator] of Object.entries(knownInstructions)) {
        console.log(`   ${name}: [${discriminator.join(', ')}]`);
        console.log(`      Hex: ${Buffer.from(discriminator).toString('hex')}`);
    }
    
    console.log('\n🎯 РЕКОМЕНДАЦИЯ:');
    console.log('Попробуйте discriminator для "lending_account_init":');
    
    const recommendedInput = 'global:lending_account_init';
    const recommendedHash = crypto.createHash('sha256').update(recommendedInput).digest();
    const recommendedDiscriminator = recommendedHash.slice(0, 8);
    
    console.log(`   [${Array.from(recommendedDiscriminator).join(', ')}]`);
    console.log(`   Hex: ${recommendedDiscriminator.toString('hex')}`);
    
    console.log('\n🎉 ПОИСК DISCRIMINATOR ЗАВЕРШЕН!');
}

findLendingAccountInitDiscriminator().catch(console.error);
