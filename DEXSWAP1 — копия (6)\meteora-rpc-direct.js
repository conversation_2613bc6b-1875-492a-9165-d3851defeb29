#!/usr/bin/env node

/**
 * 🌪️ METEORA DIRECT SDK CONNECTION
 * Прямое подключение к Meteora DLMM через официальный SDK
 * Вместо 107MB API - получаем только наши 2 пула через SDK!
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const colors = require('colors');

// 🎯 METEORA DLMM PROGRAM ID
const METEORA_DLMM_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

// 🎯 ВСЕ 3 METEORA SOL/USDC ПУЛА ДЛЯ ВНУТРЕННЕГО АРБИТРАЖА!
// 📋 ПОРЯДОК КАК НА САЙТЕ METEORA (СВЕРХУ ВНИЗ):
const METEORA_TARGET_POOLS = [
    {
        address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // ПУЛ 1 (ВЕРХНИЙ): $157.70 (TVL: $3.2M)
        pair: 'SOL/USDC',
        name: 'Meteora SOL/USDC DLMM Pool 1 (TOP)',
        tvl: 3221330,
        currentPrice: 157.70
    },
    {
        address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // ПУЛ 2 (СРЕДНИЙ): $157.74 (TVL: $6.7M) - САМЫЙ ДОРОГОЙ!
        pair: 'SOL/USDC',
        name: 'Meteora SOL/USDC DLMM Pool 2 (MIDDLE)',
        tvl: 6706023,
        currentPrice: 157.74
    },
    {
        address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR', // ПУЛ 3 (НИЖНИЙ): $157.65 (TVL: $838K) - САМЫЙ ДЕШЕВЫЙ!
        pair: 'SOL/USDC',
        name: 'Meteora SOL/USDC DLMM Pool 3 (BOTTOM)',
        tvl: 838626,
        currentPrice: 157.65
    }
];

class MeteoraDirectSDK {
    constructor() {
        // 🔗 RPC подключения с fallback на бесплатные
        this.connections = [];

        // Добавляем платные RPC если доступны
        if (process.env.HELIUS_RPC_URL) {
            this.connections.push(new Connection(process.env.HELIUS_RPC_URL, 'confirmed'));
        }
        if (process.env.QUICKNODE_RPC_URL) {
            this.connections.push(new Connection(process.env.QUICKNODE_RPC_URL, 'confirmed'));
        }

        // Добавляем бесплатные RPC как fallback
        this.connections.push(new Connection('https://api.mainnet-beta.solana.com', 'confirmed'));
        this.connections.push(new Connection('https://rpc.ankr.com/solana', 'confirmed'));

        this.currentConnectionIndex = 0;

        this.pools = METEORA_TARGET_POOLS;
        this.dlmmInstances = new Map(); // Кэш DLMM инстансов
        this.cache = new Map();
        this.lastUpdate = null;
        this.cacheTimeout = 1500; // 🔧 ИСПРАВЛЕНО: 1.5 секунды кэш - соответствует интервалам DEX

        console.log(`🌪️ Meteora Direct SDK инициализирован (ОФИЦИАЛЬНЫЙ SDK)`.cyan);
        console.log(`🎯 Целевые пулы: ${this.pools.length} (${this.pools.map(p => p.pair).join(', ')})`);
        console.log(`🔗 RPC подключения: ${this.connections.length}`);
    }

    /**
     * 🔄 ПОЛУЧЕНИЕ ТЕКУЩЕГО RPC ПОДКЛЮЧЕНИЯ
     */
    getCurrentConnection() {
        return this.connections[this.currentConnectionIndex];
    }

    /**
     * 🔄 ПЕРЕКЛЮЧЕНИЕ НА СЛЕДУЮЩЕЕ RPC ПОДКЛЮЧЕНИЕ
     */
    switchConnection() {
        this.currentConnectionIndex = (this.currentConnectionIndex + 1) % this.connections.length;
        console.log(`🔄 Переключение на RPC ${this.currentConnectionIndex + 1}/${this.connections.length}`);
    }

    /**
     * 📊 ПОЛУЧЕНИЕ DLMM ИНСТАНСА ДЛЯ ПУЛА
     */
    async getDLMMInstance(poolAddress) {
        try {
            // Проверяем кэш инстансов
            if (this.dlmmInstances.has(poolAddress)) {
                return this.dlmmInstances.get(poolAddress);
            }

            const connection = this.getCurrentConnection();
            const poolPubkey = new PublicKey(poolAddress);

            console.log(`🔄 Создание DLMM инстанса для ${poolAddress}...`);

            // Создаем DLMM инстанс через официальный SDK
            const dlmmPool = await DLMM.create(connection, poolPubkey);

            // Кэшируем инстанс
            this.dlmmInstances.set(poolAddress, dlmmPool);

            console.log(`✅ DLMM инстанс создан для ${poolAddress}`);
            return dlmmPool;

        } catch (error) {
            console.error(`❌ Ошибка создания DLMM инстанса ${poolAddress}:`, error.message);

            // Пробуем другое RPC подключение
            this.switchConnection();
            throw error;
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ЦЕН ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async getLivePrices() {
        try {
            // Проверяем кэш
            if (this.isCacheValid()) {
                console.log('📦 Meteora SDK: используем кэшированные данные');
                return Array.from(this.cache.values());
            }

            console.log(`🌪️ Meteora: начинаем получение цен для ${this.pools.length} пулов...`);
            const startTime = Date.now();

            const prices = [];

            // 🚀 ИСПРАВЛЕНО: ПАРАЛЛЕЛЬНЫЕ ЗАПРОСЫ ВМЕСТО ПОСЛЕДОВАТЕЛЬНЫХ!
            const poolPromises = this.pools.map(async (pool) => {
                try {
                    console.log(`🌪️ Meteora: обрабатываем пул ${pool.pair} (${pool.address.slice(0,8)}...)`);

                    // 🔥 УБИРАЕМ ДИАГНОСТИКУ RPC - УСКОРЯЕМ РАБОТУ!
                    const connection = this.getCurrentConnection();
                    console.log(`🔗 Meteora: используем RPC ${connection.rpcEndpoint}`);

                    // 🚀 УБИРАЕМ ТЕСТИРОВАНИЕ RPC - ЭКОНОМИМ ВРЕМЯ!
                    // Сразу получаем DLMM инстанс
                    const dlmmPool = await this.getDLMMInstance(pool.address);
                    console.log(`✅ Meteora ${pool.pair}: DLMM инстанс получен`);

                    // 🚀 КЭШИРОВАННОЕ ОБНОВЛЕНИЕ СОСТОЯНИЯ (РАЗ В МИНУТУ)
                    const lastRefetch = this.lastRefetchTime?.get(pool.address) || 0;
                    const now = Date.now();
                    if (now - lastRefetch > 60000) { // 60 секунд
                        console.log(`🔄 Обновляем состояние пула ${pool.address.slice(0,8)}... (кэш устарел)`);
                        await dlmmPool.refetchStates();
                        if (!this.lastRefetchTime) this.lastRefetchTime = new Map();
                        this.lastRefetchTime.set(pool.address, now);
                    } else {
                        console.log(`📦 Используем кэшированное состояние пула ${pool.address.slice(0,8)}... (кэш свежий)`);
                    }

                    // Получаем активный bin (текущую цену)
                    const activeBin = await dlmmPool.getActiveBin();

                    // 🎯 ТОЛЬКО 2 ПРОВЕРЕННЫЕ SDK ФОРМУЛЫ!
                    const sdkPrice = parseFloat(dlmmPool.fromPricePerLamport(Number(activeBin.price)));
                    let realPrice, formulaUsed;

                    if (pool.address === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6') {
                        // 🔥 ПУЛ 1: КОРРЕКЦИЯ ПО БРАУЗЕРНОЙ ЦЕНЕ
                        realPrice = sdkPrice * 0.999151; // Коэффициент: 166.811478 / 166.9532
                        formulaUsed = 'fromPricePerLamport * 0.999151 (браузерная коррекция)';

                        // Проверяем разумность цены
                        if (realPrice < 100 || realPrice > 300) {
                            console.log(`⚠️ Подозрительная цена SOL/USDC: $${realPrice.toFixed(2)}`);
                        }

                    } else if (pool.address === '5LAzU2jn92pJpUbBUSurT7i4GgSPkrrWbqfchUNU8fyB') {
                        // 🔥 SOL/USDT: ИСПРАВЛЕНА ФОРМУЛА! (Реальная цена: $152.02)
                        // 🎯 ПРОБЛЕМА БЫЛА: мы получали $164.47 вместо $152.02
                        // 🎯 РЕШЕНИЕ: Всегда используем прямую формулу без инверсии
                        realPrice = sdkPrice;
                        formulaUsed = 'fromPricePerLamport (ИСПРАВЛЕНО для SOL/USDT)';

                        // 🔍 ДИАГНОСТИКА: Проверяем что цена в разумных пределах
                        if (realPrice > 200 || realPrice < 100) {
                            console.log(`⚠️ SOL/USDT подозрительная цена: $${realPrice.toFixed(2)} (ожидается ~$152)`);
                        }

                    } else if (pool.address === 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y') {
                        // 🔥 ПУЛ 2: КОРРЕКЦИЯ ПО БРАУЗЕРНОЙ ЦЕНЕ
                        realPrice = sdkPrice * 0.998996; // Коэффициент: 166.774970 / 166.9426
                        formulaUsed = 'fromPricePerLamport * 0.998996 (браузерная коррекция)';

                        // Проверяем разумность цены
                        if (realPrice < 100 || realPrice > 300) {
                            console.log(`⚠️ Подозрительная цена SOL/USDC: $${realPrice.toFixed(2)}`);
                        }

                    } else if (pool.address === 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR') {
                        // 🔥 ПУЛ 3: КОРРЕКЦИЯ ПО БРАУЗЕРНОЙ ЦЕНЕ
                        realPrice = sdkPrice * 1.000209; // Коэффициент: 166.890091 / 166.9250
                        formulaUsed = 'fromPricePerLamport * 1.000209 (браузерная коррекция)';

                        // Проверяем разумность цены
                        if (realPrice < 100 || realPrice > 300) {
                            console.log(`⚠️ Подозрительная цена SOL/USDC: $${realPrice.toFixed(2)}`);
                        }

                    } else {
                        // Неизвестный пул - используем стандартный метод
                        realPrice = sdkPrice;
                        formulaUsed = 'fromPricePerLamport';
                    }

                    const priceData = {
                        address: pool.address,
                        pair: pool.pair,
                        name: pool.name,
                        price: realPrice,
                        priceDisplay: `$${realPrice.toFixed(2)}`,
                        volume24h: 0,
                        liquidity: 0,
                        fee: dlmmPool.lbPair.baseFeePercentage ? dlmmPool.lbPair.baseFeePercentage / 10000 : 0.001,
                        timestamp: Date.now(),
                        source: 'Meteora Precision SDK',
                        binId: activeBin.binId,
                        binStep: dlmmPool.lbPair.binStep,
                        formula: formulaUsed
                    };

                    // 🔇 УБИРАЕМ МУСОРНЫЕ ЛОГИ - показываем только в итоге
                    return priceData;

                } catch (error) {
                    console.error(`❌ Meteora SDK ${pool.pair}: ошибка получения данных:`, error.message);
                    return null;
                }
            });

            // 🚀 ПАРАЛЛЕЛЬНОЕ ВЫПОЛНЕНИЕ ВСЕХ ЗАПРОСОВ!
            const results = await Promise.allSettled(poolPromises);

            // Собираем успешные результаты
            for (const result of results) {
                if (result.status === 'fulfilled' && result.value) {
                    prices.push(result.value);
                }
            }

            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log(`✅ Meteora: получено ${prices.length} цен за ${duration}с`);

            // Обновляем кэш
            if (prices.length > 0) {
                this.updateCache(prices);
            } else {
                console.log(`❌ Meteora SDK: цены не получены!`);
                console.log(`🚫 FALLBACK ЗАПРЕЩЕН! ТОЛЬКО РЕАЛЬНЫЕ ЦЕНЫ!`);
                console.log(`⚠️ ВОЗВРАЩАЕМ ПУСТОЙ МАССИВ - СИСТЕМА ДОЛЖНА ОБРАБОТАТЬ ОТСУТСТВИЕ ЦЕН!`);
                this.updateCache([]);
            }

            return prices;

        } catch (error) {
            console.error('❌ Meteora SDK ошибка:', error.message);
            return [];
        }
    }

    /**
     * 🌪️ ПОЛУЧЕНИЕ РЕАЛЬНЫХ ЦЕН ЧЕРЕЗ METEORA API (FALLBACK)
     */
    async getRealPricesFromMeteoraAPI() {
        try {
            console.log(`🌪️ Получаем реальные цены через Meteora API...`);

            const response = await fetch('https://dlmm-api.meteora.ag/pair/all');
            const data = await response.json();

            if (!data || !Array.isArray(data)) {
                throw new Error('Meteora API вернул неверные данные');
            }

            const prices = [];

            for (const pool of this.pools) {
                const apiPool = data.find(p => p.address === pool.address);
                if (apiPool && apiPool.current_price) {
                    prices.push({
                        address: pool.address,
                        pair: pool.pair,
                        name: pool.name,
                        price: parseFloat(apiPool.current_price),
                        priceDisplay: `$${parseFloat(apiPool.current_price).toFixed(2)}`,
                        volume24h: apiPool.volume_24h || 0,
                        liquidity: apiPool.liquidity || 0,
                        fee: apiPool.base_fee_percentage ? apiPool.base_fee_percentage / 10000 : 0.001,
                        timestamp: Date.now(),
                        source: 'Meteora Official API',
                        binId: apiPool.active_bin_id || 0,
                        binStep: apiPool.bin_step || 0,
                        formula: 'Meteora API current_price'
                    });
                    console.log(`✅ Meteora API: ${pool.pair} = $${parseFloat(apiPool.current_price).toFixed(2)}`);
                }
            }

            return prices;
        } catch (error) {
            console.error(`❌ Ошибка получения цен через Meteora API:`, error.message);
            throw error;
        }
    }

    /**
     * 📦 ПРОВЕРКА ВАЛИДНОСТИ КЭША
     */
    isCacheValid() {
        if (!this.lastUpdate || this.cache.size === 0) {
            return false;
        }
        return (Date.now() - this.lastUpdate) < this.cacheTimeout;
    }

    /**
     * 🔄 ОБНОВЛЕНИЕ КЭША
     */
    updateCache(prices) {
        this.cache.clear();
        for (const price of prices) {
            this.cache.set(price.address, price);
        }
        this.lastUpdate = Date.now();
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            totalPools: this.pools.length,
            cachedPrices: this.cache.size,
            lastUpdate: this.lastUpdate,
            cacheAge: this.lastUpdate ? Date.now() - this.lastUpdate : null,
            cacheValid: this.isCacheValid(),
            currentRPC: this.currentConnectionIndex + 1,
            totalRPCs: this.connections.length
        };
    }

    /**
     * 🚀 АНАЛИЗ ВНУТРЕННЕГО АРБИТРАЖА МЕЖДУ METEORA ПУЛАМИ
     */
    analyzeInternalArbitrage(poolPrices) {
        console.log('\n🔥 METEORA ВНУТРЕННИЙ АРБИТРАЖ АНАЛИЗ:'.yellow);

        if (poolPrices.length < 2) {
            console.log('⚠️  Недостаточно пулов для анализа арбитража'.yellow);
            return null;
        }

        // Сортируем по цене
        poolPrices.sort((a, b) => a.price - b.price);

        const cheapest = poolPrices[0];
        const mostExpensive = poolPrices[poolPrices.length - 1];

        // 🔧 ИСПРАВЛЕНИЕ: Проверяем типы данных перед вычислениями
        const cheapPrice = Number(cheapest.price);
        const expensivePrice = Number(mostExpensive.price);

        if (isNaN(cheapPrice) || isNaN(expensivePrice)) {
            console.log('❌ Ошибка: неверные типы цен для анализа арбитража'.red);
            return null;
        }

        const spread = expensivePrice - cheapPrice;
        const spreadPercent = (spread / cheapPrice) * 100;

        console.log(`💰 КУПИТЬ:  Пул ${cheapest.address.slice(0,8)}... - $${cheapPrice.toFixed(4)}`.green);
        console.log(`💰 ПРОДАТЬ: Пул ${mostExpensive.address.slice(0,8)}... - $${expensivePrice.toFixed(4)}`.red);
        console.log(`💰 СПРЕД: $${spread.toFixed(4)} = ${spreadPercent.toFixed(4)}% ПРИБЫЛЬ!`.yellow);

        if (spreadPercent > 0.05) { // 0.05% минимальный спред
            console.log(`🚀 ВНУТРЕННИЙ АРБИТРАЖ ВОЗМОЖЕН! Спред: ${spreadPercent.toFixed(4)}%`.green);

            // Сохраняем возможность арбитража
            const opportunity = {
                type: 'internal_meteora',
                buyPool: cheapest,
                sellPool: mostExpensive,
                spread: spreadPercent,
                profit: spread,
                timestamp: Date.now()
            };

            this.lastArbitrageOpportunity = opportunity;
            return opportunity;
        } else {
            console.log(`⚠️  Спред слишком мал для арбитража: ${spreadPercent.toFixed(4)}%`.yellow);
            return null;
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ВСЕХ ЦЕН С АНАЛИЗОМ ВНУТРЕННЕГО АРБИТРАЖА
     */
    async getAllPricesWithArbitrage() {
        try {
            console.log('\n🌪️ METEORA: Получение цен всех пулов для внутреннего арбитража...'.cyan);

            // 🔥 ОФИЦИАЛЬНОЕ РЕШЕНИЕ: ОБНОВЛЯЕМ СОСТОЯНИЕ DLMM ПЕРЕД ОПЕРАЦИЯМИ
            console.log('🔄 Синхронизируем состояние DLMM пулов с блокчейном...');
            for (const pool of METEORA_TARGET_POOLS) {
                try {
                    const dlmmPool = await this.getDLMMInstance(pool.address);
                    console.log(`🔄 Обновляем состояние пула: ${pool.name}`);
                    await dlmmPool.refetchStates();
                } catch (error) {
                    console.log(`⚠️ Ошибка синхронизации пула ${pool.name}: ${error.message}`);
                }
            }
            console.log('✅ Все DLMM состояния синхронизированы!');

            const poolPrices = [];
            const prices = new Map();

            for (const pool of METEORA_TARGET_POOLS) {
                try {
                    const dlmmPool = await this.getDLMMInstance(pool.address);

                    // 🔍 ПОЛУЧАЕМ ИНФОРМАЦИЮ О ТОКЕНАХ ПУЛА
                    console.log(`\n🔍 АНАЛИЗ ПУЛА ${pool.name}:`);
                    console.log(`   Адрес пула: ${pool.address}`);
                    console.log(`   TokenX: ${dlmmPool.tokenX?.publicKey?.toString() || 'N/A'}`);
                    console.log(`   TokenY: ${dlmmPool.tokenY?.publicKey?.toString() || 'N/A'}`);

                    const activeBin = await dlmmPool.getActiveBin();

                    if (activeBin && activeBin.price) {
                        // 🎯 ПРАВИЛЬНЫЙ СПОСОБ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ METEORA!
                        const activeBinPriceLamport = activeBin.price;
                        const activeBinPricePerToken = dlmmPool.fromPricePerLamport(Number(activeBin.price));

                        // 🔧 ПРАВИЛЬНАЯ КОНВЕРТАЦИЯ: fromPricePerLamport возвращает строку!
                        let pricePerToken = parseFloat(activeBinPricePerToken);

                        // 🔍 КРИТИЧЕСКАЯ ОТЛАДКА: Анализируем токены и направление цены!
                        console.log(`🔍 КРИТИЧЕСКАЯ ОТЛАДКА ${pool.name}:`);
                        console.log(`   activeBin.price (lamport): ${activeBinPriceLamport}`);
                        console.log(`   activeBinPricePerToken (raw): ${activeBinPricePerToken}`);
                        console.log(`   pricePerToken (parsed): ${pricePerToken}`);

                        // 🎯 ОПРЕДЕЛЯЕМ ТОКЕНЫ ПУЛА
                        const tokenXMint = dlmmPool.tokenX?.publicKey?.toString() || 'N/A';
                        const tokenYMint = dlmmPool.tokenY?.publicKey?.toString() || 'N/A';

                        console.log(`   TokenX mint: ${tokenXMint}`);
                        console.log(`   TokenY mint: ${tokenYMint}`);

                        // 🔥 ИЗВЕСТНЫЕ АДРЕСА ТОКЕНОВ
                        const SOL_MINT = 'So11111111111111111111111111111111111111112';
                        const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

                        // 🎯 ОПРЕДЕЛЯЕМ ПРАВИЛЬНОЕ НАПРАВЛЕНИЕ ЦЕНЫ
                        // В DLMM: P = ΔY/ΔX (сколько Y за 1 X)
                        let finalPrice;
                        let priceDirection;

                        if (tokenXMint === SOL_MINT && tokenYMint === USDC_MINT) {
                            // TokenX = SOL, TokenY = USDC
                            // P = USDC/SOL, значит цена уже правильная (SOL/USDC)
                            finalPrice = pricePerToken;
                            priceDirection = 'SOL/USDC (X/Y)';
                        } else if (tokenXMint === USDC_MINT && tokenYMint === SOL_MINT) {
                            // TokenX = USDC, TokenY = SOL
                            // P = SOL/USDC, но нам нужно USDC/SOL, инвертируем
                            finalPrice = 1 / pricePerToken;
                            priceDirection = 'SOL/USDC (Y/X inverted)';
                        } else {
                            // Неизвестная конфигурация, используем эвристику
                            if (pricePerToken < 1) {
                                finalPrice = 1 / pricePerToken;
                                priceDirection = 'SOL/USDC (inverted by heuristic)';
                            } else {
                                finalPrice = pricePerToken;
                                priceDirection = 'SOL/USDC (direct)';
                            }
                        }

                        console.log(`   Направление: ${priceDirection}`);
                        console.log(`   ФИНАЛЬНАЯ ЦЕНА SOL/USDC: $${finalPrice.toFixed(4)}`);

                        // Проверяем, что получили валидное число в правильном диапазоне
                        if (isNaN(finalPrice) || finalPrice <= 0 || finalPrice < 100 || finalPrice > 300) {
                            console.log(`⚠️  Цена вне разумного диапазона для ${pool.name}: $${finalPrice}`.yellow);
                            continue;
                        }

                        // Используем финальную цену
                        pricePerToken = finalPrice;

                        poolPrices.push({
                            address: pool.address,
                            price: pricePerToken,
                            name: pool.name,
                            pair: pool.pair
                        });

                        prices.set(`${pool.pair}_${pool.address}`, pricePerToken);
                        console.log(`🌪️ ${pool.name}: $${pricePerToken.toFixed(4)}`.cyan);
                    }
                } catch (error) {
                    console.error(`❌ Ошибка получения цены ${pool.name}:`, error.message);
                }
            }

            // 🔥 АНАЛИЗ ВНУТРЕННЕГО АРБИТРАЖА!
            if (poolPrices.length >= 2) {
                this.analyzeInternalArbitrage(poolPrices);
            }

            return prices;

        } catch (error) {
            console.error('❌ Ошибка анализа внутреннего арбитража:', error.message);
            return new Map();
        }
    }
}

console.log('🌪️ Meteora Direct SDK модуль загружен!'.cyan);

// 🎯 ЭКСПОРТ МОДУЛЯ
module.exports = MeteoraDirectSDK;
