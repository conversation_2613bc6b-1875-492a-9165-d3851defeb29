{"name": "solana-flash-loans", "version": "1.0.0", "description": "Flash loans implementation for Solana DEX arbitrage", "main": "solana-flash-loan-manager.js", "scripts": {"start": "node arbitrage-example.js", "enhanced": "node enhanced-arbitrage.js", "integrator": "node dex-integrator.js", "full": "node full-arbitrage-monitor.js", "test": "node test/test-flash-loans.js", "dev": "nodemon arbitrage-example.js", "install-deps": "npm install"}, "keywords": ["solana", "flash-loans", "arbitrage", "defi", "dex", "marginfi", "jupiter", "orca", "raydium"], "author": "Mempool Arbitrage System", "license": "MIT", "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@ellipsis-labs/phoenix-sdk": "^2.0.3", "@meteora-ag/dlmm": "^1.5.4", "@mrgnlabs/marginfi-client-v2": "^6.1.0", "@mrgnlabs/mrgn-common": "^2.0.3", "@openbook-dex/openbook-v2": "^0.2.10", "@orca-so/whirlpools": "^2.2.0", "@raydium-io/raydium-sdk-v2": "^0.1.139-alpha", "@saberhq/stableswap-sdk": "^3.0.0", "@saberhq/token-utils": "^3.0.0", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.91.8", "axios": "^1.10.0", "bignumber.js": "^9.1.2", "bs58": "^6.0.0", "dotenv": "^16.3.1", "node-fetch": "^3.3.2", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/mempool-arbitrage"}}