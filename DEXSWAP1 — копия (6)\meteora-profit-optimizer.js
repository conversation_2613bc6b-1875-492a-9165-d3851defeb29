/**
 * 🎯 ОПТИМИЗАТОР ПРИБЫЛИ ДЛЯ METEORA ПУЛОВ
 * 
 * Находит максимальную прибыль для пулов $3M и $7M
 * Основано на реальных расчетах пользователя
 */

class MeteoraOptimizer {
    constructor() {
        // Реальные Meteora пулы
        this.POOLS = {
            SMALL: {
                name: 'Meteora Small Pool',
                liquidity: 3000000, // $3M
                fee: 0.0004 // 0.04%
            },
            LARGE: {
                name: 'Meteora Large Pool', 
                liquidity: 7000000, // $7M
                fee: 0.0004 // 0.04%
            }
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.TRANSACTION_COST = 0.01; // $0.01
        
        console.log('🎯 MeteoraOptimizer инициализирован');
        console.log('📊 Пулы: $3M и $7M Meteora');
    }

    /**
     * 🧮 РАСЧЕТ ДВИЖЕНИЯ ЦЕНЫ ПРИ ПОКУПКЕ
     */
    calculatePriceMovement(buyAmount, poolLiquidity) {
        // Процент от пула
        const poolPercent = (buyAmount / poolLiquidity) * 100;
        
        // Новая ликвидность после покупки
        const newLiquidity = poolLiquidity + buyAmount;
        
        // Движение цены (линейная аппроксимация)
        const priceMovement = (buyAmount / poolLiquidity) * 100;
        
        return {
            buyAmount,
            poolPercent: poolPercent.toFixed(2),
            newLiquidity,
            priceMovement: priceMovement.toFixed(2),
            newPriceLevel: (100 + priceMovement).toFixed(2)
        };
    }

    /**
     * 💰 РАСЧЕТ SLIPPAGE ПРИ ПРОДАЖЕ
     */
    calculateSellSlippage(sellAmount, poolLiquidity) {
        // 1% от пула
        const onePercent = poolLiquidity / 100;
        
        // Сколько процентов составляет продажа
        const sellPercent = (sellAmount / poolLiquidity) * 100;
        
        // Slippage (квадратичная функция для больших объемов)
        const slippage = Math.pow(sellPercent / 100, 1.2) * 100;
        
        // Потери от slippage
        const slippageLoss = sellAmount * (slippage / 100);
        
        // Комиссия пула
        const poolFee = sellAmount * this.POOLS.LARGE.fee;
        
        // Чистая выручка
        const netRevenue = sellAmount - slippageLoss - poolFee;
        
        return {
            sellAmount,
            sellPercent: sellPercent.toFixed(2),
            slippage: slippage.toFixed(2),
            slippageLoss: Math.round(slippageLoss),
            poolFee: Math.round(poolFee),
            netRevenue: Math.round(netRevenue),
            onePercent: Math.round(onePercent)
        };
    }

    /**
     * 🎯 ПОЛНЫЙ РАСЧЕТ АРБИТРАЖА
     */
    calculateArbitrage(flashLoanAmount, sellAmount, buyPool, sellPool) {
        // 1. Покупка в маленьком пуле (поднимаем цену)
        const priceMove = this.calculatePriceMovement(flashLoanAmount, buyPool.liquidity);
        
        // 2. Продажа в большом пуле (получаем USDC)
        const sellResult = this.calculateSellSlippage(sellAmount, sellPool.liquidity);
        
        // 3. Доход от повышенной цены
        const priceBonus = sellAmount * (parseFloat(priceMove.priceMovement) / 100);
        
        // 4. Общая выручка
        const totalRevenue = sellResult.netRevenue + priceBonus;
        
        // 5. Затраты
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const buyPoolFee = flashLoanAmount * buyPool.fee;
        const totalCosts = flashLoanAmount + flashLoanFee + buyPoolFee + this.TRANSACTION_COST;
        
        // 6. Чистая прибыль
        const netProfit = totalRevenue - totalCosts;
        const roi = (netProfit / flashLoanAmount) * 100;
        
        return {
            inputs: {
                flashLoanAmount,
                sellAmount,
                buyPool: buyPool.name,
                sellPool: sellPool.name
            },
            priceMovement: {
                percent: parseFloat(priceMove.priceMovement),
                bonus: Math.round(priceBonus),
                newLiquidity: priceMove.newLiquidity
            },
            sellAnalysis: sellResult,
            costs: {
                flashLoan: flashLoanAmount,
                flashLoanFee: Math.round(flashLoanFee),
                buyPoolFee: Math.round(buyPoolFee),
                transaction: this.TRANSACTION_COST,
                total: Math.round(totalCosts)
            },
            revenue: {
                sellRevenue: sellResult.netRevenue,
                priceBonus: Math.round(priceBonus),
                total: Math.round(totalRevenue)
            },
            result: {
                netProfit: Math.round(netProfit),
                roi: roi.toFixed(2),
                profitable: netProfit > 0
            }
        };
    }

    /**
     * 🏆 ПОИСК ОПТИМАЛЬНОЙ СТРАТЕГИИ
     */
    findOptimalStrategy() {
        console.log('\n🏆 ПОИСК ОПТИМАЛЬНОЙ СТРАТЕГИИ ДЛЯ METEORA ПУЛОВ');
        console.log('=' .repeat(80));
        
        // Тестируем разные размеры Flash Loan
        const flashLoanSizes = [
            100000, 200000, 300000, 400000, 500000, 
            600000, 700000, 800000, 900000, 1000000
        ];
        
        // Коэффициенты продажи (продаем больше чем покупаем)
        const sellMultipliers = [0.6, 0.7, 0.8, 0.9, 1.0, 1.2, 1.5];
        
        let bestStrategy = { result: { netProfit: -Infinity } };
        const allResults = [];
        
        console.log('📊 АНАЛИЗ РАЗЛИЧНЫХ СТРАТЕГИЙ:\n');
        
        // Тестируем оба пула как источник покупки
        [this.POOLS.SMALL, this.POOLS.LARGE].forEach(buyPool => {
            const sellPool = buyPool === this.POOLS.SMALL ? this.POOLS.LARGE : this.POOLS.SMALL;
            
            console.log(`🌊 ПОКУПКА В ${buyPool.name.toUpperCase()} ($${(buyPool.liquidity/1000000).toFixed(1)}M)`);
            console.log(`💰 ПРОДАЖА В ${sellPool.name.toUpperCase()} ($${(sellPool.liquidity/1000000).toFixed(1)}M)`);
            
            flashLoanSizes.forEach(flashLoan => {
                sellMultipliers.forEach(multiplier => {
                    const sellAmount = Math.round(flashLoan * multiplier);
                    
                    const result = this.calculateArbitrage(flashLoan, sellAmount, buyPool, sellPool);
                    allResults.push(result);
                    
                    if (result.result.netProfit > bestStrategy.result.netProfit) {
                        bestStrategy = result;
                    }
                    
                    const status = result.result.profitable ? '✅' : '❌';
                    const emoji = result.result.profitable ? '💚' : '🔴';
                    
                    console.log(`   ${status} FL:$${flashLoan.toLocaleString()} → Sell:$${sellAmount.toLocaleString()} (${multiplier}x):`);
                    console.log(`      📈 Движение цены: ${result.priceMovement.percent}%`);
                    console.log(`      💸 Slippage продажи: ${result.sellAnalysis.slippage}%`);
                    console.log(`      ${emoji} Прибыль: $${result.result.netProfit.toLocaleString()} (ROI: ${result.result.roi}%)`);
                });
                console.log('');
            });
            console.log('');
        });
        
        return { bestStrategy, allResults };
    }

    /**
     * 📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕЙ СТРАТЕГИИ
     */
    analyzeBestStrategy(strategy) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ОПТИМАЛЬНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        const s = strategy;
        
        console.log(`🎯 ПАРАМЕТРЫ ОПЕРАЦИИ:`);
        console.log(`   💰 Flash Loan: $${s.inputs.flashLoanAmount.toLocaleString()}`);
        console.log(`   🛒 Покупка в: ${s.inputs.buyPool}`);
        console.log(`   💸 Продажа: $${s.inputs.sellAmount.toLocaleString()} в ${s.inputs.sellPool}`);
        console.log(`   📊 Соотношение: ${(s.inputs.sellAmount / s.inputs.flashLoanAmount).toFixed(1)}x`);
        
        console.log(`\n📈 ДВИЖЕНИЕ ЦЕНЫ:`);
        console.log(`   🚀 Повышение: ${s.priceMovement.percent}%`);
        console.log(`   💎 Бонус от цены: $${s.priceMovement.bonus.toLocaleString()}`);
        console.log(`   🌊 Новая ликвидность: $${s.priceMovement.newLiquidity.toLocaleString()}`);
        
        console.log(`\n💸 АНАЛИЗ ПРОДАЖИ:`);
        console.log(`   📊 Процент от пула: ${s.sellAnalysis.sellPercent}%`);
        console.log(`   💔 Slippage: ${s.sellAnalysis.slippage}%`);
        console.log(`   💸 Потери от slippage: $${s.sellAnalysis.slippageLoss.toLocaleString()}`);
        console.log(`   💰 Комиссия пула: $${s.sellAnalysis.poolFee.toLocaleString()}`);
        console.log(`   💎 Чистая выручка: $${s.sellAnalysis.netRevenue.toLocaleString()}`);
        
        console.log(`\n💰 ФИНАНСОВЫЙ АНАЛИЗ:`);
        console.log(`   📥 Общая выручка: $${s.revenue.total.toLocaleString()}`);
        console.log(`   📤 Общие затраты: $${s.costs.total.toLocaleString()}`);
        console.log(`   💚 Чистая прибыль: $${s.result.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${s.result.roi}%`);
        
        // Потенциал масштабирования
        const dailyPotential = s.result.netProfit * 20;
        const monthlyPotential = dailyPotential * 30;
        
        console.log(`\n💎 ПОТЕНЦИАЛ МАСШТАБИРОВАНИЯ:`);
        console.log(`   📅 Дневной потенциал (20 операций): $${dailyPotential.toLocaleString()}`);
        console.log(`   📆 Месячный потенциал: $${monthlyPotential.toLocaleString()}`);
        console.log(`   🚀 Годовой потенциал: $${(monthlyPotential * 12).toLocaleString()}`);
        
        return strategy;
    }

    /**
     * 📈 ТОП-10 ЛУЧШИХ СТРАТЕГИЙ
     */
    showTopStrategies(allResults) {
        console.log('\n📈 ТОП-10 САМЫХ ПРИБЫЛЬНЫХ СТРАТЕГИЙ');
        console.log('=' .repeat(80));
        
        const profitable = allResults
            .filter(r => r.result.profitable)
            .sort((a, b) => b.result.netProfit - a.result.netProfit)
            .slice(0, 10);
        
        if (profitable.length === 0) {
            console.log('❌ НЕ НАЙДЕНО ПРИБЫЛЬНЫХ СТРАТЕГИЙ');
            return;
        }
        
        profitable.forEach((strategy, index) => {
            const s = strategy;
            console.log(`${index + 1}. 💰 FL:$${s.inputs.flashLoanAmount.toLocaleString()} → Sell:$${s.inputs.sellAmount.toLocaleString()}`);
            console.log(`   📈 Движение: ${s.priceMovement.percent}% | Slippage: ${s.sellAnalysis.slippage}%`);
            console.log(`   💚 Прибыль: $${s.result.netProfit.toLocaleString()} (ROI: ${s.result.roi}%)`);
            console.log(`   🎯 ${s.inputs.buyPool} → ${s.inputs.sellPool}`);
            console.log('');
        });
        
        return profitable;
    }

    /**
     * 🎯 СРАВНЕНИЕ С ПОЛЬЗОВАТЕЛЬСКИМИ РАСЧЕТАМИ
     */
    compareWithUserCalculations() {
        console.log('\n🎯 СРАВНЕНИЕ С ТВОИМИ РАСЧЕТАМИ');
        console.log('=' .repeat(80));
        
        // Пример 1: FL $500K, продажа $350K
        const example1 = this.calculateArbitrage(500000, 350000, this.POOLS.SMALL, this.POOLS.LARGE);
        
        // Пример 2: FL $500K, продажа $300K  
        const example2 = this.calculateArbitrage(500000, 300000, this.POOLS.SMALL, this.POOLS.LARGE);
        
        console.log('📊 ПРИМЕР 1 (твой расчет: 5-6% прибыль):');
        console.log(`   💰 FL: $500K → Продажа: $350K`);
        console.log(`   📈 Движение цены: ${example1.priceMovement.percent}%`);
        console.log(`   💸 Slippage: ${example1.sellAnalysis.slippage}%`);
        console.log(`   💚 Прибыль: $${example1.result.netProfit.toLocaleString()} (${example1.result.roi}%)`);
        
        console.log('\n📊 ПРИМЕР 2 (твой расчет: 7% прибыль):');
        console.log(`   💰 FL: $500K → Продажа: $300K`);
        console.log(`   📈 Движение цены: ${example2.priceMovement.percent}%`);
        console.log(`   💸 Slippage: ${example2.sellAnalysis.slippage}%`);
        console.log(`   💚 Прибыль: $${example2.result.netProfit.toLocaleString()} (${example2.result.roi}%)`);
        
        return { example1, example2 };
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК ОПТИМИЗАТОРА METEORA ПРИБЫЛИ...\n');
    
    const optimizer = new MeteoraOptimizer();
    
    try {
        // Сравнение с пользовательскими расчетами
        const userComparison = optimizer.compareWithUserCalculations();
        
        // Поиск оптимальной стратегии
        const { bestStrategy, allResults } = optimizer.findOptimalStrategy();
        
        // Анализ лучшей стратегии
        const analysis = optimizer.analyzeBestStrategy(bestStrategy);
        
        // Топ-10 стратегий
        const topStrategies = optimizer.showTopStrategies(allResults);
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${bestStrategy.result.netProfit.toLocaleString()}`);
        console.log(`💰 ОПТИМАЛЬНЫЙ FLASH LOAN: $${bestStrategy.inputs.flashLoanAmount.toLocaleString()}`);
        console.log(`📈 ROI: ${bestStrategy.result.roi}%`);
        
        return { analysis, topStrategies, userComparison };
        
    } catch (error) {
        console.error('❌ Ошибка оптимизации:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { MeteoraOptimizer };
