/**
 * 🎯 КАЛЬКУЛЯТОР ТОЧКИ БЕЗУБЫТОЧНОСТИ
 * 
 * Находит минимальное повышение цены для прибыльности
 * Формула: Price_Increase > Buy_Slippage + Sell_Slippage + All_Fees
 */

class BreakEvenCalculator {
    constructor() {
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.POOL_FEE = 0.0025; // 0.25% средняя комиссия DEX
        
        console.log('🎯 BreakEvenCalculator инициализирован');
        console.log('📐 Формула: Повышение цены > Slippage покупки + Slippage продажи + Комиссии');
    }

    /**
     * 🧮 РАСЧЕТ SLIPPAGE ПРИ ПОКУПКЕ
     */
    calculateBuySlippage(buyAmount, poolLiquidity) {
        const ratio = buyAmount / poolLiquidity;
        // Квадратичная функция slippage
        const slippagePercent = Math.pow(ratio, 1.2) * 100;
        return {
            ratio: (ratio * 100).toFixed(2),
            slippagePercent: slippagePercent.toFixed(4),
            slippageCost: Math.round(buyAmount * slippagePercent / 100)
        };
    }

    /**
     * 💰 РАСЧЕТ SLIPPAGE ПРИ ПРОДАЖЕ
     */
    calculateSellSlippage(sellAmount, poolLiquidity) {
        const ratio = sellAmount / poolLiquidity;
        // Более мягкая функция для больших пулов
        const slippagePercent = Math.pow(ratio, 1.5) * 100;
        return {
            ratio: (ratio * 100).toFixed(4),
            slippagePercent: slippagePercent.toFixed(4),
            slippageCost: Math.round(sellAmount * slippagePercent / 100)
        };
    }

    /**
     * 📈 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ
     */
    calculatePriceIncrease(buyAmount, poolLiquidity) {
        const ratio = buyAmount / poolLiquidity;
        // Повышение цены от покупки
        const priceIncreasePercent = Math.pow(ratio, 0.8) * 100;
        return {
            ratio: (ratio * 100).toFixed(2),
            priceIncreasePercent: priceIncreasePercent.toFixed(4),
            priceBonus: Math.round(buyAmount * priceIncreasePercent / 100)
        };
    }

    /**
     * 🎯 АНАЛИЗ ТОЧКИ БЕЗУБЫТОЧНОСТИ
     */
    analyzeBreakEven(buyAmount, sellAmount, buyPoolLiquidity, sellPoolLiquidity) {
        // 1. Расчет повышения цены
        const priceIncrease = this.calculatePriceIncrease(buyAmount, buyPoolLiquidity);
        
        // 2. Расчет slippage при покупке
        const buySlippage = this.calculateBuySlippage(buyAmount, buyPoolLiquidity);
        
        // 3. Расчет slippage при продаже
        const sellSlippage = this.calculateSellSlippage(sellAmount, sellPoolLiquidity);
        
        // 4. Расчет всех комиссий
        const buyFee = buyAmount * this.POOL_FEE;
        const sellFee = sellAmount * this.POOL_FEE;
        const flashLoanFee = buyAmount * this.FLASH_LOAN_FEE;
        const totalFees = buyFee + sellFee + flashLoanFee;
        
        // 5. Общие затраты в процентах
        const totalCostsPercent = (
            parseFloat(buySlippage.slippagePercent) +
            parseFloat(sellSlippage.slippagePercent) +
            (totalFees / buyAmount * 100)
        );
        
        // 6. Прибыльность
        const priceIncreasePercent = parseFloat(priceIncrease.priceIncreasePercent);
        const profitable = priceIncreasePercent > totalCostsPercent;
        const margin = priceIncreasePercent - totalCostsPercent;
        
        // 7. Денежные расчеты
        const totalCostsDollar = parseInt(buySlippage.slippageCost) + parseInt(sellSlippage.slippageCost) + totalFees;
        const priceBonusDollar = sellAmount * (priceIncreasePercent / 100);
        const netProfit = priceBonusDollar - totalCostsDollar;
        
        return {
            inputs: {
                buyAmount,
                sellAmount,
                buyPoolLiquidity,
                sellPoolLiquidity
            },
            priceIncrease: {
                percent: priceIncreasePercent,
                bonus: Math.round(priceBonusDollar)
            },
            costs: {
                buySlippagePercent: parseFloat(buySlippage.slippagePercent),
                sellSlippagePercent: parseFloat(sellSlippage.slippagePercent),
                feesPercent: (totalFees / buyAmount * 100),
                totalPercent: totalCostsPercent,
                totalDollar: Math.round(totalCostsDollar)
            },
            result: {
                profitable,
                margin: margin.toFixed(4),
                netProfit: Math.round(netProfit),
                roi: (netProfit / buyAmount * 100).toFixed(4)
            },
            details: {
                buySlippage,
                sellSlippage,
                fees: {
                    buy: Math.round(buyFee),
                    sell: Math.round(sellFee),
                    flashLoan: Math.round(flashLoanFee),
                    total: Math.round(totalFees)
                }
            }
        };
    }

    /**
     * 🔍 ПОИСК МИНИМАЛЬНОГО ПОВЫШЕНИЯ ЦЕНЫ
     */
    findMinimumPriceIncrease(sellAmount, sellPoolLiquidity) {
        console.log('\n🔍 ПОИСК МИНИМАЛЬНОГО ПОВЫШЕНИЯ ЦЕНЫ ДЛЯ ПРИБЫЛЬНОСТИ');
        console.log('=' .repeat(80));
        
        // Фиксированные параметры
        const sellSlippage = this.calculateSellSlippage(sellAmount, sellPoolLiquidity);
        const sellFee = sellAmount * this.POOL_FEE;
        
        console.log(`📊 Фиксированные затраты на продажу $${sellAmount.toLocaleString()}:`);
        console.log(`   💸 Slippage продажи: ${sellSlippage.slippagePercent}%`);
        console.log(`   💰 Комиссия продажи: ${(this.POOL_FEE * 100).toFixed(2)}%`);
        console.log(`   🏦 Flash Loan комиссия: ${(this.FLASH_LOAN_FEE * 100).toFixed(2)}%`);
        
        // Тестируем разные размеры покупки
        const buyAmounts = [100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 900000, 1000000];
        const buyPoolSizes = [500000, 1000000, 2000000, 3000000, 5000000];
        
        let bestScenario = { margin: -Infinity };
        
        console.log('\n📈 АНАЛИЗ РАЗЛИЧНЫХ СЦЕНАРИЕВ:\n');
        
        buyPoolSizes.forEach(buyPoolSize => {
            console.log(`🌊 ПУЛ ДЛЯ ПОКУПКИ: $${buyPoolSize.toLocaleString()}`);
            
            buyAmounts.forEach(buyAmount => {
                if (buyAmount > sellAmount * 1.5) return; // Разумное ограничение
                
                const analysis = this.analyzeBreakEven(buyAmount, sellAmount, buyPoolSize, sellPoolLiquidity);
                
                if (parseFloat(analysis.result.margin) > bestScenario.margin) {
                    bestScenario = analysis;
                }
                
                const status = analysis.result.profitable ? '✅' : '❌';
                const emoji = analysis.result.profitable ? '💚' : '🔴';
                
                console.log(`   ${status} Покупка $${buyAmount.toLocaleString()} (${analysis.details.buySlippage.ratio}% от пула):`);
                console.log(`      📈 Повышение цены: ${analysis.priceIncrease.percent}%`);
                console.log(`      💸 Общие затраты: ${analysis.costs.totalPercent.toFixed(4)}%`);
                console.log(`      ${emoji} Маржа: ${analysis.result.margin}% | Прибыль: $${analysis.result.netProfit.toLocaleString()}`);
            });
            console.log('');
        });
        
        return bestScenario;
    }

    /**
     * 📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕГО СЦЕНАРИЯ
     */
    analyzeBestScenario(scenario) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕГО СЦЕНАРИЯ');
        console.log('=' .repeat(80));
        
        const inputs = scenario.inputs;
        
        console.log(`🎯 ПАРАМЕТРЫ ОПЕРАЦИИ:`);
        console.log(`   💰 Покупка: $${inputs.buyAmount.toLocaleString()}`);
        console.log(`   💸 Продажа: $${inputs.sellAmount.toLocaleString()}`);
        console.log(`   🌊 Пул покупки: $${inputs.buyPoolLiquidity.toLocaleString()}`);
        console.log(`   🐋 Пул продажи: $${inputs.sellPoolLiquidity.toLocaleString()}`);
        
        console.log(`\n📈 ПОВЫШЕНИЕ ЦЕНЫ:`);
        console.log(`   🚀 Процент: ${scenario.priceIncrease.percent}%`);
        console.log(`   💎 Бонус: $${scenario.priceIncrease.bonus.toLocaleString()}`);
        
        console.log(`\n💸 ЗАТРАТЫ:`);
        console.log(`   🛒 Slippage покупки: ${scenario.costs.buySlippagePercent.toFixed(4)}%`);
        console.log(`   💰 Slippage продажи: ${scenario.costs.sellSlippagePercent.toFixed(4)}%`);
        console.log(`   🏦 Комиссии: ${scenario.costs.feesPercent.toFixed(4)}%`);
        console.log(`   📊 ОБЩИЕ ЗАТРАТЫ: ${scenario.costs.totalPercent.toFixed(4)}%`);
        
        console.log(`\n🏆 РЕЗУЛЬТАТ:`);
        console.log(`   📊 Маржа: ${scenario.result.margin}%`);
        console.log(`   💚 Чистая прибыль: $${scenario.result.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${scenario.result.roi}%`);
        console.log(`   ✅ Прибыльность: ${scenario.result.profitable ? 'ДА' : 'НЕТ'}`);
        
        // Формула безубыточности
        console.log(`\n📐 ФОРМУЛА БЕЗУБЫТОЧНОСТИ:`);
        console.log(`   Повышение цены (${scenario.priceIncrease.percent}%) > Затраты (${scenario.costs.totalPercent.toFixed(4)}%)`);
        console.log(`   ${scenario.priceIncrease.percent}% ${scenario.result.profitable ? '>' : '<'} ${scenario.costs.totalPercent.toFixed(4)}%`);
        console.log(`   Маржа: ${scenario.result.margin}%`);
        
        return scenario;
    }

    /**
     * 🎯 РЕКОМЕНДАЦИИ ДЛЯ ПРИБЫЛЬНОСТИ
     */
    generateRecommendations(bestScenario) {
        console.log('\n🎯 РЕКОМЕНДАЦИИ ДЛЯ ДОСТИЖЕНИЯ ПРИБЫЛЬНОСТИ');
        console.log('=' .repeat(80));
        
        if (bestScenario.result.profitable) {
            console.log('✅ НАЙДЕН ПРИБЫЛЬНЫЙ СЦЕНАРИЙ!');
            console.log(`   💰 Используйте параметры из лучшего сценария`);
            console.log(`   📈 Маржа: ${bestScenario.result.margin}%`);
        } else {
            console.log('❌ ВСЕ СЦЕНАРИИ УБЫТОЧНЫ. РЕКОМЕНДАЦИИ:');
            
            const requiredIncrease = bestScenario.costs.totalPercent + 1; // +1% для маржи
            
            console.log(`\n🎯 ДЛЯ ПРИБЫЛЬНОСТИ НУЖНО:`);
            console.log(`   📈 Минимальное повышение цены: ${requiredIncrease.toFixed(2)}%`);
            console.log(`   🌊 Или найти пул с меньшей ликвидностью`);
            console.log(`   💰 Или использовать пулы с меньшими комиссиями`);
            console.log(`   🔄 Или искать реальные арбитражные возможности`);
            
            // Расчет нужной ликвидности
            const targetRatio = Math.pow(requiredIncrease / 100, 1/0.8);
            const neededLiquidity = bestScenario.inputs.buyAmount / targetRatio;
            
            console.log(`\n🧮 АЛЬТЕРНАТИВНЫЕ ПАРАМЕТРЫ:`);
            console.log(`   🌊 Нужная ликвидность пула: $${Math.round(neededLiquidity).toLocaleString()}`);
            console.log(`   📊 Соотношение: ${(targetRatio * 100).toFixed(2)}%`);
        }
        
        console.log(`\n💡 ОБЩИЕ РЕКОМЕНДАЦИИ:`);
        console.log(`   • Ищите пулы с комиссиями <0.1% вместо 0.25%`);
        console.log(`   • Используйте протоколы с меньшими Flash Loan комиссиями`);
        console.log(`   • Рассмотрите реальный арбитраж между DEX`);
        console.log(`   • Изучите liquidation арбитраж`);
        console.log(`   • Попробуйте MEV стратегии`);
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК КАЛЬКУЛЯТОРА ТОЧКИ БЕЗУБЫТОЧНОСТИ...\n');
    
    const calculator = new BreakEvenCalculator();
    
    try {
        // Параметры для анализа
        const sellAmount = 400000; // $400K продажа
        const sellPoolLiquidity = 44000000; // $44M ORCA
        
        // Поиск минимального повышения цены
        const bestScenario = calculator.findMinimumPriceIncrease(sellAmount, sellPoolLiquidity);
        
        // Детальный анализ
        const analysis = calculator.analyzeBestScenario(bestScenario);
        
        // Рекомендации
        calculator.generateRecommendations(bestScenario);
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ЛУЧШАЯ МАРЖА: ${bestScenario.result.margin}%`);
        console.log(`💰 ПРИБЫЛЬНОСТЬ: ${bestScenario.result.profitable ? 'ДА' : 'НЕТ'}`);
        
        return analysis;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { BreakEvenCalculator };
