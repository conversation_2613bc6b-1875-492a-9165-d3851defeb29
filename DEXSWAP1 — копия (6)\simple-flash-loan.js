/**
 * 🔥 ПРОСТОЙ FLASH LOAN ДЛЯ ПОЛУЧЕНИЯ SOL
 * СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА
 * ИСПОЛЬЗУЕТСЯ ДЛЯ СОЗДАНИЯ POSITION АККАУНТОВ
 */

const { Connection, PublicKey, TransactionInstruction, ComputeBudgetProgram, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { Keypair } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

class SimpleFlashLoan {
    constructor(wallet, marginfiAccountAddress, connection) {
        this.wallet = wallet;
        this.marginfiAccountAddress = new PublicKey(marginfiAccountAddress);
        this.connection = connection;

        // 🔥 КОНСТАНТЫ ИЗ ОСНОВНОГО КОДА
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

        // 🔥 MARGINFI GROUP
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        
        // 🔥 БАНКИ
        this.BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh')
        };

        // 🔥 VAULT АККАУНТЫ (ИЗ ОСНОВНОГО КОДА)
        this.VAULTS = {
            SOL: {
                liquidityVault: new PublicKey('2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe'),
                vaultAuthority: new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD'),
                userTokenAccount: new PublicKey('68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk')
            },
            USDC: {
                liquidityVault: new PublicKey('3qqXJmLgZNvJYhEKKKd1WvsZjuJHdqKKhx8YbdL1Ma8E'),
                vaultAuthority: new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD'),
                userTokenAccount: new PublicKey('6VK1ksrmYGMBWUUZfygGF8tHRGpNxQEWv8pfvzQHdyyY')
            }
        };

        // 🔥 POOL ADDRESSES
        this.POOL_ADDRESSES = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',  // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'   // Pool 3
        ];

        // DLMM инстансы
        this.dlmmPools = null;

        // Созданные position keypairs (сохраняем для получения адресов)
        this.createdPositions = [];

        // Все signers из SDK транзакций
        this.allSigners = [];

        // ALT таблицы для сжатия
        this.altTables = null;
    }

    /**
     * 🚀 СОЗДАНИЕ FLASH LOAN ТРАНЗАКЦИИ С METEORA SDK
     */
    async createFlashLoanWithPositions() {
        console.log('🔥 СОЗДАНИЕ FLASH LOAN С POSITION АККАУНТАМИ...');
        
        const instructions = [];

        // 0-1: ComputeBudget (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
        const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
            units: 1400000
        });
        instructions.push(computeUnitLimitIx);
        
        const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 0
        });
        instructions.push(computeUnitPriceIx);

        // 2: START Flash Loan (ВРЕМЕННЫЙ endIndex - ИСПРАВИМ ПОЗЖЕ)
        const tempStartFlashLoanIx = this.createStartFlashLoanInstruction(999); // Временное значение
        instructions.push(tempStartFlashLoanIx);

        // 3: BORROW SOL (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
        const borrowAmount = 1 * 1e9; // 1 SOL для создания position (достаточно для 0.057 SOL)
        const borrowSOLIx = this.createBorrowInstruction(borrowAmount, this.BANKS.SOL);
        instructions.push(borrowSOLIx);

        // 4: ПРОПУСКАЕМ ВСЕ TRANSFER - КОШЕЛЕК ДОЛЖЕН БЫТЬ ПОПОЛНЕН ЗАРАНЕЕ!
        console.log('⚠️ ВНИМАНИЕ: Кошелек должен иметь достаточно SOL для создания position!');

        // 5-N: СОЗДАНИЕ POSITION ЧЕРЕЗ SDK (ТЕПЕРЬ ЕСТЬ ДЕНЬГИ НА КОШЕЛЬКЕ!)
        await this.initializeDLMMPools();

        console.log('🔥 СОЗДАЕМ POSITION (ДЕНЬГИ УЖЕ НА КОШЕЛЬКЕ!)');
        const positionInstructions = await this.createPositionInstruction(0); // Только первый пул

        // Добавляем инструкции из SDK БЕЗ ДУБЛИКАТОВ
        if (Array.isArray(positionInstructions)) {
            console.log(`📊 SDK создал ${positionInstructions.length} инструкций`);

            // ФИЛЬТРУЕМ ДУБЛИКАТЫ
            const uniqueInstructions = this.removeDuplicateInstructions(positionInstructions);
            console.log(`🔧 После удаления дубликатов: ${uniqueInstructions.length} инструкций`);

            instructions.push(...uniqueInstructions);
        } else {
            instructions.push(positionInstructions);
        }

        // N+1: ПРОПУСКАЕМ DEPOSIT - ЗАЙМ ОСТАЕТСЯ В MARGINFI
        console.log('⚠️ ПРОПУСКАЕМ DEPOSIT - займ остается в MarginFi для REPAY');

        // N+2: END Flash Loan (ДОЛЖЕН БЫТЬ НА ПОЗИЦИИ endIndex!)
        const endFlashLoanIx = this.createEndFlashLoanInstruction();
        instructions.push(endFlashLoanIx);

        // N+3: REPAY SOL (ПОСЛЕ END Flash Loan!)
        const repaySOLIx = this.createRepayInstruction(this.BANKS.SOL, true);
        instructions.push(repaySOLIx);

        // Проверяем позицию END Flash Loan
        const actualEndIndex = instructions.length - 2; // -2 потому что REPAY после END
        console.log(`🎯 END Flash Loan будет на позиции: ${actualEndIndex}`);

        console.log(`✅ Создано ${instructions.length} инструкций`);

        // ФИНАЛЬНАЯ ПРОВЕРКА НА ДУБЛИКАТЫ ВО ВСЕЙ ТРАНЗАКЦИИ
        console.log('🔧 Финальная проверка на дубликаты...');
        const finalInstructions = this.removeDuplicateInstructions(instructions);

        if (finalInstructions.length !== instructions.length) {
            console.log(`⚠️ Удалено ${instructions.length - finalInstructions.length} дубликатов из всей транзакции`);
        }

        // ИСПРАВЛЯЕМ endIndex В START FLASH LOAN
        const endFlashLoanIndex = finalInstructions.length - 2; // END Flash Loan перед REPAY
        console.log(`🎯 Исправляем endIndex на ${endFlashLoanIndex}`);

        // Пересоздаем START Flash Loan с правильным endIndex
        finalInstructions[2] = this.createStartFlashLoanInstruction(endFlashLoanIndex);

        console.log(`✅ Финальное количество инструкций: ${finalInstructions.length}`);
        return finalInstructions;
    }

    /**
     * 🔧 УДАЛЕНИЕ ДУБЛИРУЮЩИХСЯ ИНСТРУКЦИЙ
     */
    removeDuplicateInstructions(instructions) {
        const seen = new Set();
        const unique = [];

        for (const ix of instructions) {
            // Создаем уникальный ключ для инструкции
            const key = JSON.stringify({
                programId: ix.programId.toString(),
                data: Array.from(ix.data),
                keys: ix.keys.map(k => ({
                    pubkey: k.pubkey.toString(),
                    isSigner: k.isSigner,
                    isWritable: k.isWritable
                }))
            });

            if (!seen.has(key)) {
                seen.add(key);
                unique.push(ix);
            } else {
                console.log(`🔧 Удален дубликат: ${ix.programId.toString().slice(0, 8)}...`);
            }
        }

        return unique;
    }

    /**
     * 🔧 ИНИЦИАЛИЗАЦИЯ DLMM POOLS
     */
    async initializeDLMMPools() {
        if (!this.dlmmPools) {
            console.log('🔧 Инициализация DLMM pools...');
            this.dlmmPools = await DLMM.createMultiple(
                this.connection,
                this.POOL_ADDRESSES.map(addr => new PublicKey(addr))
            );
            console.log(`✅ Создано ${this.dlmmPools.length} DLMM инстансов`);
        }
    }

    /**
     * 🔧 СОЗДАНИЕ POSITION ИНСТРУКЦИИ ЧЕРЕЗ SDK
     */
    async createPositionInstruction(poolIndex) {
        const dlmmPool = this.dlmmPools[poolIndex];
        
        // Получаем активный bin
        const activeBin = await dlmmPool.getActiveBin();
        
        // Минимальная стратегия
        const minBinId = activeBin.binId;
        const maxBinId = activeBin.binId + 1;

        // Минимальные суммы
        const totalXAmount = new BN(1000);
        const totalYAmount = new BN(1000);

        // Создаем position keypair
        const newPosition = new Keypair();

        // Сохраняем для получения адреса после выполнения
        this.createdPositions.push({
            poolIndex: poolIndex + 1,
            keypair: newPosition,
            poolAddress: this.POOL_ADDRESSES[poolIndex]
        });

        console.log(`🔑 Pool ${poolIndex + 1} Position: ${newPosition.publicKey.toString()}`);

        // Создаем транзакцию через SDK С ПРАВИЛЬНЫМИ TOKEN АККАУНТАМИ
        const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
            positionPubKey: newPosition.publicKey,
            user: this.wallet.publicKey,
            totalXAmount,
            totalYAmount,
            strategy: {
                maxBinId,
                minBinId,
                strategyType: 0, // Spot
            },
            // 🔥 УКАЗЫВАЕМ MARGINFI TOKEN АККАУНТЫ!
            userTokenX: this.VAULTS.SOL.userTokenAccount, // SOL из MarginFi
            userTokenY: this.VAULTS.USDC ? this.VAULTS.USDC.userTokenAccount : undefined, // USDC если есть
        });

        console.log(`📊 SDK создал ${createPositionTx.instructions.length} инструкций`);

        // Собираем все signers из SDK транзакции
        if (createPositionTx.signers && createPositionTx.signers.length > 0) {
            console.log(`🔑 SDK требует ${createPositionTx.signers.length} дополнительных signers`);
            this.allSigners.push(...createPositionTx.signers);
        }

        // Добавляем position keypair как signer
        this.allSigners.push(newPosition);

        // Возвращаем ВСЕ инструкции из SDK (не только первую!)
        return createPositionTx.instructions;
    }

    /**
     * 🔧 START FLASH LOAN (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createStartFlashLoanInstruction(endIndex) {
        console.log(`🔧 START Flash Loan с endIndex: ${endIndex}`);

        const instructionData = Buffer.alloc(16);
        const correctDiscriminator = [14, 131, 33, 220, 81, 186, 180, 107];
        Buffer.from(correctDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(endIndex), 8);

        const accounts = [
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
            { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 BORROW INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createBorrowInstruction(amount, bankAddress) {
        console.log(`🔧 BORROW ${amount} от банка ${bankAddress.toString().slice(0,8)}...`);

        const vaultInfo = this.VAULTS.SOL;
        const borrowDiscriminator = [0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f];

        const instructionData = Buffer.alloc(16);
        Buffer.from(borrowDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);

        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: new PublicKey(this.marginfiAccountAddress), isSigner: false, isWritable: true },
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: bankAddress, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 SPL TOKEN TRANSFER ИЗ MARGINFI VAULT НА КОШЕЛЕК
     */
    createSPLTransferFromVaultInstruction(amount) {
        console.log(`🔧 SPL TOKEN TRANSFER ${amount} lamports из MarginFi vault на кошелек`);

        const vaultInfo = this.VAULTS.SOL;

        // SPL Token Transfer discriminator
        const transferDiscriminator = [3]; // SPL Token Transfer = 3

        // Создаем instruction data для SPL Token Transfer
        const instructionData = Buffer.alloc(9);
        instructionData[0] = 3; // Transfer instruction
        instructionData.writeBigUInt64LE(BigInt(amount), 1); // amount

        return new TransactionInstruction({
            keys: [
                { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },     // source (MarginFi vault)
                { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },   // destination (user account)
                { pubkey: vaultInfo.vaultAuthority, isSigner: true, isWritable: false }      // authority (vault authority)
            ],
            programId: this.TOKEN_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 WITHDRAW INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createWithdrawInstruction(tokenType, amount) {
        console.log(`🔧 WITHDRAW ${amount} ${tokenType} на кошелек`);

        const vaultInfo = this.VAULTS.SOL;
        const withdrawDiscriminator = [0x24, 0x48, 0x4a, 0x13, 0xd2, 0xd2, 0xc0, 0xc0]; // ПРАВИЛЬНЫЙ ИЗ official-discriminators-final.js

        const instructionData = Buffer.alloc(17);
        Buffer.from(withdrawDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);
        instructionData.writeUInt8(0, 16); // withdrawAll = false

        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: this.BANKS.SOL, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 DEPOSIT INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createDepositInstruction(tokenType, amount) {
        console.log(`🔧 DEPOSIT ${amount} ${tokenType} обратно в MarginFi`);

        const vaultInfo = this.VAULTS.SOL;
        const depositDiscriminator = [242, 35, 198, 137, 82, 225, 242, 182];

        const instructionData = Buffer.alloc(16);
        Buffer.from(depositDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);

        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: this.BANKS.SOL, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 REPAY INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createRepayInstruction(bankAddress, repayAll) {
        const discriminator = [234, 103, 67, 82, 208, 234, 219, 166];
        const instructionData = Buffer.concat([
            Buffer.from(discriminator),
            Buffer.from([0, 0, 0, 0, 0, 0, 0, 0]),
            Buffer.from([repayAll ? 1 : 0])
        ]);

        const vaultInfo = this.VAULTS.SOL;
        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
            { pubkey: bankAddress, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 END FLASH LOAN (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    createEndFlashLoanInstruction() {
        const discriminator = [105, 124, 201, 106, 153, 2, 8, 156];
        const instructionData = Buffer.from(discriminator);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
     */
    loadALTTablesDirectly() {
        try {
            console.log('🔥 ЗАГРУЗКА ALT ТАБЛИЦ НАПРЯМУЮ ИЗ ФАЙЛА...');

            const fs = require('fs');
            const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));

            console.log(`📊 Загружено из файла: ${fileData.totalTables} ALT таблиц`);

            // 🔥 ПРЕОБРАЗУЕМ ОБЪЕКТ tables В МАССИВ
            const formattedALTs = [];
            let totalAccounts = 0;
            let index = 0;

            for (const [tableName, tableData] of Object.entries(fileData.tables)) {
                if (tableData.address && tableData.addresses) {
                    const formattedALT = {
                        key: new PublicKey(tableData.address),
                        state: {
                            addresses: tableData.addresses.map(addr => new PublicKey(addr))
                        }
                    };
                    formattedALTs.push(formattedALT);
                    totalAccounts += tableData.addresses.length;
                    console.log(`✅ ALT ${index + 1} (${tableName}): ${tableData.addresses.length} адресов`);
                    index++;
                }
            }

            console.log(`📊 Всего аккаунтов: ${totalAccounts}`);
            return formattedALTs;

        } catch (error) {
            console.log(`❌ Ошибка загрузки ALT таблиц: ${error.message}`);
            return []; // ВОЗВРАЩАЕМ ПУСТОЙ МАССИВ ПРИ ОШИБКЕ
        }
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ СОЗДАННЫХ POSITION АДРЕСОВ
     */
    getCreatedPositionAddresses() {
        return this.createdPositions.map(pos => ({
            poolIndex: pos.poolIndex,
            poolAddress: pos.poolAddress,
            positionAddress: pos.keypair.publicKey.toString()
        }));
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ FLASH LOAN С ALT СЖАТИЕМ
     */
    async executeFlashLoan() {
        try {
            console.log('\n🚀 ЗАПУСК FLASH LOAN ДЛЯ СОЗДАНИЯ POSITIONS...');

            // 🔥 ЗАГРУЖАЕМ ALT ТАБЛИЦЫ ДЛЯ СЖАТИЯ
            console.log('🔧 Загрузка ALT таблиц для сжатия...');
            this.altTables = this.loadALTTablesDirectly();

            const instructions = await this.createFlashLoanWithPositions();

            // 🔥 СОЗДАЕМ VERSIONED TRANSACTION С ALT СЖАТИЕМ
            const { TransactionMessage, VersionedTransaction } = require('@solana/web3.js');

            // Получаем recent blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();

            // Создаем сжатое сообщение
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            }).compileToV0Message(this.altTables);

            // Создаем versioned транзакцию
            const versionedTx = new VersionedTransaction(messageV0);

            console.log(`📊 Транзакция сжата с ${this.altTables.length} ALT таблицами`);

            // Собираем ВСЕ уникальные signers
            const uniqueSigners = new Set();
            uniqueSigners.add(this.wallet); // Основной кошелек

            // Добавляем все signers из SDK
            this.allSigners.forEach(signer => {
                if (signer && signer.publicKey) {
                    uniqueSigners.add(signer);
                }
            });

            const signers = Array.from(uniqueSigners);
            console.log(`🔑 Всего signers: ${signers.length}`);
            signers.forEach((signer, i) => {
                console.log(`   ${i}: ${signer.publicKey.toString().slice(0, 8)}...`);
            });

            // Подписываем versioned транзакцию
            versionedTx.sign(signers);

            console.log('📤 Отправка сжатой транзакции...');
            const signature = await this.connection.sendTransaction(versionedTx);

            console.log(`📝 Signature: ${signature}`);
            console.log('⏳ Ожидание подтверждения...');

            // Ждем подтверждения
            await this.connection.confirmTransaction(signature, 'confirmed');

            console.log(`✅ FLASH LOAN ВЫПОЛНЕН! Signature: ${signature}`);
            return signature;
        } catch (error) {
            console.error('❌ Ошибка Flash Loan:', error);
            throw error;
        }
    }
}

module.exports = SimpleFlashLoan;
