# 🚀 RAYDIUM BOT - КОПИЯ СИСТЕМЫ ДЛЯ RAYDIUM ИНТЕГРАЦИИ

## 📁 СТРУКТУРА ПРОЕКТА

Эта папка содержит копию всех необходимых файлов для создания нового бота, интегрированного с Raydium.

### 🔥 ОСНОВНЫЕ ФАЙЛЫ:

#### 🎯 Главный файл запуска:
- `duplicate-instruction-fixer.js` - Основной файл для запуска и тестирования

#### 🔧 Основная система:
- `complete-flash-loan-with-liquidity.js` - Полная система flash loan с ликвидностью
- `low-level-marginfi-integration.js` - Низкоуровневая интеграция с MarginFi
- `meteora-hybrid-implementation.js` - Гибридная реализация Meteora
- `master-transaction-controller.js` - Мастер контроллер транзакций

#### 🎭 Система обфускации:
- `dynamic-obfuscation-manager.js` - Динамический менеджер обфускации
- `custom-obfuscation-manager.js` - Кастомный менеджер обфускации

#### 🔍 Диагностика:
- `transaction-size-diagnostic.js` - Диагностика размера транзакций
- `meteora-swap-diagnostic.js` - Диагностика Meteora swap

#### ⚙️ Конфигурация и утилиты:
- `trading-config.js` - Конфигурация торговли
- `centralized-amount-converter.js` - Централизованный конвертер сумм
- `meteora-bin-cache-manager.js` - Менеджер кэша bin arrays
- `transaction-size-calculator.js` - Калькулятор размера транзакций

#### 📦 Конфигурационные файлы:
- `package.json` - Зависимости Node.js
- `wallet.json` - Конфигурация кошелька
- `.env` - Переменные окружения (если есть)

## 🚀 ЗАПУСК

```bash
# Переход в папку raydium
cd raydium

# Установка зависимостей (если нужно)
npm install

# Запуск основного файла
node duplicate-instruction-fixer.js
```

## 🎯 ЦЕЛЬ ПРОЕКТА

Создание нового бота для работы с Raydium, используя проверенную архитектуру из основного проекта.

### 🔄 ПЛАНИРУЕМЫЕ ИЗМЕНЕНИЯ:

1. **Замена Meteora на Raydium:**
   - Интеграция с Raydium AMM
   - Интеграция с Raydium CLMM
   - Использование Raydium SDK

2. **Адаптация под Raydium:**
   - Изменение адресов программ
   - Адаптация логики swap
   - Настройка пулов Raydium

3. **Сохранение архитектуры:**
   - Система обфускации
   - ALT таблицы
   - Flash loan логика
   - Диагностические инструменты

## 📋 СЛЕДУЮЩИЕ ШАГИ:

1. ✅ Скопированы все необходимые файлы
2. 🔄 Адаптация под Raydium (в процессе)
3. 🔄 Тестирование новой системы
4. 🔄 Оптимизация производительности

## ⚠️ ВАЖНЫЕ ЗАМЕЧАНИЯ:

- Все файлы скопированы из рабочей системы
- Сохранена оригинальная архитектура
- Требуется адаптация под Raydium
- Необходимо обновить конфигурацию

## 🔗 СВЯЗАННЫЕ ФАЙЛЫ:

Основная система находится в корневой папке проекта. Эта папка содержит независимую копию для разработки Raydium интеграции.
