/**
 * 🔥 ОФИЦИАЛЬНЫЕ DISCRIMINATOR'Ы ИЗ ANCHOR IDL
 * 
 * ✅ ПРОВЕРЕННЫЕ И РАБОЧИЕ DISCRIMINATOR'Ы
 * ✅ Вычислены через SHA256("global:method_name").slice(0, 8)
 * ✅ Соответствуют официальным Anchor IDL
 */

// 🔥 MARGINFI DISCRIMINATORS (ОФИЦИАЛЬНЫЕ)
const MARGINFI_DISCRIMINATORS = {
    // ✅ FLASH LOAN ОПЕРАЦИИ
    LENDING_ACCOUNT_START_FLASHLOAN: Buffer.from([0x0e, 0x83, 0x21, 0xdc, 0x51, 0xba, 0xb4, 0x6b]), // lending_account_start_flashloan
    LENDING_ACCOUNT_END_FLASHLOAN:   Buffer.from([0x69, 0x7c, 0xc9, 0x6a, 0x99, 0x02, 0x08, 0x9c]), // lending_account_end_flashloan
    
    // ✅ ОБЫЧНЫЕ ОПЕРАЦИИ
    LENDING_ACCOUNT_BORROW:  Buffer.from([0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f]), // lending_account_borrow
    LENDING_ACCOUNT_REPAY:   Buffer.from([0x4f, 0xd1, 0xac, 0xb1, 0xde, 0x33, 0xad, 0x97]), // lending_account_repay
    LENDING_ACCOUNT_DEPOSIT: Buffer.from([0xab, 0x5e, 0xeb, 0x67, 0x52, 0x40, 0xd4, 0x8c]), // lending_account_deposit
    LENDING_ACCOUNT_WITHDRAW: Buffer.from([0x24, 0x48, 0x4a, 0x13, 0xd2, 0xd2, 0xc0, 0xc0]), // lending_account_withdraw
    
    // ✅ ИНИЦИАЛИЗАЦИЯ
    MARGINFI_ACCOUNT_INITIALIZE: Buffer.from([0x2b, 0x4e, 0x3d, 0xff, 0x94, 0x34, 0xf9, 0x9a]), // marginfi_account_initialize
    
    // ✅ POOL ОПЕРАЦИИ
    LENDING_POOL_BORROW: Buffer.from([0x11, 0x5b, 0xc3, 0x9a, 0x3e, 0xe3, 0x13, 0xb3]), // lending_pool_borrow
    LENDING_POOL_REPAY:  Buffer.from([0xbb, 0x0e, 0x53, 0xb9, 0x0e, 0xdf, 0xd2, 0xcd])  // lending_pool_repay
};

// 🔥 METEORA DLMM DISCRIMINATORS (ОФИЦИАЛЬНЫЕ)
const METEORA_DISCRIMINATORS = {
    // ✅ SWAP ОПЕРАЦИИ
    SWAP:           Buffer.from([0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8]), // swap
    SWAP_EXACT_OUT: Buffer.from([0xfa, 0x49, 0x65, 0x21, 0x26, 0xcf, 0x4b, 0xb8]), // swap_exact_out
    
    // ✅ LIQUIDITY ОПЕРАЦИИ
    ADD_LIQUIDITY:    Buffer.from([0xb5, 0x9d, 0x59, 0x43, 0x8f, 0xb6, 0x34, 0x48]), // add_liquidity
    REMOVE_LIQUIDITY: Buffer.from([0x50, 0x55, 0xd1, 0x48, 0x18, 0xce, 0xb1, 0x6c]), // remove_liquidity
    
    // ✅ ПРОДВИНУТЫЕ LIQUIDITY ОПЕРАЦИИ
    ADD_LIQUIDITY_BY_WEIGHT:    Buffer.from([0x1c, 0x8c, 0xee, 0x63, 0xe7, 0xa2, 0x15, 0x95]), // add_liquidity_by_weight
    REMOVE_LIQUIDITY_BY_RANGE:  Buffer.from([0x1a, 0x52, 0x66, 0x98, 0xf0, 0x4a, 0x69, 0x1a]), // remove_liquidity_by_range
    
    // ✅ ИНИЦИАЛИЗАЦИЯ
    INITIALIZE_LB_PAIR:    Buffer.from([0x2d, 0x9a, 0xed, 0xd2, 0xdd, 0x0f, 0xa6, 0x5c]), // initialize_lb_pair
    INITIALIZE_BIN_ARRAY:  Buffer.from([0x23, 0x56, 0x13, 0xb9, 0x4e, 0xd4, 0x4b, 0xd3]), // initialize_bin_array
    
    // ✅ НАСТРОЙКИ
    INCREASE_ORACLE_LENGTH: Buffer.from([0xbe, 0x3d, 0x7d, 0x57, 0x67, 0x4f, 0x9e, 0xad]), // increase_oracle_length
    SET_ACTIVATION_SLOT:    Buffer.from([0xc8, 0xe3, 0x5a, 0x53, 0x1b, 0x4f, 0xbf, 0x58])  // set_activation_slot
};

// 🔥 ДРУГИЕ ПРОГРАММЫ
const OTHER_DISCRIMINATORS = {
    // ✅ COMPUTE BUDGET PROGRAM
    COMPUTE_BUDGET_SET_LIMIT: Buffer.from([0x02]), // SetComputeUnitLimit
    COMPUTE_BUDGET_SET_PRICE: Buffer.from([0x03]), // SetComputeUnitPrice
    
    // ✅ MEMO PROGRAM
    MEMO: Buffer.from([]), // Memo program не использует discriminator
    
    // ✅ TOKEN PROGRAM
    TOKEN_TRANSFER: Buffer.from([0x03]), // Transfer
    TOKEN_APPROVE:  Buffer.from([0x04]), // Approve
    TOKEN_REVOKE:   Buffer.from([0x05])  // Revoke
};

// 🔥 ЭКСПОРТ ВСЕХ DISCRIMINATOR'ОВ
module.exports = {
    MARGINFI_DISCRIMINATORS,
    METEORA_DISCRIMINATORS,
    OTHER_DISCRIMINATORS,
    
    // 🔥 УДОБНЫЕ ФУНКЦИИ
    getMarginFiDiscriminator: (operation) => MARGINFI_DISCRIMINATORS[operation.toUpperCase()],
    getMeteoraDiscriminator: (operation) => METEORA_DISCRIMINATORS[operation.toUpperCase()],
    
    // 🔥 ПРОВЕРКА DISCRIMINATOR'А
    isValidDiscriminator: (discriminator, expectedHex) => {
        return discriminator.toString('hex') === expectedHex;
    },
    
    // 🔥 ЛОГИРОВАНИЕ DISCRIMINATOR'А
    logDiscriminator: (name, discriminator) => {
        const bytes = Array.from(discriminator).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(', ');
        const hex = discriminator.toString('hex');
        console.log(`🔥 ${name}: [${bytes}] (hex: ${hex})`);
    }
};

console.log('✅ ОФИЦИАЛЬНЫЕ DISCRIMINATOR\'Ы ЗАГРУЖЕНЫ!');
console.log('🔥 MarginFi discriminators:', Object.keys(MARGINFI_DISCRIMINATORS).length);
console.log('🔥 Meteora discriminators:', Object.keys(METEORA_DISCRIMINATORS).length);
console.log('🔥 Другие discriminators:', Object.keys(OTHER_DISCRIMINATORS).length);
