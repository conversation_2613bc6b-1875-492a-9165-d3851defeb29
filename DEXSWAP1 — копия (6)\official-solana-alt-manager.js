/**
 * 🔥 ОФИЦИАЛЬНЫЙ SOLANA ADDRESS LOOKUP TABLE MANAGER
 * Создан строго по официальной документации Solana
 * 
 * ОФИЦИАЛЬНЫЕ ИСТОЧНИКИ:
 * 1. https://solana.com/developers/guides/advanced/lookup-tables
 * 2. https://medium.com/coinmonks/using-address-lookup-tables-in-solana-a-comprehensive-guide-0d506fe6f70f
 * 3. https://www.quicknode.com/guides/solana-development/accounts-and-data/how-to-use-lookup-tables-on-solana
 * 
 * ОФИЦИАЛЬНЫЙ МЕТОД ЗАГРУЗКИ:
 * const lookupTableAccount = (await connection.getAddressLookupTable(lookupTableAddress)).value;
 * 
 * ПОЛНЫЙ ЦИКЛ ИНФОРМАЦИИ:
 * - Откуда получает ALT адреса (Jupiter API, MarginFi, Кастомная)
 * - Как загружает с блокчейна (connection.getAddressLookupTable)
 * - Как сериализует и хранит данные
 * - Полная диагностика всех процессов
 * - Статистика сжатия и экономии байт
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class OfficialSolanaALTManager {
  constructor(connection) {
    this.connection = connection;
    
    // Кэш загруженных ALT таблиц
    this.loadedTables = new Map(); // address -> { account, source, loadedAt, metadata }
    
    // Источники ALT адресов
    this.altSources = new Map(); // address -> source
    
    // Кастомная ALT адрес (создана create-universal-alt.js)
    this.CUSTOM_ALT_ADDRESS = 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe';
    
    // Детальная статистика
    this.stats = {
      // Общие запросы
      totalRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
      cacheHits: 0,
      
      // По источникам
      sources: {
        JUPITER: { count: 0, addresses: 0, compressionSavings: 0 },
        MARGINFI: { count: 0, addresses: 0, compressionSavings: 0 },
        CUSTOM: { count: 0, addresses: 0, compressionSavings: 0 }
      },
      
      // Общая статистика
      totalAddresses: 0,
      totalCompressionSavings: 0,
      averageAddressesPerTable: 0
    };
    
    console.log(`🔥 ОФИЦИАЛЬНЫЙ SOLANA ALT MANAGER ИНИЦИАЛИЗИРОВАН`);
    console.log(`   📡 RPC Endpoint: ${connection.rpcEndpoint}`);
    console.log(`   🎯 Кастомная ALT: ${this.CUSTOM_ALT_ADDRESS}`);
    console.log(`   💾 Кэш: Пустой (0 таблиц)`);
    console.log(`   📋 Источники: Jupiter, MarginFi, Кастомная`);
  }

  /**
   * 🎯 ИЗВЛЕЧЕНИЕ ALT АДРЕСОВ ИЗ JUPITER API ОТВЕТА
   * Официальный метод получения ALT из Jupiter swap-instructions
   */
  extractJupiterALTAddresses(jupiterResponse) {
    console.log(`🎯 ИЗВЛЕЧЕНИЕ JUPITER ALT АДРЕСОВ`);
    console.log(`   📡 Источник: Jupiter swap-instructions API`);
    console.log(`   🔍 Поиск в полях: addressLookupTableAddresses, lookupTables`);
    
    const jupiterALT = [];
    
    // Официальное поле Jupiter API
    if (jupiterResponse.addressLookupTableAddresses && Array.isArray(jupiterResponse.addressLookupTableAddresses)) {
      jupiterALT.push(...jupiterResponse.addressLookupTableAddresses);
      console.log(`   ✅ addressLookupTableAddresses: ${jupiterResponse.addressLookupTableAddresses.length} ALT`);
    }
    
    // Дополнительное поле (если есть)
    if (jupiterResponse.lookupTables && Array.isArray(jupiterResponse.lookupTables)) {
      jupiterALT.push(...jupiterResponse.lookupTables);
      console.log(`   ✅ lookupTables: ${jupiterResponse.lookupTables.length} ALT`);
    }
    
    // Убираем дубликаты
    const uniqueJupiterALT = [...new Set(jupiterALT)];
    
    console.log(`   📊 ИТОГО Jupiter ALT: ${uniqueJupiterALT.length} (уникальных)`);
    uniqueJupiterALT.forEach((addr, index) => {
      console.log(`      ${index + 1}. ${addr}`);
      this.altSources.set(addr, 'JUPITER');
    });
    
    return uniqueJupiterALT;
  }

  /**
   * 🏦 ИЗВЛЕЧЕНИЕ ALT АДРЕСОВ ИЗ MARGINFI ДАННЫХ
   * Получение ALT из MarginFi flash loan инструкций
   */
  extractMarginFiALTAddresses(marginfiData) {
    console.log(`🏦 ИЗВЛЕЧЕНИЕ MARGINFI ALT АДРЕСОВ`);
    console.log(`   📡 Источник: MarginFi flash loan instructions`);
    
    const marginfiALT = [];
    
    // Проверяем addressLookupTableAccounts
    if (marginfiData.addressLookupTableAccounts && Array.isArray(marginfiData.addressLookupTableAccounts)) {
      marginfiData.addressLookupTableAccounts.forEach(alt => {
        if (alt.key) {
          const altAddress = alt.key.toString();
          marginfiALT.push(altAddress);
          this.altSources.set(altAddress, 'MARGINFI');
        }
      });
      console.log(`   ✅ addressLookupTableAccounts: ${marginfiData.addressLookupTableAccounts.length} ALT`);
    }
    
    // Проверяем lookupTables
    if (marginfiData.lookupTables && Array.isArray(marginfiData.lookupTables)) {
      marginfiData.lookupTables.forEach(addr => {
        marginfiALT.push(addr);
        this.altSources.set(addr, 'MARGINFI');
      });
      console.log(`   ✅ lookupTables: ${marginfiData.lookupTables.length} ALT`);
    }
    
    console.log(`   📊 ИТОГО MarginFi ALT: ${marginfiALT.length}`);
    marginfiALT.forEach((addr, index) => {
      console.log(`      ${index + 1}. ${addr}`);
    });
    
    return marginfiALT;
  }

  /**
   * 🔥 ОФИЦИАЛЬНАЯ ЗАГРУЗКА ОДНОЙ ALT ТАБЛИЦЫ
   * Строго по документации Solana: connection.getAddressLookupTable()
   */
  async loadSingleALT(altAddress, source = 'UNKNOWN') {
    console.log(`🔥 ЗАГРУЗКА ALT ТАБЛИЦЫ: ${altAddress}`);
    console.log(`   📡 Источник: ${source}`);
    console.log(`   🔧 Метод: connection.getAddressLookupTable() (ОФИЦИАЛЬНЫЙ SOLANA)`);
    
    this.stats.totalRequests++;
    
    try {
      // Проверяем кэш
      if (this.loadedTables.has(altAddress)) {
        console.log(`   💾 НАЙДЕНА В КЭШЕ`);
        this.stats.cacheHits++;
        const cachedInfo = this.loadedTables.get(altAddress);
        console.log(`   ⏰ Загружена: ${cachedInfo.loadedAt.toLocaleTimeString()}`);
        console.log(`   📋 Адресов: ${cachedInfo.metadata.addressCount}`);
        return cachedInfo.account;
      }
      
      // Создаем PublicKey из строки
      const altPublicKey = new PublicKey(altAddress);
      console.log(`   🔑 PublicKey создан: ${altPublicKey.toString()}`);
      
      // ОФИЦИАЛЬНЫЙ МЕТОД ЗАГРУЗКИ ПО ДОКУМЕНТАЦИИ SOLANA
      console.log(`   📡 Выполняем connection.getAddressLookupTable()...`);
      const lookupTableResponse = await this.connection.getAddressLookupTable(altPublicKey);
      
      // Проверяем результат
      if (!lookupTableResponse.value) {
        console.log(`   ❌ ALT НЕ НАЙДЕНА В БЛОКЧЕЙНЕ: ${altAddress}`);
        this.stats.failedLoads++;
        return null;
      }
      
      const lookupTableAccount = lookupTableResponse.value;
      const addressCount = lookupTableAccount.state.addresses.length;
      const compressionSavings = addressCount * 32; // 32 байта на адрес
      
      console.log(`   ✅ ALT УСПЕШНО ЗАГРУЖЕНА`);
      console.log(`   📍 Адрес таблицы: ${lookupTableAccount.key.toString()}`);
      console.log(`   📋 Количество адресов: ${addressCount}`);
      console.log(`   👤 Авторитет: ${lookupTableAccount.state.authority?.toString() || 'Нет'}`);
      console.log(`   🔢 Слот деактивации: ${lookupTableAccount.state.deactivationSlot}`);
      console.log(`   💾 Экономия сжатия: ${compressionSavings} байт`);
      
      // Показываем первые 3 адреса для диагностики
      console.log(`   🔍 ПЕРВЫЕ 3 АДРЕСА В ТАБЛИЦЕ:`);
      lookupTableAccount.state.addresses.slice(0, 3).forEach((addr, index) => {
        const addrStr = addr.toString();
        console.log(`      ${index + 1}. ${addrStr.slice(0, 8)}...${addrStr.slice(-8)}`);
      });
      
      // Создаем метаданные
      const metadata = {
        addressCount: addressCount,
        compressionSavings: compressionSavings,
        authority: lookupTableAccount.state.authority?.toString() || null,
        deactivationSlot: lookupTableAccount.state.deactivationSlot,
        firstThreeAddresses: lookupTableAccount.state.addresses.slice(0, 3).map(addr => addr.toString())
      };
      
      // Сохраняем в кэш с полной информацией
      const altInfo = {
        account: lookupTableAccount,
        source: source,
        loadedAt: new Date(),
        metadata: metadata
      };
      
      this.loadedTables.set(altAddress, altInfo);
      
      // Обновляем статистику по источникам
      if (this.stats.sources[source]) {
        this.stats.sources[source].count++;
        this.stats.sources[source].addresses += addressCount;
        this.stats.sources[source].compressionSavings += compressionSavings;
      }
      
      // Обновляем общую статистику
      this.stats.successfulLoads++;
      this.stats.totalAddresses += addressCount;
      this.stats.totalCompressionSavings += compressionSavings;
      this.stats.averageAddressesPerTable = this.stats.totalAddresses / this.stats.successfulLoads;
      
      console.log(`   💾 ALT СОХРАНЕНА В КЭШ (${source})`);
      console.log(`   📊 Общая статистика: ${this.stats.successfulLoads} таблиц, ${this.stats.totalAddresses} адресов`);
      
      return lookupTableAccount;
      
    } catch (error) {
      console.log(`   ❌ ОШИБКА ЗАГРУЗКИ ALT: ${error.message}`);
      console.log(`   📋 Stack trace: ${error.stack}`);
      this.stats.failedLoads++;
      return null;
    }
  }

  /**
   * 🚀 ЗАГРУЗКА ВСЕХ ALT ДЛЯ АРБИТРАЖА
   * Jupiter ALT + MarginFi ALT + Кастомная ALT
   */
  async loadArbitrageALT(jupiterResponse, marginfiData = null) {
    console.log(`🚀 ЗАГРУЗКА ВСЕХ ALT ДЛЯ АРБИТРАЖА`);
    console.log(`═══════════════════════════════════════════════════════════════`);
    
    const allALTAddresses = [];
    
    // 1. Извлекаем Jupiter ALT
    console.log(`\n🎯 ШАГ 1: ИЗВЛЕЧЕНИЕ JUPITER ALT`);
    const jupiterALT = this.extractJupiterALTAddresses(jupiterResponse);
    allALTAddresses.push(...jupiterALT.map(addr => ({ address: addr, source: 'JUPITER' })));
    
    // 2. Извлекаем MarginFi ALT (если есть)
    if (marginfiData) {
      console.log(`\n🏦 ШАГ 2: ИЗВЛЕЧЕНИЕ MARGINFI ALT`);
      const marginfiALT = this.extractMarginFiALTAddresses(marginfiData);
      allALTAddresses.push(...marginfiALT.map(addr => ({ address: addr, source: 'MARGINFI' })));
    }
    
    // 3. Добавляем кастомную ALT
    console.log(`\n🔥 ШАГ 3: ДОБАВЛЕНИЕ КАСТОМНОЙ ALT`);
    allALTAddresses.push({ address: this.CUSTOM_ALT_ADDRESS, source: 'CUSTOM' });
    console.log(`   ✅ Кастомная ALT добавлена: ${this.CUSTOM_ALT_ADDRESS}`);
    
    console.log(`\n📊 ВСЕГО ALT ДЛЯ ЗАГРУЗКИ: ${allALTAddresses.length}`);
    allALTAddresses.forEach(({ address, source }, index) => {
      console.log(`   ${index + 1}. ${address} (${source})`);
    });
    
    // 4. Загружаем все ALT параллельно
    console.log(`\n🔄 ШАГ 4: ПАРАЛЛЕЛЬНАЯ ЗАГРУЗКА ALT`);
    const loadPromises = allALTAddresses.map(({ address, source }) => 
      this.loadSingleALT(address, source)
    );
    
    const results = await Promise.allSettled(loadPromises);
    
    // 5. Собираем успешно загруженные ALT
    const loadedALTAccounts = [];
    results.forEach((result, index) => {
      const { address, source } = allALTAddresses[index];
      if (result.status === 'fulfilled' && result.value) {
        loadedALTAccounts.push(result.value);
        console.log(`   ✅ ALT ${index + 1}: ЗАГРУЖЕНА (${source})`);
      } else {
        console.log(`   ❌ ALT ${index + 1}: ОШИБКА (${source}) - ${address}`);
      }
    });
    
    // 6. Финальная статистика
    console.log(`\n🎯 ФИНАЛЬНЫЙ РЕЗУЛЬТАТ ЗАГРУЗКИ ALT:`);
    console.log(`   ✅ Успешно загружено: ${loadedALTAccounts.length}/${allALTAddresses.length}`);
    console.log(`   🎯 Jupiter ALT: ${this.stats.sources.JUPITER.count} (${this.stats.sources.JUPITER.addresses} адресов)`);
    console.log(`   🏦 MarginFi ALT: ${this.stats.sources.MARGINFI.count} (${this.stats.sources.MARGINFI.addresses} адресов)`);
    console.log(`   🔥 Кастомная ALT: ${this.stats.sources.CUSTOM.count} (${this.stats.sources.CUSTOM.addresses} адресов)`);
    console.log(`   💾 Общая экономия: ${this.stats.totalCompressionSavings} байт (${(this.stats.totalCompressionSavings / 1024).toFixed(2)} KB)`);
    console.log(`   📊 Среднее адресов на таблицу: ${this.stats.averageAddressesPerTable.toFixed(1)}`);
    
    return loadedALTAccounts;
  }

  /**
   * 🎯 СОВМЕСТИМОСТЬ: loadJupiterALT
   * Метод для совместимости с существующим кодом
   */
  async loadJupiterALT(altAddresses) {
    console.log(`🎯 ЗАГРУЗКА JUPITER ALT (МЕТОД СОВМЕСТИМОСТИ)`);
    console.log(`   📋 Адресов для загрузки: ${altAddresses.length}`);
    
    // Добавляем кастомную ALT если её нет в списке
    if (!altAddresses.includes(this.CUSTOM_ALT_ADDRESS)) {
      altAddresses.push(this.CUSTOM_ALT_ADDRESS);
      console.log(`   🔥 ДОБАВЛЕНА КАСТОМНАЯ ALT: ${this.CUSTOM_ALT_ADDRESS}`);
    }
    
    // Загружаем все ALT
    const loadPromises = altAddresses.map((addr, index) => {
      const source = addr === this.CUSTOM_ALT_ADDRESS ? 'CUSTOM' : 'JUPITER';
      return this.loadSingleALT(addr, source);
    });
    
    const results = await Promise.allSettled(loadPromises);
    
    // Собираем успешно загруженные
    const loadedTables = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        loadedTables.push(result.value);
      }
    });
    
    console.log(`   ✅ Загружено ALT: ${loadedTables.length}/${altAddresses.length}`);
    return loadedTables;
  }

  /**
   * 📊 ПОЛНАЯ ДИАГНОСТИКА ALT MANAGER
   * Показывает всю информацию о загруженных ALT
   */
  diagnose() {
    console.log(`📊 ПОЛНАЯ ДИАГНОСТИКА ОФИЦИАЛЬНОГО SOLANA ALT MANAGER`);
    console.log(`═══════════════════════════════════════════════════════════════`);

    // Общая статистика
    console.log(`🔥 ОБЩАЯ СТАТИСТИКА:`);
    console.log(`   📡 Всего запросов: ${this.stats.totalRequests}`);
    console.log(`   ✅ Успешных загрузок: ${this.stats.successfulLoads}`);
    console.log(`   ❌ Неудачных загрузок: ${this.stats.failedLoads}`);
    console.log(`   💾 Попаданий в кэш: ${this.stats.cacheHits}`);
    console.log(`   📋 Размер кэша: ${this.loadedTables.size} таблиц`);
    console.log(`   🎯 Успешность: ${this.stats.totalRequests > 0 ? ((this.stats.successfulLoads / this.stats.totalRequests) * 100).toFixed(1) : 0}%`);

    // Статистика по источникам
    console.log(`\n📈 СТАТИСТИКА ПО ИСТОЧНИКАМ:`);
    Object.entries(this.stats.sources).forEach(([source, data]) => {
      const icon = source === 'JUPITER' ? '🎯' : source === 'MARGINFI' ? '🏦' : '🔥';
      console.log(`   ${icon} ${source}:`);
      console.log(`      📊 Таблиц: ${data.count}`);
      console.log(`      🔑 Адресов: ${data.addresses}`);
      console.log(`      💾 Экономия: ${data.compressionSavings} байт`);
      console.log(`      📊 Среднее адресов: ${data.count > 0 ? (data.addresses / data.count).toFixed(1) : 0}`);
    });

    // Общая статистика адресов
    console.log(`\n🔑 СТАТИСТИКА АДРЕСОВ:`);
    console.log(`   📋 Всего адресов: ${this.stats.totalAddresses}`);
    console.log(`   📊 Среднее на таблицу: ${this.stats.averageAddressesPerTable.toFixed(1)}`);
    console.log(`   💾 Общая экономия: ${this.stats.totalCompressionSavings} байт (${(this.stats.totalCompressionSavings / 1024).toFixed(2)} KB)`);
    console.log(`   🎯 Ожидаемый размер транзакции: ~${1232 - (this.stats.totalCompressionSavings * 0.8)} байт`);

    // Детальная информация о загруженных ALT
    console.log(`\n🔍 ДЕТАЛЬНАЯ ИНФОРМАЦИЯ О ЗАГРУЖЕННЫХ ALT:`);
    if (this.loadedTables.size === 0) {
      console.log(`   📭 Нет загруженных ALT таблиц`);
    } else {
      let index = 1;
      for (const [address, info] of this.loadedTables) {
        const icon = info.source === 'JUPITER' ? '🎯' : info.source === 'MARGINFI' ? '🏦' : '🔥';
        console.log(`   ${index}. ${icon} ${address}`);
        console.log(`      📡 Источник: ${info.source}`);
        console.log(`      📋 Адресов: ${info.metadata.addressCount}`);
        console.log(`      👤 Авторитет: ${info.metadata.authority || 'Нет'}`);
        console.log(`      💾 Экономия: ${info.metadata.compressionSavings} байт`);
        console.log(`      ⏰ Загружена: ${info.loadedAt.toLocaleString()}`);
        console.log(`      🔍 Первые адреса: ${info.metadata.firstThreeAddresses.slice(0, 2).map(addr => addr.slice(0, 8) + '...').join(', ')}`);
        index++;
      }
    }

    // Проверка кастомной ALT
    console.log(`\n🔥 ПРОВЕРКА КАСТОМНОЙ ALT:`);
    const customALTInfo = this.loadedTables.get(this.CUSTOM_ALT_ADDRESS);
    if (customALTInfo) {
      console.log(`   ✅ КАСТОМНАЯ ALT ЗАГРУЖЕНА`);
      console.log(`   📋 Адресов: ${customALTInfo.metadata.addressCount}`);
      console.log(`   💾 Экономия: ${customALTInfo.metadata.compressionSavings} байт`);
    } else {
      console.log(`   ❌ КАСТОМНАЯ ALT НЕ ЗАГРУЖЕНА`);
    }

    return {
      ...this.stats,
      cacheSize: this.loadedTables.size,
      customALTLoaded: !!customALTInfo,
      loadedAddresses: Array.from(this.loadedTables.keys())
    };
  }

  /**
   * 🧹 ОЧИСТКА КЭША И СБРОС СТАТИСТИКИ
   */
  clearCache() {
    console.log(`🧹 ОЧИСТКА КЭША ALT MANAGER`);

    const clearedTablesCount = this.loadedTables.size;
    const clearedSourcesCount = this.altSources.size;

    // Очищаем кэш
    this.loadedTables.clear();
    this.altSources.clear();

    // Сбрасываем статистику
    this.stats = {
      totalRequests: 0,
      successfulLoads: 0,
      failedLoads: 0,
      cacheHits: 0,
      sources: {
        JUPITER: { count: 0, addresses: 0, compressionSavings: 0 },
        MARGINFI: { count: 0, addresses: 0, compressionSavings: 0 },
        CUSTOM: { count: 0, addresses: 0, compressionSavings: 0 }
      },
      totalAddresses: 0,
      totalCompressionSavings: 0,
      averageAddressesPerTable: 0
    };

    console.log(`   ✅ Очищено ${clearedTablesCount} ALT таблиц из кэша`);
    console.log(`   ✅ Очищено ${clearedSourcesCount} источников ALT`);
    console.log(`   📊 Статистика сброшена`);
    console.log(`   💾 Кэш пуст, готов к новой загрузке`);
  }

  /**
   * 📋 ПОЛУЧЕНИЕ КРАТКОЙ СТАТИСТИКИ
   */
  getStats() {
    return {
      totalRequests: this.stats.totalRequests,
      successfulLoads: this.stats.successfulLoads,
      failedLoads: this.stats.failedLoads,
      cacheHits: this.stats.cacheHits,
      cacheSize: this.loadedTables.size,
      totalAddresses: this.stats.totalAddresses,
      totalCompressionSavings: this.stats.totalCompressionSavings,
      averageAddressesPerTable: this.stats.averageAddressesPerTable,
      sources: { ...this.stats.sources },
      customALTLoaded: this.loadedTables.has(this.CUSTOM_ALT_ADDRESS)
    };
  }

  /**
   * 🔍 ПРОВЕРКА СТАТУСА КАСТОМНОЙ ALT
   */
  checkCustomALT() {
    console.log(`🔍 ПРОВЕРКА СТАТУСА КАСТОМНОЙ ALT`);
    console.log(`   📍 Адрес: ${this.CUSTOM_ALT_ADDRESS}`);

    const customALTInfo = this.loadedTables.get(this.CUSTOM_ALT_ADDRESS);
    if (customALTInfo) {
      console.log(`   ✅ СТАТУС: ЗАГРУЖЕНА`);
      console.log(`   📋 Адресов: ${customALTInfo.metadata.addressCount}`);
      console.log(`   💾 Экономия: ${customALTInfo.metadata.compressionSavings} байт`);
      console.log(`   ⏰ Загружена: ${customALTInfo.loadedAt.toLocaleString()}`);
      return true;
    } else {
      console.log(`   ❌ СТАТУС: НЕ ЗАГРУЖЕНА`);
      return false;
    }
  }
}

module.exports = OfficialSolanaALTManager;
