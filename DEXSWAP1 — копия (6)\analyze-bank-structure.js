const { Connection, PublicKey } = require('@solana/web3.js');

async function analyzeBankStructure() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const usdcBank = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
    
    console.log('🔍 АНАЛИЗ СТРУКТУРЫ USDC БАНКА...');
    
    const accountInfo = await connection.getAccountInfo(usdcBank);
    
    if (!accountInfo) {
        console.log('❌ Bank не найден');
        return;
    }
    
    console.log('🔍 USDC BANK INFO:');
    console.log('   Address:', usdcBank.toString());
    console.log('   Data length:', accountInfo.data.length);
    console.log('   Owner:', accountInfo.owner.toString());
    
    // Анализируем первые 200 байт
    console.log('\n🔍 ПЕРВЫЕ 200 БАЙТ (HEX):');
    const hexData = accountInfo.data.slice(0, 200).toString('hex');
    for (let i = 0; i < hexData.length; i += 64) {
        const offset = i / 2;
        const line = hexData.slice(i, i + 64);
        console.log(`   ${offset.toString().padStart(3, '0')}: ${line}`);
    }
    
    // Ищем возможные PublicKey (32 байта)
    console.log('\n🔍 ВОЗМОЖНЫЕ PUBLICKEY АДРЕСА:');
    for (let offset = 0; offset < 200; offset += 8) {
        try {
            const keyBytes = accountInfo.data.slice(offset, offset + 32);
            const pubkey = new PublicKey(keyBytes);
            const keyStr = pubkey.toString();
            
            // Проверяем что это валидный ключ (не все нули)
            if (!keyStr.startsWith('11111111111111111111111111111111')) {
                console.log(`   Offset ${offset}: ${keyStr}`);
                
                // Проверяем существует ли этот аккаунт
                try {
                    const testAccount = await connection.getAccountInfo(pubkey);
                    if (testAccount) {
                        console.log(`     ✅ СУЩЕСТВУЕТ! Owner: ${testAccount.owner.toString()}`);
                        
                        // Проверяем это Token Account?
                        if (testAccount.owner.toString() === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                            console.log(`     🪙 TOKEN ACCOUNT! Data length: ${testAccount.data.length}`);
                        }
                    }
                } catch (e) {
                    // Игнорируем ошибки проверки
                }
            }
        } catch (e) {
            // Игнорируем невалидные ключи
        }
    }
    
    // Проверим известные адреса
    console.log('\n🔍 ПРОВЕРКА ИЗВЕСТНЫХ АДРЕСОВ:');
    const knownAddresses = [
        { name: 'USDC Mint', address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' },
        { name: 'Token Program', address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' },
        { name: 'MarginFi Program', address: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA' },
        { name: 'Old Vault', address: '7jaiZR5Sk8hdYN9MxTpczTcwbWpb5WEoxSANuUwveuat' },
        { name: 'New Vault', address: 'RHvmTfRooBgugCTMS85Exw33SMhqoZ4jjZn8aaShwoP' }
    ];
    
    for (const known of knownAddresses) {
        const keyBytes = new PublicKey(known.address).toBytes();
        const hexKey = Buffer.from(keyBytes).toString('hex');
        
        // Ищем этот ключ в данных банка
        const bankHex = accountInfo.data.toString('hex');
        const index = bankHex.indexOf(hexKey);
        
        if (index !== -1) {
            const offset = index / 2;
            console.log(`   ${known.name}: найден на offset ${offset}`);
        } else {
            console.log(`   ${known.name}: НЕ НАЙДЕН`);
        }
    }
}

analyzeBankStructure().catch(console.error);
