const { Connection, PublicKey } = require('@solana/web3.js');

async function getTransactionData() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    // Успешная транзакция с addLiquidityByStrategy2
    const signature = 'x6eSZaFrt79QZfj1fddDLnfDbb4UnnPA9kus4hQTSC4Epz66M4yK9pCv68pVzgLE7QLzvnCBAiLC5rK8esXFkJP';
    
    try {
        console.log('🔍 Получаем данные транзакции...');
        
        const tx = await connection.getTransaction(signature, {
            encoding: 'base64',
            commitment: 'confirmed',
            maxSupportedTransactionVersion: 0
        });
        
        if (!tx) {
            console.error('❌ Транзакция не найдена');
            return;
        }
        
        console.log('✅ Транзакция найдена!');
        console.log('📋 Количество инструкций:', tx.transaction.message.instructions.length);
        
        // Ищем инструкцию addLiquidityByStrategy2 (должна быть #5, но проверим все)
        const METEORA_PROGRAM = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
        
        tx.transaction.message.instructions.forEach((instruction, index) => {
            const programId = tx.transaction.message.accountKeys[instruction.programIdIndex];

            if (programId === METEORA_PROGRAM) {
                console.log(`\n🔥 METEORA ИНСТРУКЦИЯ #${index}:`);
                console.log('Program ID:', programId);
                console.log('Data (base64):', instruction.data);

                // Декодируем base64 в Buffer
                const dataBuffer = Buffer.from(instruction.data, 'base64');
                console.log('Data (hex):', dataBuffer.toString('hex'));
                console.log('Data (bytes):', Array.from(dataBuffer));

                // Первые 8 байт - это discriminator
                const discriminator = dataBuffer.slice(0, 8);
                console.log('🎯 DISCRIMINATOR:', Array.from(discriminator));
                console.log('Discriminator (hex):', discriminator.toString('hex'));

                // Определяем тип инструкции по размеру данных
                if (dataBuffer.length > 80) {
                    console.log('📋 ТИП: AddLiquidityByStrategy2 (большие данные)');
                } else if (dataBuffer.length > 20) {
                    console.log('📋 ТИП: Swap2 (средние данные)');

                    // Парсим данные Swap2
                    if (dataBuffer.length >= 24) {
                        const amountIn = dataBuffer.readBigUInt64LE(8);
                        const minAmountOut = dataBuffer.readBigUInt64LE(16);
                        console.log(`   Amount In: ${amountIn}`);
                        console.log(`   Min Amount Out: ${minAmountOut}`);
                    }
                } else {
                    console.log('📋 ТИП: Другая инструкция');
                }

                console.log('Accounts:', instruction.accounts.length);
                instruction.accounts.forEach((accountIndex, i) => {
                    const account = tx.transaction.message.accountKeys[accountIndex];
                    console.log(`  Account ${i}: ${account}`);
                });
            }
        });
        
    } catch (error) {
        console.error('❌ Ошибка:', error.message);
    }
}

getTransactionData();
