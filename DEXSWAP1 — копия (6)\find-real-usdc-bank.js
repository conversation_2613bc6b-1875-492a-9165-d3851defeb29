/**
 * 🔍 ПОИСК ПРАВИЛЬНОГО USDC БАНКА MARGINFI
 * 
 * Проблема: Мы используем 2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB (WSOL банк) для USDC
 * Решение: Найти правильный USDC банк через официальный SDK
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const { Keypair } = require('@solana/web3.js');

async function findRealUsdcBank() {
    try {
        console.log('🔍 ПОИСК ПРАВИЛЬНОГО USDC БАНКА MARGINFI...\n');
        
        // Подключение к Solana
        const connection = new Connection('https://solana-mainnet.g.alchemy.com/v2/alch-demo', 'confirmed');
        
        // Создаем временный кошелек для анализа
        const tempKeypair = Keypair.generate();
        const wallet = new NodeWallet(tempKeypair);
        
        // Инициализируем MarginFi клиент
        console.log('🚀 Инициализируем MarginFi клиент...');
        const config = getConfig("production");
        const marginfiClient = await MarginfiClient.fetch(config, wallet, connection, {
            readOnly: true
        });
        console.log('✅ MarginFi клиент инициализирован');
        
        // Получаем все банки
        console.log('🏦 Получаем все банки...');
        const banks = marginfiClient.banks;
        console.log(`✅ Найдено ${banks.size} банков`);
        
        // Ищем USDC банк
        console.log('\n🔍 ПОИСК USDC БАНКА:');
        const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        let usdcBank = null;
        let wsolBank = null;
        
        for (const [bankAddress, bank] of banks) {
            const mintAddress = bank.mint.toString();
            const symbol = bank.tokenSymbol || 'UNKNOWN';
            
            console.log(`   Банк: ${bankAddress.toString()}`);
            console.log(`      Mint: ${mintAddress}`);
            console.log(`      Symbol: ${symbol}`);
            
            if (mintAddress === usdcMint.toString()) {
                usdcBank = { address: bankAddress, bank };
                console.log(`      🎯 НАЙДЕН USDC БАНК!`);
            }
            
            if (mintAddress === 'So11111111111111111111111111111111111111112') {
                wsolBank = { address: bankAddress, bank };
                console.log(`      🔥 НАЙДЕН WSOL БАНК!`);
            }
            
            console.log('');
        }
        
        console.log('\n🎯 РЕЗУЛЬТАТЫ ПОИСКА:');
        
        if (usdcBank) {
            console.log(`✅ ПРАВИЛЬНЫЙ USDC БАНК: ${usdcBank.address.toString()}`);
            console.log(`   Mint: ${usdcBank.bank.mint.toString()}`);
            console.log(`   Symbol: ${usdcBank.bank.tokenSymbol}`);
        } else {
            console.log('❌ USDC банк не найден!');
        }
        
        if (wsolBank) {
            console.log(`🔥 WSOL БАНК: ${wsolBank.address.toString()}`);
            console.log(`   Mint: ${wsolBank.bank.mint.toString()}`);
            console.log(`   Symbol: ${wsolBank.bank.tokenSymbol}`);
        }
        
        // Проверяем наш текущий "USDC" банк
        console.log('\n🔍 ПРОВЕРКА НАШЕГО ТЕКУЩЕГО "USDC" БАНКА:');
        const ourCurrentUsdcBank = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
        
        if (banks.has(ourCurrentUsdcBank)) {
            const bank = banks.get(ourCurrentUsdcBank);
            console.log(`❌ НАШ "USDC" БАНК: ${ourCurrentUsdcBank.toString()}`);
            console.log(`   Реальный Mint: ${bank.mint.toString()}`);
            console.log(`   Реальный Symbol: ${bank.tokenSymbol}`);
            
            if (bank.mint.toString() === 'So11111111111111111111111111111111111111112') {
                console.log('🔥 ПОДТВЕРЖДЕНО: Это WSOL банк, а не USDC!');
            }
        }
        
        console.log('\n🎉 ПОИСК ЗАВЕРШЕН!');
        
        if (usdcBank && wsolBank) {
            console.log('\n📋 ИСПРАВЛЕНИЕ ДЛЯ КОДА:');
            console.log('this.BANKS = {');
            console.log(`    USDC: new PublicKey('${usdcBank.address.toString()}'), // ✅ ПРАВИЛЬНЫЙ USDC БАНК`);
            console.log(`    SOL: new PublicKey('${wsolBank.address.toString()}'),  // ✅ ПРАВИЛЬНЫЙ WSOL БАНК`);
            console.log('};');
        }
        
    } catch (error) {
        console.error(`❌ Ошибка поиска: ${error.message}`);
        console.error(`   Стек: ${error.stack}`);
    }
}

// Запускаем поиск
findRealUsdcBank();
