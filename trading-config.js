/**
 * 🎯 ЦЕНТРАЛИЗОВАННЫЙ КОНФИГ ТОРГОВОЙ СИСТЕМЫ
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🔧 НАСТРОЙКИ: Все параметры торговли в одном месте
 * 💰 РАСЧЕТЫ: Единые функции расчета прибыли
 * 🛡️ БЕЗОПАСНОСТЬ: Лимиты и защита от потерь
 * 🎯 ОБНОВЛЕНО: Точные комиссии из логов + новые требования
 * 🔥 РАЗМЕРЫ: Единый модуль расчета размера транзакций
 * ═══════════════════════════════════════════════════════════════════════════════
 */

// 🔥 ИМПОРТ ЕДИНОГО МОДУЛЯ РАСЧЕТА РАЗМЕРА ТРАНЗАКЦИЙ
const {
  SOLANA_LIMITS,
  ALT_CONFIG,
  calculateFlashLoanArbitrageSize,
  calculateJupiterSwapSize,
  logTransactionSizeBreakdown
} = require('./src/transaction-size-calculator');

// 🎯 ЦЕНТРАЛИЗОВАННЫЕ НАСТРОЙКИ ТОРГОВЛИ
const TRADING_CONFIG = {
  // 🌐 RPC ENDPOINTS (БЕЗ DEMO КЛЮЧЕЙ!)
  RPC_ENDPOINTS: {
    PRIMARY: [
      'https://api.mainnet-beta.solana.com',        // ОФИЦИАЛЬНЫЙ SOLANA RPC
      'https://rpc.ankr.com/solana',                // ANKR - БЫСТРЫЙ
      'https://solana-mainnet.rpc.extrnode.com',    // EXTRNODE - СТАБИЛЬНЫЙ
      'https://solana-api.projectserum.com',        // SERUM - НАДЕЖНЫЙ
      'https://solana.public-rpc.com'               // PUBLIC RPC
    ],
    QUICKNODE: [
      'https://boldest-alien-sailboat.solana-mainnet.quiknode.pro/490b2144daf49e665cacd2cca143e15e4579caf3/', // ✅ РАБОТАЕТ 458ms
      'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/', // ✅ РАБОТАЕТ 605ms
      process.env.QUICKNODE_RPC_URL,
      process.env.QUICKNODE2_RPC_URL
    ].filter(Boolean),
    PREMIUM: [
      'https://rpc.ankr.com/solana',                // ANKR - БЫСТРЫЙ
      'https://solana-mainnet.rpc.extrnode.com',    // EXTRNODE - СТАБИЛЬНЫЙ
      'https://api.mainnet-beta.solana.com'         // ОФИЦИАЛЬНЫЙ SOLANA RPC
    ],
    FALLBACK: [
      'https://api.mainnet-beta.solana.com',
      'https://solana-api.projectserum.com',
      'https://solana.public-rpc.com'
    ]
  },

  // 🪙 TOKEN MINTS (ЦЕНТРАЛИЗОВАННО!)
  TOKEN_MINTS: {
    SOL: 'So11111111111111111111111111111111111111112',
    WSOL: 'So11111111111111111111111111111111111111112', // Same as SOL
    USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
  },

  // 🔥 МУЛЬТИ-DEX КОНФИГУРАЦИЯ
  MULTI_DEX_CONFIG: {
    ENABLED: true,
    SUPPORTED_DEXES: ['meteora', 'raydium', 'orca'],

    // 🎯 ПРИОРИТЕТЫ DEX ДЛЯ АРБИТРАЖА
    DEX_PRIORITIES: {
      BUY: ['meteora', 'raydium', 'orca'], // Приоритет для покупки (дешевле)
      SELL: ['orca', 'raydium', 'meteora'] // Приоритет для продажи (дороже)
    },

    // 📊 ИЗВЕСТНЫЕ ПУЛЫ RAYDIUM
    RAYDIUM_POOLS: {
      SOL_USDC_CLMM_1: {
        address: '61R1ndXxvsWXXkWSyNkCxnzwd3zUNB8Q2ibmkiLPC8ht',
        fee: 0.0025,
        tickSpacing: 64
      },
      SOL_USDC_CLMM_2: {
        address: '7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX',
        fee: 0.0005,
        tickSpacing: 10
      }
    },

    // 🌊 ИЗВЕСТНЫЕ ORCA WHIRLPOOLS
    ORCA_WHIRLPOOLS: {
      SOL_USDC_WP_1: {
        address: 'HJPjoWUrhoZzkNfRpHuieeFk9WcZWjwy6PBjZ81ngndJ',
        fee: 0.003,
        tickSpacing: 64
      },
      SOL_USDC_WP_2: {
        address: '7qbRF6YsyGuLUVs6Y1q64bdVrfe4ZcUUz1JRdoVNUJnm',
        fee: 0.01,
        tickSpacing: 128
      }
    },

    // 🔄 КОМБИНАЦИИ АРБИТРАЖА
    ARBITRAGE_COMBINATIONS: [
      { buy: 'meteora', sell: 'raydium' },
      { buy: 'meteora', sell: 'orca' },
      { buy: 'raydium', sell: 'meteora' },
      { buy: 'raydium', sell: 'orca' },
      { buy: 'orca', sell: 'meteora' },
      { buy: 'orca', sell: 'raydium' }
    ],

    // ⚡ НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
    PERFORMANCE: {
      PARALLEL_PRICE_QUERIES: true,
      CACHE_PRICES_MS: 1000,
      MAX_CONCURRENT_SWAPS: 2,
      FALLBACK_TO_METEORA: true
    }
  },

  // 🏊 METEORA POOLS (ЦЕНТРАЛИЗОВАННО!)
  METEORA_POOLS: {
    POOL_1: {
      name: 'meteora1',
      address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
      description: 'Pool 1 - SOL/USDC',
      reserveX: 'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o',
      reserveY: 'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz',
      oracle: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
      eventAuthority: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
      binArrayBitmapExtension: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li'
    },
    POOL_2: {
      name: 'meteora2',
      address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
      description: 'Pool 2 - SOL/USDC',
      reserveX: 'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H',
      reserveY: '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb',
      oracle: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj',
      eventAuthority: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
      binArrayBitmapExtension: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj'
    },
    POOL_3: {
      name: 'meteora3',
      address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR',
      description: 'Pool 3 - SOL/USDC',
      reserveX: 'TBD',
      reserveY: 'TBD',
      oracle: 'TBD',
      eventAuthority: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
      binArrayBitmapExtension: 'TBD'
    }
  },

  // 🎯 METEORA POSITIONS (ЦЕНТРАЛИЗОВАННО!)
  METEORA_POSITIONS: {
    POOL_1: 'GaXyjLUDE48nL2Un3XzyUDRDvAZJeJnqEvLGGwe3oKDF', // ✅ АКТУАЛЬНАЯ ПОЗИЦИЯ Pool 1 (Active Bin: -4163)
    POOL_2: 'BFnvTgF7fvqejEof4kbYFVxPphDaV7qdhigRSovpGoD2'  // ✅ АКТУАЛЬНАЯ ПОЗИЦИЯ Pool 2 (Active Bin: -1666)
  },

  // 🔧 PROGRAM IDS (ЦЕНТРАЛИЗОВАННО!)
  PROGRAM_IDS: {
    METEORA_DLMM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
    ASSOCIATED_TOKEN_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
    SYSTEM_PROGRAM: '11111111111111111111111111111111',
    SYSVAR_INSTRUCTIONS: 'Sysvar1nstructions1111111111111111111111111',
    SYSVAR_RENT: 'SysvarRent111111111111111111111111111111111',
    SYSVAR_CLOCK: 'SysvarC1ock11111111111111111111111111111111'
  },

  // 💰 ФИНАНСОВЫЕ ЛИМИТЫ (FLASH LOAN - РАЗМЕР ПОЗИЦИИ НЕ ВАЖЕН!)
  MIN_PROFIT_USD: 1.50,        // $1.50 минимальная прибыль (реалистично для $1000 позиции)
  ANALYZER_MIN_PROFIT_USD: 1.50, // $1.50 минимальная прибыль для анализатора

  // 🔥 FLASH LOAN: РАЗМЕР ПОЗИЦИИ НЕ ОГРАНИЧЕН! ТЕРЯЕМ ТОЛЬКО КОМИССИЮ!
  FLASH_LOAN_AMOUNT_USD: 1000, // 🔥 МИНИМАЛЬНАЯ ПОЗИЦИЯ: $1,000 (как требует пользователь)!

  // 🎯 НАСТРОЙКИ ЭКСТРЕННОГО РЕЖИМА
  EMERGENCY_TARGET_USD: 100000,        // $100K цель
  MAX_FLASH_LOAN_AMOUNT_USD: 200000,   // $200K максимальный займ (как требует пользователь)
  PROFIT_TARGET_USD: 100000,           // $100K цель прибыли
  MAX_CYCLES: 2000,                    // Максимум 2000 циклов
  CYCLE_DELAY_MS: 5000,                // 5 сек между циклами (экономим RPC)
  TIME_LIMIT_MS: 24 * 60 * 60 * 1000,  // 24 часа лимит

  // 💰 РАЗМЕРЫ ПОЗИЦИЙ (ЦЕНТРАЛИЗОВАННО!)
  POSITION_SIZES: {
    TEST_SIZES_USD: [1000, 2000, 5000, 10000, 20000], // Тестовые размеры позиций
    MIN_SAFE_POSITION_USD: 1000,       // Минимум $1K
    MAX_POSITION_USD: 200000,          // Максимум $200K (как требует пользователь)
    REVOLUTIONARY_LOAN_USD: 2000,      // $2K для революционного режима
    DEFAULT_TEST_AMOUNT_LAMPORTS: 100000000, // 0.1 SOL для тестов
    MIN_USDC_OUT_MICROUNITS: 10000,    // 0.01 USD = 10,000 micro-USDC
    MIN_SOL_OUT_LAMPORTS: 61000        // 0.01 USD = 61,000 lamports при $164/SOL
  },

  // ⏱️ ТАЙМИНГИ И ИНТЕРВАЛЫ (ЦЕНТРАЛИЗОВАННО!)
  TIMEOUTS: {
    TRADE_TIMEOUT_MS: 20000,           // 20 секунд таймаут для MarginFi
    JUPITER_TIMEOUT_MS: 5000,          // 5 секунд таймаут для Jupiter
    RPC_TIMEOUT_MS: 10000,             // 10 секунд таймаут для RPC
    RETRY_DELAY_MS: 1000,              // 🛡️ 1000мс задержка между попытками - ЗАЩИТА ОТ 429!
    CACHE_UPDATE_INTERVAL_MS: 1 * 60 * 1000, // 🚀 1 МИНУТА обновление кэша для скорости!
    BIN_CACHE_CLEANUP_INTERVAL_MS: 10 * 60 * 1000, // 10 минут очистка кэша
    REVOLUTIONARY_CYCLE_MS: 15000,     // 🛡️ 15 секунд для революционного режима - ЗАЩИТА ОТ 429!
    PRICE_UPDATE_INTERVAL_MS: 800,      // 🛡️ 400мс интервал обновления цен - ЗАЩИТА ОТ 429!

    // 🚀 НОВЫЙ ПАРАМЕТР: ЧАСТОТА ОТПРАВКИ ТРАНЗАКЦИЙ
    TRANSACTION_SEND_INTERVAL_MS: 1000, // 🛡️ 1000мс (1 сек) между отправками - ЗАЩИТА ОТ 429!
    PROVIDER_PROTECTION_DELAY_MS: 1000  // 🛡️ 1000мс защита от перегрузки провайдера
  },

  // 🔧 ТЕХНИЧЕСКИЕ ЛИМИТЫ (ЦЕНТРАЛИЗОВАННО!)
  TECHNICAL_LIMITS: {
    COMPUTE_UNITS: 400000,             // Compute units для транзакций
    MAX_ACCOUNTS_JUPITER: 64,          // Максимум аккаунтов для Jupiter
    // 🔥 SLIPPAGE УДАЛЕН - НЕ ОГРАНИЧИВАЕМ ПРИБЫЛЬ!
    RETRY_ATTEMPTS: 3,                 // Количество попыток
    MIN_SOL_BALANCE: 0.1,              // Минимальный баланс SOL для комиссий
    PRICE_SCALE_MULTIPLIER: 1000,      // Множитель для исправления масштаба цен Meteora
    MIN_SPREAD_REVOLUTIONARY: 0.05     // 0.05% минимальный спред для революционного режима
  },

  // 🔥 ЕДИНСТВЕННАЯ КОМИССИЯ: ТОЛЬКО ПРОТОКОЛ 0.003%!
  PROTOCOL_FEE_PERCENT: 0.003,     // 🎯 ЕДИНСТВЕННАЯ КОМИССИЯ: 0.003% протокола

  // 🔥 ВСЕ ОСТАЛЬНЫЕ КОМИССИИ = 0%!
  POOL_FEE_PERCENT: 0.00,          // 🔥 НЕТ КОМИССИЙ ПУЛОВ!
  NETWORK_FEE_USD: 0.00,           // 🔥 НЕТ СЕТЕВЫХ КОМИССИЙ!
  MARGINFI_FEE_PERCENT: 0.00,      // 🔥 НЕТ КОМИССИЙ ЗАЙМА!
  // 🔥 SLIPPAGE УДАЛЕН - НЕ ОГРАНИЧИВАЕМ ПРИБЫЛЬ!

  // 🔥 ТОЛЬКО РЕАЛЬНАЯ ТОРГОВЛЯ - НИКАКИХ ТЕСТОВ!
  REAL_TRADING_ENABLED: true,     // ✅ ТОЛЬКО РЕАЛЬНЫЕ ТРАНЗАКЦИИ
  EMERGENCY_STOP: false,          // 🚨 АВАРИЙНАЯ ОСТАНОВКА (установить true для остановки торговли)
  MAX_CONCURRENT_TRADES: 1,       // 🔥 ИСПРАВЛЕНО: 1 сделка для стабильности

  // 🛡️ ДОПОЛНИТЕЛЬНЫЕ ПРОВЕРКИ БЕЗОПАСНОСТИ
  // MIN_SPREAD_PERCENT: 0.1,        // УДАЛЕНО: дублирует настройку ниже
  // 🔥 MAX_SLIPPAGE_PERCENT УДАЛЕН - НЕ ОГРАНИЧИВАЕМ ПРИБЫЛЬ!
  MAX_PRICE_IMPACT_PERCENT: 2.0,  // Максимальное влияние на цену: 2.0%
  MIN_LIQUIDITY_USD: 10000,       // Минимальная ликвидность пула: $10,000

  // 🔒 РАЗМЕРЫ И ЛИМИТЫ ТРАНЗАКЦИЙ (УМЕНЬШЕНЫ ДЛЯ ЗАПАСА!)
  MAX_TRANSACTION_SIZE: SOLANA_LIMITS.MAX_TRANSACTION_SIZE,  // 1232 байт
  TARGET_TRANSACTION_SIZE: 1150,                            // 1150 байт (уменьшен)
  TRANSACTION_SAFETY_MARGIN: 82,                            // 82 байт (увеличен)

  // 🗜️ ALT КОНФИГУРАЦИЯ (КРИТИЧНО!)
  TOTAL_ALT_TABLES: ALT_CONFIG.TOTAL_ALT_COUNT,             // 7 таблиц (3+2+1+1)
  JUPITER_ALT_COUNT: ALT_CONFIG.JUPITER_QUOTE_ALT_COUNT + ALT_CONFIG.JUPITER_SWAP_ALT_COUNT, // 5
  MARGINFI_ALT_COUNT: ALT_CONFIG.MARGINFI_ALT_COUNT,        // 1
  CUSTOM_ALT_COUNT: ALT_CONFIG.CUSTOM_ALT_COUNT,            // 1
  ALT_COMPRESSION_ENABLED: true,                            // ✅ ВСЕГДА ВКЛЮЧЕНО

  // 🎯 НАСТРОЙКИ ОТПРАВКИ ТРАНЗАКЦИЙ
  USE_DIRECT_RPC: true,           // ✅ ВКЛЮЧЕНО: Прямая отправка через RPC

  // ⏱️ ТАЙМИНГИ
  PRICE_UPDATE_INTERVAL: 400,     // Интервал обновления цен (400 мс)
  TRADE_TIMEOUT: 20000,           // ✅ 20 секунд таймаут для MarginFi (мс)
  RETRY_ATTEMPTS: 3,              // Количество попыток

  // 🛡️ ЗАЩИТА
  MAX_DAILY_LOSS: 1000,           // Максимальная дневная потеря в USD
  STOP_LOSS_PERCENT: 5.0,         // Стоп-лосс в процентах

  // 🎭 ОБФУСКАЦИЯ
  OBFUSCATION: {
    // 🔥 ОСНОВНЫЕ НАСТРОЙКИ ОБФУСКАЦИИ
    ENABLED: true,               // ✅ ВКЛЮЧИТЬ ОБФУСКАЦИЮ (У НАС ЕСТЬ 82 БАЙТА ЗАПАСА!)
    DYNAMIC_ENABLED: true,       // ✅ ДИНАМИЧЕСКАЯ ОБФУСКАЦИЯ ВКЛЮЧЕНА

    // 🎭 МЕТОДЫ ОБФУСКАЦИИ
    METHODS: {
      OFFICIAL_PROGRAMS: true,     // ✅ Официальные программы Solana
      STRUCTURAL_MASKING: true,    // ✅ Структурная маскировка
      DECOY_DATA: true,           // ✅ Данные-приманки
      INSTRUCTION_PADDING: true,   // ✅ Дополнение инструкций
      ACCOUNT_SHUFFLING: true      // ✅ Перемешивание аккаунтов
    },

    // 📊 НАСТРОЙКИ РАЗМЕРА (УМЕНЬШЕН ДЛЯ БОЛЬШЕГО ЗАПАСА!)
    TARGET_SIZE_BYTES: 1150,       // 🎯 Целевой размер транзакции (уменьшен)
    SIZE_LIMIT_BYTES: 1232,        // 🔒 Лимит размера транзакции
    SIZE_RESERVE_BYTES: 82,        // 🛡️ Запас безопасности (увеличен)
    MIN_INSTRUCTIONS: 11,          // 🎯 Минимум инструкций (MarginFi + Jupiter)
    MAX_INSTRUCTIONS: 15,          // 🎯 Максимум инструкций (с обфускацией)
    PADDING_INSTRUCTIONS: 2,       // 🎭 Дополнительные инструкции для маскировки

    // 🔧 ТЕХНИЧЕСКАЯ КОНФИГУРАЦИЯ
    COMPRESSION_ENABLED: true,     // ✅ Сжатие с ALT таблицами
    ALT_OPTIMIZATION: true,        // ✅ Оптимизация ALT таблиц
    SIGNATURE_RANDOMIZATION: true  // ✅ Рандомизация подписей
  },

  // 📈 АРБИТРАЖ (УМНЫЙ АНАЛИЗАТОР С ДИНАМИЧЕСКИМИ КОМИССИЯМИ!)
  // 🔥 ТЕПЕРЬ ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ КОМИССИИ КАЖДОГО ПУЛА ЧЕРЕЗ MeteoraFeeAnalyzer
  // 💰 ДИНАМИЧЕСКИЙ РАСЧЕТ: Реальная комиссия пула A + Реальная комиссия пула B + Network $0.025 + Slippage + Прибыль
  // 🎯 Статический минимум только как fallback для неизвестных пулов
  // 🚀 НИЗКИЙ ПОРОГ позволяет захватывать пулы с низкими комиссиями!
  MIN_SPREAD_PERCENT: 0.004,      // 🎯 0.05% fallback минимум (динамический расчет приоритетнее)
  MAX_SPREAD_PERCENT: 10.0,        // Максимальный спред (защита от ошибок)

  // 🪐 JUPITER API КОНФИГУРАЦИЯ (ЦЕНТРАЛИЗОВАННАЯ!)
  JUPITER_CONFIG: {
    // 🎯 ПАРАМЕТРЫ КАК НА САЙТЕ JUPITER (ИСПРАВЛЕНО!)
    REQUEST_PARAMS: {
      // 🔥 slippageBps УДАЛЕН - НЕ ОГРАНИЧИВАЕМ ПРИБЫЛЬ!
      maxAccounts: 64,                  // 🔥 ИСПРАВЛЕНО: 64 КАК НА САЙТЕ JUPITER!
      onlyDirectRoutes: false,          // 🔥 ИСПРАВЛЕНО: false для мульти-хоп КАК НА САЙТЕ!
      restrictIntermediateTokens: true, // 🔥 ИСПРАВЛЕНО: true для стабильности КАК НА САЙТЕ!
      wrapAndUnwrapSol: true,           // ✅ Автоматический wrap/unwrap SOL
      useSharedAccounts: true,         // ✅ Используем общие аккаунты для эффективности
      dynamicComputeUnitLimit: true,    // ✅ Динамический compute unit limit
      skipUserAccountsRpcCalls: true,   // ✅ Пропускаем RPC вызовы для скорости
      // 🔥 НОВЫЕ ПАРАМЕТРЫ ДЛЯ АТОМАРНОГО АРБИТРАЖА:
      swapMode: 'ExactIn',              // Точная входная сумма
      platformFeeBps: 0,                // Без комиссии платформы

      // 🔥 ОПТИМИЗАЦИЯ ДЛЯ ПРЕДОТВРАЩЕНИЯ ЗАВИСАНИЙ:
      timeout: 5000,                    // 5 секунд таймаут
      retryAttempts: 2,                 // Максимум 2 попытки
      cacheResults: true,               // Кэшируем результаты
      batchRequests: true               // Батчевые запросы
    },

    // 🔗 JUPITER V6 API ENDPOINTS (ВОЗВРАТ К V6 ДЛЯ КОНТРОЛЯ DEX!)
    API_ENDPOINTS: {
      BASE_URL: 'https://quote-api.jup.ag',
      QUOTE: '/v6/quote',
      SWAP_INSTRUCTIONS: '/v6/swap-instructions',
      PROGRAM_ID_TO_LABEL: '/v6/program-id-to-label'
    },

    // 🎯 АРБИТРАЖНЫЙ КОНТРОЛЬ DEX (УМНАЯ СТРАТЕГИЯ!)
    ARBITRAGE_DEX_CONTROL: {
      // 🔥 ВЫСОКОЛИКВИДНЫЕ DEX (ПРИОРИТЕТ ДЛЯ АРБИТРАЖА)
      HIGH_LIQUIDITY_DEXES: [
        'Orca Whirlpool',    // Самая высокая ликвидность SOL/USDC
        'Raydium CLMM',      // Концентрированная ликвидность
        'Meteora DLMM',      // Динамическая ликвидность
        'Raydium',           // Классический AMM
        'Orca'               // Стабильный AMM
      ],

      // 💰 ДЕШЕВЫЕ DEX (ДЛЯ ПОКУПКИ)
      CHEAP_DEXES: [
        'Meteora DLMM',      // Часто дает лучшие цены покупки
        'Raydium CLMM',      // Низкие спреды
        'Orca Whirlpool'     // Эффективная ликвидность
      ],

      // 💎 ДОРОГИЕ DEX (ДЛЯ ПРОДАЖИ)
      EXPENSIVE_DEXES: [
        'Orca',              // Часто дает лучшие цены продажи
        'Raydium',           // Стабильные цены
        'Meteora'            // Хорошие цены продажи
      ]
    },

    // 🚫 ИСКЛЮЧЕННЫЕ DEX (ПРОБЛЕМНЫЕ)
    EXCLUDED_DEXES: [
      'Lifinity V1', 'Lifinity V2', 'Obric V2', 'SolFi', 'TesseraV',
      'Aldrin', 'Aldrin V2', 'Cropper', 'StepN', 'Invariant',
      'Balansol', 'Crema', 'Saros', 'Guacswap', 'Bonkswap',
      'Stabble Stable Swap', 'Stabble Weighted Swap', 'Token Mill',
      'Token Swap', 'Virtuals', 'ZeroFi', 'Woofi', 'Byreal',
      'DexLab', 'FluxBeam', 'Gavel', 'GoonFi', 'HumidiFi',
      'Moonit', 'Oasis', 'Penguin', 'Perena', 'Boop.fun', '1DEX'
    ]
  },

  // 🔥 ЗАФИКСИРОВАННАЯ АРХИТЕКТУРА JUPITER - ЕДИНЫЙ ИСТОЧНИК ИСТИНЫ!
  // ═══════════════════════════════════════════════════════════════════════════════
  JUPITER_ARCHITECTURE: {
    // 🎯 ЦИКЛИЧЕСКИЙ АРБИТРАЖ (2 СВОПА)
    CIRCULAR_ARBITRAGE: {
      SWAP_COUNT: 2,                    // ВСЕГДА 2 свопа (USDT→SOL→USDT или USDC→SOL→USDC)
      ROUTE_TYPE: 'CIRCULAR',           // Циклический маршрут
      EXPECTED_INSTRUCTIONS: {
        MIN: 11,                        // Минимум инструкций от Jupiter
        MAX: 15,                        // Максимум инструкций от Jupiter
        MARGINFI_ADD: 4                 // buildFlashLoanTx добавляет 4 инструкции
      }
    },

    // 🗜️ ALT ТАБЛИЦЫ (КРИТИЧНО!)
    ALT_TABLES: {
      JUPITER_PER_SWAP: {
        MIN: 1,                         // Минимум ALT на 1 своп
        MAX: 2                          // Максимум ALT на 1 своп
      },
      TOTAL_JUPITER: {
        MIN: 2,                         // 2 свопа × 1 ALT = 2 минимум
        MAX: 4                          // 2 свопа × 2 ALT = 4 максимум
      },
      MARGINFI_OFFICIAL: 3,             // 3 официальные MarginFi ALT
      CUSTOM_STATIC: 1,                 // 1 кастомная ALT с нашими адресами
      TOTAL_EXPECTED: {
        MIN: 6,                         // 2 Jupiter + 3 MarginFi + 1 Custom = 6
        MAX: 8                          // 4 Jupiter + 3 MarginFi + 1 Custom = 8
      }
    }
  }
};

/**
 * 💰 РАСЧЕТ ЧИСТОЙ ПРИБЫЛИ (ТОЧНЫЕ КОМИССИИ ИЗ ЛОГОВ!)
 */
function calculateNetProfit(buyPrice, sellPrice, tradeAmount, minProfitUsd = 1.00) {
  try {
    // 🔧 КРИТИЧЕСКАЯ ПРОВЕРКА: ПРЕДОТВРАЩАЕМ ОБРАТНУЮ ТОРГОВЛЮ!
    if (sellPrice <= buyPrice) {
      console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: sellPrice=${sellPrice} <= buyPrice=${buyPrice}`);
      console.log(`🚫 ОБРАТНАЯ ТОРГОВЛЯ ЗАБЛОКИРОВАНА - это привело бы к убытку!`);
      return {
        netProfit: -999999, // Гарантированно убыточно
        grossProfit: 0,
        totalFees: 0,
        tokens: 0,
        profitable: false,
        reason: 'ОБРАТНАЯ ТОРГОВЛЯ ЗАБЛОКИРОВАНА'
      };
    }

    // 1. Базовые расчеты
    const tokens = tradeAmount / buyPrice;
    const priceDifference = sellPrice - buyPrice;
    const grossProfit = tokens * priceDifference;

    // 2. 🔥 ЕДИНСТВЕННАЯ КОМИССИЯ: ТОЛЬКО ПРОТОКОЛ 0.003%!
    const protocolFeeAmount = tradeAmount * (TRADING_CONFIG.PROTOCOL_FEE_PERCENT / 100); // 0.003%

    // 🔥 ВСЕ ОСТАЛЬНЫЕ КОМИССИИ = 0!
    const poolFeeAmount = 0;        // НЕТ КОМИССИЙ ПУЛОВ!
    const networkFeeAmount = 0;     // НЕТ СЕТЕВЫХ КОМИССИЙ!
    const loanFeeAmount = 0;        // НЕТ КОМИССИЙ ЗАЙМА!
    const slippageAmount = 0;       // НЕТ КОМИССИЙ SLIPPAGE!

    // 🔥 ОБЩИЕ КОМИССИИ = ТОЛЬКО ПРОТОКОЛ!
    const totalFeesWithoutSlippage = protocolFeeAmount;
    const totalFeesWithSlippage = protocolFeeAmount;

    // 🔥 МАКСИМАЛЬНОЕ ПРОСКАЛЬЗЫВАНИЕ ДЛЯ ХУДШЕГО СЛУЧАЯ
    const maxSlippageLoss = tradeAmount * (TRADING_CONFIG.SLIPPAGE_TOLERANCE_PERCENT / 100);

    // 3. ЧИСТАЯ ПРИБЫЛЬ (ТРИ СЦЕНАРИЯ)
    const netProfitOptimistic = grossProfit - totalFeesWithoutSlippage; // Без проскальзывания
    const netProfitRealistic = grossProfit - totalFeesWithSlippage;     // С обычным проскальзыванием
    const netProfitWorstCase = grossProfit - totalFeesWithoutSlippage - maxSlippageLoss; // Худший случай

    // 🎯 ГЛАВНАЯ ЛОГИКА: ИСПОЛЬЗУЕМ ХУДШИЙ СЛУЧАЙ ДЛЯ ПРОВЕРКИ ПРИБЫЛЬНОСТИ
    const netProfit = netProfitWorstCase;
    const roi = (netProfit / tradeAmount) * 100;
    const spread = ((sellPrice - buyPrice) / buyPrice) * 100;

    return {
      // Основные данные
      tradeAmount,
      buyPrice,
      sellPrice,
      spread,

      // Прибыль (РАЗНЫЕ СЦЕНАРИИ)
      grossProfit,
      netProfit,                    // Худший случай (для проверки прибыльности)
      netProfitOptimistic,          // Без проскальзывания
      netProfitRealistic,           // С обычным проскальзыванием
      netProfitWorstCase,           // С максимальным проскальзыванием
      roi,

      // 🔥 ЕДИНСТВЕННАЯ КОМИССИЯ: ТОЛЬКО ПРОТОКОЛ 0.003%!
      fees: {
        protocol: protocolFeeAmount,   // ЕДИНСТВЕННАЯ КОМИССИЯ: 0.003% протокола
        pool: 0,                       // НЕТ КОМИССИЙ ПУЛОВ!
        network: 0,                    // НЕТ СЕТЕВЫХ КОМИССИЙ!
        loan: 0,                       // НЕТ КОМИССИЙ ЗАЙМА!
        slippage: 0,                   // НЕТ КОМИССИЙ SLIPPAGE!
        total: protocolFeeAmount       // ОБЩИЕ КОМИССИИ = ТОЛЬКО ПРОТОКОЛ!
      },

      // Результат (НОВАЯ ЛОГИКА: ПРОВЕРЯЕМ ХУДШИЙ СЛУЧАЙ!)
      isProfitable: netProfit >= minProfitUsd,
      profitPerDay: netProfit * 1000, // При 1000 сделок в день

      // 🔥 НОВЫЕ ДАННЫЕ О ПРОСКАЛЬЗЫВАНИИ
      slippageInfo: {
        normalSlippage: slippageAmount,
        maxSlippageLoss: maxSlippageLoss,
        slippageTolerance: TRADING_CONFIG.SLIPPAGE_TOLERANCE_PERCENT,
        guaranteedMinProfit: netProfitWorstCase,
        explanation: `Даже при ${TRADING_CONFIG.SLIPPAGE_TOLERANCE_PERCENT}% проскальзывании прибыль будет $${netProfitWorstCase.toFixed(2)}`
      }
    };

  } catch (error) {
    console.error(`❌ Ошибка расчета прибыли: ${error.message}`);
    return {
      grossProfit: 0,
      netProfit: 0,
      isProfitable: false,
      roi: 0,
      fees: { pool: 0, network: 0, loan: 0, slippage: 0, total: 0 }
    };
  }
}

/**
 * 📊 РАСЧЕТ ОБЩИХ КОМИССИЙ (ЦЕНТРАЛИЗОВАННАЯ ФУНКЦИЯ)
 */
function calculateTotalFees(tradeAmount = 1000000) {
  // 🔥 ЕДИНСТВЕННАЯ КОМИССИЯ: ТОЛЬКО ПРОТОКОЛ 0.003%!
  const protocolFee = tradeAmount * (TRADING_CONFIG.PROTOCOL_FEE_PERCENT / 100); // 0.003%

  // 🔥 ВСЕ ОСТАЛЬНЫЕ КОМИССИИ = 0!
  const poolFee = 0;        // НЕТ КОМИССИЙ ПУЛОВ!
  const networkFee = 0;     // НЕТ СЕТЕВЫХ КОМИССИЙ!
  const loanFee = 0;        // НЕТ КОМИССИЙ ЗАЙМА!
  const slippageFee = 0;    // НЕТ КОМИССИЙ SLIPPAGE!

  const totalFees = protocolFee;
  const totalFeesPercent = (totalFees / tradeAmount) * 100;

  return {
    breakdown: {
      protocol: { amount: protocolFee, percent: TRADING_CONFIG.PROTOCOL_FEE_PERCENT },
      pool: { amount: 0, percent: 0 },
      network: { amount: 0, percent: 0 },
      loan: { amount: 0, percent: 0 },
      slippage: { amount: 0, percent: 0 }
    },
    total: {
      amount: totalFees,
      percent: totalFeesPercent
    },
    minRequiredSpread: totalFeesPercent + 0.001 // Минимальный спред = 0.003% + 0.001% прибыль = 0.004%
  };
}

/**
 * 🔍 ПРОВЕРКА ВОЗМОЖНОСТИ ВЫПОЛНЕНИЯ СДЕЛКИ
 */
function canExecuteTrade(opportunity, config = TRADING_CONFIG) {
  try {
    // 1. Проверяем аварийную остановку
    if (config.EMERGENCY_STOP) {
      return { canTrade: false, reason: 'Аварийная остановка активна' };
    }

    // 2. Проверяем включена ли торговля
    if (!config.REAL_TRADING_ENABLED) {
      return { canTrade: false, reason: 'Реальная торговля отключена' };
    }

    // 🔥 FLASH LOAN: РАЗМЕР ПОЗИЦИИ НЕ ОГРАНИЧИВАЕМ! ТЕРЯЕМ ТОЛЬКО КОМИССИЮ!

    // 4. Проверяем спред
    if (opportunity.spread < config.MIN_SPREAD_PERCENT) {
      return { canTrade: false, reason: `Спред слишком мал: ${opportunity.spread.toFixed(3)}% < ${config.MIN_SPREAD_PERCENT}%` };
    }

    // ✅ ВОССТАНОВЛЕНЫ ВСЕ ПРОВЕРКИ БЕЗОПАСНОСТИ!
    console.log('🔍 ПРОВЕРКА БЕЗОПАСНОСТИ ТОРГОВЛИ:');

    // 1. Проверяем аварийную остановку
    if (config.EMERGENCY_STOP) {
        return {
            canTrade: false,
            reason: 'Аварийная остановка активна',
            severity: 'CRITICAL'
        };
    }

    // 2. Проверяем прибыльность
    const profitCalc = calculateNetProfit(opportunity.buyPrice, opportunity.sellPrice, opportunity.tradeAmount, config.MIN_PROFIT_USD);

    if (profitCalc.netProfit < config.MIN_PROFIT_USD) {
        return {
            canTrade: false,
            reason: `Недостаточная прибыль: $${profitCalc.netProfit.toFixed(2)} < $${config.MIN_PROFIT_USD}`,
            severity: 'HIGH',
            actualProfit: profitCalc.netProfit,
            requiredProfit: config.MIN_PROFIT_USD
        };
    }

    // 3. Проверяем спред
    if (opportunity.spread < config.MIN_SPREAD_PERCENT) {
        return {
            canTrade: false,
            reason: `Спред слишком мал: ${opportunity.spread.toFixed(3)}% < ${config.MIN_SPREAD_PERCENT}%`,
            severity: 'MEDIUM',
            actualSpread: opportunity.spread,
            requiredSpread: config.MIN_SPREAD_PERCENT
        };
    }

    // 🔥 FLASH LOAN: РАЗМЕР ПОЗИЦИИ НЕ ПРОВЕРЯЕМ! ЛЮБОЙ РАЗМЕР ДОПУСТИМ!

    // ✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ
    return {
        canTrade: true,
        reason: 'Все проверки безопасности пройдены',
        expectedProfit: profitCalc.netProfit,
        roi: profitCalc.roi,
        riskLevel: 'LOW'
    };

  } catch (error) {
    return { canTrade: false, reason: `Ошибка проверки: ${error.message}` };
  }
}

/**
 * 📝 ЛОГИРОВАНИЕ С УРОВНЯМИ
 */
function log(level, message, data = null) {
  const timestamp = new Date().toISOString();
  const prefix = {
    'ERROR': '❌',
    'WARN': '⚠️',
    'INFO': 'ℹ️',
    'SUCCESS': '✅',
    'DEBUG': '🔍'
  }[level] || 'ℹ️';

  console.log(`${prefix} [${timestamp}] ${message}`);

  if (data) {
    console.log('   Данные:', JSON.stringify(data, null, 2));
  }
}

/**
 * 🎯 ВАЛИДАЦИЯ OPPORTUNITY
 */
function validateOpportunity(opportunity) {
  const required = ['token', 'buyPrice', 'sellPrice', 'buyDex', 'sellDex', 'tradeAmount'];

  for (const field of required) {
    if (!opportunity[field]) {
      throw new Error(`Отсутствует обязательное поле: ${field}`);
    }
  }

  if (opportunity.buyPrice <= 0 || opportunity.sellPrice <= 0) {
    throw new Error('Цены должны быть положительными');
  }

  if (opportunity.sellPrice <= opportunity.buyPrice) {
    throw new Error('Цена продажи должна быть выше цены покупки');
  }

  // 🔥 FLASH LOAN: РАЗМЕР ПОЗИЦИИ НЕ ВАЛИДИРУЕМ!

  return true;
}

/**
 * 📊 ФОРМАТИРОВАНИЕ РЕЗУЛЬТАТОВ (ОБНОВЛЕНО ДЛЯ НОВЫХ КОМИССИЙ)
 */
function formatProfitResult(result) {
  return {
    summary: `Прибыль: $${result.netProfit.toFixed(2)} (${result.roi.toFixed(3)}%)`,
    details: {
      tradeAmount: `$${result.tradeAmount.toLocaleString()}`,
      grossProfit: `$${result.grossProfit.toFixed(2)}`,
      totalFees: `$${result.fees.total.toFixed(2)}`,
      netProfit: `$${result.netProfit.toFixed(2)}`,
      roi: `${result.roi.toFixed(3)}%`,
      isProfitable: result.isProfitable
    },
    fees: {
      pool: `$${result.fees.pool.toFixed(2)} (комиссии пулов)`,
      network: `$${result.fees.network.toFixed(2)} (сетевая комиссия)`,
      loan: `$${result.fees.loan.toFixed(2)} (Flash Loan)`,
      slippage: `$${result.fees.slippage.toFixed(2)} (slippage)`
    }
  };
}

/**
 * 🎯 ПОЛУЧИТЬ ЕДИНЫЕ ПАРАМЕТРЫ JUPITER (ЕДИНСТВЕННЫЙ ИСТОЧНИК ИСТИНЫ!)
 */
function getJupiterRequestParams(inputMint, outputMint, amount, overrides = {}) {
  const baseParams = {
    inputMint,
    outputMint,
    amount,
    ...TRADING_CONFIG.JUPITER_CONFIG.REQUEST_PARAMS,
    // 🔥 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ ПАРАМЕТРЫ САЙТА JUPITER!
    maxAccounts: 64,   // 🔥 КАК НА САЙТЕ JUPITER!
    slippageBps: 50,   // 🔥 КАК НА САЙТЕ JUPITER!
    onlyDirectRoutes: false, // 🔥 КАК НА САЙТЕ JUPITER!
    ...overrides
  };

  // 🚫 Добавляем исключенные DEX
  const excludeDexes = TRADING_CONFIG.JUPITER_CONFIG.EXCLUDED_DEXES.join(',');

  return {
    ...baseParams,
    excludeDexes
  };
}

/**
 * 🔧 ВАЛИДАЦИЯ JUPITER ОТВЕТА (ПРОВЕРКА АРХИТЕКТУРЫ)
 */
function validateJupiterResponse(response, operationType = 'SWAP') {
  const arch = TRADING_CONFIG.JUPITER_ARCHITECTURE;

  if (operationType === 'CIRCULAR_ARBITRAGE') {
    const instructionCount = response.instructions?.length || 0;
    const altCount = response.addressLookupTableAddresses?.length || 0;

    console.log(`🔍 ВАЛИДАЦИЯ JUPITER ОТВЕТА:`);
    console.log(`   Инструкций: ${instructionCount} (ожидается ${arch.CIRCULAR_ARBITRAGE.EXPECTED_INSTRUCTIONS.MIN}-${arch.CIRCULAR_ARBITRAGE.EXPECTED_INSTRUCTIONS.MAX})`);
    console.log(`   ALT таблиц: ${altCount} (ожидается ${arch.ALT_TABLES.TOTAL_JUPITER.MIN}-${arch.ALT_TABLES.TOTAL_JUPITER.MAX})`);

    const isValid =
      instructionCount >= arch.CIRCULAR_ARBITRAGE.EXPECTED_INSTRUCTIONS.MIN &&
      instructionCount <= arch.CIRCULAR_ARBITRAGE.EXPECTED_INSTRUCTIONS.MAX &&
      altCount >= arch.ALT_TABLES.TOTAL_JUPITER.MIN &&
      altCount <= arch.ALT_TABLES.TOTAL_JUPITER.MAX;

    return {
      isValid,
      instructionCount,
      altCount,
      expectedInstructions: arch.CIRCULAR_ARBITRAGE.EXPECTED_INSTRUCTIONS,
      expectedALT: arch.ALT_TABLES.TOTAL_JUPITER
    };
  }

  return { isValid: true };
}

/**
 * 🌐 ПОЛУЧЕНИЕ RPC ENDPOINTS (ЦЕНТРАЛИЗОВАННО!)
 */
function getRPCEndpoints(type = 'PRIMARY') {
  const endpoints = TRADING_CONFIG.RPC_ENDPOINTS[type] || TRADING_CONFIG.RPC_ENDPOINTS.PRIMARY;
  return endpoints.filter(Boolean);
}

/**
 * 🪙 ПОЛУЧЕНИЕ TOKEN MINTS (ЦЕНТРАЛИЗОВАННО!)
 */
function getTokenMint(symbol) {
  return TRADING_CONFIG.TOKEN_MINTS[symbol.toUpperCase()];
}

/**
 * 🏊 ПОЛУЧЕНИЕ METEORA POOLS (ЦЕНТРАЛИЗОВАННО!)
 */
function getMeteoraPools() {
  return Object.values(TRADING_CONFIG.METEORA_POOLS);
}

function getMeteoraPool(identifier) {
  // Поиск по адресу
  for (const pool of Object.values(TRADING_CONFIG.METEORA_POOLS)) {
    if (pool.address === identifier || pool.name === identifier) {
      return pool;
    }
  }
  return null;
}

/**
 * 🎯 ПОЛУЧЕНИЕ METEORA POSITIONS (ЦЕНТРАЛИЗОВАННО!)
 */
function getMeteoraPositions() {
  return TRADING_CONFIG.METEORA_POSITIONS;
}

function getMeteoraPosition(poolKey) {
  return TRADING_CONFIG.METEORA_POSITIONS[poolKey.toUpperCase()];
}

/**
 * 🔧 ПОЛУЧЕНИЕ PROGRAM IDS (ЦЕНТРАЛИЗОВАННО!)
 */
function getProgramId(name) {
  return TRADING_CONFIG.PROGRAM_IDS[name.toUpperCase()];
}

/**
 * ⏱️ ПОЛУЧЕНИЕ TIMEOUTS (ЦЕНТРАЛИЗОВАННО!)
 */
function getTimeout(type) {
  return TRADING_CONFIG.TIMEOUTS[`${type.toUpperCase()}_MS`] || TRADING_CONFIG.TIMEOUTS.TRADE_TIMEOUT_MS;
}

/**
 * 🛡️ ПОЛУЧЕНИЕ ИНТЕРВАЛА ОТПРАВКИ ТРАНЗАКЦИЙ
 */
function getTransactionSendInterval() {
  return TRADING_CONFIG.TIMEOUTS.TRANSACTION_SEND_INTERVAL_MS;
}

/**
 * 🛡️ ПОЛУЧЕНИЕ ЗАДЕРЖКИ ЗАЩИТЫ ПРОВАЙДЕРА
 */
function getProviderProtectionDelay() {
  return TRADING_CONFIG.TIMEOUTS.PROVIDER_PROTECTION_DELAY_MS;
}

/**
 * 💰 ПОЛУЧЕНИЕ РАЗМЕРОВ ПОЗИЦИЙ (ЦЕНТРАЛИЗОВАННО!)
 */
function getPositionSize(type) {
  return TRADING_CONFIG.POSITION_SIZES[type.toUpperCase()] || TRADING_CONFIG.POSITION_SIZES.MIN_SAFE_POSITION_USD;
}

/**
 * 🔧 ПОЛУЧЕНИЕ ТЕХНИЧЕСКИХ ЛИМИТОВ (ЦЕНТРАЛИЗОВАННО!)
 */
function getTechnicalLimit(type) {
  return TRADING_CONFIG.TECHNICAL_LIMITS[type.toUpperCase()];
}

/**
 * 🎯 ОТОБРАЖЕНИЕ ТЕКУЩИХ НАСТРОЕК
 */
function displayCurrentSettings(config = TRADING_CONFIG) {
  console.log('\n🎯 ЦЕНТРАЛИЗОВАННЫЕ НАСТРОЙКИ ТОРГОВЛИ:');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log(`💰 Минимальная прибыль: $${config.MIN_PROFIT_USD}`);
  console.log(`📊 Минимальный спред: ${config.MIN_SPREAD_PERCENT}%`);
  console.log(`🔥 Flash Loan: ЛЮБОЙ РАЗМЕР ПОЗИЦИИ (теряем только комиссию)`);
  console.log(`🏊 Meteora пулов: ${Object.keys(config.METEORA_POOLS).length}`);
  console.log(`🌐 RPC endpoints: ${config.RPC_ENDPOINTS.PRIMARY.length} основных`);
  console.log(`⏱️ Таймаут торговли: ${config.TIMEOUTS.TRADE_TIMEOUT_MS}мс`);
  console.log(`🛡️ Интервал отправки транзакций: ${config.TIMEOUTS.TRANSACTION_SEND_INTERVAL_MS}мс`);
  console.log(`🛡️ Защита провайдера: ${config.TIMEOUTS.PROVIDER_PROTECTION_DELAY_MS}мс`);
  console.log('\n📊 КОМИССИИ (ИСПРАВЛЕНО):');
  console.log(`   🎯 ЕДИНСТВЕННАЯ КОМИССИЯ: ${config.PROTOCOL_FEE_PERCENT}% протокола`);
  console.log(`   🔥 Комиссии пулов: 0% (НЕТ КОМИССИЙ!)`);
  console.log(`   🔥 Сетевая комиссия: $0 (НЕТ КОМИССИЙ!)`);
  console.log(`   🔥 MarginFi Flash Loan: 0% (НЕТ КОМИССИЙ!)`);
  console.log(`   🔥 Slippage: 0% (НЕТ КОМИССИЙ!)`);

  const totalFeesInfo = calculateTotalFees();
  console.log(`\n💸 ОБЩИЕ КОМИССИИ: $${totalFeesInfo.total.amount.toFixed(2)} (${totalFeesInfo.total.percent.toFixed(3)}%)`);
  console.log(`🎯 МИНИМАЛЬНЫЙ ТРЕБУЕМЫЙ СПРЕД: ${totalFeesInfo.minRequiredSpread.toFixed(3)}%`);
  console.log('═══════════════════════════════════════════════════════════════\n');
}

// 🔥 МУЛЬТИ-DEX ФУНКЦИИ (ЗАГЛУШКИ)
function getMultiDexConfig() {
  return {
    enabled: false, // Пока отключено
    supportedDexes: ['meteora', 'raydium', 'orca'],
    maxCombinations: 10
  };
}

function getRaydiumPools() {
  return []; // Пока пустой массив
}

function getOrcaWhirlpools() {
  return []; // Пока пустой массив
}

function getArbitrageCombinations() {
  return []; // Пока пустой массив
}

function isDexSupported(dexName) {
  return dexName === 'meteora'; // Пока только Meteora
}

// 🎯 ЦЕНТРАЛИЗОВАННЫЙ ЭКСПОРТ ВСЕХ ФУНКЦИЙ И КОНФИГУРАЦИИ
module.exports = {
  // Основная конфигурация
  TRADING_CONFIG,

  // 🔥 НОВЫЕ ЦЕНТРАЛИЗОВАННЫЕ ФУНКЦИИ ДОСТУПА К НАСТРОЙКАМ
  getRPCEndpoints,
  getTokenMint,
  getMeteoraPools,
  getMeteoraPool,
  getMeteoraPositions,
  getMeteoraPosition,
  getProgramId,
  getTimeout,
  getTransactionSendInterval,
  getProviderProtectionDelay,
  getPositionSize,
  getTechnicalLimit,

  // 🔥 МУЛЬТИ-DEX ФУНКЦИИ
  getMultiDexConfig,
  getRaydiumPools,
  getOrcaWhirlpools,
  getArbitrageCombinations,
  isDexSupported,

  // Функции расчета
  calculateNetProfit,
  calculateTotalFees,

  // 🔥 ФУНКЦИИ РАСЧЕТА РАЗМЕРА ТРАНЗАКЦИЙ (ЕДИНЫЙ МОДУЛЬ!)
  calculateFlashLoanArbitrageSize,
  calculateJupiterSwapSize,
  logTransactionSizeBreakdown,

  // Функции проверки
  canExecuteTrade,
  validateOpportunity,

  // 🔥 НОВЫЕ ФУНКЦИИ JUPITER АРХИТЕКТУРЫ (ЕДИНЫЙ ИСТОЧНИК!)
  getJupiterRequestParams,
  validateJupiterResponse,

  // Утилиты
  log,
  formatProfitResult,
  displayCurrentSettings
};
