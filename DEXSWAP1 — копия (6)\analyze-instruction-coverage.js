const fs = require('fs');

/**
 * 🔥 ПОЛНЫЙ АНАЛИЗ ПОКРЫТИЯ КАЖДОЙ ИНСТРУКЦИИ
 * ИЗВЛЕКАЕМ ВСЕ КЛЮЧИ И СОПОСТАВЛЯЕМ С ALT ТАБЛИЦАМИ
 */

console.log('🔥 ПОЛНЫЙ АНАЛИЗ ПОКРЫТИЯ ИНСТРУКЦИЙ');

// Загружаем результат последнего теста
let testResult;
try {
    testResult = JSON.parse(fs.readFileSync('complete-flash-loan-direct-test.json', 'utf8'));
    console.log('✅ Результат теста загружен');
} catch (error) {
    console.log('❌ Не удалось загрузить результат теста:', error.message);
    process.exit(1);
}

// Загружаем ALT таблицы
let altConfig;
try {
    altConfig = JSON.parse(fs.readFileSync('final-custom-alt-config.json', 'utf8'));
    console.log('✅ ALT конфигурация загружена');
} catch (error) {
    console.log('❌ Не удалось загрузить ALT конфигурацию:', error.message);
    process.exit(1);
}

// Создаём Set всех адресов в ALT таблицах
const altAddresses = new Set();
altConfig.addresses.forEach(addr => {
    altAddresses.add(addr.address);
});

console.log(`📊 Всего адресов в ALT: ${altAddresses.size}`);

/**
 * 🔍 АНАЛИЗ КАЖДОЙ ИНСТРУКЦИИ
 */
async function analyzeInstructionCoverage() {
    console.log('\n🔍 АНАЛИЗ ПОКРЫТИЯ КАЖДОЙ ИНСТРУКЦИИ...');
    
    // Создаём экземпляр для получения инструкций
    const CompleteFlashLoanStructure = require('./complete-flash-loan-structure.js');
    const { Connection, Keypair } = require('@solana/web3.js');
    
    const connection = new Connection('https://api.devnet.solana.com');
    const wallet = Keypair.generate();
    
    const flashLoanStructure = new CompleteFlashLoanStructure(
        connection,
        wallet,
        '********************************************'
    );
    
    try {
        // Получаем все инструкции
        const result = await flashLoanStructure.createOptimizedFlashLoanTransaction();
        const instructions = result.instructions;
        
        console.log(`📋 Анализируем ${instructions.length} инструкций`);
        
        const instructionAnalysis = [];
        const allUncoveredKeys = new Set();
        
        // Анализируем каждую инструкцию
        for (let i = 0; i < instructions.length; i++) {
            const instruction = instructions[i];
            const keys = instruction.keys || [];
            
            console.log(`\n📊 ИНСТРУКЦИЯ ${i}: ${instruction.programId.toString().slice(0,8)}...`);
            console.log(`   📋 Аккаунтов: ${keys.length}`);
            
            const instructionKeys = [];
            const uncoveredInThisInstruction = [];
            
            // Анализируем каждый ключ в инструкции
            keys.forEach((key, keyIndex) => {
                const address = key.pubkey.toString();
                const isCovered = altAddresses.has(address);
                
                const keyInfo = {
                    index: keyIndex,
                    address: address,
                    shortAddress: address.slice(0,8) + '...',
                    isSigner: key.isSigner,
                    isWritable: key.isWritable,
                    isCovered: isCovered,
                    isDynamic: key.isSigner || address.includes('Sysvar') || address.includes('11111111111111111111111111111111')
                };
                
                instructionKeys.push(keyInfo);
                
                if (!isCovered && !keyInfo.isDynamic) {
                    uncoveredInThisInstruction.push(keyInfo);
                    allUncoveredKeys.add(address);
                }
                
                const status = isCovered ? '✅' : (keyInfo.isDynamic ? '🔄' : '❌');
                console.log(`   ${status} ${keyIndex}: ${keyInfo.shortAddress} ${keyInfo.isSigner ? '(signer)' : ''} ${keyInfo.isWritable ? '(writable)' : ''}`);
            });
            
            const analysis = {
                instructionIndex: i,
                programId: instruction.programId.toString(),
                totalKeys: keys.length,
                coveredKeys: instructionKeys.filter(k => k.isCovered).length,
                uncoveredKeys: uncoveredInThisInstruction.length,
                dynamicKeys: instructionKeys.filter(k => k.isDynamic).length,
                staticKeys: instructionKeys.filter(k => !k.isDynamic).length,
                coveragePercent: instructionKeys.filter(k => !k.isDynamic).length > 0 ? 
                    Math.round((instructionKeys.filter(k => k.isCovered && !k.isDynamic).length / instructionKeys.filter(k => !k.isDynamic).length) * 100) : 100,
                keys: instructionKeys,
                uncoveredStaticKeys: uncoveredInThisInstruction
            };
            
            instructionAnalysis.push(analysis);
            
            console.log(`   📈 Покрытие статических: ${analysis.coveragePercent}% (${analysis.coveredKeys}/${analysis.staticKeys})`);
            if (uncoveredInThisInstruction.length > 0) {
                console.log(`   🚨 Непокрытых статических: ${uncoveredInThisInstruction.length}`);
                uncoveredInThisInstruction.forEach(key => {
                    console.log(`      ❌ ${key.shortAddress}`);
                });
            }
        }
        
        // Создаём итоговый отчёт
        const report = {
            timestamp: new Date().toISOString(),
            totalInstructions: instructions.length,
            altAddressesCount: altAddresses.size,
            instructionAnalysis: instructionAnalysis,
            summary: {
                totalUncoveredKeys: allUncoveredKeys.size,
                uncoveredKeysList: Array.from(allUncoveredKeys),
                biggestInstructions: instructionAnalysis
                    .filter(a => a.totalKeys >= 15)
                    .sort((a, b) => b.totalKeys - a.totalKeys)
                    .slice(0, 10),
                worstCoverage: instructionAnalysis
                    .filter(a => a.staticKeys > 0)
                    .sort((a, b) => a.coveragePercent - b.coveragePercent)
                    .slice(0, 5)
            }
        };
        
        // Сохраняем детальный отчёт
        fs.writeFileSync('instruction-coverage-analysis.json', JSON.stringify(report, null, 2));
        console.log('\n💾 Детальный анализ сохранён в instruction-coverage-analysis.json');
        
        // Выводим итоговую статистику
        console.log('\n🎯 ИТОГОВАЯ СТАТИСТИКА:');
        console.log(`📋 Всего инструкций: ${report.totalInstructions}`);
        console.log(`🔗 Адресов в ALT: ${report.altAddressesCount}`);
        console.log(`❌ Непокрытых ключей: ${report.summary.totalUncoveredKeys}`);
        
        console.log('\n🚨 САМЫЕ БОЛЬШИЕ ИНСТРУКЦИИ:');
        report.summary.biggestInstructions.forEach(inst => {
            console.log(`   ${inst.instructionIndex}: ${inst.totalKeys} ключей, покрытие ${inst.coveragePercent}%`);
        });
        
        console.log('\n❌ ХУДШЕЕ ПОКРЫТИЕ:');
        report.summary.worstCoverage.forEach(inst => {
            console.log(`   ${inst.instructionIndex}: ${inst.coveragePercent}% (${inst.uncoveredKeys} непокрытых)`);
        });
        
        console.log('\n🔥 ВСЕ НЕПОКРЫТЫЕ КЛЮЧИ:');
        report.summary.uncoveredKeysList.forEach((key, index) => {
            console.log(`   ${index + 1}. ${key.slice(0,8)}...${key.slice(-8)}`);
        });
        
        // Создаём файл для добавления в ALT
        const keysToAdd = report.summary.uncoveredKeysList.map((key, index) => ({
            address: key,
            name: `Uncovered Key ${index + 1}`,
            category: 'UNCOVERED_STATIC'
        }));
        
        fs.writeFileSync('keys-to-add-to-alt.json', JSON.stringify({
            timestamp: new Date().toISOString(),
            totalKeys: keysToAdd.length,
            keys: keysToAdd
        }, null, 2));
        
        console.log(`\n💾 Ключи для добавления в ALT сохранены в keys-to-add-to-alt.json`);
        console.log(`🔥 ГОТОВО К ДОБАВЛЕНИЮ: ${keysToAdd.length} КЛЮЧЕЙ`);
        
        return report;
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        return null;
    }
}

// Запускаем анализ
analyzeInstructionCoverage()
    .then(report => {
        if (report) {
            console.log('\n🎉 АНАЛИЗ ЗАВЕРШЁН!');
            console.log(`📊 Найдено ${report.summary.totalUncoveredKeys} непокрытых ключей`);
        } else {
            console.log('\n❌ АНАЛИЗ НЕ УДАЛСЯ!');
        }
        process.exit(0);
    })
    .catch(error => {
        console.error('❌ ФАТАЛЬНАЯ ОШИБКА:', error.message);
        process.exit(1);
    });
