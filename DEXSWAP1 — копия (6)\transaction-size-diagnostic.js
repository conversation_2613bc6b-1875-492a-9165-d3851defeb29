const { Connection, PublicKey, Transaction, TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
const fs = require('fs');

/**
 * 🔍 ДИАГНОСТИЧЕСКИЙ ИНСТРУМЕНТ ДЛЯ АНАЛИЗА ПРОБЛЕМ С РАЗМЕРОМ ТРАНЗАКЦИЙ
 * 
 * Проблемы для анализа:
 * 1. "encoding overruns Uint8Array" - превышение лимита размера транзакции
 * 2. Meteora InvalidInput (6002) - неправильные данные в swap инструкции
 */
class TransactionSizeDiagnostic {
    constructor() {
        this.connection = new Connection('https://api.mainnet-beta.solana.com');
        this.SOLANA_TRANSACTION_LIMIT = 1232; // Официальный лимит Solana
        this.SAFE_MARGIN = 50; // Безопасный запас
        this.TARGET_SIZE = this.SOLANA_TRANSACTION_LIMIT - this.SAFE_MARGIN;
    }

    /**
     * 🔍 АНАЛИЗ ПРОБЛЕМЫ "encoding overruns Uint8Array"
     */
    analyzeEncodingOverrun(instructions, altTables = []) {
        console.log('🔍 АНАЛИЗ ПРОБЛЕМЫ "encoding overruns Uint8Array"...');
        console.log('═══════════════════════════════════════════════════════════');

        try {
            // 1. АНАЛИЗ БЕЗ ALT ТАБЛИЦ
            console.log('\n1️⃣ АНАЛИЗ БЕЗ ALT ТАБЛИЦ:');
            const withoutALTResult = this.calculateTransactionSizeWithoutALT(instructions);
            
            // 2. АНАЛИЗ С ALT ТАБЛИЦАМИ
            console.log('\n2️⃣ АНАЛИЗ С ALT ТАБЛИЦАМИ:');
            const withALTResult = this.calculateTransactionSizeWithALT(instructions, altTables);
            
            // 3. РЕКОМЕНДАЦИИ
            console.log('\n3️⃣ РЕКОМЕНДАЦИИ:');
            this.provideRecommendations(withoutALTResult, withALTResult, instructions.length);
            
            return {
                withoutALT: withoutALTResult,
                withALT: withALTResult,
                canFitInTransaction: withALTResult.success && withALTResult.size <= this.SOLANA_TRANSACTION_LIMIT
            };

        } catch (error) {
            console.error('❌ Ошибка анализа:', error.message);
            return { error: error.message };
        }
    }

    /**
     * 🔍 РАСЧЕТ РАЗМЕРА БЕЗ ALT ТАБЛИЦ
     */
    calculateTransactionSizeWithoutALT(instructions) {
        try {
            console.log('📊 Расчет размера БЕЗ ALT таблиц...');
            
            // Подсчитываем уникальные аккаунты
            const uniqueAccounts = new Set();
            let totalDataSize = 0;
            
            instructions.forEach((ix, index) => {
                // 🔧 БЕЗОПАСНАЯ ПРОВЕРКА ИНСТРУКЦИИ
                if (!ix) {
                    console.log(`   Инструкция ${index + 1}: UNDEFINED`);
                    return;
                }

                const programId = ix.programId ? ix.programId.toString().slice(0, 8) : 'UNDEFINED';
                const keysLength = ix.keys ? ix.keys.length : 0;
                const dataLength = ix.data ? ix.data.length : 0;

                console.log(`   Инструкция ${index + 1}: ${programId}... (${keysLength} аккаунтов, ${dataLength} байт данных)`);

                // Добавляем program ID (с проверкой)
                if (ix.programId) {
                    uniqueAccounts.add(ix.programId.toString());
                }

                // Добавляем все аккаунты (с проверкой)
                if (ix.keys && Array.isArray(ix.keys)) {
                    ix.keys.forEach(key => {
                        if (key && key.pubkey) {
                            uniqueAccounts.add(key.pubkey.toString());
                        }
                    });
                }

                totalDataSize += dataLength;
            });

            const accountsCount = uniqueAccounts.size;
            const accountsSize = accountsCount * 32; // 32 байта на аккаунт
            const instructionsOverhead = instructions.length * 2; // ~2 байта overhead на инструкцию
            const messageOverhead = 64; // Blockhash + signatures + metadata
            
            const estimatedSize = accountsSize + totalDataSize + instructionsOverhead + messageOverhead;
            
            console.log(`   📋 Уникальных аккаунтов: ${accountsCount}`);
            console.log(`   📊 Размер аккаунтов: ${accountsSize} байт`);
            console.log(`   💾 Размер данных: ${totalDataSize} байт`);
            console.log(`   🔧 Overhead: ${instructionsOverhead + messageOverhead} байт`);
            console.log(`   📏 ОБЩИЙ РАЗМЕР: ${estimatedSize} байт`);
            console.log(`   🚨 ПРЕВЫШЕНИЕ ЛИМИТА: ${estimatedSize > this.SOLANA_TRANSACTION_LIMIT ? 'ДА' : 'НЕТ'} (лимит: ${this.SOLANA_TRANSACTION_LIMIT})`);
            
            return {
                success: estimatedSize <= this.SOLANA_TRANSACTION_LIMIT,
                size: estimatedSize,
                accountsCount,
                accountsSize,
                dataSize: totalDataSize,
                overhead: instructionsOverhead + messageOverhead,
                exceedsLimit: estimatedSize > this.SOLANA_TRANSACTION_LIMIT,
                excessBytes: Math.max(0, estimatedSize - this.SOLANA_TRANSACTION_LIMIT)
            };

        } catch (error) {
            console.error('❌ Ошибка расчета без ALT:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔍 РАСЧЕТ РАЗМЕРА С ALT ТАБЛИЦАМИ
     */
    calculateTransactionSizeWithALT(instructions, altTables) {
        try {
            console.log('📊 Расчет размера С ALT таблицами...');
            
            if (!altTables || altTables.length === 0) {
                console.log('⚠️ ALT таблицы не предоставлены');
                return this.calculateTransactionSizeWithoutALT(instructions);
            }

            // Собираем все адреса из ALT таблиц
            const altAddresses = new Set();
            altTables.forEach((alt, index) => {
                if (alt.state && alt.state.addresses) {
                    alt.state.addresses.forEach(addr => {
                        altAddresses.add(addr.toString());
                    });
                    console.log(`   ALT ${index + 1}: ${alt.state.addresses.length} адресов`);
                }
            });

            console.log(`   📊 Всего адресов в ALT: ${altAddresses.size}`);

            // Анализируем покрытие
            const uniqueAccounts = new Set();
            const coveredByALT = new Set();
            const notCoveredByALT = new Set();
            let totalDataSize = 0;
            
            instructions.forEach((ix, index) => {
                // 🔧 БЕЗОПАСНАЯ ПРОВЕРКА ИНСТРУКЦИИ
                if (!ix) {
                    return;
                }

                // Program ID (с проверкой)
                if (ix.programId) {
                    const programId = ix.programId.toString();
                    uniqueAccounts.add(programId);

                    if (altAddresses.has(programId)) {
                        coveredByALT.add(programId);
                    } else {
                        notCoveredByALT.add(programId);
                    }
                }

                // Аккаунты инструкции (с проверкой)
                if (ix.keys && Array.isArray(ix.keys)) {
                    ix.keys.forEach(key => {
                        if (key && key.pubkey) {
                            const keyStr = key.pubkey.toString();
                            uniqueAccounts.add(keyStr);

                            if (altAddresses.has(keyStr)) {
                                coveredByALT.add(keyStr);
                            } else {
                                notCoveredByALT.add(keyStr);
                            }
                        }
                    });
                }

                // Размер данных (с проверкой)
                if (ix.data) {
                    totalDataSize += ix.data.length;
                }
            });

            const totalAccounts = uniqueAccounts.size;
            const coveredAccounts = coveredByALT.size;
            const uncoveredAccounts = notCoveredByALT.size;
            
            // Размер с ALT сжатием
            const uncoveredAccountsSize = uncoveredAccounts * 32; // Только несжатые аккаунты
            const altTablesSize = altTables.length * 32; // 32 байта на ALT таблицу
            const instructionsOverhead = instructions.length * 2;
            const messageOverhead = 64;
            
            const compressedSize = uncoveredAccountsSize + altTablesSize + totalDataSize + instructionsOverhead + messageOverhead;
            
            console.log(`   📋 Всего аккаунтов: ${totalAccounts}`);
            console.log(`   ✅ Покрыто ALT: ${coveredAccounts} (${((coveredAccounts/totalAccounts)*100).toFixed(1)}%)`);
            console.log(`   ❌ НЕ покрыто ALT: ${uncoveredAccounts} (${((uncoveredAccounts/totalAccounts)*100).toFixed(1)}%)`);
            console.log(`   📊 Размер несжатых аккаунтов: ${uncoveredAccountsSize} байт`);
            console.log(`   🗜️ Размер ALT таблиц: ${altTablesSize} байт`);
            console.log(`   💾 Размер данных: ${totalDataSize} байт`);
            console.log(`   🔧 Overhead: ${instructionsOverhead + messageOverhead} байт`);
            console.log(`   📏 СЖАТЫЙ РАЗМЕР: ${compressedSize} байт`);
            console.log(`   🚨 ПРЕВЫШЕНИЕ ЛИМИТА: ${compressedSize > this.SOLANA_TRANSACTION_LIMIT ? 'ДА' : 'НЕТ'} (лимит: ${this.SOLANA_TRANSACTION_LIMIT})`);
            
            // Показываем несжатые аккаунты
            if (uncoveredAccounts > 0) {
                console.log(`\n   🔥 АККАУНТЫ НЕ ПОКРЫТЫЕ ALT (${uncoveredAccounts}):`);
                Array.from(notCoveredByALT).slice(0, 10).forEach((addr, i) => {
                    console.log(`      ${i + 1}. ${addr.slice(0, 8)}...${addr.slice(-8)}`);
                });
                if (uncoveredAccounts > 10) {
                    console.log(`      ... и еще ${uncoveredAccounts - 10} аккаунтов`);
                }
            }
            
            return {
                success: compressedSize <= this.SOLANA_TRANSACTION_LIMIT,
                size: compressedSize,
                totalAccounts,
                coveredAccounts,
                uncoveredAccounts,
                compressionRatio: ((totalAccounts * 32 - compressedSize) / (totalAccounts * 32) * 100).toFixed(1),
                altTablesCount: altTables.length,
                exceedsLimit: compressedSize > this.SOLANA_TRANSACTION_LIMIT,
                excessBytes: Math.max(0, compressedSize - this.SOLANA_TRANSACTION_LIMIT),
                uncoveredAddresses: Array.from(notCoveredByALT)
            };

        } catch (error) {
            console.error('❌ Ошибка расчета с ALT:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔍 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ
     */
    provideRecommendations(withoutALT, withALT, instructionsCount) {
        console.log('💡 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ:');
        
        if (withALT.success) {
            console.log('✅ Транзакция помещается в лимит с ALT таблицами');
            console.log(`   📏 Размер: ${withALT.size}/${this.SOLANA_TRANSACTION_LIMIT} байт`);
            console.log(`   📈 Запас: ${this.SOLANA_TRANSACTION_LIMIT - withALT.size} байт`);
        } else {
            console.log('❌ Транзакция НЕ помещается в лимит даже с ALT таблицами');
            console.log(`   📏 Превышение: ${withALT.excessBytes} байт`);
            
            console.log('\n🔧 ВОЗМОЖНЫЕ РЕШЕНИЯ:');
            console.log('1. Разделить транзакцию на несколько частей');
            console.log('2. Уменьшить количество инструкций');
            console.log('3. Создать дополнительные ALT таблицы для несжатых аккаунтов');
            console.log('4. Оптимизировать данные инструкций');
            
            if (withALT.uncoveredAccounts > 0) {
                console.log(`\n🎯 ПРИОРИТЕТ: Добавить ${withALT.uncoveredAccounts} несжатых аккаунтов в ALT таблицы`);
                console.log(`   💾 Экономия: ~${withALT.uncoveredAccounts * 32} байт`);
            }
        }
        
        if (instructionsCount > 15) {
            console.log(`\n⚠️ МНОГО ИНСТРУКЦИЙ: ${instructionsCount} (рекомендуется < 15)`);
            console.log('   Рассмотрите разделение на несколько транзакций');
        }
    }

    /**
     * 🔍 АНАЛИЗ METEORA SWAP ОШИБКИ 6002
     */
    analyzeMeteoraSwapError(instructions) {
        console.log('\n🔍 АНАЛИЗ METEORA SWAP ОШИБКИ 6002...');
        console.log('═══════════════════════════════════════════════════════════');

        const meteoraProgram = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
        const meteoraInstructions = instructions.filter(ix =>
            ix.programId.toString() === meteoraProgram
        );

        if (meteoraInstructions.length === 0) {
            console.log('⚠️ Meteora инструкции не найдены');
            return { found: false };
        }

        console.log(`📊 Найдено Meteora инструкций: ${meteoraInstructions.length}`);

        meteoraInstructions.forEach((ix, index) => {
            console.log(`\n🔍 METEORA ИНСТРУКЦИЯ ${index + 1}:`);
            console.log(`   Program: ${ix.programId.toString()}`);
            console.log(`   Аккаунтов: ${ix.keys.length}`);
            console.log(`   Данных: ${ix.data.length} байт`);
            console.log(`   Данные (hex): ${ix.data.toString('hex')}`);

            // Анализ первых 8 байт (discriminator)
            if (ix.data.length >= 8) {
                const discriminator = ix.data.slice(0, 8);
                console.log(`   Discriminator: [${Array.from(discriminator).join(', ')}]`);
                console.log(`   Discriminator (hex): ${discriminator.toString('hex')}`);
            }

            // Анализ аккаунтов
            console.log(`   🔑 АККАУНТЫ:`);
            ix.keys.forEach((key, i) => {
                const flags = [];
                if (key.isSigner) flags.push('signer');
                if (key.isWritable) flags.push('writable');
                console.log(`      ${i}: ${key.pubkey.toString()} ${flags.length > 0 ? `(${flags.join(', ')})` : ''}`);
            });
        });

        // Общие проблемы с Meteora Swap
        console.log('\n💡 ВОЗМОЖНЫЕ ПРИЧИНЫ ОШИБКИ 6002 (InvalidInput):');
        console.log('1. Неправильный discriminator инструкции');
        console.log('2. Неправильный порядок аккаунтов');
        console.log('3. Неправильные флаги аккаунтов (signer/writable)');
        console.log('4. Неправильные данные swap (amount, slippage, etc.)');
        console.log('5. Устаревшие bin arrays или неправильные bin array аккаунты');
        console.log('6. Неправильный пул или token accounts');

        return {
            found: true,
            count: meteoraInstructions.length,
            instructions: meteoraInstructions.map(ix => ({
                programId: ix.programId.toString(),
                accountsCount: ix.keys.length,
                dataSize: ix.data.length,
                discriminator: ix.data.length >= 8 ? ix.data.slice(0, 8).toString('hex') : null
            }))
        };
    }

    /**
     * 📊 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ (ОСНОВНОЙ МЕТОД)
     */
    async analyzeTransactionSize(instructions, altTables = [], payerKey = null) {
        console.log('📊 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ...');

        try {
            // Анализ без ALT
            const withoutALT = this.calculateTransactionSizeWithoutALT(instructions);

            // Анализ с ALT
            const withALT = this.calculateTransactionSizeWithALT(instructions, altTables);

            const analysis = {
                instructions: instructions.length,
                altTables: altTables.length,
                sizeWithoutALT: withoutALT.size,
                sizeWithALT: withALT.size,
                totalSize: withALT.size, // Используем размер с ALT как основной
                canFitInTransaction: withALT.success,
                uniqueAccounts: withoutALT.uniqueAccounts,
                coveredByALT: withALT.coveredAccounts || 0,
                uncoveredByALT: withALT.uncoveredAccounts || 0,
                compressionRatio: withoutALT.size > 0 ? (withoutALT.size - withALT.size) / withoutALT.size : 0,
                recommendations: []
            };

            // Добавляем рекомендации
            if (!analysis.canFitInTransaction) {
                analysis.recommendations.push('Разделить транзакцию на части');
                analysis.recommendations.push('Добавить больше аккаунтов в ALT таблицы');
            }

            if (analysis.uncoveredByALT > 0) {
                analysis.recommendations.push(`Добавить ${analysis.uncoveredByALT} аккаунтов в ALT`);
            }

            console.log(`✅ Анализ завершен: ${analysis.totalSize} байт (лимит: ${this.SOLANA_TRANSACTION_LIMIT})`);

            return analysis;

        } catch (error) {
            console.error(`❌ Ошибка анализа размера: ${error.message}`);
            return {
                instructions: instructions.length,
                altTables: altTables.length,
                totalSize: 0,
                canFitInTransaction: false,
                error: error.message
            };
        }
    }

    /**
     * 🔍 ПОЛНЫЙ ДИАГНОСТИЧЕСКИЙ ОТЧЕТ
     */
    async generateFullReport(instructions, altTables = []) {
        console.log('🔍 ГЕНЕРАЦИЯ ПОЛНОГО ДИАГНОСТИЧЕСКОГО ОТЧЕТА...');
        console.log('═══════════════════════════════════════════════════════════');

        const report = {
            timestamp: new Date().toISOString(),
            instructionsCount: instructions.length,
            altTablesCount: altTables.length
        };

        // 1. Анализ размера транзакции
        report.sizeAnalysis = this.analyzeEncodingOverrun(instructions, altTables);

        // 2. Анализ Meteora ошибок
        report.meteoraAnalysis = this.analyzeMeteoraSwapError(instructions);

        // 3. Общие рекомендации
        console.log('\n🎯 ОБЩИЕ РЕКОМЕНДАЦИИ:');
        if (!report.sizeAnalysis.canFitInTransaction) {
            console.log('❌ КРИТИЧНО: Транзакция не помещается в лимит');
            console.log('   Необходимо разделить на несколько транзакций');
        } else {
            console.log('✅ Размер транзакции в пределах лимита');
        }

        if (report.meteoraAnalysis.found) {
            console.log('⚠️ Найдены Meteora инструкции - проверьте корректность данных');
        }

        // Сохраняем отчет
        const reportPath = `diagnostic-report-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 Отчет сохранен: ${reportPath}`);

        return report;
    }
}

module.exports = TransactionSizeDiagnostic;
