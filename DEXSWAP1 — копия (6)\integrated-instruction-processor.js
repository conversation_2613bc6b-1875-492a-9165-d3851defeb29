#!/usr/bin/env node

/**
 * 🔧 ИНТЕГРИРОВАННЫЙ ПРОЦЕССОР ИНСТРУКЦИЙ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Полная обработка инструкций с правильным порядком
 * 📋 ФУНКЦИИ: Нормализация + ALT загрузка + Валидация + Порядок
 * 🔧 ИНТЕГРАЦИЯ: Все компоненты в единой системе
 */

const UnifiedInstructionNormalizer = require('./unified-instruction-normalizer.js');
const JupiterALTLoader = require('./jupiter-alt-loader.js');
const CompleteALTManager = require('./complete-alt-manager.js');

class IntegratedInstructionProcessor {
  constructor(connection, wallet) {
    this.connection = connection;
    this.wallet = wallet;
    
    // Инициализация компонентов
    this.normalizer = new UnifiedInstructionNormalizer();
    this.jupiterALTLoader = new JupiterALTLoader(connection);
    this.altManager = new CompleteALTManager(connection);
    
    // Статистика
    this.stats = {
      processed: 0,
      normalized: 0,
      altLoaded: 0,
      errors: 0,
      totalInstructions: 0,
      totalALT: 0
    };
    
    console.log('🔧 ИНТЕГРИРОВАННЫЙ ПРОЦЕССОР ИНСТРУКЦИЙ ИНИЦИАЛИЗИРОВАН');
    console.log('   ✅ Unified Normalizer готов');
    console.log('   ✅ Jupiter ALT Loader готов');
    console.log('   ✅ Complete ALT Manager готов');
  }

  /**
   * 🎯 ГЛАВНАЯ ФУНКЦИЯ: ПОЛНАЯ ОБРАБОТКА JUPITER ОТВЕТА
   * @param {Object} jupiterResponse - Полный ответ от Jupiter API
   * @returns {Object} - Обработанные инструкции + ALT таблицы
   */
  async processJupiterResponse(jupiterResponse) {
    console.log('\n🎯 ПОЛНАЯ ОБРАБОТКА JUPITER ОТВЕТА');
    console.log('═'.repeat(60));

    try {
      this.stats.processed++;

      // ШАГ 1: ИЗВЛЕЧЕНИЕ ИНСТРУКЦИЙ ИЗ JUPITER ОТВЕТА
      const rawInstructions = this.extractInstructionsFromJupiterResponse(jupiterResponse);
      console.log(`📋 Извлечено инструкций: ${rawInstructions.length}`);

      // ШАГ 2: НОРМАЛИЗАЦИЯ ВСЕХ ИНСТРУКЦИЙ
      const normalizedResult = this.normalizer.normalizeInstructions(rawInstructions, 'JUPITER');
      const normalizedInstructions = normalizedResult.instructions;
      console.log(`✅ Нормализовано инструкций: ${normalizedInstructions.length}`);
      this.stats.normalized += normalizedInstructions.length;

      // ШАГ 3: ЗАГРУЗКА JUPITER ALT ТАБЛИЦ
      const jupiterALT = await this.jupiterALTLoader.loadAllJupiterALT(jupiterResponse);
      console.log(`🪐 Загружено Jupiter ALT: ${jupiterALT.length}`);

      // ШАГ 4: ЗАГРУЗКА ВСЕХ ОСТАЛЬНЫХ ALT (MARGINFI + CUSTOM)
      const allALT = await this.altManager.loadAllALT(jupiterResponse, null);
      console.log(`🔗 Загружено всех ALT: ${allALT.length}`);
      this.stats.altLoaded = allALT.length;

      // ШАГ 5: ПРАВИЛЬНЫЙ ПОРЯДОК ИНСТРУКЦИЙ
      const orderedInstructions = this.applyCorrectInstructionOrder(normalizedInstructions);
      console.log(`📋 Упорядочено инструкций: ${orderedInstructions.length}`);

      // ШАГ 6: ФИНАЛЬНАЯ ВАЛИДАЦИЯ
      const validationResult = this.validateProcessedInstructions(orderedInstructions, allALT);

      const result = {
        instructions: orderedInstructions,
        addressLookupTableAccounts: allALT,
        jupiterALT: jupiterALT,
        validation: validationResult,
        stats: {
          originalInstructions: rawInstructions.length,
          normalizedInstructions: normalizedInstructions.length,
          finalInstructions: orderedInstructions.length,
          totalALT: allALT.length,
          jupiterALT: jupiterALT.length,
          normalizationErrors: normalizedResult.errors.length
        }
      };

      console.log('\n✅ ПОЛНАЯ ОБРАБОТКА ЗАВЕРШЕНА');
      console.log(`   📊 Финальных инструкций: ${orderedInstructions.length}`);
      console.log(`   🗜️ ALT таблиц: ${allALT.length}`);
      console.log(`   ✅ Валидация: ${validationResult.isValid ? 'ПРОЙДЕНА' : 'ПРОВАЛЕНА'}`);

      this.stats.totalInstructions += orderedInstructions.length;
      this.stats.totalALT += allALT.length;

      return result;

    } catch (error) {
      console.error(`❌ Ошибка полной обработки: ${error.message}`);
      this.stats.errors++;
      throw error;
    }
  }

  /**
   * 📋 ИЗВЛЕЧЕНИЕ ИНСТРУКЦИЙ ИЗ JUPITER ОТВЕТА
   */
  extractInstructionsFromJupiterResponse(jupiterResponse) {
    console.log('\n📋 ИЗВЛЕЧЕНИЕ ИНСТРУКЦИЙ ИЗ JUPITER ОТВЕТА');
    console.log('─'.repeat(50));

    const instructions = [];

    try {
      // Проверяем различные поля где могут быть инструкции
      const possibleFields = [
        'setupInstructions',
        'swapInstruction', 
        'cleanupInstruction',
        'instructions',
        'computeBudgetInstructions'
      ];

      possibleFields.forEach(field => {
        if (jupiterResponse[field]) {
          if (Array.isArray(jupiterResponse[field])) {
            console.log(`🔍 Найдено поле ${field}: ${jupiterResponse[field].length} инструкций`);
            instructions.push(...jupiterResponse[field]);
          } else {
            console.log(`🔍 Найдено поле ${field}: 1 инструкция`);
            instructions.push(jupiterResponse[field]);
          }
        }
      });

      // Проверяем вложенные объекты
      if (jupiterResponse.swapTransaction && jupiterResponse.swapTransaction.instructions) {
        console.log(`🔍 Найдены инструкции в swapTransaction: ${jupiterResponse.swapTransaction.instructions.length}`);
        instructions.push(...jupiterResponse.swapTransaction.instructions);
      }

      console.log(`✅ Всего извлечено инструкций: ${instructions.length}`);
      return instructions;

    } catch (error) {
      console.error(`❌ Ошибка извлечения инструкций: ${error.message}`);
      return [];
    }
  }

  /**
   * 📋 ПРИМЕНЕНИЕ ПРАВИЛЬНОГО ПОРЯДКА ИНСТРУКЦИЙ
   * Согласно официальной документации Jupiter:
   * 1. ComputeBudget
   * 2. Setup (создание ATA)
   * 3. Swap
   * 4. Cleanup (unwrap SOL)
   * 5. CloseAccount
   */
  applyCorrectInstructionOrder(instructions) {
    console.log('\n📋 ПРИМЕНЕНИЕ ПРАВИЛЬНОГО ПОРЯДКА ИНСТРУКЦИЙ');
    console.log('─'.repeat(50));

    const categorizedInstructions = {
      computeBudget: [],
      setup: [],
      swap: [],
      cleanup: [],
      closeAccount: [],
      other: []
    };

    // Категоризация инструкций
    instructions.forEach((instruction, index) => {
      const programId = instruction.programId.toString();
      const category = this.categorizeInstruction(instruction, programId);
      
      categorizedInstructions[category].push(instruction);
      console.log(`   ${index + 1}. ${category.toUpperCase()}: ${programId.slice(0, 8)}...`);
    });

    // Сборка в правильном порядке
    const orderedInstructions = [
      ...categorizedInstructions.computeBudget,
      ...categorizedInstructions.setup,
      ...categorizedInstructions.swap,
      ...categorizedInstructions.cleanup,
      ...categorizedInstructions.closeAccount,
      ...categorizedInstructions.other
    ];

    console.log(`✅ ПОРЯДОК ИНСТРУКЦИЙ ПРИМЕНЕН:`);
    console.log(`   ComputeBudget: ${categorizedInstructions.computeBudget.length}`);
    console.log(`   Setup: ${categorizedInstructions.setup.length}`);
    console.log(`   Swap: ${categorizedInstructions.swap.length}`);
    console.log(`   Cleanup: ${categorizedInstructions.cleanup.length}`);
    console.log(`   CloseAccount: ${categorizedInstructions.closeAccount.length}`);
    console.log(`   Other: ${categorizedInstructions.other.length}`);

    return orderedInstructions;
  }

  /**
   * 🔍 КАТЕГОРИЗАЦИЯ ИНСТРУКЦИИ
   */
  categorizeInstruction(instruction, programId) {
    // ComputeBudget Program
    if (programId === 'ComputeBudget111111111111111111111111111111') {
      return 'computeBudget';
    }

    // Token Program
    if (programId === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
      const dataHex = instruction.data.toString('hex');
      const firstByte = dataHex.slice(0, 2);
      
      if (firstByte === '09') { // CloseAccount
        return 'closeAccount';
      } else if (firstByte === '11') { // SyncNative (unwrap SOL)
        return 'cleanup';
      } else {
        return 'setup'; // InitializeAccount, Transfer, etc.
      }
    }

    // Associated Token Program
    if (programId === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') {
      return 'setup';
    }

    // System Program
    if (programId === '11111111111111111111111111111111') {
      return 'setup';
    }

    // Jupiter Programs (swap)
    const jupiterPrograms = [
      'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4', // Jupiter V6
      'JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB'  // Jupiter V4
    ];

    if (jupiterPrograms.includes(programId)) {
      return 'swap';
    }

    // Остальные DEX программы (swap)
    const dexPrograms = [
      '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM', // Orca
      '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8', // Raydium
      'MERLuDFBMmsHnsBPZw2sDQZHvXFMwp8EdjudcU2HKky'  // Meteora
    ];

    if (dexPrograms.includes(programId)) {
      return 'swap';
    }

    return 'other';
  }

  /**
   * ✅ ВАЛИДАЦИЯ ОБРАБОТАННЫХ ИНСТРУКЦИЙ
   */
  validateProcessedInstructions(instructions, altAccounts) {
    console.log('\n✅ ВАЛИДАЦИЯ ОБРАБОТАННЫХ ИНСТРУКЦИЙ');
    console.log('─'.repeat(50));

    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      stats: {
        totalInstructions: instructions.length,
        totalALT: altAccounts.length,
        estimatedSize: 0
      }
    };

    try {
      // Проверка инструкций
      instructions.forEach((instruction, index) => {
        if (!instruction.programId) {
          validation.errors.push(`Инструкция ${index}: отсутствует programId`);
        }
        
        if (!instruction.keys || !Array.isArray(instruction.keys)) {
          validation.errors.push(`Инструкция ${index}: отсутствуют keys`);
        }
        
        if (!Buffer.isBuffer(instruction.data)) {
          validation.errors.push(`Инструкция ${index}: data не является Buffer`);
        }
      });

      // Проверка ALT
      if (altAccounts.length === 0) {
        validation.warnings.push('Отсутствуют ALT таблицы - транзакция может быть большой');
      }

      // Оценка размера
      validation.stats.estimatedSize = this.estimateTransactionSize(instructions, altAccounts);

      if (validation.stats.estimatedSize > 1232) {
        validation.errors.push(`Оценочный размер транзакции слишком большой: ${validation.stats.estimatedSize} байт`);
      }

      validation.isValid = validation.errors.length === 0;

      console.log(`✅ ВАЛИДАЦИЯ ЗАВЕРШЕНА:`);
      console.log(`   Статус: ${validation.isValid ? 'ПРОЙДЕНА' : 'ПРОВАЛЕНА'}`);
      console.log(`   Ошибок: ${validation.errors.length}`);
      console.log(`   Предупреждений: ${validation.warnings.length}`);
      console.log(`   Оценочный размер: ${validation.stats.estimatedSize} байт`);

      return validation;

    } catch (error) {
      validation.isValid = false;
      validation.errors.push(`Ошибка валидации: ${error.message}`);
      return validation;
    }
  }

  /**
   * 📊 ОЦЕНКА РАЗМЕРА ТРАНЗАКЦИИ
   */
  estimateTransactionSize(instructions, altAccounts) {
    // Базовый размер транзакции
    let size = 64; // Подпись + заголовки

    // Размер инструкций (приблизительно)
    size += instructions.length * 50; // ~50 байт на инструкцию

    // Размер ключей (с учетом ALT сжатия)
    const totalKeys = instructions.reduce((sum, ix) => sum + ix.keys.length, 0);
    const compressedKeys = Math.max(0, totalKeys - (altAccounts.length * 30)); // ALT сжимает ~30 ключей каждая
    size += compressedKeys * 32; // 32 байта на несжатый ключ

    // ALT ссылки
    size += altAccounts.length * 32;

    return size;
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    return {
      ...this.stats,
      normalizerStats: this.normalizer.getStats(),
      jupiterALTStats: this.jupiterALTLoader.getStats(),
      altManagerStats: this.altManager.getStats()
    };
  }
}

module.exports = IntegratedInstructionProcessor;
