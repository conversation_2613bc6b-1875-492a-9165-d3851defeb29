#!/bin/bash

# 🔥 SOLANA BUG BOUNTY HUNTER - ENVIRONMENT SETUP
# Автоматическая настройка всей среды для охоты за багами

echo "🚀 Setting up Solana Bug Bounty Hunter Environment..."

# Проверяем операционную систему
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    OS="windows"
else
    echo "❌ Unsupported OS: $OSTYPE"
    exit 1
fi

echo "✅ Detected OS: $OS"

# Устанавливаем Rust
echo "📦 Installing Rust..."
if ! command -v rustc &> /dev/null; then
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    echo "✅ Rust installed successfully"
else
    echo "✅ Rust already installed"
fi

# Устанавливаем Solana CLI
echo "📦 Installing Solana CLI..."
if ! command -v solana &> /dev/null; then
    sh -c "$(curl -sSfL https://release.solana.com/stable/install)"
    export PATH="$HOME/.local/share/solana/install/active_release/bin:$PATH"
    echo "✅ Solana CLI installed successfully"
else
    echo "✅ Solana CLI already installed"
fi

# Устанавливаем Anchor
echo "📦 Installing Anchor Framework..."
if ! command -v anchor &> /dev/null; then
    cargo install --git https://github.com/coral-xyz/anchor avm --locked --force
    avm install latest
    avm use latest
    echo "✅ Anchor installed successfully"
else
    echo "✅ Anchor already installed"
fi

# Создаем директорию проекта
echo "📁 Creating project directory..."
mkdir -p solana-bug-hunter
cd solana-bug-hunter

# Инициализируем Cargo проект
echo "📦 Initializing Cargo project..."
cargo init --name solana-bug-hunter

# Создаем конфигурацию Solana
echo "⚙️ Configuring Solana..."
solana config set --url https://api.devnet.solana.com
solana-keygen new --no-bip39-passphrase --silent --outfile ~/.config/solana/id.json

# Получаем немного SOL для тестирования
echo "💰 Requesting devnet SOL..."
solana airdrop 2

# Устанавливаем дополнительные инструменты
echo "🛠️ Installing additional tools..."

# Git (если не установлен)
if ! command -v git &> /dev/null; then
    if [[ "$OS" == "linux" ]]; then
        sudo apt-get update && sudo apt-get install -y git
    elif [[ "$OS" == "macos" ]]; then
        xcode-select --install
    fi
fi

# Node.js и npm (для некоторых инструментов)
if ! command -v node &> /dev/null; then
    if [[ "$OS" == "linux" ]]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == "macos" ]]; then
        brew install node
    fi
fi

# Python (для некоторых скриптов)
if ! command -v python3 &> /dev/null; then
    if [[ "$OS" == "linux" ]]; then
        sudo apt-get install -y python3 python3-pip
    elif [[ "$OS" == "macos" ]]; then
        brew install python3
    fi
fi

echo "🎉 Environment setup completed!"
echo "📍 Project location: $(pwd)"
echo "🔑 Solana keypair: ~/.config/solana/id.json"
echo "💰 Devnet SOL balance: $(solana balance)"
echo ""
echo "🚀 Ready to start bug hunting!"
echo "Next step: Run 'cargo run' to start the bug hunter"
