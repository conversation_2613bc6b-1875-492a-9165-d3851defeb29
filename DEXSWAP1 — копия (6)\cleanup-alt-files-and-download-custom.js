/**
 * 🗑️ ОЧИСТКА ВСЕХ ЛИШНИХ ALT ФАЙЛОВ И СКАЧИВАНИЕ ОБНОВЛЕННОЙ КАСТОМНОЙ ALT
 * 
 * ✅ Оставляем только 4 таблицы: 3 MarginFi + 1 Custom
 * ❌ Удаляем все лишние файлы ALT
 * 🔄 Скачиваем обновленную кастомную ALT: FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe
 */

const fs = require('fs');
const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config();

// 🗑️ СПИСОК ВСЕХ ЛИШНИХ ALT ФАЙЛОВ ДЛЯ УДАЛЕНИЯ
const ALT_FILES_TO_DELETE = [
    // Кэш файлы (устаревшие)
    'all-alt-tables-cache.json',
    'custom-alt-cache.json',
    'meteora-alt-cache.json',
    'meteora-alt-cache-backup.json',
    'meteora-alt-cache-before-256-fix.json',
    'meteora-alt-cache-before-custom.json',
    'meteora-alt-cache-before-dedup.json',
    'meteora-alt-cache-before-removal.json',
    'meteora-alt-cache-clean.json',
    'marginfi-alt-cache.json',
    'marginfi-alt-cache-backup.json',
    'marginfi-alt-cache-before-dedup.json',
    'marginfi-alt-cache-clean.json',
    'official-marginfi-alt-cache.json',
    
    // Конфигурации (лишние)
    'alt-loading-config.json',
    'universal-alt-config.json',
    'unified-7-alt-system.json',
    'jupiter-meteora-alt-tables.json',
    'meteora-alt-addresses.json',
    'meteora-alt-detailed.json',
    'enhanced-arbitrage-alt.json',
    'specialized-arbitrage-alt.json',
    'clean-alt-system-no-duplicates.json',
    'fixed-alt-system-no-problematic.json',
    
    // Анализы (лишние)
    'alt-compression-analysis.json',
    'alt-duplication-investigation.json',
    'full-alt-mechanism-report.json',
    'alt-update-error.json',
    'alt-update-result.json',
    
    // Скрипты (устаревшие)
    'alt-tables-manager.js',
    'quick-7-alt-loader.js',
    'ultra-fast-alt-loader.js',
    'ultra-fast-alt-loader-unified.js',
    'quick-alt-loader.js',
    'jupiter-alt-fetcher.js',
    'jupiter-alt-loader.js',
    'jupiter-alt-manager.js',
    'meteora-alt-integration.js',
    'meteora-alt-ready.js',
    'official-alt-manager.js',
    'official-alt-detector.js',
    'official-alt-validator.js',
    'real-alt-manager.js',
    'clean-alt-manager.js',
    'fixed-alt-manager.js',
    'complete-alt-manager.js',
    
    // Создание/обновление (лишние)
    'create-unified-7-alt-system.js',
    'create-enhanced-alt-table.js',
    'add-custom-alt-table.js',
    'update-alt-system-clean.js',
    
    // Анализаторы (лишние)
    'full-alt-mechanism-analyzer.js',
    'analyze-alt-compression.js',
    'analyze-alt-coverage-detailed.js',
    'alt-compression-fixer.js',
    'selective-alt-optimizer.js'
];

// ✅ ПРАВИЛЬНЫЕ ALT ТАБЛИЦЫ (ТОЛЬКО 4!)
const CORRECT_ALT_TABLES = {
    marginfi1: 'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // 256 адресов
    marginfi2: '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // 256 адресов  
    marginfi3: 'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // 19 адресов
    custom: 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'    // ОБНОВЛЕННАЯ с пулами Meteora
};

async function cleanupALTFilesAndDownloadCustom() {
    try {
        console.log('🗑️ ОЧИСТКА ЛИШНИХ ALT ФАЙЛОВ И СКАЧИВАНИЕ ОБНОВЛЕННОЙ КАСТОМНОЙ ALT...');
        console.log('=' .repeat(80));
        
        // 1. Удаляем все лишние файлы
        console.log('\n🗑️ УДАЛЕНИЕ ЛИШНИХ ФАЙЛОВ:');
        let deletedCount = 0;
        let notFoundCount = 0;
        
        for (const fileName of ALT_FILES_TO_DELETE) {
            try {
                if (fs.existsSync(fileName)) {
                    fs.unlinkSync(fileName);
                    console.log(`   ✅ Удален: ${fileName}`);
                    deletedCount++;
                } else {
                    console.log(`   ⚪ Не найден: ${fileName}`);
                    notFoundCount++;
                }
            } catch (error) {
                console.log(`   ❌ Ошибка удаления ${fileName}: ${error.message}`);
            }
        }
        
        console.log(`\n📊 РЕЗУЛЬТАТ УДАЛЕНИЯ:`);
        console.log(`   ✅ Удалено файлов: ${deletedCount}`);
        console.log(`   ⚪ Не найдено: ${notFoundCount}`);
        console.log(`   📋 Всего в списке: ${ALT_FILES_TO_DELETE.length}`);
        
        // 2. Подключаемся к RPC
        console.log('\n🔗 ПОДКЛЮЧЕНИЕ К RPC...');
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение установлено');
        
        // 3. Скачиваем все 4 правильные ALT таблицы
        console.log('\n📥 СКАЧИВАНИЕ ПРАВИЛЬНЫХ ALT ТАБЛИЦ:');
        const correctALTData = {
            timestamp: new Date().toISOString(),
            source: "Cleaned ALT Tables - Only 4 Correct Tables",
            totalTables: 4,
            totalAccounts: 0,
            tables: {}
        };
        
        for (const [name, address] of Object.entries(CORRECT_ALT_TABLES)) {
            try {
                console.log(`\n📋 Загрузка ${name}: ${address}`);
                
                const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
                
                if (altAccount?.value) {
                    const addressCount = altAccount.value.state.addresses.length;
                    const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
                    
                    correctALTData.tables[name] = {
                        address: address,
                        accountCount: addressCount,
                        addresses: addresses,
                        valid: true,
                        loadedAt: new Date().toISOString()
                    };
                    
                    correctALTData.totalAccounts += addressCount;
                    
                    console.log(`   ✅ ${name}: ${addressCount} адресов`);
                    
                    // Показываем первые несколько адресов для проверки
                    console.log(`   📋 Первые адреса:`);
                    addresses.slice(0, 3).forEach((addr, idx) => {
                        console.log(`      ${idx + 1}. ${addr.slice(0, 8)}...${addr.slice(-8)}`);
                    });
                    
                    if (name === 'custom') {
                        console.log(`   🔥 КАСТОМНАЯ ALT ОБНОВЛЕНА! Адресов: ${addressCount}`);
                        
                        // Проверяем наличие адресов пулов Meteора
                        const meteoraPools = [
                            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
                            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
                            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'  // Pool 3
                        ];
                        
                        const foundPools = meteoraPools.filter(pool => addresses.includes(pool));
                        console.log(`   🌪️ Найдено пулов Meteora: ${foundPools.length}/3`);
                        foundPools.forEach(pool => {
                            console.log(`      ✅ ${pool}`);
                        });
                    }
                    
                } else {
                    console.log(`   ❌ ${name}: ALT не найдена`);
                    correctALTData.tables[name] = {
                        address: address,
                        accountCount: 0,
                        addresses: [],
                        valid: false,
                        error: "ALT not found"
                    };
                }
                
            } catch (error) {
                console.log(`   ❌ Ошибка загрузки ${name}: ${error.message}`);
                correctALTData.tables[name] = {
                    address: address,
                    accountCount: 0,
                    addresses: [],
                    valid: false,
                    error: error.message
                };
            }
        }
        
        // 4. Сохраняем правильные данные
        console.log('\n💾 СОХРАНЕНИЕ ПРАВИЛЬНЫХ ALT ДАННЫХ:');
        
        const correctCacheFile = 'correct-alt-tables-cache.json';
        fs.writeFileSync(correctCacheFile, JSON.stringify(correctALTData, null, 2));
        console.log(`✅ Сохранено: ${correctCacheFile}`);
        
        // 5. Итоговый отчет
        console.log('\n🎯 ИТОГОВЫЙ ОТЧЕТ:');
        console.log('=' .repeat(80));
        console.log(`🗑️ Удалено лишних файлов: ${deletedCount}`);
        console.log(`📥 Загружено ALT таблиц: ${Object.keys(correctALTData.tables).length}/4`);
        console.log(`📊 Всего адресов: ${correctALTData.totalAccounts}`);
        
        console.log('\n✅ ПРАВИЛЬНЫЕ ALT ТАБЛИЦЫ:');
        Object.entries(correctALTData.tables).forEach(([name, data]) => {
            if (data.valid) {
                console.log(`   ✅ ${name}: ${data.accountCount} адресов`);
            } else {
                console.log(`   ❌ ${name}: ОШИБКА - ${data.error}`);
            }
        });
        
        console.log('\n🎉 ОЧИСТКА И ОБНОВЛЕНИЕ ЗАВЕРШЕНЫ!');
        console.log('🔥 Теперь система использует только 4 правильные ALT таблицы');
        
        return {
            success: true,
            deletedFiles: deletedCount,
            loadedTables: Object.keys(correctALTData.tables).length,
            totalAccounts: correctALTData.totalAccounts,
            cacheFile: correctCacheFile
        };
        
    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        console.error(error.stack);
        return {
            success: false,
            error: error.message
        };
    }
}

// Запуск очистки
if (require.main === module) {
    cleanupALTFilesAndDownloadCustom()
        .then(result => {
            if (result.success) {
                console.log('\n🎉 ОЧИСТКА УСПЕШНО ЗАВЕРШЕНА!');
                process.exit(0);
            } else {
                console.log('\n💥 ОЧИСТКА ПРОВАЛЕНА!');
                process.exit(1);
            }
        })
        .catch(error => {
            console.error('\n💥 ФАТАЛЬНАЯ ОШИБКА:', error);
            process.exit(1);
        });
}

module.exports = { cleanupALTFilesAndDownloadCustom, CORRECT_ALT_TABLES };
