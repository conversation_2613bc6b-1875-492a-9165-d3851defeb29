#!/usr/bin/env powershell

Write-Host "🛑 ЗАПУСК БЕЗОПАСНОГО БОТА БЕЗ РЕАЛЬНЫХ ТРАНЗАКЦИЙ!" -ForegroundColor Red
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Yellow
Write-Host "🧪 РЕЖИМ: Только симуляция и тестирование" -ForegroundColor Green
Write-Host "🛑 РЕАЛЬНЫЕ ТРАНЗАКЦИИ: Полностью отключены" -ForegroundColor Red
Write-Host "✅ БЕЗОПАСНОСТЬ: Максимальная" -ForegroundColor Green
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Yellow
Write-Host ""

Write-Host "🔧 Останавливаем все существующие процессы Node.js..." -ForegroundColor Cyan
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Все процессы Node.js остановлены!" -ForegroundColor Green
} catch {
    Write-Host "ℹ️ Процессы Node.js не найдены или уже остановлены" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Запускаем безопасную версию бота..." -ForegroundColor Green
Write-Host ""

# Устанавливаем переменные окружения для безопасности
$env:TEST_MODE = "true"
$env:DISABLE_REAL_TRADING = "true"
$env:DISABLE_REAL_TRANSACTIONS = "true"
$env:SIMULATION_ONLY = "true"
$env:EMERGENCY_SAFE_MODE = "true"

# Запускаем безопасный бот
node test-bot-safe.js

Write-Host ""
Write-Host "🛑 Безопасный бот завершен!" -ForegroundColor Red
Read-Host "Нажмите Enter для выхода..."
