/**
 * 🔍 ДИАГНОСТИКА ПОЧЕМУ MARGINFI FLASH LOAN НЕ РАБОТАЕТ
 * 
 * Анализирует:
 * 1. Состояние MarginFi аккаунта
 * 2. Ликвидность банков
 * 3. Health factor
 * 4. Проверки check_flashloan_can_start
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class MarginFiFlashLoanDiagnostic {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      console.log('🔧 ИНИЦИАЛИЗАЦИЯ ДИАГНОСТИКИ MARGINFI FLASH LOAN');
      console.log('═══════════════════════════════════════════════════════════════');

      // Загружаем wallet
      let walletData;
      try {
        walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      } catch (walletError) {
        console.log('❌ Не удалось загрузить wallet.json');
        console.log('💡 Убедитесь что файл wallet.json существует в текущей директории');
        return false;
      }

      // Создаем Keypair из массива байтов
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);

      if (!this.wallet || !this.wallet.publicKey) {
        console.log('❌ Wallet не создан или не имеет publicKey');
        return false;
      }
      console.log(`💼 Wallet загружен: ${this.wallet.publicKey.toString()}`);

      // Создаем MarginFi client
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client создан');

      // Получаем MarginFi аккаунты
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      console.log(`📊 Найдено MarginFi аккаунтов: ${accounts.length}`);

      if (accounts.length > 0) {
        this.marginfiAccount = accounts[0];
        console.log(`✅ Используем аккаунт: ${this.marginfiAccount.address.toString()}`);
      } else {
        console.log('❌ MarginFi аккаунты не найдены');
        console.log('💡 Возможно нужно создать MarginFi аккаунт');
        return false;
      }

      return true;

    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      console.error(`🔍 Stack trace: ${error.stack}`);
      return false;
    }
  }

  async diagnoseFlashLoanFailure() {
    console.log('\n🔍 ДИАГНОСТИКА ПРИЧИН НЕУДАЧИ FLASH LOAN');
    console.log('═══════════════════════════════════════════════════════════════');

    // 1. Проверяем состояние аккаунта
    await this.checkAccountState();

    // 2. Проверяем ликвидность банков
    await this.checkBankLiquidity();

    // 3. Проверяем health factor
    await this.checkHealthFactor();

    // 4. Проверяем флаги аккаунта
    await this.checkAccountFlags();

    // 5. Тестируем создание flash loan инструкций
    await this.testFlashLoanInstructions();
  }

  async checkAccountState() {
    console.log('\n1️⃣ ПРОВЕРКА СОСТОЯНИЯ MARGINFI АККАУНТА');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      const balances = this.marginfiAccount.activeBalances;
      console.log(`📊 Активных позиций: ${balances.length}`);

      if (balances.length === 0) {
        console.log('✅ Аккаунт пустой - идеален для flash loans');
        return;
      }

      let totalAssets = 0;
      let totalLiabilities = 0;

      for (const balance of balances) {
        const bank = this.marginfiClient.getBankByPk(balance.bankPk);
        const symbol = bank.tokenSymbol || 'UNKNOWN';
        const amount = balance.computeUsdValue(bank, 'asset').toNumber();
        const debt = balance.computeUsdValue(bank, 'liability').toNumber();

        console.log(`   ${symbol}: Assets $${amount.toFixed(2)}, Debt $${debt.toFixed(2)}`);
        totalAssets += amount;
        totalLiabilities += debt;
      }

      console.log(`💰 Общие активы: $${totalAssets.toFixed(2)}`);
      console.log(`💸 Общие долги: $${totalLiabilities.toFixed(2)}`);
      console.log(`💵 Чистая стоимость: $${(totalAssets - totalLiabilities).toFixed(2)}`);

      if (totalLiabilities > 0) {
        console.log('⚠️ ЕСТЬ ДОЛГИ - это может блокировать flash loans');
        console.log('💡 Рекомендация: Погасите долги или используйте чистый аккаунт');
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки состояния: ${error.message}`);
    }
  }

  async checkBankLiquidity() {
    console.log('\n2️⃣ ПРОВЕРКА ЛИКВИДНОСТИ БАНКОВ');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      const usdcBank = this.marginfiClient.getBankByTokenSymbol('USDC');
      if (!usdcBank) {
        console.log('❌ USDC банк не найден');
        return;
      }

      const capacity = usdcBank.computeRemainingCapacity();
      const borrowCapacity = capacity.borrowCapacity.div(1000000).toNumber(); // USDC имеет 6 decimals

      console.log(`💰 USDC банк ликвидность:`);
      console.log(`   Доступно для займа: ${borrowCapacity.toFixed(2)} USDC`);
      console.log(`   Банк адрес: ${usdcBank.address.toString()}`);

      if (borrowCapacity < 50000) {
        console.log('⚠️ НИЗКАЯ ЛИКВИДНОСТЬ - может быть причиной неудачи');
      } else {
        console.log('✅ Ликвидность достаточная');
      }

      // Проверяем другие банки
      const banks = this.marginfiClient.banks;
      console.log(`\n📊 Всего банков: ${banks.length}`);
      
      for (const bank of banks.slice(0, 5)) { // Показываем первые 5
        const symbol = bank.tokenSymbol || 'UNKNOWN';
        const capacity = bank.computeRemainingCapacity();
        console.log(`   ${symbol}: ${capacity.borrowCapacity.toString()} единиц`);
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки ликвидности: ${error.message}`);
    }
  }

  async checkHealthFactor() {
    console.log('\n3️⃣ ПРОВЕРКА HEALTH FACTOR');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      const healthComponents = this.marginfiAccount.computeHealthComponents();
      console.log(`🏥 Health Components:`);
      console.log(`   Assets: ${healthComponents.assets.toFixed(2)}`);
      console.log(`   Liabilities: ${healthComponents.liabilities.toFixed(2)}`);

      const healthFactor = this.marginfiAccount.computeHealthFactor();
      console.log(`🏥 Health Factor: ${healthFactor ? healthFactor.toFixed(4) : 'UNLIMITED'}`);

      if (healthFactor && healthFactor < 1.1) {
        console.log('❌ НИЗКИЙ HEALTH FACTOR - блокирует flash loans');
        console.log('💡 Рекомендация: Добавьте коллатерал или погасите долги');
      } else {
        console.log('✅ Health Factor в норме');
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки health factor: ${error.message}`);
    }
  }

  async checkAccountFlags() {
    console.log('\n4️⃣ ПРОВЕРКА ФЛАГОВ АККАУНТА');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Получаем raw данные аккаунта
      const accountInfo = await this.connection.getAccountInfo(this.marginfiAccount.address);
      if (!accountInfo) {
        console.log('❌ Не удалось получить данные аккаунта');
        return;
      }

      console.log(`📊 Размер данных аккаунта: ${accountInfo.data.length} байт`);
      console.log(`👤 Владелец: ${accountInfo.owner.toString()}`);

      // Проверяем флаги (если возможно)
      // Флаги обычно находятся в начале данных аккаунта
      const flags = accountInfo.data.readUInt8(0);
      console.log(`🏁 Флаги аккаунта: ${flags} (0x${flags.toString(16)})`);

      if (flags & 1) {
        console.log('⚠️ АККАУНТ В СОСТОЯНИИ FLASH LOAN - это может блокировать новые займы');
      } else {
        console.log('✅ Аккаунт не в состоянии flash loan');
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки флагов: ${error.message}`);
    }
  }

  async testFlashLoanInstructions() {
    console.log('\n5️⃣ ТЕСТ СОЗДАНИЯ FLASH LOAN ИНСТРУКЦИЙ');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      console.log('🧪 Тестируем buildFlashLoanTx с пустыми инструкциями...');

      const testTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: [], // Пустые инструкции
        signers: []
      });

      if (testTx) {
        console.log('✅ buildFlashLoanTx работает');
        console.log(`📏 Размер транзакции: ${testTx.serialize().length} байт`);
      } else {
        console.log('❌ buildFlashLoanTx вернул null');
      }

    } catch (error) {
      console.error(`❌ Ошибка тестирования flash loan: ${error.message}`);
      
      if (error.message.includes('6009')) {
        console.log('🚨 ОШИБКА 6009: RiskEngine rejected operation');
        console.log('💡 Причины: низкий health factor, долги, или проблемы с коллатералом');
      } else if (error.message.includes('6027')) {
        console.log('🚨 ОШИБКА 6027: Illegal flashloan');
        console.log('💡 Причины: неправильная структура инструкций или end_index');
      } else {
        console.log(`💡 Неизвестная ошибка: ${error.message}`);
      }
    }
  }
}

async function main() {
  const diagnostic = new MarginFiFlashLoanDiagnostic();
  
  const initialized = await diagnostic.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать диагностику');
    return;
  }

  await diagnostic.diagnoseFlashLoanFailure();

  console.log('\n🎯 ЗАКЛЮЧЕНИЕ ДИАГНОСТИКИ');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('💡 Проверьте результаты выше для выявления проблем');
  console.log('💡 Основные причины неудач flash loan:');
  console.log('   1. Низкий health factor');
  console.log('   2. Существующие долги в аккаунте');
  console.log('   3. Недостаточная ликвидность банка');
  console.log('   4. Неправильная структура инструкций');
  console.log('   5. Проблемы с oracle данными');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MarginFiFlashLoanDiagnostic;
