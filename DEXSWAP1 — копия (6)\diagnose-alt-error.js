/**
 * 🔍 ДИАГНОСТИКА ОШИБКИ ALT "account type mismatch 2 != 1"
 * 
 * Этот скрипт помогает диагностировать проблемы с Address Lookup Tables
 * и ошибки десериализации типа "account type mismatch"
 */

const { Connection, PublicKey, AddressLookupTableAccount } = require('@solana/web3.js');

class ALTDiagnostic {
  constructor() {
    this.connection = new Connection('https://api.mainnet-beta.solana.com');
    this.ALT_PROGRAM_ID = 'AddressLookupTab1e1111111111111111111111111';
  }

  /**
   * 🔍 ДИАГНОСТИКА КОНКРЕТНОГО ALT АДРЕСА
   */
  async diagnoseALT(altAddress) {
    console.log(`\n🔍 ДИАГНОСТИКА ALT: ${altAddress}`);
    console.log(`${'='.repeat(60)}`);

    try {
      const pubkey = new PublicKey(altAddress);
      const accountInfo = await this.connection.getAccountInfo(pubkey);

      if (!accountInfo) {
        console.log(`❌ АККАУНТ НЕ НАЙДЕН: ${altAddress}`);
        return { valid: false, error: 'Account not found' };
      }

      console.log(`✅ Аккаунт найден`);
      console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
      console.log(`   🏦 Owner: ${accountInfo.owner.toString()}`);
      console.log(`   💰 Lamports: ${accountInfo.lamports}`);

      // Проверяем owner
      if (accountInfo.owner.toString() !== this.ALT_PROGRAM_ID) {
        console.log(`❌ НЕПРАВИЛЬНЫЙ OWNER!`);
        console.log(`   Ожидается: ${this.ALT_PROGRAM_ID}`);
        console.log(`   Получен:   ${accountInfo.owner.toString()}`);
        return { valid: false, error: 'Wrong owner' };
      }

      // Проверяем размер данных
      if (accountInfo.data.length < 56) {
        console.log(`❌ ДАННЫЕ СЛИШКОМ МАЛЕНЬКИЕ!`);
        console.log(`   Минимум: 56 байт`);
        console.log(`   Получено: ${accountInfo.data.length} байт`);
        return { valid: false, error: 'Data too small' };
      }

      // Проверяем typeIndex
      const typeIndex = accountInfo.data[0];
      console.log(`   🔢 Type Index: ${typeIndex}`);

      if (typeIndex !== 1) {
        console.log(`❌ НЕПРАВИЛЬНЫЙ TYPE INDEX!`);
        console.log(`   Ожидается: 1 (Address Lookup Table)`);
        console.log(`   Получен:   ${typeIndex}`);
        
        // Определяем тип аккаунта
        const accountType = this.getAccountType(typeIndex);
        console.log(`   📋 Тип аккаунта: ${accountType}`);
        
        return { valid: false, error: `Wrong type index: ${typeIndex}` };
      }

      // Пытаемся десериализовать
      try {
        const altAccount = new AddressLookupTableAccount({
          key: pubkey,
          state: AddressLookupTableAccount.deserialize(accountInfo.data)
        });

        console.log(`✅ ДЕСЕРИАЛИЗАЦИЯ УСПЕШНА!`);
        console.log(`   📊 Адресов в таблице: ${altAccount.state.addresses?.length || 0}`);
        console.log(`   🔄 Активна: ${altAccount.isActive()}`);
        console.log(`   📅 Deactivation Slot: ${altAccount.state.deactivationSlot}`);

        return { valid: true, account: altAccount };

      } catch (deserializeError) {
        console.log(`❌ ОШИБКА ДЕСЕРИАЛИЗАЦИИ: ${deserializeError.message}`);
        return { valid: false, error: deserializeError.message };
      }

    } catch (error) {
      console.log(`❌ ОБЩАЯ ОШИБКА: ${error.message}`);
      return { valid: false, error: error.message };
    }
  }

  /**
   * 🔍 ОПРЕДЕЛЕНИЕ ТИПА АККАУНТА ПО TYPE INDEX
   */
  getAccountType(typeIndex) {
    const types = {
      0: 'Uninitialized',
      1: 'Address Lookup Table',
      2: 'Token Account',
      3: 'Mint Account',
      4: 'Multisig Account',
      5: 'Program Account'
    };

    return types[typeIndex] || `Unknown (${typeIndex})`;
  }

  /**
   * 🔍 ДИАГНОСТИКА СПИСКА ALT АДРЕСОВ
   */
  async diagnoseMultipleALTs(altAddresses) {
    console.log(`\n🔍 ДИАГНОСТИКА ${altAddresses.length} ALT АДРЕСОВ`);
    console.log(`${'='.repeat(60)}`);

    const results = {
      valid: [],
      invalid: [],
      errors: {}
    };

    for (let i = 0; i < altAddresses.length; i++) {
      const address = altAddresses[i];
      console.log(`\n📋 ${i + 1}/${altAddresses.length}: ${address}`);
      
      const result = await this.diagnoseALT(address);
      
      if (result.valid) {
        results.valid.push(address);
        console.log(`✅ ВАЛИДНЫЙ ALT`);
      } else {
        results.invalid.push(address);
        results.errors[address] = result.error;
        console.log(`❌ НЕВАЛИДНЫЙ ALT: ${result.error}`);
      }
    }

    // Итоговый отчет
    console.log(`\n📊 ИТОГОВЫЙ ОТЧЕТ:`);
    console.log(`${'='.repeat(60)}`);
    console.log(`✅ Валидных ALT: ${results.valid.length}`);
    console.log(`❌ Невалидных ALT: ${results.invalid.length}`);
    
    if (results.invalid.length > 0) {
      console.log(`\n❌ ПРОБЛЕМНЫЕ ALT:`);
      results.invalid.forEach(address => {
        console.log(`   ${address}: ${results.errors[address]}`);
      });
    }

    return results;
  }
}

// Экспорт для использования в других модулях
module.exports = ALTDiagnostic;

// Если запускается напрямую
if (require.main === module) {
  const diagnostic = new ALTDiagnostic();
  
  // Пример использования
  const testALTs = [
    // Добавьте сюда проблемные ALT адреса для диагностики
  ];

  if (testALTs.length > 0) {
    diagnostic.diagnoseMultipleALTs(testALTs)
      .then(results => {
        console.log(`\n🎯 ДИАГНОСТИКА ЗАВЕРШЕНА`);
        process.exit(0);
      })
      .catch(error => {
        console.error(`❌ Ошибка диагностики: ${error.message}`);
        process.exit(1);
      });
  } else {
    console.log(`\n💡 ИСПОЛЬЗОВАНИЕ:`);
    console.log(`const ALTDiagnostic = require('./diagnose-alt-error.js');`);
    console.log(`const diagnostic = new ALTDiagnostic();`);
    console.log(`diagnostic.diagnoseALT('YOUR_ALT_ADDRESS_HERE');`);
  }
}
