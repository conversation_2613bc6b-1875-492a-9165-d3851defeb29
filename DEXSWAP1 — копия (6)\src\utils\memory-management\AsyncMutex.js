class AsyncMutex {
  constructor() {
    this.locked = false;
    this.queue = [];
  }
  
  async acquire() {
    return new Promise(resolve => {
      if (!this.locked) {
        this.locked = true;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }
  
  release() {
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      next();
    } else {
      this.locked = false;
    }
  }
  
  async withLock(fn) {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }
  
  isLocked() {
    return this.locked;
  }
  
  getQueueLength() {
    return this.queue.length;
  }
}

module.exports = AsyncMutex;