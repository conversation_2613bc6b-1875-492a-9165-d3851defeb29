/**
 * 🎯 РЕАЛЬНЫЙ КАЛЬКУЛЯТОР METEORA ПУЛА
 * 
 * Используем РЕАЛЬНЫЕ данные из скриншота:
 * - TVL: $3,469,308
 * - SOL: 9,348.85
 * - USDC: 1,861,483
 * - Bin Step: 10 (1%)
 * - Base Fee: 0.1%
 * - Protocol Fee: 0.005002415%
 */

class MeteoraRealPoolCalculator {
    constructor() {
        // РЕАЛЬНЫЕ данные из скриншота
        this.REAL_POOL = {
            tvl: 3469308,
            sol_amount: 9348.85,
            usdc_amount: 1861483.00,
            sol_price: 1861483.00 / 9348.85, // ~199.15 USDC/SOL
            bin_step: 10, // 1% между bins
            base_fee: 0.001, // 0.1%
            max_fee: 0.10, // 10%
            protocol_fee: 0.00005002415, // 0.005002415%
            dynamic_fee: 0.001000483 // 0.1000483%
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.TRANSACTION_COST = 0.001; // $0.001 Solana fee
        
        console.log('🎯 РЕАЛЬНЫЙ Meteora Pool Calculator');
        console.log(`   TVL: $${this.REAL_POOL.tvl.toLocaleString()}`);
        console.log(`   SOL: ${this.REAL_POOL.sol_amount.toLocaleString()}`);
        console.log(`   USDC: $${this.REAL_POOL.usdc_amount.toLocaleString()}`);
        console.log(`   Цена SOL: $${this.REAL_POOL.sol_price.toFixed(2)}`);
        console.log(`   Bin Step: ${this.REAL_POOL.bin_step} (1%)`);
    }

    /**
     * 📈 РАСЧЕТ ВЛИЯНИЯ ДОБАВЛЕНИЯ USDC НА ЦЕНУ SOL
     */
    calculateUSDCAdditionImpact(usdcToAdd) {
        const currentUSDC = this.REAL_POOL.usdc_amount;
        const currentSOL = this.REAL_POOL.sol_amount;
        const currentPrice = this.REAL_POOL.sol_price;
        
        // Новый баланс после добавления USDC
        const newUSDC = currentUSDC + usdcToAdd;
        
        // Упрощенная модель: цена растет пропорционально увеличению USDC
        const usdcIncreaseRatio = newUSDC / currentUSDC;
        const priceIncreaseRatio = Math.pow(usdcIncreaseRatio, 0.8); // Степень 0.8 для реализма
        const newPrice = currentPrice * priceIncreaseRatio;
        
        const priceIncreasePercent = ((newPrice - currentPrice) / currentPrice) * 100;
        
        return {
            usdcAdded: usdcToAdd,
            oldUSDC: currentUSDC,
            newUSDC: newUSDC,
            usdcIncreasePercent: ((usdcIncreaseRatio - 1) * 100).toFixed(2),
            oldPrice: currentPrice,
            newPrice: newPrice,
            priceIncreasePercent: priceIncreasePercent.toFixed(2),
            priceDifference: (newPrice - currentPrice).toFixed(2)
        };
    }

    /**
     * 💰 РАСЧЕТ КОМИССИЙ ПРИ ДОБАВЛЕНИИ ЛИКВИДНОСТИ
     */
    calculateLiquidityProvisionCosts(usdcAmount) {
        const protocolFee = usdcAmount * this.REAL_POOL.protocol_fee;
        const transactionFee = this.TRANSACTION_COST;
        const totalCosts = protocolFee + transactionFee;
        
        return {
            usdcAmount,
            protocolFee: protocolFee.toFixed(2),
            transactionFee: transactionFee.toFixed(3),
            totalCosts: totalCosts.toFixed(2),
            costPercent: ((totalCosts / usdcAmount) * 100).toFixed(4)
        };
    }

    /**
     * 🎯 РАСЧЕТ АРБИТРАЖНОЙ ВОЗМОЖНОСТИ
     */
    calculateArbitrageOpportunity(flashLoanAmount = 650000) {
        console.log(`\n🎯 АРБИТРАЖНАЯ ВОЗМОЖНОСТЬ`);
        console.log(`💰 Flash Loan: $${flashLoanAmount.toLocaleString()}`);
        console.log('=' .repeat(70));
        
        // Параметры стратегии
        const usdcToAdd = flashLoanAmount * 0.77; // 77% на добавление USDC
        const reserveAmount = flashLoanAmount - usdcToAdd;
        
        console.log(`📊 РАСПРЕДЕЛЕНИЕ СРЕДСТВ:`);
        console.log(`   💧 Добавление USDC: $${usdcToAdd.toLocaleString()} (77%)`);
        console.log(`   🔄 Резерв: $${reserveAmount.toLocaleString()} (23%)`);
        
        // ШАГ 1: Рассчитываем влияние на цену
        const priceImpact = this.calculateUSDCAdditionImpact(usdcToAdd);
        
        console.log(`\n📈 ШАГ 1 - ВЛИЯНИЕ НА ЦЕНУ:`);
        console.log(`   💰 Добавляем USDC: $${usdcToAdd.toLocaleString()}`);
        console.log(`   📊 Увеличение USDC: +${priceImpact.usdcIncreasePercent}%`);
        console.log(`   🚀 Рост цены SOL: +${priceImpact.priceIncreasePercent}%`);
        console.log(`   💲 Старая цена: $${priceImpact.oldPrice.toFixed(2)}`);
        console.log(`   💲 Новая цена: $${priceImpact.newPrice.toFixed(2)}`);
        console.log(`   💰 Разница: +$${priceImpact.priceDifference}`);
        
        // ШАГ 2: Рассчитываем комиссии за добавление ликвидности
        const lpCosts = this.calculateLiquidityProvisionCosts(usdcToAdd);
        
        console.log(`\n💸 ШАГ 2 - КОМИССИИ ЗА ДОБАВЛЕНИЕ ЛИКВИДНОСТИ:`);
        console.log(`   🏛️ Protocol Fee: $${lpCosts.protocolFee} (${this.REAL_POOL.protocol_fee * 100}%)`);
        console.log(`   ⛽ Transaction Fee: $${lpCosts.transactionFee}`);
        console.log(`   💰 Общие расходы: $${lpCosts.totalCosts} (${lpCosts.costPercent}%)`);
        
        // ШАГ 3: Рассчитываем арбитраж
        // Предполагаем, что можем купить SOL по старой цене в другом пуле
        const solToBuy = reserveAmount / priceImpact.oldPrice;
        const solSellValue = solToBuy * priceImpact.newPrice;
        
        // Slippage при продаже в большом пуле (7M)
        const largePoolTvl = 7000000;
        const slippagePercent = (solSellValue / (largePoolTvl / 100));
        const slippageLoss = solSellValue * (slippagePercent / 100);
        const tradingFee = solSellValue * this.REAL_POOL.base_fee;
        
        const netRevenue = solSellValue - slippageLoss - tradingFee;
        
        console.log(`\n⚡ ШАГ 3 - АРБИТРАЖ:`);
        console.log(`   💰 Покупаем SOL: ${solToBuy.toFixed(2)} по $${priceImpact.oldPrice.toFixed(2)}`);
        console.log(`   💵 Потрачено: $${reserveAmount.toLocaleString()}`);
        console.log(`   🚀 Продаем SOL: ${solToBuy.toFixed(2)} по $${priceImpact.newPrice.toFixed(2)}`);
        console.log(`   💰 Валовая выручка: $${Math.round(solSellValue).toLocaleString()}`);
        console.log(`   📉 Slippage: ${slippagePercent.toFixed(2)}% = -$${Math.round(slippageLoss).toLocaleString()}`);
        console.log(`   💳 Trading Fee: -$${Math.round(tradingFee).toLocaleString()}`);
        console.log(`   ✅ Чистая выручка: $${Math.round(netRevenue).toLocaleString()}`);
        
        // ШАГ 4: Финальный расчет
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalCosts = reserveAmount + parseFloat(lpCosts.totalCosts) + flashLoanFee;
        const netProfit = netRevenue - totalCosts;
        const roi = (netProfit / flashLoanAmount) * 100;
        
        console.log(`\n💰 ШАГ 4 - ФИНАЛЬНЫЙ РАСЧЕТ:`);
        console.log(`   💸 Общие расходы:`);
        console.log(`     - Покупка SOL: $${reserveAmount.toLocaleString()}`);
        console.log(`     - LP комиссии: $${lpCosts.totalCosts}`);
        console.log(`     - Flash Loan: $${Math.round(flashLoanFee).toLocaleString()}`);
        console.log(`     - ИТОГО: $${Math.round(totalCosts).toLocaleString()}`);
        console.log(`   💵 Чистая выручка: $${Math.round(netRevenue).toLocaleString()}`);
        console.log(`   🎯 ЧИСТАЯ ПРИБЫЛЬ: $${Math.round(netProfit).toLocaleString()}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}%`);
        
        return {
            flashLoanAmount,
            usdcAdded: usdcToAdd,
            priceIncrease: parseFloat(priceImpact.priceIncreasePercent),
            lpCosts: parseFloat(lpCosts.totalCosts),
            arbitrageRevenue: Math.round(netRevenue),
            totalCosts: Math.round(totalCosts),
            netProfit: Math.round(netProfit),
            roi: parseFloat(roi.toFixed(2)),
            profitable: netProfit > 0
        };
    }

    /**
     * 🏆 ТЕСТИРОВАНИЕ РАЗНЫХ СУММ
     */
    testDifferentAmounts() {
        console.log(`\n🏆 ТЕСТИРОВАНИЕ РАЗНЫХ СУММ FLASH LOAN`);
        console.log('=' .repeat(70));
        
        const amounts = [300000, 500000, 650000, 800000, 1000000];
        const results = [];
        
        amounts.forEach(amount => {
            console.log(`\n📊 ТЕСТ $${amount.toLocaleString()}:`);
            const result = this.calculateArbitrageOpportunity(amount);
            results.push(result);
            
            const status = result.profitable ? '✅' : '❌';
            const emoji = result.profitable ? '💚' : '🔴';
            console.log(`${status} $${amount.toLocaleString()}: ${emoji} $${result.netProfit.toLocaleString()} (${result.roi}%)`);
        });
        
        return results;
    }

    /**
     * 📊 АНАЛИЗ ТОЛЬКО ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
     */
    analyzeLiquidityAdditionOnly() {
        console.log(`\n📊 АНАЛИЗ ТОЛЬКО ДОБАВЛЕНИЯ ЛИКВИДНОСТИ`);
        console.log('=' .repeat(70));
        
        const amounts = [100000, 250000, 500000, 750000, 1000000];
        
        amounts.forEach(amount => {
            const impact = this.calculateUSDCAdditionImpact(amount);
            const costs = this.calculateLiquidityProvisionCosts(amount);
            
            console.log(`\n💰 ДОБАВЛЕНИЕ $${amount.toLocaleString()} USDC:`);
            console.log(`   📈 Рост цены SOL: +${impact.priceIncreasePercent}%`);
            console.log(`   💲 Новая цена: $${impact.newPrice.toFixed(2)} (было $${impact.oldPrice.toFixed(2)})`);
            console.log(`   💸 Комиссии: $${costs.totalCosts} (${costs.costPercent}%)`);
            console.log(`   🎯 Чистое влияние: +${(parseFloat(impact.priceIncreasePercent) - parseFloat(costs.costPercent)).toFixed(2)}%`);
        });
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК АНАЛИЗА РЕАЛЬНОГО METEORA ПУЛА...\n');
    
    const calculator = new MeteoraRealPoolCalculator();
    
    try {
        // Анализ только добавления ликвидности
        calculator.analyzeLiquidityAdditionOnly();
        
        // Полный арбитражный анализ
        const results = calculator.testDifferentAmounts();
        
        // Находим лучший результат
        const bestResult = results.reduce((best, current) => 
            current.netProfit > best.netProfit ? current : best
        );
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ЛУЧШИЙ РЕЗУЛЬТАТ:`);
        console.log(`   💰 Flash Loan: $${bestResult.flashLoanAmount.toLocaleString()}`);
        console.log(`   📈 Рост цены: +${bestResult.priceIncrease}%`);
        console.log(`   🎯 Чистая прибыль: $${bestResult.netProfit.toLocaleString()}`);
        console.log(`   📊 ROI: ${bestResult.roi}%`);
        console.log(`🚀 СТРАТЕГИЯ С РЕАЛЬНЫМИ ДАННЫМИ ГОТОВА!`);
        
        return bestResult;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск
if (require.main === module) {
    console.log('🚀 Запуск анализа...');
    main().then(() => {
        console.log('✅ Анализ завершен');
    }).catch(error => {
        console.error('❌ Ошибка:', error);
    });
}

module.exports = { MeteoraRealPoolCalculator };
