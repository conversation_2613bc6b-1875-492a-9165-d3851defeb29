// 🎯 METEORA QUOTE EXTRACTOR - ПОЛУЧЕНИЕ РЕАЛЬНЫХ BID/ASK КОТИРОВОК
// Где хранится информация о реальных ценах покупки/продажи в Meteora

const { Connection, PublicKey } = require('@solana/web3.js');
const { DLMM } = require('@meteora-ag/dlmm');
const BN = require('bn.js');

console.log('🎯 METEORA QUOTE EXTRACTOR - ПОИСК РЕАЛЬНЫХ КОТИРОВОК'.cyan.bold);
console.log('═'.repeat(70));

class MeteoraQuoteExtractor {
    constructor(connection) {
        this.connection = connection;
    }

    /**
     * 🔍 ГЛАВНАЯ ФУНКЦИЯ - ПОЛУЧЕНИЕ РЕАЛЬНЫХ BID/ASK КОТИРОВОК
     */
    async getRealBidAskQuotes(poolAddress, testAmountUSD = 1000) {
        try {
            console.log(`\n🎯 АНАЛИЗ ПУЛА: ${poolAddress}`);
            console.log(`💰 Тестовая сумма: $${testAmountUSD}`);
            
            // 1. Создаем DLMM инстанс
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress));
            
            // 2. Получаем базовую информацию о пуле
            const poolInfo = await this.getPoolBasicInfo(dlmmPool);
            console.log(`\n📊 БАЗОВАЯ ИНФОРМАЦИЯ ПУЛА:`);
            console.log(`   Token X: ${poolInfo.tokenX.symbol} (${poolInfo.tokenX.mint})`);
            console.log(`   Token Y: ${poolInfo.tokenY.symbol} (${poolInfo.tokenY.mint})`);
            console.log(`   Текущая цена: $${poolInfo.currentPrice.toFixed(6)}`);
            console.log(`   TVL: $${poolInfo.tvl.toLocaleString()}`);
            
            // 3. Рассчитываем тестовые суммы в lamports
            const testAmountSOL = testAmountUSD / poolInfo.currentPrice;
            const testAmountSOLLamports = new BN(Math.floor(testAmountSOL * 1e9)); // SOL в lamports
            const testAmountUSDCLamports = new BN(Math.floor(testAmountUSD * 1e6)); // USDC в micro-USDC
            
            console.log(`\n💱 ТЕСТОВЫЕ СУММЫ:`);
            console.log(`   ${testAmountSOL.toFixed(6)} SOL = ${testAmountSOLLamports.toString()} lamports`);
            console.log(`   $${testAmountUSD} USDC = ${testAmountUSDCLamports.toString()} micro-USDC`);
            
            // 4. ПОЛУЧАЕМ РЕАЛЬНЫЕ КОТИРОВКИ
            const quotes = await this.getBidAskQuotes(dlmmPool, testAmountSOLLamports, testAmountUSDCLamports);
            
            // 5. АНАЛИЗИРУЕМ РЕЗУЛЬТАТЫ
            const analysis = this.analyzeQuotes(quotes, poolInfo.currentPrice, testAmountUSD);
            
            return {
                poolAddress,
                poolInfo,
                quotes,
                analysis,
                testAmountUSD
            };
            
        } catch (error) {
            console.error(`❌ Ошибка анализа пула ${poolAddress}: ${error.message}`);
            throw error;
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ БАЗОВОЙ ИНФОРМАЦИИ О ПУЛЕ
     */
    async getPoolBasicInfo(dlmmPool) {
        // Получаем информацию о токенах
        const tokenX = {
            mint: dlmmPool.tokenX.publicKey.toString(),
            symbol: dlmmPool.tokenX.symbol || 'UNKNOWN',
            decimals: dlmmPool.tokenX.decimal
        };
        
        const tokenY = {
            mint: dlmmPool.tokenY.publicKey.toString(), 
            symbol: dlmmPool.tokenY.symbol || 'UNKNOWN',
            decimals: dlmmPool.tokenY.decimal
        };
        
        // Получаем текущую цену (предполагаем SOL/USDC)
        const currentPrice = await this.getCurrentPrice(dlmmPool);
        
        // Получаем TVL (приблизительно)
        const tvl = await this.estimateTVL(dlmmPool, currentPrice);
        
        return {
            tokenX,
            tokenY,
            currentPrice,
            tvl
        };
    }

    /**
     * 💰 ПОЛУЧЕНИЕ ТЕКУЩЕЙ ЦЕНЫ
     */
    async getCurrentPrice(dlmmPool) {
        try {
            // Получаем активный bin для определения текущей цены
            const activeBin = await dlmmPool.getActiveBin();
            if (activeBin && activeBin.price) {
                return parseFloat(activeBin.price);
            }
            
            // Fallback: пробуем через небольшой quote
            const smallAmount = new BN(1000000); // 0.001 SOL
            const quote = await dlmmPool.swapQuote(smallAmount, false); // SOL -> USDC
            
            if (quote && quote.outAmount && quote.outAmount.gt(new BN(0))) {
                const priceFromQuote = quote.outAmount.toNumber() / 1e6 / (smallAmount.toNumber() / 1e9);
                return priceFromQuote;
            }
            
            return 164; // Fallback цена
        } catch (error) {
            console.log(`⚠️ Не удалось получить точную цену: ${error.message}`);
            return 164; // Fallback
        }
    }

    /**
     * 💧 ОЦЕНКА TVL
     */
    async estimateTVL(dlmmPool, currentPrice) {
        try {
            // Получаем баланс токенов в пуле (приблизительно)
            // Это упрощенная оценка - в реальности нужно суммировать все bins
            return 1000000; // Placeholder - $1M
        } catch (error) {
            return 1000000; // Fallback
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ BID/ASK КОТИРОВОК
     */
    async getBidAskQuotes(dlmmPool, testAmountSOL, testAmountUSDC) {
        console.log(`\n🎯 ПОЛУЧЕНИЕ РЕАЛЬНЫХ BID/ASK КОТИРОВОК:`);
        
        const quotes = {};
        
        try {
            // 1. BID QUOTE - Продажа SOL за USDC (SOL -> USDC)
            console.log(`\n📤 BID QUOTE (Продажа SOL):`);
            console.log(`   Продаем: ${testAmountSOL.toString()} lamports SOL`);
            
            const bidQuote = await dlmmPool.swapQuote(
                testAmountSOL,  // amount in SOL lamports
                false,          // swapYtoX = false (SOL -> USDC)
                new BN(100),    // 1% slippage
                await dlmmPool.getBinArrayForSwap(false)
            );
            
            if (bidQuote && bidQuote.outAmount) {
                const usdcReceived = bidQuote.outAmount.toNumber() / 1e6; // micro-USDC to USDC
                const solSold = testAmountSOL.toNumber() / 1e9; // lamports to SOL
                const bidPrice = usdcReceived / solSold;
                
                quotes.bid = {
                    solAmount: solSold,
                    usdcReceived: usdcReceived,
                    price: bidPrice,
                    priceImpact: bidQuote.priceImpact || 0,
                    fee: bidQuote.fee ? bidQuote.fee.toNumber() / 1e6 : 0,
                    raw: bidQuote
                };
                
                console.log(`   ✅ Получили: $${usdcReceived.toFixed(2)} USDC`);
                console.log(`   💰 BID цена: $${bidPrice.toFixed(6)}/SOL`);
                console.log(`   📈 Price Impact: ${(bidQuote.priceImpact || 0).toFixed(4)}%`);
            }
            
        } catch (error) {
            console.log(`   ❌ Ошибка BID quote: ${error.message}`);
            quotes.bid = { error: error.message };
        }
        
        try {
            // 2. ASK QUOTE - Покупка SOL за USDC (USDC -> SOL)
            console.log(`\n📥 ASK QUOTE (Покупка SOL):`);
            console.log(`   Покупаем за: ${testAmountUSDC.toString()} micro-USDC`);
            
            const askQuote = await dlmmPool.swapQuote(
                testAmountUSDC, // amount in USDC micro-units
                true,           // swapYtoX = true (USDC -> SOL)
                new BN(100),    // 1% slippage
                await dlmmPool.getBinArrayForSwap(true)
            );
            
            if (askQuote && askQuote.outAmount) {
                const solReceived = askQuote.outAmount.toNumber() / 1e9; // lamports to SOL
                const usdcSpent = testAmountUSDC.toNumber() / 1e6; // micro-USDC to USDC
                const askPrice = usdcSpent / solReceived;
                
                quotes.ask = {
                    usdcAmount: usdcSpent,
                    solReceived: solReceived,
                    price: askPrice,
                    priceImpact: askQuote.priceImpact || 0,
                    fee: askQuote.fee ? askQuote.fee.toNumber() / 1e6 : 0,
                    raw: askQuote
                };
                
                console.log(`   ✅ Получили: ${solReceived.toFixed(6)} SOL`);
                console.log(`   💰 ASK цена: $${askPrice.toFixed(6)}/SOL`);
                console.log(`   📈 Price Impact: ${(askQuote.priceImpact || 0).toFixed(4)}%`);
            }
            
        } catch (error) {
            console.log(`   ❌ Ошибка ASK quote: ${error.message}`);
            quotes.ask = { error: error.message };
        }
        
        return quotes;
    }

    /**
     * 📊 АНАЛИЗ КОТИРОВОК
     */
    analyzeQuotes(quotes, currentPrice, testAmountUSD) {
        console.log(`\n📊 АНАЛИЗ КОТИРОВОК:`);
        
        const analysis = {
            hasValidQuotes: false,
            bidAskSpread: 0,
            bidAskSpreadPercent: 0,
            totalFees: 0,
            priceImpact: 0,
            recommendation: 'UNKNOWN'
        };
        
        if (quotes.bid && !quotes.bid.error && quotes.ask && !quotes.ask.error) {
            analysis.hasValidQuotes = true;
            
            // Bid-Ask спред
            analysis.bidAskSpread = quotes.ask.price - quotes.bid.price;
            analysis.bidAskSpreadPercent = (analysis.bidAskSpread / quotes.bid.price) * 100;
            
            // Общие комиссии
            analysis.totalFees = (quotes.bid.fee || 0) + (quotes.ask.fee || 0);
            
            // Price impact
            analysis.priceImpact = Math.max(quotes.bid.priceImpact || 0, quotes.ask.priceImpact || 0);
            
            console.log(`   💰 BID (продажа): $${quotes.bid.price.toFixed(6)}/SOL`);
            console.log(`   💰 ASK (покупка): $${quotes.ask.price.toFixed(6)}/SOL`);
            console.log(`   📏 Bid-Ask спред: $${analysis.bidAskSpread.toFixed(6)} (${analysis.bidAskSpreadPercent.toFixed(4)}%)`);
            console.log(`   💸 Общие комиссии: $${analysis.totalFees.toFixed(4)}`);
            console.log(`   📈 Max Price Impact: ${analysis.priceImpact.toFixed(4)}%`);
            
            // Рекомендация
            if (analysis.bidAskSpreadPercent > 1.0) {
                analysis.recommendation = 'AVOID - Слишком большой спред';
            } else if (analysis.priceImpact > 2.0) {
                analysis.recommendation = 'CAUTION - Высокий price impact';
            } else if (analysis.bidAskSpreadPercent < 0.1) {
                analysis.recommendation = 'GOOD - Узкий спред';
            } else {
                analysis.recommendation = 'ACCEPTABLE - Нормальные условия';
            }
            
            console.log(`   🎯 Рекомендация: ${analysis.recommendation}`);
        } else {
            console.log(`   ❌ Не удалось получить валидные котировки`);
            if (quotes.bid && quotes.bid.error) {
                console.log(`      BID ошибка: ${quotes.bid.error}`);
            }
            if (quotes.ask && quotes.ask.error) {
                console.log(`      ASK ошибка: ${quotes.ask.error}`);
            }
        }
        
        return analysis;
    }

    /**
     * 🔍 СРАВНЕНИЕ КОТИРОВОК МЕЖДУ ПУЛАМИ
     */
    async comparePoolQuotes(poolAddresses, testAmountUSD = 1000) {
        console.log(`\n🔍 СРАВНЕНИЕ КОТИРОВОК МЕЖДУ ${poolAddresses.length} ПУЛАМИ:`);
        console.log('═'.repeat(70));
        
        const poolQuotes = [];
        
        // Получаем котировки для всех пулов
        for (const poolAddress of poolAddresses) {
            try {
                const quotes = await this.getRealBidAskQuotes(poolAddress, testAmountUSD);
                poolQuotes.push(quotes);
            } catch (error) {
                console.log(`❌ Ошибка для пула ${poolAddress}: ${error.message}`);
            }
        }
        
        // Анализируем арбитражные возможности
        return this.findArbitrageOpportunities(poolQuotes);
    }

    /**
     * 🎯 ПОИСК АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ
     */
    findArbitrageOpportunities(poolQuotes) {
        console.log(`\n🎯 ПОИСК АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ:`);
        
        const opportunities = [];
        
        for (let i = 0; i < poolQuotes.length; i++) {
            for (let j = 0; j < poolQuotes.length; j++) {
                if (i === j) continue;
                
                const buyPool = poolQuotes[i];
                const sellPool = poolQuotes[j];
                
                if (!buyPool.quotes.ask || buyPool.quotes.ask.error ||
                    !sellPool.quotes.bid || sellPool.quotes.bid.error) {
                    continue;
                }
                
                // Покупаем в buyPool, продаем в sellPool
                const buyPrice = buyPool.quotes.ask.price;
                const sellPrice = sellPool.quotes.bid.price;
                const spread = sellPrice - buyPrice;
                const spreadPercent = (spread / buyPrice) * 100;
                
                if (spread > 0) {
                    const opportunity = {
                        buyPool: buyPool.poolAddress,
                        sellPool: sellPool.poolAddress,
                        buyPrice,
                        sellPrice,
                        spread,
                        spreadPercent,
                        estimatedProfit: (spread * (buyPool.testAmountUSD / buyPrice)) - 
                                       (buyPool.quotes.ask.fee + sellPool.quotes.bid.fee),
                        totalFees: buyPool.quotes.ask.fee + sellPool.quotes.bid.fee,
                        maxPriceImpact: Math.max(buyPool.analysis.priceImpact, sellPool.analysis.priceImpact)
                    };
                    
                    opportunities.push(opportunity);
                }
            }
        }
        
        // Сортируем по прибыльности
        opportunities.sort((a, b) => b.estimatedProfit - a.estimatedProfit);
        
        console.log(`\n📊 НАЙДЕНО ${opportunities.length} АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ:`);
        
        opportunities.slice(0, 5).forEach((opp, index) => {
            console.log(`\n${index + 1}. 💰 ВОЗМОЖНОСТЬ:`);
            console.log(`   Покупка: ${opp.buyPool.slice(0, 8)}... за $${opp.buyPrice.toFixed(6)}`);
            console.log(`   Продажа: ${opp.sellPool.slice(0, 8)}... за $${opp.sellPrice.toFixed(6)}`);
            console.log(`   Спред: $${opp.spread.toFixed(6)} (${opp.spreadPercent.toFixed(4)}%)`);
            console.log(`   Прибыль: $${opp.estimatedProfit.toFixed(4)}`);
            console.log(`   Комиссии: $${opp.totalFees.toFixed(4)}`);
            console.log(`   Max Price Impact: ${opp.maxPriceImpact.toFixed(4)}%`);
        });
        
        return opportunities;
    }
}

// 🎯 ЭКСПОРТ И ПРИМЕР ИСПОЛЬЗОВАНИЯ
module.exports = { MeteoraQuoteExtractor };

// Пример использования:
if (require.main === module) {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const extractor = new MeteoraQuoteExtractor(connection);
    
    // Тестовые пулы из ваших скриншотов
    const testPools = [
        'Av7fi8RQPefh8sUYM9gKf3Y3pKRVR9gP5jjYNzaARsgN', // Pool A (большой)
        'ARwi1S4DaiTG5DX7S4M4ZsrXqpMD1MrTmbu9ue2tpmEq'  // Pool B (маленький)
    ];
    
    (async () => {
        try {
            console.log('🚀 ЗАПУСК АНАЛИЗА РЕАЛЬНЫХ КОТИРОВОК...\n');
            
            // Анализируем каждый пул отдельно
            for (const poolAddress of testPools) {
                await extractor.getRealBidAskQuotes(poolAddress, 1000);
                console.log('\n' + '─'.repeat(70));
            }
            
            // Сравниваем пулы между собой
            await extractor.comparePoolQuotes(testPools, 1000);
            
        } catch (error) {
            console.error('❌ Ошибка:', error);
        }
    })();
}

console.log(`
🎯 КЛЮЧЕВЫЕ МЕСТА ГДЕ ХРАНИТСЯ ИНФОРМАЦИЯ:

1️⃣ DLMM.swapQuote() - ОСНОВНОЙ ИСТОЧНИК:
   - Входные параметры: amount, swapYtoX, slippage, binArrays
   - Выходные данные: outAmount, minOutAmount, priceImpact, fee
   - Это РЕАЛЬНЫЕ котировки с учетом ликвидности!

2️⃣ getBinArrayForSwap() - ДАННЫЕ О ЛИКВИДНОСТИ:
   - Возвращает активные bins с ликвидностью
   - Учитывает реальное распределение токенов
   - Влияет на price impact

3️⃣ getActiveBin() - ТЕКУЩАЯ ЦЕНА:
   - Активный bin с текущей ценой
   - Центральная точка для расчетов

4️⃣ ФОРМУЛА РЕАЛЬНОЙ ЦЕНЫ:
   BID (продажа): swapQuote(amountSOL, false) → USDC/SOL
   ASK (покупка): swapQuote(amountUSDC, true) → USDC/SOL
   
   СПРЕД = ASK - BID (покупка всегда дороже продажи!)

🚀 ИНТЕГРИРУЕМ В BMETEORA.JS ДЛЯ РЕАЛЬНОГО АРБИТРАЖА!
`);
