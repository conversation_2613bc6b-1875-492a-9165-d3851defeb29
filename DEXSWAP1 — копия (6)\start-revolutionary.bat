@echo off
echo.
echo 🔥 РЕВОЛЮЦИОННАЯ ТОРГОВАЯ СИСТЕМА
echo ================================
echo.
echo 🚀 Выберите режим запуска:
echo.
echo 1. Meteora Revolutionary Mode (рекомендуется)
echo 2. Full Revolutionary System  
echo 3. Revolutionary Test Mode
echo 4. Meteora Revolutionary с параметрами
echo.
set /p choice="Введите номер (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🌪️ Запуск Meteora Revolutionary Mode...
    node BMeteora.js --revolutionary
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 🚀 Запуск Full Revolutionary System...
    node start-revolutionary-trading.js main
    goto end
)

if "%choice%"=="3" (
    echo.
    echo 🧪 Запуск Revolutionary Test Mode...
    node start-revolutionary-trading.js test
    goto end
)

if "%choice%"=="4" (
    echo.
    echo 🌪️ Запуск Meteora Revolutionary с дополнительными параметрами...
    echo.
    echo Доступные параметры:
    echo --revolutionary, -r  : Революционный режим
    echo --emergency         : Экстренный режим ($100k за 24 часа)
    echo --aggressive        : Агрессивный режим (спреды от 0.1%%)
    echo.
    set /p params="Введите параметры: "
    node BMeteora.js %params%
    goto end
)

echo.
echo ❌ Неверный выбор!
echo.

:end
echo.
echo 🔥 Нажмите любую клавишу для выхода...
pause >nul
