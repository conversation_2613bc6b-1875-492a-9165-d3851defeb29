#!/usr/bin/env node

/**
 * 🛡️ СИСТЕМА ЗАЩИТЫ ОТ УБЫТКОВ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📚 ОСНОВАНО НА: https://www.anchor-lang.com/docs/features/errors
 * 🎯 ЦЕЛЬ: Автоматический откат транзакции если прибыль меньше минимальной
 */

const { Connection, PublicKey, Transaction } = require('@solana/web3.js');

class ProfitProtectionSystem {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    
    // КОНФИГУРАЦИЯ ЗАЩИТЫ ОТ УБЫТКОВ
    this.PROTECTION_CONFIG = {
      // Минимальная прибыль в USD
      MIN_PROFIT_USD: 1.00,
      
      // Минимальная прибыль в процентах
      MIN_PROFIT_PERCENT: 0.1, // 0.1%
      
      // Максимальные потери в USD (стоп-лосс)
      MAX_LOSS_USD: 5.00,
      
      // Максимальные потери в процентах
      MAX_LOSS_PERCENT: 0.5, // 0.5%
      
      // Максимальная комиссия транзакции
      MAX_TRANSACTION_FEE_SOL: 0.01, // 0.01 SOL
      
      // Минимальный ROI (Return on Investment)
      MIN_ROI_PERCENT: 0.05 // 0.05%
    };
  }

  // 🛡️ ОСНОВНАЯ ФУНКЦИЯ ЗАЩИТЫ ОТ УБЫТКОВ
  validateProfitability(tradeData) {
    console.log('🛡️ ПРОВЕРКА ЗАЩИТЫ ОТ УБЫТКОВ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`💰 Ожидаемая прибыль: $${tradeData.expectedProfitUsd.toFixed(4)}`);
    console.log(`📊 Ожидаемый ROI: ${tradeData.expectedProfitPercent.toFixed(4)}%`);
    console.log(`💸 Комиссия транзакции: ${tradeData.transactionFeeSol.toFixed(6)} SOL`);
    console.log(`🎯 Размер позиции: $${tradeData.positionSizeUsd.toFixed(2)}`);

    const validationResults = [];

    // ПРОВЕРКА 1: Минимальная прибыль в USD
    const minProfitCheck = this.checkMinimumProfitUsd(tradeData.expectedProfitUsd);
    validationResults.push(minProfitCheck);

    // ПРОВЕРКА 2: Минимальная прибыль в процентах
    const minPercentCheck = this.checkMinimumProfitPercent(tradeData.expectedProfitPercent);
    validationResults.push(minPercentCheck);

    // ПРОВЕРКА 3: Максимальные потери (стоп-лосс)
    const maxLossCheck = this.checkMaximumLoss(tradeData);
    validationResults.push(maxLossCheck);

    // ПРОВЕРКА 4: Комиссия транзакции
    const feeCheck = this.checkTransactionFee(tradeData.transactionFeeSol, tradeData.expectedProfitUsd);
    validationResults.push(feeCheck);

    // ПРОВЕРКА 5: ROI (Return on Investment)
    const roiCheck = this.checkROI(tradeData.expectedProfitUsd, tradeData.positionSizeUsd);
    validationResults.push(roiCheck);

    // ИТОГОВОЕ РЕШЕНИЕ
    const allPassed = validationResults.every(result => result.passed);
    const failedChecks = validationResults.filter(result => !result.passed);

    console.log('');
    console.log('📋 РЕЗУЛЬТАТЫ ПРОВЕРОК:');
    console.log('─────────────────────────────────────────────────────────────');
    validationResults.forEach(result => {
      console.log(`${result.passed ? '✅' : '❌'} ${result.name}: ${result.message}`);
    });

    console.log('');
    console.log('🎯 ИТОГОВОЕ РЕШЕНИЕ:');
    console.log('═══════════════════════════════════════════════════════════════');

    if (allPassed) {
      console.log('✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ - ТРАНЗАКЦИЯ РАЗРЕШЕНА!');
      console.log('🚀 Сделка будет выполнена');
      return {
        allowed: true,
        reason: 'Все проверки защиты от убытков пройдены',
        checks: validationResults
      };
    } else {
      console.log('❌ ЗАЩИТА ОТ УБЫТКОВ АКТИВИРОВАНА - ТРАНЗАКЦИЯ ОТКЛОНЕНА!');
      console.log('🛡️ Сделка будет отменена для защиты от потерь');
      console.log('');
      console.log('🚨 ПРИЧИНЫ ОТКЛОНЕНИЯ:');
      failedChecks.forEach(check => {
        console.log(`   • ${check.name}: ${check.message}`);
      });
      
      return {
        allowed: false,
        reason: 'Защита от убытков активирована',
        failedChecks: failedChecks,
        checks: validationResults
      };
    }
  }

  // ПРОВЕРКА 1: Минимальная прибыль в USD
  checkMinimumProfitUsd(expectedProfitUsd) {
    const passed = expectedProfitUsd >= this.PROTECTION_CONFIG.MIN_PROFIT_USD;
    return {
      name: 'Минимальная прибыль USD',
      passed: passed,
      message: passed 
        ? `$${expectedProfitUsd.toFixed(4)} >= $${this.PROTECTION_CONFIG.MIN_PROFIT_USD}` 
        : `$${expectedProfitUsd.toFixed(4)} < $${this.PROTECTION_CONFIG.MIN_PROFIT_USD} (НЕДОСТАТОЧНО!)`,
      expectedValue: this.PROTECTION_CONFIG.MIN_PROFIT_USD,
      actualValue: expectedProfitUsd
    };
  }

  // ПРОВЕРКА 2: Минимальная прибыль в процентах
  checkMinimumProfitPercent(expectedProfitPercent) {
    const passed = expectedProfitPercent >= this.PROTECTION_CONFIG.MIN_PROFIT_PERCENT;
    return {
      name: 'Минимальная прибыль %',
      passed: passed,
      message: passed 
        ? `${expectedProfitPercent.toFixed(4)}% >= ${this.PROTECTION_CONFIG.MIN_PROFIT_PERCENT}%` 
        : `${expectedProfitPercent.toFixed(4)}% < ${this.PROTECTION_CONFIG.MIN_PROFIT_PERCENT}% (НЕДОСТАТОЧНО!)`,
      expectedValue: this.PROTECTION_CONFIG.MIN_PROFIT_PERCENT,
      actualValue: expectedProfitPercent
    };
  }

  // ПРОВЕРКА 3: Максимальные потери (стоп-лосс)
  checkMaximumLoss(tradeData) {
    // Если ожидаемая прибыль отрицательная - это потери
    const isLoss = tradeData.expectedProfitUsd < 0;
    
    if (!isLoss) {
      return {
        name: 'Стоп-лосс',
        passed: true,
        message: 'Ожидается прибыль, не потери',
        expectedValue: 0,
        actualValue: tradeData.expectedProfitUsd
      };
    }

    const lossUsd = Math.abs(tradeData.expectedProfitUsd);
    const lossPercent = Math.abs(tradeData.expectedProfitPercent);
    
    const usdCheck = lossUsd <= this.PROTECTION_CONFIG.MAX_LOSS_USD;
    const percentCheck = lossPercent <= this.PROTECTION_CONFIG.MAX_LOSS_PERCENT;
    const passed = usdCheck && percentCheck;

    return {
      name: 'Стоп-лосс',
      passed: passed,
      message: passed 
        ? `Потери в пределах лимита: $${lossUsd.toFixed(4)} (${lossPercent.toFixed(4)}%)` 
        : `ПРЕВЫШЕН СТОП-ЛОСС: $${lossUsd.toFixed(4)} (${lossPercent.toFixed(4)}%)`,
      expectedValue: { maxLossUsd: this.PROTECTION_CONFIG.MAX_LOSS_USD, maxLossPercent: this.PROTECTION_CONFIG.MAX_LOSS_PERCENT },
      actualValue: { lossUsd, lossPercent }
    };
  }

  // ПРОВЕРКА 4: Комиссия транзакции
  checkTransactionFee(transactionFeeSol, expectedProfitUsd) {
    const feeCheck = transactionFeeSol <= this.PROTECTION_CONFIG.MAX_TRANSACTION_FEE_SOL;
    
    // Дополнительная проверка: комиссия не должна съедать всю прибыль
    const solPrice = 150; // Примерная цена SOL
    const feeUsd = transactionFeeSol * solPrice;
    const profitAfterFee = expectedProfitUsd - feeUsd;
    const feeRatioCheck = profitAfterFee > 0;

    const passed = feeCheck && feeRatioCheck;

    return {
      name: 'Комиссия транзакции',
      passed: passed,
      message: passed 
        ? `${transactionFeeSol.toFixed(6)} SOL ($${feeUsd.toFixed(4)}) - приемлемо` 
        : `ВЫСОКАЯ КОМИССИЯ: ${transactionFeeSol.toFixed(6)} SOL ($${feeUsd.toFixed(4)})`,
      expectedValue: this.PROTECTION_CONFIG.MAX_TRANSACTION_FEE_SOL,
      actualValue: transactionFeeSol,
      additionalInfo: {
        feeUsd: feeUsd,
        profitAfterFee: profitAfterFee
      }
    };
  }

  // ПРОВЕРКА 5: ROI (Return on Investment)
  checkROI(expectedProfitUsd, positionSizeUsd) {
    const roi = (expectedProfitUsd / positionSizeUsd) * 100;
    const passed = roi >= this.PROTECTION_CONFIG.MIN_ROI_PERCENT;

    return {
      name: 'ROI (Return on Investment)',
      passed: passed,
      message: passed 
        ? `ROI ${roi.toFixed(4)}% >= ${this.PROTECTION_CONFIG.MIN_ROI_PERCENT}%` 
        : `НИЗКИЙ ROI: ${roi.toFixed(4)}% < ${this.PROTECTION_CONFIG.MIN_ROI_PERCENT}%`,
      expectedValue: this.PROTECTION_CONFIG.MIN_ROI_PERCENT,
      actualValue: roi
    };
  }

  // 🔧 СОЗДАНИЕ JAVASCRIPT КОДА ДЛЯ ИНТЕГРАЦИИ
  generateIntegrationCode() {
    return `
// 🛡️ ИНТЕГРАЦИЯ ЗАЩИТЫ ОТ УБЫТКОВ В ТОРГОВУЮ СИСТЕМУ
// ═══════════════════════════════════════════════════════════════

// 1. ПРОВЕРКА ПЕРЕД ВЫПОЛНЕНИЕМ СДЕЛКИ
async function executeArbitrageWithProtection(arbitrageOpportunity) {
  const profitProtection = new ProfitProtectionSystem();
  
  // Подготавливаем данные для проверки
  const tradeData = {
    expectedProfitUsd: arbitrageOpportunity.expectedProfit,
    expectedProfitPercent: arbitrageOpportunity.spreadPercent,
    transactionFeeSol: 0.005, // Примерная комиссия
    positionSizeUsd: arbitrageOpportunity.flashLoanAmount
  };
  
  // КРИТИЧЕСКАЯ ПРОВЕРКА ЗАЩИТЫ ОТ УБЫТКОВ
  const protection = profitProtection.validateProfitability(tradeData);
  
  if (!protection.allowed) {
    console.log('🛡️ ЗАЩИТА ОТ УБЫТКОВ: Сделка отменена');
    console.log(\`📋 Причина: \${protection.reason}\`);
    return { success: false, reason: protection.reason };
  }
  
  // Если защита пройдена - выполняем сделку
  console.log('✅ ЗАЩИТА ПРОЙДЕНА: Выполняем арбитраж');
  return await executeActualArbitrage(arbitrageOpportunity);
}

// 2. ИНТЕГРАЦИЯ В ATOMIC TRANSACTION BUILDER
class AtomicTransactionBuilderWithProtection extends AtomicTransactionBuilderFixed {
  
  async buildCompleteArbitrageTransaction(opportunity) {
    // ОБЯЗАТЕЛЬНАЯ ПРОВЕРКА ЗАЩИТЫ ОТ УБЫТКОВ
    const profitProtection = new ProfitProtectionSystem();
    
    const tradeData = {
      expectedProfitUsd: opportunity.expectedProfit || 0,
      expectedProfitPercent: opportunity.spreadPercent || 0,
      transactionFeeSol: 0.005,
      positionSizeUsd: opportunity.flashLoanAmount || 50000
    };
    
    const protection = profitProtection.validateProfitability(tradeData);
    
    // КРИТИЧЕСКАЯ ТОЧКА: ОТКАТ ЕСЛИ НЕ ПРИБЫЛЬНО
    if (!protection.allowed) {
      throw new Error(\`Защита от убытков: \${protection.reason}\`);
    }
    
    // Продолжаем только если защита пройдена
    return await super.buildCompleteArbitrageTransaction(opportunity);
  }
}

// 3. ИСПОЛЬЗОВАНИЕ В ОСНОВНОЙ СИСТЕМЕ
// В файле test-bot-safe.js добавить:
const profitProtection = new ProfitProtectionSystem();

// Перед каждой сделкой:
if (opportunity) {
  const protection = profitProtection.validateProfitability({
    expectedProfitUsd: opportunity.expectedProfit,
    expectedProfitPercent: opportunity.spreadPercent,
    transactionFeeSol: 0.005,
    positionSizeUsd: 50000
  });
  
  if (protection.allowed) {
    // Выполняем сделку
    await executeArbitrage(opportunity);
  } else {
    // Пропускаем сделку
    console.log('🛡️ Сделка пропущена из-за защиты от убытков');
  }
}
`;
  }

  // 📊 ТЕСТИРОВАНИЕ СИСТЕМЫ ЗАЩИТЫ
  runProtectionTests() {
    console.log('🧪 ТЕСТИРОВАНИЕ СИСТЕМЫ ЗАЩИТЫ ОТ УБЫТКОВ');
    console.log('═══════════════════════════════════════════════════════════════');

    const testCases = [
      {
        name: 'Прибыльная сделка',
        data: {
          expectedProfitUsd: 2.50,
          expectedProfitPercent: 0.15,
          transactionFeeSol: 0.003,
          positionSizeUsd: 50000
        },
        expectedResult: true
      },
      {
        name: 'Низкая прибыль USD',
        data: {
          expectedProfitUsd: 0.50,
          expectedProfitPercent: 0.15,
          transactionFeeSol: 0.003,
          positionSizeUsd: 50000
        },
        expectedResult: false
      },
      {
        name: 'Низкая прибыль %',
        data: {
          expectedProfitUsd: 2.00,
          expectedProfitPercent: 0.05,
          transactionFeeSol: 0.003,
          positionSizeUsd: 50000
        },
        expectedResult: false
      },
      {
        name: 'Высокая комиссия',
        data: {
          expectedProfitUsd: 1.50,
          expectedProfitPercent: 0.15,
          transactionFeeSol: 0.02,
          positionSizeUsd: 50000
        },
        expectedResult: false
      },
      {
        name: 'Убыточная сделка',
        data: {
          expectedProfitUsd: -2.00,
          expectedProfitPercent: -0.10,
          transactionFeeSol: 0.003,
          positionSizeUsd: 50000
        },
        expectedResult: false
      }
    ];

    testCases.forEach((testCase, index) => {
      console.log(`\n🧪 ТЕСТ ${index + 1}: ${testCase.name}`);
      console.log('─────────────────────────────────────────────────────────────');
      
      const result = this.validateProfitability(testCase.data);
      const passed = result.allowed === testCase.expectedResult;
      
      console.log(`📊 Результат: ${result.allowed ? 'РАЗРЕШЕНО' : 'ОТКЛОНЕНО'}`);
      console.log(`✅ Тест ${passed ? 'ПРОЙДЕН' : 'ПРОВАЛЕН'}`);
      
      if (!passed) {
        console.log(`❌ Ожидалось: ${testCase.expectedResult}, получено: ${result.allowed}`);
      }
    });
  }
}

// 🚀 ЗАПУСК ТЕСТИРОВАНИЯ
async function main() {
  const protection = new ProfitProtectionSystem();
  
  console.log('🛡️ СИСТЕМА ЗАЩИТЫ ОТ УБЫТКОВ');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('📚 Основано на официальной документации Anchor');
  console.log('🎯 Автоматический откат транзакции при недостаточной прибыли');
  console.log('');

  // Запускаем тесты
  protection.runProtectionTests();

  console.log('\n📋 КОД ДЛЯ ИНТЕГРАЦИИ:');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log(protection.generateIntegrationCode());
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ProfitProtectionSystem;
