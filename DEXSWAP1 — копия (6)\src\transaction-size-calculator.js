/**
 * 🔥 ЕДИНЫЙ МОДУЛЬ ДЛЯ РАСЧЕТА РАЗМЕРА SOLANA ТРАНЗАКЦИЙ
 * 
 * ЭТОТ МОДУЛЬ СОДЕРЖИТ ВСЕ ПРАВИЛЬНЫЕ ФОРМУЛЫ И КОНСТАНТЫ
 * ИСПОЛЬЗУЕТСЯ ВЕЗДЕ ВМЕСТО САМОПАЛЬНЫХ РАСЧЕТОВ!
 * 
 * Источники:
 * - Официальная документация Solana
 * - https://medium.com/@asmiller1989/solana-transactions-in-depth-1f7f7fe06ac2
 * - Реальные тесты с транзакциями
 */

// 🔒 ОФИЦИАЛЬНЫЕ ЛИМИТЫ SOLANA
const SOLANA_LIMITS = {
  MAX_TRANSACTION_SIZE: 1232,        // IPv6 MTU (1280) - Network Headers (48)
  RECOMMENDED_TARGET: 1200,          // Рекомендуемый размер с запасом
  SAFETY_MARGIN: 32                  // Запас безопасности
};

// 🗜️ ALT КОНФИГУРАЦИЯ (КРИТИЧНО!)
const ALT_CONFIG = {
  // 🪐 Jupiter ALT таблицы
  JUPITER_QUOTE_ALT_COUNT: 3,        // Quote API возвращает 3 ALT
  JUPITER_SWAP_ALT_COUNT: 2,         // Swap API возвращает 2 ALT
  
  // 🏦 MarginFi ALT таблицы  
  MARGINFI_ALT_COUNT: 1,             // 1 официальная MarginFi ALT
  
  // 🔧 Кастомные ALT таблицы
  CUSTOM_ALT_COUNT: 1,               // 1 наша ALT для 18 статичных адресов
  CUSTOM_STATIC_ADDRESSES: 18,       // Количество наших статичных адресов
  
  // 📊 Общее количество ALT
  get TOTAL_ALT_COUNT() {
    return this.JUPITER_QUOTE_ALT_COUNT + this.JUPITER_SWAP_ALT_COUNT + 
           this.MARGINFI_ALT_COUNT + this.CUSTOM_ALT_COUNT;
  }
};

// 📏 РАЗМЕРЫ КОМПОНЕНТОВ ТРАНЗАКЦИИ
const COMPONENT_SIZES = {
  SIGNATURE: 64,                     // Каждая подпись = 64 байта
  MESSAGE_HEADER: 3,                 // MessageHeader = 3 байта
  RECENT_BLOCKHASH: 32,              // Recent blockhash = 32 байта
  ALT_COMPRESSED_KEYS: 32,           // ALT сжимает ВСЕ ключи в 32 байта!
  
  // Размеры инструкций
  INSTRUCTION_BASE: 1,               // program_id_index = 1 байт
  ACCOUNTS_LENGTH_PREFIX: 1,         // compact-u16 для длины массива accounts
  DATA_LENGTH_PREFIX: 4,             // compact-u16 для длины данных (обычно 4 байта)
  ACCOUNT_INDEX: 1                   // Каждый account index = 1 байт
};

// 🔧 ТИПОВЫЕ РАЗМЕРЫ ИНСТРУКЦИЙ
const INSTRUCTION_SIZES = {
  // 🏦 MarginFi Flash Loan
  FLASH_LOAN_BEGIN: 50,
  FLASH_LOAN_BORROW: 80,
  FLASH_LOAN_REPAY: 80,
  FLASH_LOAN_END: 50,
  
  // 🪐 Jupiter Swaps
  JUPITER_SETUP: 100,
  JUPITER_SWAP: 150,
  JUPITER_CLEANUP: 50,
  
  // 🎭 Обфускация
  COMPUTE_BUDGET_PRICE: 20,
  COMPUTE_BUDGET_LIMIT: 20,
  
  // 🔧 Системные
  SYSTEM_TRANSFER: 40,
  TOKEN_TRANSFER: 60
};

/**
 * 🧮 ОСНОВНАЯ ФУНКЦИЯ РАСЧЕТА РАЗМЕРА ТРАНЗАКЦИИ
 */
function calculateTransactionSize(config) {
  const {
    signers = 3,
    instructions = [],
    useALT = true,
    obfuscationLevel = 0,
    customInstructions = []
  } = config;
  
  // 1️⃣ Подписи
  const signaturesSize = signers * COMPONENT_SIZES.SIGNATURE;
  
  // 2️⃣ Message Header
  const messageHeaderSize = COMPONENT_SIZES.MESSAGE_HEADER;
  
  // 3️⃣ Account Keys (КРИТИЧНО!)
  const accountKeysSize = useALT ? 
    COMPONENT_SIZES.ALT_COMPRESSED_KEYS : 
    calculateUncompressedKeysSize(instructions);
  
  // 4️⃣ Recent Blockhash
  const recentBlockhashSize = COMPONENT_SIZES.RECENT_BLOCKHASH;
  
  // 5️⃣ Инструкции
  const instructionsSize = calculateInstructionsSize(instructions, customInstructions);
  
  // 6️⃣ Базовый размер
  const baseSize = signaturesSize + messageHeaderSize + accountKeysSize + 
                   recentBlockhashSize + instructionsSize;
  
  // 7️⃣ Обфускация
  const availableSpace = SOLANA_LIMITS.MAX_TRANSACTION_SIZE - baseSize;
  const obfuscationSize = calculateObfuscationSize(obfuscationLevel, availableSpace);
  
  // 8️⃣ Итоговый размер
  const totalSize = baseSize + obfuscationSize;
  
  return {
    totalSize,
    breakdown: {
      signatures: signaturesSize,
      messageHeader: messageHeaderSize,
      accountKeys: accountKeysSize,
      recentBlockhash: recentBlockhashSize,
      instructions: instructionsSize,
      obfuscation: obfuscationSize
    },
    isValid: totalSize <= SOLANA_LIMITS.MAX_TRANSACTION_SIZE,
    availableSpace: SOLANA_LIMITS.MAX_TRANSACTION_SIZE - totalSize,
    altConfig: {
      totalALTTables: ALT_CONFIG.TOTAL_ALT_COUNT,
      compressionEnabled: useALT,
      compressionSavings: useALT ? calculateUncompressedKeysSize(instructions) - COMPONENT_SIZES.ALT_COMPRESSED_KEYS : 0
    }
  };
}

/**
 * 📊 РАСЧЕТ РАЗМЕРА ИНСТРУКЦИЙ
 */
function calculateInstructionsSize(instructions, customInstructions = []) {
  let totalSize = 0;

  // Защита от некорректных входных данных
  if (!Array.isArray(instructions)) {
    console.warn(`⚠️ instructions не является массивом:`, typeof instructions);
    return 500; // Консервативная оценка
  }

  // Стандартные инструкции
  instructions.forEach(instructionType => {
    if (typeof instructionType === 'string' && INSTRUCTION_SIZES[instructionType]) {
      totalSize += INSTRUCTION_SIZES[instructionType];
    } else {
      console.warn(`⚠️ Неизвестный тип инструкции: ${instructionType}`);
      totalSize += 50; // Консервативная оценка
    }
  });

  // Кастомные инструкции с детальным расчетом
  if (Array.isArray(customInstructions)) {
    customInstructions.forEach(ix => {
      if (ix && typeof ix === 'object') {
        const keysCount = ix.keys ? ix.keys.length : 0;
        const dataSize = getInstructionDataSize(ix.data);

        const instructionSize = COMPONENT_SIZES.INSTRUCTION_BASE +
                               COMPONENT_SIZES.ACCOUNTS_LENGTH_PREFIX +
                               (keysCount * COMPONENT_SIZES.ACCOUNT_INDEX) +
                               COMPONENT_SIZES.DATA_LENGTH_PREFIX +
                               dataSize;

        totalSize += instructionSize;
      }
    });
  }

  return totalSize;
}

/**
 * 🔍 ПОЛУЧЕНИЕ РАЗМЕРА ДАННЫХ ИНСТРУКЦИИ
 */
function getInstructionDataSize(data) {
  if (!data) return 0;
  
  if (Buffer.isBuffer(data)) {
    return data.length;
  } else if (Array.isArray(data)) {
    return data.length;
  } else if (typeof data === 'object' && data.length !== undefined) {
    return data.length;
  } else {
    return 32; // Консервативная оценка
  }
}

/**
 * 🗜️ РАСЧЕТ РАЗМЕРА БЕЗ ALT СЖАТИЯ
 */
function calculateUncompressedKeysSize(instructions) {
  // Консервативная оценка: ~10 уникальных ключей на инструкцию
  const instructionCount = Array.isArray(instructions) ? instructions.length : 0;
  const estimatedUniqueKeys = Math.max(instructionCount * 10, 50);
  return estimatedUniqueKeys * 32;
}

/**
 * 🎭 РАСЧЕТ РАЗМЕРА ОБФУСКАЦИИ
 */
function calculateObfuscationSize(level, availableSpace) {
  // Защита от некорректных входных данных
  if (typeof level !== 'number' || typeof availableSpace !== 'number') {
    console.warn(`⚠️ Некорректные параметры обфускации: level=${level}, availableSpace=${availableSpace}`);
    return 0;
  }

  if (level === 0 || availableSpace < 50) return 0;

  let obfuscationSize = 0;

  if (level >= 1) {
    obfuscationSize += INSTRUCTION_SIZES.COMPUTE_BUDGET_PRICE;
  }

  if (level >= 2) {
    obfuscationSize += INSTRUCTION_SIZES.COMPUTE_BUDGET_LIMIT;
  }

  // Дополнительная обфускация по уровням
  if (level >= 3) {
    obfuscationSize += 30; // Дополнительные инструкции
  }

  // Не превышать доступное место (с защитой от отрицательных значений)
  const maxObfuscation = Math.max(0, availableSpace - SOLANA_LIMITS.SAFETY_MARGIN);
  return Math.min(obfuscationSize, maxObfuscation);
}

/**
 * 🔥 ПРЕДУСТАНОВЛЕННЫЕ КОНФИГУРАЦИИ
 */
const PRESET_CONFIGS = {
  // Flash Loan + 2 Jupiter Swaps
  FLASH_LOAN_ARBITRAGE: {
    signers: 3,
    instructions: [
      'FLASH_LOAN_BEGIN',
      'FLASH_LOAN_BORROW', 
      'JUPITER_SETUP',
      'JUPITER_SWAP',      // USDC → SOL
      'JUPITER_SWAP',      // SOL → USDC
      'JUPITER_CLEANUP',
      'FLASH_LOAN_REPAY',
      'FLASH_LOAN_END'
    ],
    useALT: true,
    obfuscationLevel: 1
  },
  
  // Простой Jupiter Swap
  SIMPLE_JUPITER_SWAP: {
    signers: 1,
    instructions: [
      'JUPITER_SETUP',
      'JUPITER_SWAP',
      'JUPITER_CLEANUP'
    ],
    useALT: true,
    obfuscationLevel: 0
  }
};

/**
 * 🎯 БЫСТРЫЕ ФУНКЦИИ ДЛЯ ТИПОВЫХ СЛУЧАЕВ
 */
function calculateFlashLoanArbitrageSize(obfuscationLevel = 1) {
  return calculateTransactionSize({
    ...PRESET_CONFIGS.FLASH_LOAN_ARBITRAGE,
    obfuscationLevel
  });
}

function calculateMeteoraSwapSize(swapCount = 1, obfuscationLevel = 0) {
  const instructions = ['JUPITER_SETUP'];
  for (let i = 0; i < swapCount; i++) {
    instructions.push('JUPITER_SWAP');
  }
  instructions.push('JUPITER_CLEANUP');
  
  return calculateTransactionSize({
    signers: 1,
    instructions,
    useALT: true,
    obfuscationLevel
  });
}

/**
 * 📊 ДИАГНОСТИКА И ЛОГИРОВАНИЕ
 */
function logTransactionSizeBreakdown(result) {
  console.log(`🔍 ДИАГНОСТИКА РАЗМЕРА ТРАНЗАКЦИИ:`);
  console.log(`   📝 Подписи: ${result.breakdown.signatures} байт`);
  console.log(`   📋 Message header: ${result.breakdown.messageHeader} байт`);
  console.log(`   🗜️ Account keys: ${result.breakdown.accountKeys} байт`);
  console.log(`   🔗 Recent blockhash: ${result.breakdown.recentBlockhash} байт`);
  console.log(`   ⚙️ Инструкции: ${result.breakdown.instructions} байт`);
  console.log(`   🎭 Обфускация: ${result.breakdown.obfuscation} байт`);
  console.log(`   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━`);
  console.log(`   📊 ИТОГО: ${result.totalSize} байт`);
  console.log(`   🔒 ЛИМИТ: ${SOLANA_LIMITS.MAX_TRANSACTION_SIZE} байт`);
  console.log(`   🎯 ЗАПАС: ${result.availableSpace} байт`);
  console.log(`   ✅ ВАЛИДНА: ${result.isValid ? 'ДА' : 'НЕТ'}`);
  
  if (result.altConfig.compressionEnabled) {
    console.log(`   🗜️ ALT таблиц: ${result.altConfig.totalALTTables}`);
    console.log(`   💾 ALT экономия: ${result.altConfig.compressionSavings} байт`);
  }
}

module.exports = {
  // Основные функции
  calculateTransactionSize,
  calculateFlashLoanArbitrageSize,
  calculateMeteoraSwapSize,
  
  // Утилиты
  logTransactionSizeBreakdown,
  
  // Константы
  SOLANA_LIMITS,
  ALT_CONFIG,
  COMPONENT_SIZES,
  INSTRUCTION_SIZES,
  PRESET_CONFIGS
};
