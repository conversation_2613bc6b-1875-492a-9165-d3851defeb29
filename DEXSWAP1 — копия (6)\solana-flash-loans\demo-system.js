/**
 * 🎬 ДЕМОНСТРАЦИЯ ПОЛНОЙ СИСТЕМЫ: 8 DEX × 15 ТОКЕНОВ
 */

import { 
  DEX_CONFIG, 
  TOKEN_CONFIG, 
  ARBITRAGE_PAIRS,
  getActiveDEXes,
  getActiveTokens,
  EXPANSION_STATS
} from './dex-config.js';

class SystemDemo {
  constructor() {
    this.activeDEXes = getActiveDEXes();
    this.activeTokens = getActiveTokens();
    
    console.log('🎬 SYSTEM DEMO STARTING...');
    console.log('═══════════════════════════════════════════════');
  }

  /**
   * 📊 ПОКАЗАТЬ СТАТИСТИКУ СИСТЕМЫ
   */
  showSystemStats() {
    console.log('\n📊 FINAL SYSTEM STATISTICS:');
    console.log(`✅ DEXes: ${this.activeDEXes.length}/8 (${EXPANSION_STATS.progress.dexes_percent}%)`);
    console.log(`✅ Tokens: ${this.activeTokens.length}/15 (${EXPANSION_STATS.progress.tokens_percent}%)`);
    console.log(`✅ Pairs: ${EXPANSION_STATS.current.pairs}/120 (${EXPANSION_STATS.progress.pairs_percent}%)`);
    console.log(`🔢 Total combinations: ${this.calculateCombinations()}`);
  }

  /**
   * 🔥 ПОКАЗАТЬ ВСЕ DEX
   */
  showAllDEXes() {
    console.log('\n🔥 ALL 8 ACTIVE DEXes:');
    this.activeDEXes.forEach((dex, i) => {
      console.log(`   ${i+1}. ${dex.name.toUpperCase()}`);
      console.log(`      Type: ${dex.type}`);
      console.log(`      Fees: ${dex.fees}%`);
      console.log(`      SDK: ${dex.sdk}`);
      console.log('');
    });
  }

  /**
   * 💰 ПОКАЗАТЬ ВСЕ ТОКЕНЫ
   */
  showAllTokens() {
    console.log('\n💰 ALL 15 ACTIVE TOKENS:');
    
    const tiers = {
      tier1: this.activeTokens.filter(t => t.liquidity_tier === 'tier1'),
      tier2: this.activeTokens.filter(t => t.liquidity_tier === 'tier2'),
      tier3: this.activeTokens.filter(t => t.liquidity_tier === 'tier3')
    };
    
    console.log(`\n   🥇 TIER 1 - High Liquidity (${tiers.tier1.length}):`);
    tiers.tier1.forEach(token => {
      console.log(`      ${token.symbol}: $${token.price_usd} (${token.name})`);
    });
    
    console.log(`\n   🥈 TIER 2 - Medium Liquidity (${tiers.tier2.length}):`);
    tiers.tier2.forEach(token => {
      console.log(`      ${token.symbol}: $${token.price_usd} (${token.name})`);
    });
    
    console.log(`\n   🥉 TIER 3 - Lower Liquidity (${tiers.tier3.length}):`);
    tiers.tier3.forEach(token => {
      console.log(`      ${token.symbol}: $${token.price_usd} (${token.name})`);
    });
  }

  /**
   * 🎯 ПОКАЗАТЬ АРБИТРАЖНЫЕ ПАРЫ
   */
  showArbitragePairs() {
    console.log('\n🎯 ARBITRAGE PAIRS CONFIGURATION:');
    console.log(`   Total pairs: ${ARBITRAGE_PAIRS.length}`);
    
    const byTier = {
      1: ARBITRAGE_PAIRS.filter(p => p.tier === 1),
      2: ARBITRAGE_PAIRS.filter(p => p.tier === 2),
      3: ARBITRAGE_PAIRS.filter(p => p.tier === 3)
    };
    
    console.log(`\n   🥇 Tier 1 pairs (${byTier[1].length}): High priority`);
    byTier[1].slice(0, 5).forEach(pair => {
      console.log(`      ${pair.token1} ↔ ${pair.token2} (min $${pair.min_volume_usd})`);
    });
    
    console.log(`\n   🥈 Tier 2 pairs (${byTier[2].length}): Medium priority`);
    byTier[2].slice(0, 3).forEach(pair => {
      console.log(`      ${pair.token1} ↔ ${pair.token2} (min $${pair.min_volume_usd})`);
    });
    
    console.log(`\n   🥉 Tier 3 pairs (${byTier[3].length}): Lower priority`);
    byTier[3].slice(0, 3).forEach(pair => {
      console.log(`      ${pair.token1} ↔ ${pair.token2} (min $${pair.min_volume_usd})`);
    });
  }

  /**
   * 🔢 РАСЧЕТ КОМБИНАЦИЙ
   */
  calculateCombinations() {
    const tokenPairs = (this.activeTokens.length * (this.activeTokens.length - 1)) / 2;
    const dexPairs = (this.activeDEXes.length * (this.activeDEXes.length - 1)) / 2;
    return tokenPairs * dexPairs;
  }

  /**
   * 🚀 ПОКАЗАТЬ ВОЗМОЖНОСТИ СИСТЕМЫ
   */
  showCapabilities() {
    console.log('\n🚀 SYSTEM CAPABILITIES:');
    console.log('   ✅ Real-time price monitoring across 8 DEXes');
    console.log('   ✅ Arbitrage opportunity detection');
    console.log('   ✅ Flash loan integration ready');
    console.log('   ✅ MEV optimization potential');
    console.log('   ✅ Multi-tier liquidity management');
    console.log('   ✅ Automated profit calculation');
    console.log('   ✅ Risk management by token tiers');
    console.log('   ✅ Scalable to more DEXes/tokens');
  }

  /**
   * 📈 ПОКАЗАТЬ ОЖИДАЕМУЮ ПРОИЗВОДИТЕЛЬНОСТЬ
   */
  showExpectedPerformance() {
    console.log('\n📈 EXPECTED PERFORMANCE:');
    console.log('   🎯 Scan frequency: 1 second intervals');
    console.log('   💰 Daily opportunities: 50-100 estimated');
    console.log('   ⚡ Response time: <400ms per opportunity');
    console.log('   🔥 Profit threshold: 0.5% minimum');
    console.log('   📊 Success rate: 70-85% estimated');
    console.log('   💎 Best opportunities: Tier 1 pairs');
  }

  /**
   * 🎬 ЗАПУСК ДЕМОНСТРАЦИИ
   */
  async runDemo() {
    this.showSystemStats();
    
    await this.delay(1000);
    this.showAllDEXes();
    
    await this.delay(1000);
    this.showAllTokens();
    
    await this.delay(1000);
    this.showArbitragePairs();
    
    await this.delay(1000);
    this.showCapabilities();
    
    await this.delay(1000);
    this.showExpectedPerformance();
    
    console.log('\n🎉 SYSTEM DEMO COMPLETED!');
    console.log('═══════════════════════════════════════════════');
    console.log('🚀 READY TO START FULL ARBITRAGE MONITORING!');
    console.log('💰 All 8 DEXes × 15 tokens = 120 pairs active');
    console.log('⚡ Use: npm run full (for continuous monitoring)');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 🎬 ЗАПУСК ДЕМОНСТРАЦИИ
async function main() {
  const demo = new SystemDemo();
  await demo.runDemo();
}

main().catch(console.error);
