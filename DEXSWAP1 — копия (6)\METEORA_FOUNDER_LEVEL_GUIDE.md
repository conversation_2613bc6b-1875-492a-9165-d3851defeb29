# 🌟 METEORA FOUNDER-LEVEL DEVELOPMENT GUIDE 🌟
## Фундаментальное понимание архитектуры и низкоуровневого программирования

### 🚨 КРИТИЧЕСКИЕ ПРАВИЛА METEORA РАЗРАБОТКИ

#### ⚠️ НАРУШЕНИЕ ЭТИХ ПРАВИЛ НЕДОПУСТИМО:

1. **BIN ARRAY VALIDATION** - Всегда проверяй корректность bin arrays перед операциями
2. **ACTIVE BIN VERIFICATION** - Только активный bin зарабатывает комиссии
3. **PRICE CALCULATION ACCURACY** - Используй точные формулы для расчета цен в bins
4. **LIQUIDITY CONCENTRATION** - Понимай механизм концентрированной ликвидности
5. **ZERO SLIPPAGE BINS** - Учитывай особенности торговли внутри bin
6. **DYNAMIC FEE STRUCTURE** - Адаптируйся к изменяющимся комиссиям
7. **CPI INTEGRATION SAFETY** - Безопасная интеграция через Cross-Program Invocation

### 🏗️ АРХИТЕКТУРА METEORA DLMM

#### Основные концепции DLMM (Dynamic Liquidity Market Maker)
```
DLMM = Concentrated Liquidity + Zero Slippage Bins + Dynamic Fees

Ключевые особенности:
- Liquidity распределена по дискретным bins с фиксированной шириной
- Каждый bin представляет одну ценовую точку
- Торговля внутри активного bin имеет нулевое проскальзывание
- Только активный bin зарабатывает торговые комиссии
```

#### Структура Bin Arrays
```rust
// Критическая структура данных DLMM
pub struct BinArray {
    pub index: i64,                    // Индекс массива bins
    pub version: u8,                   // Версия для совместимости
    pub padding: [u8; 7],             // Выравнивание памяти
    pub lb_pair: Pubkey,              // Ссылка на LB Pair
    pub bins: [Bin; MAX_BIN_PER_ARRAY], // Массив bins (обычно 70)
}

pub struct Bin {
    pub amount_x: u64,                // Количество токена X
    pub amount_y: u64,                // Количество токена Y
    pub price: u128,                  // Цена bin (фиксированная)
    pub liquidity_supply: u64,        // Общая ликвидность
    pub reward_per_token_stored: u128, // Накопленные награды
    pub fee_amount_x: u64,            // Накопленные комиссии X
    pub fee_amount_y: u64,            // Накопленные комиссии Y
}
```

#### Математическая модель DLMM
```rust
// Формула ликвидности в bin (Constant Sum)
// P * x + y = L
// где P = цена, x = количество токена X, y = количество токена Y, L = ликвидность

// Composition Factor (c) - процент ликвидности в токене Y
// c = y / L
// Отсюда:
// y = c * L
// x = (L / P) * (1 - c)

fn calculate_bin_composition(
    liquidity: u64,
    price: u128,
    composition_factor: f64
) -> (u64, u64) {
    let y = (liquidity as f64 * composition_factor) as u64;
    let x = ((liquidity as f64 / price as f64) * (1.0 - composition_factor)) as u64;
    (x, y)
}

// Расчет цены следующего bin
fn calculate_next_bin_price(current_price: u128, bin_step: u16) -> u128 {
    // bin_step в базисных пунктах (например, 25 = 0.25%)
    let multiplier = 1.0 + (bin_step as f64 / 10000.0);
    (current_price as f64 * multiplier) as u128
}
```

### 🔧 METEORA PROGRAM IDS (КРИТИЧЕСКИ ВАЖНО!)

#### Mainnet Program IDs
```rust
// DLMM Program
pub const DLMM_PROGRAM_ID: &str = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";

// Dynamic AMM Program  
pub const DYNAMIC_AMM_PROGRAM_ID: &str = "Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB";

// DAMM v2 Program
pub const DAMM_V2_PROGRAM_ID: &str = "DMMhJKhqGKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK";

// Memecoin Pool v1
pub const MEMECOIN_POOL_V1_ID: &str = "MEMEoooooooooooooooooooooooooooooooooooooooooooo";

// Alpha Vault Program
pub const ALPHA_VAULT_PROGRAM_ID: &str = "ALPHAaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa";
```

#### Devnet Program IDs
```rust
// Для тестирования на devnet
pub const DLMM_DEVNET_ID: &str = "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo";
```

### 🔍 НИЗКОУРОВНЕВАЯ ИНТЕГРАЦИЯ С METEORA

#### Структура аккаунтов для DLMM Swap
```rust
#[derive(Accounts)]
pub struct DlmmSwap<'info> {
    /// Пользователь, выполняющий swap
    #[account(mut)]
    pub user: Signer<'info>,
    
    /// LB Pair аккаунт (основной пул)
    #[account(mut)]
    pub lb_pair: AccountLoader<'info, LbPair>,
    
    /// Bin Array Lower (нижний массив bins)
    #[account(mut)]
    pub bin_array_bitmap_extension: Option<AccountLoader<'info, BinArrayBitmapExtension>>,
    
    /// Reserve аккаунты токенов
    #[account(mut)]
    pub reserve_x: Account<'info, TokenAccount>,
    #[account(mut)]
    pub reserve_y: Account<'info, TokenAccount>,
    
    /// Пользовательские токен аккаунты
    #[account(mut)]
    pub user_token_x: Account<'info, TokenAccount>,
    #[account(mut)]
    pub user_token_y: Account<'info, TokenAccount>,
    
    /// Bin Arrays (массивы bins для операции)
    #[account(mut)]
    pub bin_array_lower: AccountLoader<'info, BinArray>,
    #[account(mut)]
    pub bin_array_upper: AccountLoader<'info, BinArray>,
    
    /// Oracle аккаунт (если используется)
    pub oracle: Option<AccountInfo<'info>>,
    
    /// Host Fee аккаунт (для комиссий)
    pub host_fee_in: Option<Account<'info, TokenAccount>>,
    
    /// Программы
    pub token_program: Program<'info, Token>,
    pub event_authority: AccountInfo<'info>,
    pub program: AccountInfo<'info>,
}
```

#### Низкоуровневый DLMM Swap через CPI
```rust
use anchor_lang::prelude::*;
use anchor_spl::token::{self, Token, TokenAccount};

pub fn dlmm_swap_cpi(
    ctx: Context<DlmmSwapCpi>,
    amount_in: u64,
    minimum_amount_out: u64,
    swap_for_y: bool, // true = X->Y, false = Y->X
) -> Result<()> {
    // Подготовка instruction data
    let mut instruction_data = Vec::new();
    instruction_data.extend_from_slice(&[0x24, 0x8a, 0x9c, 0xa5, 0x7d, 0x13, 0x1c, 0x8c]); // swap discriminator
    instruction_data.extend_from_slice(&amount_in.to_le_bytes());
    instruction_data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    instruction_data.push(if swap_for_y { 1 } else { 0 });

    // Подготовка аккаунтов
    let accounts = vec![
        AccountMeta::new(*ctx.accounts.lb_pair.key, false),
        AccountMeta::new(*ctx.accounts.bin_array_bitmap_extension.key, false),
        AccountMeta::new(*ctx.accounts.reserve_x.key, false),
        AccountMeta::new(*ctx.accounts.reserve_y.key, false),
        AccountMeta::new(*ctx.accounts.user_token_x.key, false),
        AccountMeta::new(*ctx.accounts.user_token_y.key, false),
        AccountMeta::new(*ctx.accounts.bin_array_lower.key, false),
        AccountMeta::new(*ctx.accounts.bin_array_upper.key, false),
        AccountMeta::new(*ctx.accounts.user.key, true),
        AccountMeta::new_readonly(*ctx.accounts.token_program.key, false),
        AccountMeta::new_readonly(*ctx.accounts.event_authority.key, false),
        AccountMeta::new_readonly(*ctx.accounts.program.key, false),
    ];

    // Создание instruction
    let instruction = Instruction {
        program_id: DLMM_PROGRAM_ID,
        accounts,
        data: instruction_data,
    };

    // Выполнение CPI
    invoke(
        &instruction,
        &[
            ctx.accounts.lb_pair.to_account_info(),
            ctx.accounts.bin_array_bitmap_extension.to_account_info(),
            ctx.accounts.reserve_x.to_account_info(),
            ctx.accounts.reserve_y.to_account_info(),
            ctx.accounts.user_token_x.to_account_info(),
            ctx.accounts.user_token_y.to_account_info(),
            ctx.accounts.bin_array_lower.to_account_info(),
            ctx.accounts.bin_array_upper.to_account_info(),
            ctx.accounts.user.to_account_info(),
            ctx.accounts.token_program.to_account_info(),
            ctx.accounts.event_authority.to_account_info(),
            ctx.accounts.program.to_account_info(),
        ],
    )?;

    Ok(())
}
```

### 📊 КРИТИЧЕСКИЕ РАСЧЕТЫ ДЛЯ АРБИТРАЖА

#### Расчет выходного количества для DLMM
```rust
pub fn calculate_dlmm_amount_out(
    amount_in: u64,
    bin_arrays: &[BinArray],
    active_bin_id: i32,
    swap_for_y: bool,
) -> Result<u64> {
    let mut amount_out = 0u64;
    let mut remaining_amount_in = amount_in;
    let mut current_bin_id = active_bin_id;

    while remaining_amount_in > 0 {
        let bin = get_bin_from_arrays(bin_arrays, current_bin_id)?;
        
        if swap_for_y {
            // Swapping X for Y
            let available_x = bin.amount_x;
            let available_y = bin.amount_y;
            
            if available_x == 0 {
                current_bin_id += 1; // Move to next bin
                continue;
            }
            
            let amount_x_to_consume = std::cmp::min(remaining_amount_in, available_x);
            let amount_y_out = calculate_y_out_from_x(amount_x_to_consume, bin.price);
            
            amount_out = amount_out.checked_add(amount_y_out)
                .ok_or(ProgramError::ArithmeticOverflow)?;
            remaining_amount_in = remaining_amount_in.checked_sub(amount_x_to_consume)
                .ok_or(ProgramError::ArithmeticOverflow)?;
                
            if amount_x_to_consume == available_x {
                current_bin_id += 1; // Bin depleted, move to next
            }
        } else {
            // Swapping Y for X - аналогичная логика
            // ... implementation
        }
    }

    Ok(amount_out)
}

fn calculate_y_out_from_x(amount_x: u64, price: u128) -> u64 {
    // В DLMM: P * x + y = L (constant sum)
    // При swap X->Y: y_out = P * x_in
    ((amount_x as u128 * price) / 1_000_000_000_000_000_000) as u64 // Adjust for decimals
}
```

### 🎯 КРИТИЧЕСКИЕ ТЕХНИКИ ОПТИМИЗАЦИИ METEORA

#### Предвычисление Bin Arrays для скорости
```rust
// ✅ Кэширование bin arrays для быстрого доступа
pub struct DlmmBinCache {
    pub bin_arrays: HashMap<i64, BinArray>,
    pub active_bin_id: i32,
    pub last_update_slot: u64,
}

impl DlmmBinCache {
    pub fn new() -> Self {
        Self {
            bin_arrays: HashMap::new(),
            active_bin_id: 0,
            last_update_slot: 0,
        }
    }

    #[inline(always)]
    pub fn get_bin_fast(&self, bin_id: i32) -> Option<&Bin> {
        let array_index = bin_id / MAX_BIN_PER_ARRAY as i32;
        let bin_index = (bin_id % MAX_BIN_PER_ARRAY as i32) as usize;

        self.bin_arrays.get(&(array_index as i64))
            .and_then(|array| array.bins.get(bin_index))
    }

    #[inline(always)]
    pub fn update_cache(&mut self, bin_arrays: Vec<(i64, BinArray)>, slot: u64) {
        if slot > self.last_update_slot {
            for (index, array) in bin_arrays {
                self.bin_arrays.insert(index, array);
            }
            self.last_update_slot = slot;
        }
    }
}

// ✅ Быстрый расчет арбитражных возможностей
pub fn calculate_arbitrage_opportunity(
    dlmm_cache: &DlmmBinCache,
    other_dex_price: u64,
    amount: u64,
) -> Option<ArbitrageOpportunity> {
    let active_bin = dlmm_cache.get_bin_fast(dlmm_cache.active_bin_id)?;
    let dlmm_price = active_bin.price;

    // Быстрая проверка профитабельности
    let price_diff = if dlmm_price > other_dex_price as u128 {
        dlmm_price - other_dex_price as u128
    } else {
        other_dex_price as u128 - dlmm_price
    };

    let min_profit_threshold = dlmm_price / 1000; // 0.1% минимум

    if price_diff > min_profit_threshold {
        Some(ArbitrageOpportunity {
            profit_estimate: calculate_profit_estimate(amount, price_diff, dlmm_price),
            direction: if dlmm_price > other_dex_price as u128 {
                ArbitrageDirection::SellOnDlmm
            } else {
                ArbitrageDirection::BuyOnDlmm
            },
            confidence: calculate_confidence_score(active_bin, amount),
        })
    } else {
        None
    }
}
```

#### Оптимизированная обработка множественных Bin Arrays
```rust
// ✅ Batch обработка bin arrays для минимизации RPC вызовов
pub async fn fetch_bin_arrays_batch(
    rpc_client: &RpcClient,
    lb_pair: &Pubkey,
    active_bin_id: i32,
    range: i32, // Количество bin arrays в каждую сторону
) -> Result<Vec<(i64, BinArray)>> {
    let mut bin_array_keys = Vec::new();

    // Генерируем ключи bin arrays вокруг активного bin
    for offset in -range..=range {
        let array_index = (active_bin_id / MAX_BIN_PER_ARRAY as i32) + offset;
        let (bin_array_key, _) = derive_bin_array_pda(lb_pair, array_index as i64);
        bin_array_keys.push(bin_array_key);
    }

    // Batch запрос всех аккаунтов
    let accounts = rpc_client.get_multiple_accounts(&bin_array_keys).await?;

    let mut bin_arrays = Vec::new();
    for (i, account_opt) in accounts.iter().enumerate() {
        if let Some(account) = account_opt {
            let array_index = (active_bin_id / MAX_BIN_PER_ARRAY as i32) + (i as i32 - range);
            let bin_array: BinArray = BinArray::try_deserialize(&mut account.data.as_slice())?;
            bin_arrays.push((array_index as i64, bin_array));
        }
    }

    Ok(bin_arrays)
}

// ✅ Derive PDA для bin array
pub fn derive_bin_array_pda(lb_pair: &Pubkey, index: i64) -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[
            b"bin_array",
            lb_pair.as_ref(),
            &index.to_le_bytes(),
        ],
        &DLMM_PROGRAM_ID,
    )
}
```

### 🔒 БЕЗОПАСНОСТЬ И ВАЛИДАЦИЯ METEORA

#### Критическая валидация DLMM аккаунтов
```rust
// ✅ Полная валидация LB Pair аккаунта
pub fn validate_lb_pair(lb_pair: &LbPair) -> Result<()> {
    // Проверка инициализации
    if lb_pair.status == 0 {
        return Err(ProgramError::UninitializedAccount.into());
    }

    // Проверка активности пула
    if lb_pair.status == 2 { // Disabled
        return Err(ProgramError::Custom(1001).into()); // Pool disabled
    }

    // Проверка корректности bin step
    if lb_pair.bin_step == 0 || lb_pair.bin_step > 1000 {
        return Err(ProgramError::InvalidAccountData.into());
    }

    // Проверка валидности токенов
    if lb_pair.token_x_mint == Pubkey::default() ||
       lb_pair.token_y_mint == Pubkey::default() {
        return Err(ProgramError::InvalidAccountData.into());
    }

    // Проверка reserve аккаунтов
    if lb_pair.reserve_x == Pubkey::default() ||
       lb_pair.reserve_y == Pubkey::default() {
        return Err(ProgramError::InvalidAccountData.into());
    }

    Ok(())
}

// ✅ Валидация bin array перед использованием
pub fn validate_bin_array(
    bin_array: &BinArray,
    expected_lb_pair: &Pubkey,
    expected_index: i64,
) -> Result<()> {
    // Проверка принадлежности к правильному LB Pair
    if bin_array.lb_pair != *expected_lb_pair {
        return Err(ProgramError::InvalidAccountData.into());
    }

    // Проверка корректности индекса
    if bin_array.index != expected_index {
        return Err(ProgramError::InvalidAccountData.into());
    }

    // Проверка версии для совместимости
    if bin_array.version > CURRENT_BIN_ARRAY_VERSION {
        return Err(ProgramError::InvalidAccountData.into());
    }

    // Валидация каждого bin в массиве
    for (i, bin) in bin_array.bins.iter().enumerate() {
        if bin.liquidity_supply > 0 {
            // Проверка корректности соотношения токенов
            if bin.amount_x == 0 && bin.amount_y == 0 {
                return Err(ProgramError::Custom(1002).into()); // Invalid bin state
            }
        }
    }

    Ok(())
}

// ✅ Проверка достаточности ликвидности для swap
pub fn validate_swap_liquidity(
    bin_arrays: &[BinArray],
    active_bin_id: i32,
    amount_in: u64,
    swap_for_y: bool,
) -> Result<()> {
    let mut available_liquidity = 0u64;
    let mut current_bin_id = active_bin_id;
    let mut remaining_amount = amount_in;

    // Проходим по bins и проверяем доступную ликвидность
    for _ in 0..MAX_BINS_TO_CHECK {
        if remaining_amount == 0 {
            break;
        }

        if let Some(bin) = get_bin_from_arrays(bin_arrays, current_bin_id)? {
            let available_in_bin = if swap_for_y {
                bin.amount_x
            } else {
                bin.amount_y
            };

            let consumable = std::cmp::min(remaining_amount, available_in_bin);
            available_liquidity = available_liquidity.checked_add(consumable)
                .ok_or(ProgramError::ArithmeticOverflow)?;
            remaining_amount = remaining_amount.checked_sub(consumable)
                .ok_or(ProgramError::ArithmeticOverflow)?;

            if consumable == available_in_bin {
                current_bin_id = if swap_for_y {
                    current_bin_id + 1
                } else {
                    current_bin_id - 1
                };
            }
        } else {
            break; // No more bins available
        }
    }

    if remaining_amount > 0 {
        return Err(ProgramError::Custom(1003).into()); // Insufficient liquidity
    }

    Ok(())
}
```

### 🚀 ПРОДВИНУТЫЕ ТЕХНИКИ METEORA ИНТЕГРАЦИИ

#### Flash Loan интеграция с DLMM
```rust
// ✅ Комбинирование MarginFi flash loans с DLMM арбитражем
pub fn execute_dlmm_flash_arbitrage(
    ctx: Context<FlashArbitrageContext>,
    flash_amount: u64,
    min_profit: u64,
) -> Result<()> {
    // 1. Инициируем flash loan
    let flash_loan_ix = build_flash_loan_begin_ix(
        &ctx.accounts.marginfi_program.key(),
        &ctx.accounts.marginfi_account.key(),
        &ctx.accounts.signer.key(),
        &ctx.accounts.bank.key(),
        flash_amount,
    )?;

    // 2. Выполняем арбитраж на DLMM
    let dlmm_swap_ix = build_dlmm_swap_ix(
        &ctx.accounts.dlmm_program.key(),
        &ctx.accounts.lb_pair.key(),
        &ctx.accounts.user_token_in.key(),
        &ctx.accounts.user_token_out.key(),
        flash_amount,
        0, // minimum_amount_out будет рассчитан
        true, // swap_for_y
    )?;

    // 3. Возвращаем flash loan с прибылью
    let repay_amount = flash_amount + calculate_flash_loan_fee(flash_amount);
    let flash_loan_end_ix = build_flash_loan_end_ix(
        &ctx.accounts.marginfi_program.key(),
        &ctx.accounts.marginfi_account.key(),
        &ctx.accounts.signer.key(),
        &ctx.accounts.bank.key(),
        repay_amount,
    )?;

    // Проверяем профитабельность перед выполнением
    let estimated_profit = calculate_arbitrage_profit(
        flash_amount,
        &ctx.accounts.lb_pair,
        &ctx.accounts.other_dex_price,
    )?;

    if estimated_profit < min_profit {
        return Err(ProgramError::Custom(2001).into()); // Insufficient profit
    }

    // Выполняем все операции атомарно
    invoke_multiple(&[flash_loan_ix, dlmm_swap_ix, flash_loan_end_ix], ctx.remaining_accounts)?;

    Ok(())
}

// ✅ Построение DLMM swap instruction для CPI
fn build_dlmm_swap_ix(
    program_id: &Pubkey,
    lb_pair: &Pubkey,
    user_token_in: &Pubkey,
    user_token_out: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
    swap_for_y: bool,
) -> Result<Instruction> {
    let mut data = Vec::new();
    data.extend_from_slice(&[0x24, 0x8a, 0x9c, 0xa5, 0x7d, 0x13, 0x1c, 0x8c]); // swap discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.push(if swap_for_y { 1 } else { 0 });

    let accounts = vec![
        AccountMeta::new(*lb_pair, false),
        AccountMeta::new(*user_token_in, false),
        AccountMeta::new(*user_token_out, false),
        // ... другие необходимые аккаунты
    ];

    Ok(Instruction {
        program_id: *program_id,
        accounts,
        data,
    })
}
```

#### Мониторинг и обновление цен в реальном времени
```rust
// ✅ WebSocket подключение для мониторинга DLMM изменений
use tokio_tungstenite::{connect_async, tungstenite::Message};

pub struct DlmmPriceMonitor {
    ws_url: String,
    lb_pairs: Vec<Pubkey>,
    price_cache: Arc<Mutex<HashMap<Pubkey, DlmmPrice>>>,
}

impl DlmmPriceMonitor {
    pub async fn start_monitoring(&self) -> Result<()> {
        let (ws_stream, _) = connect_async(&self.ws_url).await?;
        let (mut write, mut read) = ws_stream.split();

        // Подписываемся на изменения аккаунтов LB Pair
        for lb_pair in &self.lb_pairs {
            let subscription = json!({
                "jsonrpc": "2.0",
                "id": 1,
                "method": "accountSubscribe",
                "params": [
                    lb_pair.to_string(),
                    {
                        "encoding": "base64",
                        "commitment": "confirmed"
                    }
                ]
            });

            write.send(Message::Text(subscription.to_string())).await?;
        }

        // Обрабатываем входящие обновления
        while let Some(msg) = read.next().await {
            match msg? {
                Message::Text(text) => {
                    if let Ok(update) = serde_json::from_str::<AccountUpdate>(&text) {
                        self.process_account_update(update).await?;
                    }
                }
                _ => {}
            }
        }

        Ok(())
    }

    async fn process_account_update(&self, update: AccountUpdate) -> Result<()> {
        if let Some(account_data) = update.params.result.value.data {
            let lb_pair: LbPair = LbPair::try_deserialize(&mut account_data.as_slice())?;

            let new_price = DlmmPrice {
                active_bin_id: lb_pair.active_id,
                bin_step: lb_pair.bin_step,
                price: calculate_bin_price(lb_pair.active_id, lb_pair.bin_step),
                timestamp: std::time::SystemTime::now(),
            };

            // Обновляем кэш цен
            let mut cache = self.price_cache.lock().await;
            cache.insert(Pubkey::from_str(&update.params.result.pubkey)?, new_price);

            // Проверяем арбитражные возможности
            self.check_arbitrage_opportunities(&lb_pair).await?;
        }

        Ok(())
    }

    async fn check_arbitrage_opportunities(&self, lb_pair: &LbPair) -> Result<()> {
        // Быстрая проверка арбитража с другими DEX
        let current_price = calculate_bin_price(lb_pair.active_id, lb_pair.bin_step);

        // Сравниваем с ценами других DEX (Jupiter, Orca, Raydium)
        let other_prices = fetch_other_dex_prices(&lb_pair.token_x_mint, &lb_pair.token_y_mint).await?;

        for (dex_name, other_price) in other_prices {
            let price_diff_pct = ((current_price as f64 - other_price as f64) / other_price as f64).abs();

            if price_diff_pct > 0.005 { // 0.5% минимальная разница
                // Отправляем сигнал для выполнения арбитража
                self.trigger_arbitrage_signal(lb_pair, &dex_name, current_price, other_price).await?;
            }
        }

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct DlmmPrice {
    pub active_bin_id: i32,
    pub bin_step: u16,
    pub price: u128,
    pub timestamp: std::time::SystemTime,
}

// ✅ Расчет цены bin по ID и step
fn calculate_bin_price(bin_id: i32, bin_step: u16) -> u128 {
    // Базовая цена (обычно 1.0 в соответствующих единицах)
    let base_price = 1_000_000_000_000_000_000u128; // 1.0 с 18 знаками

    // Множитель для каждого шага
    let step_multiplier = 1.0 + (bin_step as f64 / 10000.0);

    // Рассчитываем цену: base_price * (1 + bin_step/10000)^bin_id
    let price_f64 = (base_price as f64) * step_multiplier.powi(bin_id);
    price_f64 as u128
}
```

### 📈 МЕТРИКИ И АНАЛИТИКА METEORA

#### Сбор статистики производительности
```rust
// ✅ Детальная аналитика DLMM операций
#[derive(Debug, Clone)]
pub struct DlmmAnalytics {
    pub total_swaps: u64,
    pub total_volume_x: u64,
    pub total_volume_y: u64,
    pub total_fees_collected: u64,
    pub average_slippage: f64,
    pub successful_arbitrages: u64,
    pub failed_arbitrages: u64,
    pub average_profit_per_arbitrage: u64,
    pub bins_utilized: HashSet<i32>,
    pub peak_liquidity_bin: i32,
}

impl DlmmAnalytics {
    pub fn new() -> Self {
        Self {
            total_swaps: 0,
            total_volume_x: 0,
            total_volume_y: 0,
            total_fees_collected: 0,
            average_slippage: 0.0,
            successful_arbitrages: 0,
            failed_arbitrages: 0,
            average_profit_per_arbitrage: 0,
            bins_utilized: HashSet::new(),
            peak_liquidity_bin: 0,
        }
    }

    pub fn record_swap(&mut self, swap_data: &SwapData) {
        self.total_swaps += 1;

        if swap_data.swap_for_y {
            self.total_volume_x += swap_data.amount_in;
            self.total_volume_y += swap_data.amount_out;
        } else {
            self.total_volume_y += swap_data.amount_in;
            self.total_volume_x += swap_data.amount_out;
        }

        self.total_fees_collected += swap_data.fee_amount;

        // Обновляем среднее проскальзывание
        let current_slippage = calculate_slippage(swap_data);
        self.average_slippage = (self.average_slippage * (self.total_swaps - 1) as f64 + current_slippage) / self.total_swaps as f64;

        // Отмечаем использованные bins
        for bin_id in &swap_data.bins_used {
            self.bins_utilized.insert(*bin_id);
        }
    }

    pub fn record_arbitrage(&mut self, arbitrage_result: &ArbitrageResult) {
        if arbitrage_result.success {
            self.successful_arbitrages += 1;
            let current_avg = self.average_profit_per_arbitrage;
            self.average_profit_per_arbitrage =
                (current_avg * (self.successful_arbitrages - 1) + arbitrage_result.profit) / self.successful_arbitrages;
        } else {
            self.failed_arbitrages += 1;
        }
    }

    pub fn get_success_rate(&self) -> f64 {
        let total_attempts = self.successful_arbitrages + self.failed_arbitrages;
        if total_attempts == 0 {
            0.0
        } else {
            self.successful_arbitrages as f64 / total_attempts as f64
        }
    }

    pub fn get_roi(&self) -> f64 {
        // Возврат инвестиций на основе собранных комиссий и прибыли от арбитража
        let total_profit = self.total_fees_collected + (self.average_profit_per_arbitrage * self.successful_arbitrages);
        // ROI расчет зависит от начального капитала
        total_profit as f64 / 1_000_000.0 // Предполагаем начальный капитал 1M
    }
}

#[derive(Debug)]
pub struct SwapData {
    pub amount_in: u64,
    pub amount_out: u64,
    pub fee_amount: u64,
    pub swap_for_y: bool,
    pub bins_used: Vec<i32>,
    pub expected_amount_out: u64,
}

fn calculate_slippage(swap_data: &SwapData) -> f64 {
    if swap_data.expected_amount_out == 0 {
        return 0.0;
    }

    let slippage = (swap_data.expected_amount_out as f64 - swap_data.amount_out as f64) / swap_data.expected_amount_out as f64;
    slippage.abs()
}
```

### ⚡ ЭКСТРЕННЫЙ ЧЕКЛИСТ METEORA РАЗРАБОТКИ

#### Перед каждым DLMM взаимодействием:
- [ ] LB Pair аккаунт валидирован и активен
- [ ] Bin Arrays загружены и проверены
- [ ] Active Bin ID корректен
- [ ] Достаточная ликвидность для операции
- [ ] Reserve аккаунты соответствуют LB Pair
- [ ] Token программы корректны
- [ ] Slippage tolerance установлен
- [ ] Minimum amount out рассчитан
- [ ] Fee structure понятна

#### Перед деплоем арбитражного бота:
- [ ] Все Program IDs проверены (mainnet/devnet)
- [ ] WebSocket подключения стабильны
- [ ] Кэширование bin arrays оптимизировано
- [ ] Flash loan интеграция протестирована
- [ ] Мониторинг цен в реальном времени работает
- [ ] Аналитика и метрики настроены
- [ ] Обработка ошибок полная
- [ ] Профитабельность проверена
- [ ] Лимиты безопасности установлены

### 🎯 КРИТИЧЕСКИЕ ОШИБКИ И РЕШЕНИЯ

#### Частые ошибки DLMM интеграции:
```rust
// ❌ ОШИБКА: Использование pool address вместо reserve accounts
// Неправильно:
let pool_address = lb_pair.key();
let swap_accounts = vec![pool_address]; // НЕПРАВИЛЬНО!

// ✅ ПРАВИЛЬНО: Использование реальных reserve accounts
let reserve_x = lb_pair.reserve_x;
let reserve_y = lb_pair.reserve_y;
let swap_accounts = vec![reserve_x, reserve_y]; // ПРАВИЛЬНО!

// ❌ ОШИБКА: Неправильная сериализация instruction data
// Неправильно:
let instruction_data = vec![amount_in.to_le_bytes()]; // НЕПРАВИЛЬНО!

// ✅ ПРАВИЛЬНО: Корректный discriminator + данные
let mut instruction_data = Vec::new();
instruction_data.extend_from_slice(&[0x24, 0x8a, 0x9c, 0xa5, 0x7d, 0x13, 0x1c, 0x8c]); // discriminator
instruction_data.extend_from_slice(&amount_in.to_le_bytes());
instruction_data.extend_from_slice(&minimum_amount_out.to_le_bytes());
instruction_data.push(if swap_for_y { 1 } else { 0 });

// ❌ ОШИБКА: Игнорирование bin array bitmap extension
// Неправильно:
let accounts = vec![lb_pair, user_token_in, user_token_out]; // НЕПРАВИЛЬНО!

// ✅ ПРАВИЛЬНО: Включение всех необходимых аккаунтов
let accounts = vec![
    lb_pair,
    bin_array_bitmap_extension, // КРИТИЧЕСКИ ВАЖНО!
    reserve_x,
    reserve_y,
    user_token_in,
    user_token_out,
    bin_array_lower,
    bin_array_upper,
    // ... остальные аккаунты
];
```

#### Коды ошибок DLMM и их решения:
```rust
// Основные коды ошибок Meteora DLMM
pub const DLMM_ERROR_CODES: &[(u32, &str, &str)] = &[
    (6000, "InvalidStartBinIndex", "Проверь корректность начального bin index"),
    (6001, "InvalidBinId", "Bin ID выходит за допустимые пределы"),
    (6002, "InvalidInput", "Входные параметры некорректны"),
    (6003, "ExceededAmountSlippageTolerance", "Превышен slippage tolerance"),
    (6004, "ExceededBinSlippageTolerance", "Превышен bin slippage tolerance"),
    (6005, "CompositionFactorFlawed", "Некорректный composition factor"),
    (6006, "NonPresetBinStep", "Bin step не из предустановленных значений"),
    (6007, "ZeroLiquidity", "Нулевая ликвидность в bin"),
    (6008, "InvalidPosition", "Некорректная позиция"),
    (6009, "BinArrayNotFound", "Bin array не найден"),
    (6010, "InvalidTokenMint", "Некорректный token mint"),
    (6011, "InvalidAccountForSingleDeposit", "Некорректный аккаунт для single deposit"),
    (6012, "PairInsufficientLiquidity", "Недостаточная ликвидность в паре"),
];

// ✅ Функция для обработки ошибок DLMM
pub fn handle_dlmm_error(error_code: u32) -> String {
    for (code, name, solution) in DLMM_ERROR_CODES {
        if *code == error_code {
            return format!("DLMM Error {}: {} - Решение: {}", code, name, solution);
        }
    }
    format!("Unknown DLMM error: {}", error_code)
}
```

### 📚 РЕСУРСЫ ДЛЯ ГЛУБОКОГО ИЗУЧЕНИЯ

#### Официальная документация:
- **Meteora Docs**: https://docs.meteora.ag/
- **DLMM SDK GitHub**: https://github.com/MeteoraAg/dlmm-sdk
- **CPI Examples**: https://github.com/MeteoraAg/cpi-examples
- **Program IDs**: https://docs.meteora.ag/resources/meteora-program-ids

#### Критические файлы для изучения:
- **DLMM Program**: `/programs/lb_clmm/src/lib.rs`
- **Bin Array Logic**: `/programs/lb_clmm/src/state/bin_array.rs`
- **Swap Logic**: `/programs/lb_clmm/src/instructions/swap.rs`
- **TypeScript SDK**: `/ts-client/src/dlmm/index.ts`

#### Тестовые сети:
- **Mainnet Program**: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
- **Devnet Program**: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
- **Localnet**: Используй Anchor.toml для локальной разработки

### 🔥 ФИНАЛЬНЫЕ РЕКОМЕНДАЦИИ

1. **ВСЕГДА** тестируй на devnet перед mainnet
2. **НИКОГДА** не игнорируй валидацию bin arrays
3. **ОБЯЗАТЕЛЬНО** используй правильные discriminators
4. **ВСЕГДА** проверяй достаточность ликвидности
5. **НИКОГДА** не забывай про bin array bitmap extension
6. **ОБЯЗАТЕЛЬНО** мониторь изменения active bin
7. **ВСЕГДА** используй batch RPC запросы для оптимизации
8. **НИКОГДА** не полагайся только на SDK - понимай низкий уровень

---
**ПОМНИ**: Meteora DLMM - это революционная технология концентрированной ликвидности. Понимание её на уровне основателя даёт огромное преимущество в DeFi арбитраже! 🚀
```
```
```
