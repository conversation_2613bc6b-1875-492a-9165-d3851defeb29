const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

/**
 * 🔍 ПРОВЕРЯЕМ AUTHORITY ALT ТАБЛИЦЫ
 */

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const connection = new Connection(RPC_URL, 'confirmed');

const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');

// ИСПОЛЬЗУЕМ ТОТ ЖЕ КОШЕЛЁК ЧТО В add-meteora-to-alt.js
const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
const { Keypair } = require('@solana/web3.js');
const wallet = Keypair.fromSecretKey(new Uint8Array(walletData)); // wallet.json - это массив
const currentWallet = wallet.publicKey;

async function checkALTAuthority() {
    try {
        console.log('🔍 ПРОВЕРКА AUTHORITY ALT ТАБЛИЦЫ...');
        console.log(`📋 ALT: ${customALTAddress.toString()}`);
        console.log(`💰 Текущий кошелек: ${currentWallet.toString()}`);
        
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount || !altAccount.value) {
            console.log('❌ ALT таблица не найдена!');
            return;
        }
        
        const authority = altAccount.value.state.authority;
        console.log(`🔑 Authority ALT таблицы: ${authority ? authority.toString() : 'НЕТ'}`);
        
        if (!authority) {
            console.log('❌ ALT таблица заморожена (нет authority)!');
            return;
        }
        
        const isAuthority = authority.toString() === currentWallet.toString();
        
        if (isAuthority) {
            console.log('✅ ТЕКУЩИЙ КОШЕЛЕК ЯВЛЯЕТСЯ AUTHORITY!');
            console.log('💡 Можно добавлять адреса в ALT таблицу');
        } else {
            console.log('❌ ТЕКУЩИЙ КОШЕЛЕК НЕ ЯВЛЯЕТСЯ AUTHORITY!');
            console.log(`🔑 Нужен кошелек: ${authority.toString()}`);
            console.log(`💰 Текущий кошелек: ${currentWallet.toString()}`);
        }
        
        // ПРОВЕРЯЕМ ЕСТЬ ЛИ НЕДОСТАЮЩИЙ КЛЮЧ
        const missingKey = '********************************************';
        const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
        const keyExists = addresses.includes(missingKey);

        console.log('\n🔍 ПРОВЕРКА НЕДОСТАЮЩЕГО КЛЮЧА:');
        console.log(`🔑 Ключ: ${missingKey}`);
        console.log(`${keyExists ? '✅ КЛЮЧ УЖЕ ЕСТЬ В ALT!' : '❌ КЛЮЧ НЕ НАЙДЕН В ALT!'}`);

        if (keyExists) {
            const index = addresses.indexOf(missingKey);
            console.log(`📍 Индекс в таблице: ${index}`);
        }

        // Дополнительная информация
        console.log('\n📊 ИНФОРМАЦИЯ О ALT ТАБЛИЦЕ:');
        console.log(`   📍 Адресов в таблице: ${altAccount.value.state.addresses.length}`);
        console.log(`   🔒 Deactivation slot: ${altAccount.value.state.deactivationSlot}`);
        console.log(`   ⏰ Last extended slot: ${altAccount.value.state.lastExtendedSlot}`);
        
    } catch (error) {
        console.error('❌ ОШИБКА:', error.message);
    }
}

checkALTAuthority();
