#!/bin/bash

# 🔥 SOLANA BUG BOUNTY HUNTER - LAUNCH SCRIPT
# Автоматический запуск полной системы охоты за багами

echo "🔥 SOLANA BUG BOUNTY HUNTER - LAUNCH SEQUENCE"
echo "=============================================="
echo ""

# Проверяем, что среда настроена
if [ ! -f "Cargo.toml" ]; then
    echo "❌ Project not found. Please run setup_environment.sh first"
    exit 1
fi

# Проверяем конфигурацию
if [ ! -f "config.toml" ]; then
    echo "📝 Creating default configuration..."
    cargo run -- --mode scan --help > /dev/null 2>&1
    echo "✅ Default config created. Please edit config.toml with your settings."
    echo ""
    echo "🔧 Required configuration:"
    echo "  - Email settings for bug report submission"
    echo "  - API keys for bug bounty platforms"
    echo "  - Target program preferences"
    echo ""
    echo "After configuration, run this script again to start hunting."
    exit 0
fi

echo "🎯 STARTING BUG BOUNTY HUNTING OPERATION"
echo "Target: $25M+ in potential rewards"
echo "Expected duration: 72 hours"
echo "Test cases: 185,000,000"
echo ""

# Проверяем доступность Solana RPC
echo "🔗 Checking Solana RPC connectivity..."
if ! solana cluster-version > /dev/null 2>&1; then
    echo "⚠️  Solana RPC not accessible. Switching to devnet..."
    solana config set --url https://api.devnet.solana.com
fi

echo "✅ Solana RPC connected: $(solana config get | grep 'RPC URL')"
echo ""

# Создаем директории для результатов
mkdir -p output
mkdir -p logs

# Запускаем полную охоту
echo "🚀 LAUNCHING FULL HUNTING MODE"
echo "This will run all phases:"
echo "  Phase 1: Vulnerability Scanning"
echo "  Phase 2: Massive Fuzzing (185M tests)"
echo "  Phase 3: Exploit Generation"
echo "  Phase 4: Bug Report Submission"
echo ""

read -p "Are you ready to start hunting? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Hunting cancelled"
    exit 0
fi

echo ""
echo "🎯 HUNTING STARTED!"
echo "Monitor progress in real-time..."
echo ""

# Запускаем с логированием
RUST_LOG=info cargo run --release -- --mode hunt 2>&1 | tee logs/hunting_$(date +%Y%m%d_%H%M%S).log

# Проверяем результаты
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 HUNTING COMPLETED SUCCESSFULLY!"
    echo ""
    echo "📊 Check results:"
    echo "  - Database: bug_hunter.db"
    echo "  - Logs: logs/"
    echo "  - Reports: output/"
    echo ""
    
    # Показываем краткую статистику
    if [ -f "bug_hunter.db" ]; then
        echo "📈 Quick Statistics:"
        sqlite3 bug_hunter.db "SELECT 
            'Vulnerabilities Found: ' || vulnerabilities_found,
            'Critical Bugs: ' || critical_bugs,
            'Potential Rewards: $' || potential_rewards,
            'Reports Submitted: ' || reports_submitted
        FROM statistics WHERE id = 1;" 2>/dev/null || echo "Database statistics not available"
    fi
    
    echo ""
    echo "💰 Next steps:"
    echo "  1. Review found vulnerabilities in the database"
    echo "  2. Monitor bug bounty program responses"
    echo "  3. Track reward payments"
    echo "  4. Run periodic scans for new programs"
    echo ""
    echo "🎯 Happy hunting! May the bugs be with you! 💰"
    
else
    echo ""
    echo "❌ HUNTING FAILED"
    echo "Check logs for details: logs/"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  1. Check internet connectivity"
    echo "  2. Verify Solana RPC access"
    echo "  3. Review configuration in config.toml"
    echo "  4. Check system resources (RAM, disk space)"
    echo ""
    exit 1
fi
