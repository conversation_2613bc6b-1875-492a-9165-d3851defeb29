
/**
 * 🔒 КОМПЛЕКСНАЯ СИСТЕМА ВАЛИДАЦИИ ВХОДНЫХ ДАННЫХ
 */

class InputValidator {
  constructor() {
    this.rules = {
      // Числовые значения
      amount: { type: 'number', min: 0, max: 100000, required: true },
      slippage: { type: 'number', min: 0, max: 100, required: true },
      timeout: { type: 'integer', min: 100, max: 60000, required: false },
      profit: { type: 'number', min: 0, max: 100, required: false },

      // Строковые значения
      privateKey: { type: 'string', minLength: 64, maxLength: 66, pattern: /^[0-9a-fA-F]+$/, required: true },
      address: { type: 'string', minLength: 42, maxLength: 44, pattern: /^0x[0-9a-fA-F]{40}$/, required: true },
      symbol: { type: 'string', minLength: 1, maxLength: 20, pattern: /^[A-Z0-9]+$/, required: true },

      // Булевы значения
      activeMode: { type: 'boolean', required: false },
      testMode: { type: 'boolean', required: false },
      readOnlyMode: { type: 'boolean', required: false }
    };
  }

  validate(fieldName, value, customRules = {}) {
    const rules = { ...this.rules[fieldName], ...customRules };

    if (!rules) {
      throw new Error(`No validation rules for field: ${fieldName}`);
    }

    // Проверка обязательности
    if (rules.required && (value === undefined || value === null)) {
      throw new Error(`Field ${fieldName} is required`);
    }

    if (value === undefined || value === null) {
      return true; // Необязательное поле
    }

    // Проверка типа
    if (!this.validateType(value, rules.type)) {
      throw new Error(`Field ${fieldName} must be of type ${rules.type}`);
    }

    // Проверка диапазона для чисел
    if (rules.type === 'number' || rules.type === 'integer') {
      if (rules.min !== undefined && value < rules.min) {
        throw new Error(`Field ${fieldName} must be >= ${rules.min}`);
      }
      if (rules.max !== undefined && value > rules.max) {
        throw new Error(`Field ${fieldName} must be <= ${rules.max}`);
      }
    }

    // Проверка длины для строк
    if (rules.type === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        throw new Error(`Field ${fieldName} must be at least ${rules.minLength} characters`);
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        throw new Error(`Field ${fieldName} must be at most ${rules.maxLength} characters`);
      }
      if (rules.pattern && !rules.pattern.test(value)) {
        throw new Error(`Field ${fieldName} format is invalid`);
      }
    }

    return true;
  }

  validateType(value, expectedType) {
    switch (expectedType) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number' && !isNaN(value);
      case 'integer': return Number.isInteger(value);
      case 'boolean': return typeof value === 'boolean';
      case 'array': return Array.isArray(value);
      case 'object': return typeof value === 'object' && value !== null && !Array.isArray(value);
      default: return false;
    }
  }

  validateObject(obj, schema) {
    const errors = [];

    for (const [fieldName, rules] of Object.entries(schema)) {
      try {
        this.validate(fieldName, obj[fieldName], rules);
      } catch (error) {
        errors.push(error.message);
      }
    }

    if (errors.length > 0) {
      throw new Error(`Validation errors: ${errors.join(', ')}`);
    }

    return true;
  }
}

/**
 * 🔒 БЕЗОПАСНЫЕ ПАРСЕРЫ
 */
class SafeParsers {
  static parseFloat(value, defaultValue = 0, min = -Infinity, max = Infinity) {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }

    const parsed = parseFloat(value);
    if (isNaN(parsed)) {
      return defaultValue;
    }

    return Math.max(min, Math.min(max, parsed));
  }

  static parseInt(value, defaultValue = 0, min = -Infinity, max = Infinity) {
    if (value === undefined || value === null || value === '') {
      return defaultValue;
    }

    const parsed = parseInt(value, 10);
    if (isNaN(parsed)) {
      return defaultValue;
    }

    return Math.max(min, Math.min(max, parsed));
  }

  static parseBoolean(value, defaultValue = false) {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    if (typeof value === 'boolean') {
      return value;
    }

    if (typeof value === 'string') {
      const lower = value.toLowerCase();
      return lower === 'true' || lower === '1' || lower === 'yes';
    }

    return Boolean(value);
  }

  static sanitizeString(value, maxLength = 1000) {
    if (typeof value !== 'string') {
      return '';
    }

    // Удаляем опасные символы и паттерны
    const sanitized = value
      .replace(/[<>"'&]/g, '') // HTML/JS injection
      .replace(/[\x00-\x1F\x7F]/g, '') // Control characters
      .replace(/javascript:/gi, '') // JavaScript protocol
      .replace(/data:/gi, '') // Data protocol
      .replace(/vbscript:/gi, '') // VBScript protocol
      .replace(/on\w+\s*=/gi, '') // Event handlers (onclick, onerror, etc.)
      .replace(/expression\s*\(/gi, '') // CSS expression
      .trim();

    return sanitized.substring(0, maxLength);
  }
}

/**
 * 🔒 ВАЛИДАЦИЯ КОНФИГУРАЦИИ
 */
function validateConfiguration(config) {
  const validator = new InputValidator();

  const configSchema = {
    minProfitETH: { type: 'number', min: 0, max: 10, required: true },
    slippagePercentage: { type: 'number', min: 0, max: 100, required: true },
    maxInputETH: { type: 'number', min: 0, max: 1000, required: true },
    reserveETH: { type: 'number', min: 0, max: 100, required: true },
    activeMode: { type: 'boolean', required: false },
    testMode: { type: 'boolean', required: false }
  };

  try {
    validator.validateObject(config, configSchema);
    return true;
  } catch (error) {
    throw new Error(`Configuration validation failed: ${error.message}`);
  }
}

module.exports = { InputValidator, SafeParsers, validateConfiguration };
