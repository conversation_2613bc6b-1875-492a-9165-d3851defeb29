/**
 * 🔥 ЗАПУСК 3 ОТДЕЛЬНЫХ ТРАНЗАКЦИЙ ДЛЯ 3 ПУЛОВ
 * ИСПОЛЬЗУЕТ ПРОСТОЙ simple-flash-loan.js ПО ОДНОМУ ПУЛУ
 */

const { Connection, Keypair } = require('@solana/web3.js');
const fs = require('fs');
const SimpleFlashLoan = require('./simple-flash-loan');

async function run3Pools() {
    try {
        console.log('🚀 ЗАПУСК 3 ОТДЕЛЬНЫХ ТРАНЗАКЦИЙ ДЛЯ 3 ПУЛОВ...\n');

        // Настройка
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        const marginfiAccountAddress = '********************************************';

        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);
        console.log(`🏦 MarginFi: ${marginfiAccountAddress}\n`);

        const poolAddresses = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',  // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'   // Pool 3
        ];

        const results = [];

        // СОЗДАЕМ 3 ОТДЕЛЬНЫЕ ТРАНЗАКЦИИ
        for (let i = 0; i < 3; i++) {
            console.log(`${'='.repeat(50)}`);
            console.log(`🔥 ТРАНЗАКЦИЯ ${i + 1} - POOL ${i + 1}`);
            console.log(`📍 Адрес: ${poolAddresses[i]}`);
            console.log(`${'='.repeat(50)}`);

            try {
                // Создаем отдельный SimpleFlashLoan для каждого пула
                const flashLoan = new SimpleFlashLoan(wallet, marginfiAccountAddress, connection);
                
                // МОДИФИЦИРУЕМ НА ЛЕТУ - оставляем только один пул
                flashLoan.POOL_ADDRESSES = [poolAddresses[i]];
                console.log(`🎯 Настроен только для Pool ${i + 1}`);

                // Выполняем Flash Loan
                const signature = await flashLoan.executeFlashLoan();
                
                // Получаем созданный position
                const positionData = flashLoan.getCreatedPositionAddresses()[0];
                
                results.push({
                    poolIndex: i + 1,
                    poolAddress: poolAddresses[i],
                    positionAddress: positionData.positionAddress,
                    signature: signature,
                    timestamp: new Date().toISOString(),
                    status: 'success'
                });

                console.log(`✅ POOL ${i + 1} УСПЕШНО СОЗДАН!`);
                console.log(`🔑 Position: ${positionData.positionAddress}`);
                console.log(`📝 Signature: ${signature}\n`);

                // Пауза между транзакциями
                if (i < 2) {
                    console.log('⏳ Пауза 5 секунд перед следующей транзакцией...\n');
                    await new Promise(resolve => setTimeout(resolve, 5000));
                }

            } catch (error) {
                console.error(`❌ ОШИБКА POOL ${i + 1}:`, error.message);
                
                results.push({
                    poolIndex: i + 1,
                    poolAddress: poolAddresses[i],
                    error: error.message,
                    timestamp: new Date().toISOString(),
                    status: 'failed'
                });

                console.log(`⚠️ Продолжаем со следующим пулом...\n`);
                continue;
            }
        }

        // Сохраняем результаты
        const finalResults = {
            created: new Date().toISOString(),
            wallet: wallet.publicKey.toString(),
            marginfiAccount: marginfiAccountAddress,
            totalPools: 3,
            results: results
        };

        fs.writeFileSync('3-pools-results.json', JSON.stringify(finalResults, null, 2));

        console.log('\n🎉 ВСЕ 3 ТРАНЗАКЦИИ ЗАВЕРШЕНЫ!');
        console.log('💾 Результаты сохранены в 3-pools-results.json');
        
        const successCount = results.filter(r => r.status === 'success').length;
        console.log(`📊 Успешно: ${successCount}/3 пулов`);

        if (successCount > 0) {
            console.log('\n📋 Созданные position аккаунты:');
            results.filter(r => r.status === 'success').forEach(r => {
                console.log(`   Pool ${r.poolIndex}: ${r.positionAddress}`);
            });

            // Создаем конфиг для основного кода
            const configForMainCode = {
                positionAddresses: results.filter(r => r.status === 'success').map(r => r.positionAddress),
                poolAddresses: results.filter(r => r.status === 'success').map(r => r.poolAddress),
                lastUpdated: new Date().toISOString(),
                totalCreated: successCount
            };

            fs.writeFileSync('position-config.json', JSON.stringify(configForMainCode, null, 2));
            console.log('💾 Конфигурация для основного кода: position-config.json');
        }

        if (successCount === 3) {
            console.log('\n🎉 ВСЕ POSITION АККАУНТЫ СОЗДАНЫ УСПЕШНО!');
            console.log('💡 Теперь можно использовать основной Flash Loan код');
        } else {
            console.log(`\n⚠️ Создано только ${successCount}/3 position аккаунтов`);
            console.log('💡 Можно повторить для неудачных пулов');
        }

    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error);
        process.exit(1);
    }
}

// Запуск
if (require.main === module) {
    console.log('🔥 СОЗДАНИЕ POSITION АККАУНТОВ - 3 ОТДЕЛЬНЫЕ ТРАНЗАКЦИИ');
    console.log('⚠️ Каждый пул создается отдельной Flash Loan транзакцией\n');
    
    run3Pools().catch(console.error);
}

module.exports = { run3Pools };
