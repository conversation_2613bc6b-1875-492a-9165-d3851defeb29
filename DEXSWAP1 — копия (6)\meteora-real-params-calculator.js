/**
 * 🎯 METEORA DLMM КАЛЬКУЛЯТОР С РЕАЛЬНЫМИ ПАРАМЕТРАМИ
 * 
 * Использует реальные параметры из действующих пулов Meteora
 * Protocol Fee: 0.005053345%
 */

class MeteoraRealParamsCalculator {
    constructor() {
        // РЕАЛЬНЫЕ параметры из действующих пулов
        this.REAL_PARAMS = {
            // Протокольная комиссия (РЕАЛЬНАЯ!)
            protocol_fee: 0.00005053345, // 0.005053345%
            
            // Типичные комиссии в реальных пулах
            typical_base_fee: 0.0004, // 0.04% (4 basis points)
            typical_dynamic_fee: 0.004, // 0.4% при средней волатильности
            
            // Максимальное количество бинов на позицию
            max_bins_per_position: 69
        };
        
        console.log('🎯 MeteoraRealParamsCalculator инициализирован');
        console.log(`📊 Protocol Fee: ${(this.REAL_PARAMS.protocol_fee * 100).toFixed(9)}%`);
        console.log(`📊 Типичная базовая комиссия: ${(this.REAL_PARAMS.typical_base_fee * 100).toFixed(2)}%`);
        console.log(`📊 Типичная динамическая комиссия: ${(this.REAL_PARAMS.typical_dynamic_fee * 100).toFixed(2)}%`);
    }

    /**
     * 💰 РАСЧЕТ КОМИССИЙ С РЕАЛЬНЫМИ ПАРАМЕТРАМИ
     */
    calculateRealFees(swapAmount, baseFeeRate = null, dynamicFeeRate = null) {
        // Используем реальные или переданные параметры
        const baseFee = baseFeeRate || this.REAL_PARAMS.typical_base_fee;
        const dynamicFee = dynamicFeeRate || this.REAL_PARAMS.typical_dynamic_fee;
        const totalFeeRate = baseFee + dynamicFee;
        
        // Общая комиссия
        const totalFee = swapAmount * totalFeeRate;
        
        // Протокольная комиссия (РЕАЛЬНАЯ!)
        const protocolFee = swapAmount * this.REAL_PARAMS.protocol_fee;
        
        // Комиссия для LP
        const lpFee = totalFee - protocolFee;
        
        console.log(`💰 Расчет реальных комиссий:`);
        console.log(`   Сумма свопа: $${swapAmount.toLocaleString()}`);
        console.log(`   Базовая комиссия: ${(baseFee * 100).toFixed(2)}%`);
        console.log(`   Динамическая комиссия: ${(dynamicFee * 100).toFixed(2)}%`);
        console.log(`   Общая комиссия: ${(totalFeeRate * 100).toFixed(2)}% = $${totalFee.toFixed(2)}`);
        console.log(`   Протокольная комиссия: ${(this.REAL_PARAMS.protocol_fee * 100).toFixed(9)}% = $${protocolFee.toFixed(2)}`);
        console.log(`   Комиссия для LP: $${lpFee.toFixed(2)}`);
        
        return {
            totalFee,
            protocolFee,
            lpFee,
            totalFeeRate,
            baseFee,
            dynamicFee
        };
    }

    /**
     * 🎯 РАСЧЕТ ДОХОДА LP В АКТИВНОМ БИНЕ
     */
    calculateLPIncomeInActiveBin(swapAmount, lpSharePercent, baseFeeRate = null, dynamicFeeRate = null) {
        const feeResult = this.calculateRealFees(swapAmount, baseFeeRate, dynamicFeeRate);
        
        // Доход LP = его доля от комиссии для LP
        const lpIncome = feeResult.lpFee * (lpSharePercent / 100);
        
        console.log(`🎯 Доход LP в активном бине:`);
        console.log(`   Доля LP в бине: ${lpSharePercent.toFixed(2)}%`);
        console.log(`   Общая комиссия для LP: $${feeResult.lpFee.toFixed(2)}`);
        console.log(`   Доход LP: $${lpIncome.toFixed(2)}`);
        
        return {
            lpIncome,
            lpSharePercent,
            ...feeResult
        };
    }

    /**
     * 🔄 РАСЧЕТ ZERO-SLIPPAGE АРБИТРАЖА
     */
    calculateZeroSlippageArbitrage(params) {
        console.log('\n🔄 РАСЧЕТ ZERO-SLIPPAGE АРБИТРАЖА С РЕАЛЬНЫМИ ПАРАМЕТРАМИ:');
        console.log('=' .repeat(70));
        
        const {
            tradingVolume,
            priceDifferencePercent,
            pool1LpShare,
            pool2LpShare,
            pool1BaseFee,
            pool1DynamicFee,
            pool2BaseFee,
            pool2DynamicFee
        } = params;
        
        // Валовая прибыль от арбитража
        const grossArbitrageProfit = tradingVolume * (priceDifferencePercent / 100);
        
        console.log(`📈 Арбитражная прибыль:`);
        console.log(`   Объем торговли: $${tradingVolume.toLocaleString()}`);
        console.log(`   Разница в цене: ${priceDifferencePercent}%`);
        console.log(`   Валовая прибыль: $${grossArbitrageProfit.toFixed(2)}`);
        
        // Доходы от комиссий в Pool 1
        console.log(`\n💰 Pool 1 (покупка):`);
        const pool1Result = this.calculateLPIncomeInActiveBin(
            tradingVolume, 
            pool1LpShare, 
            pool1BaseFee, 
            pool1DynamicFee
        );
        
        // Доходы от комиссий в Pool 2
        console.log(`\n💰 Pool 2 (продажа):`);
        const pool2Result = this.calculateLPIncomeInActiveBin(
            tradingVolume, 
            pool2LpShare, 
            pool2BaseFee, 
            pool2DynamicFee
        );
        
        // Общий доход от комиссий
        const totalFeeIncome = pool1Result.lpIncome + pool2Result.lpIncome;
        
        // Чистая прибыль
        const netProfit = grossArbitrageProfit + totalFeeIncome;
        const profitPercent = (netProfit / tradingVolume) * 100;
        
        console.log(`\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
        console.log(`   Валовая прибыль от арбитража: $${grossArbitrageProfit.toFixed(2)}`);
        console.log(`   Доход от комиссий Pool 1: $${pool1Result.lpIncome.toFixed(2)}`);
        console.log(`   Доход от комиссий Pool 2: $${pool2Result.lpIncome.toFixed(2)}`);
        console.log(`   Общий доход от комиссий: $${totalFeeIncome.toFixed(2)}`);
        console.log(`   ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(2)}`);
        console.log(`   Доходность: ${profitPercent.toFixed(4)}%`);
        
        // Анализ прибыльности
        if (netProfit > 0) {
            console.log(`   ✅ СТРАТЕГИЯ ПРИБЫЛЬНА!`);
        } else {
            console.log(`   ❌ СТРАТЕГИЯ УБЫТОЧНА!`);
        }
        
        return {
            grossArbitrageProfit,
            totalFeeIncome,
            netProfit,
            profitPercent,
            pool1Result,
            pool2Result
        };
    }

    /**
     * 📊 СРАВНЕНИЕ С ОБЫЧНЫМ АРБИТРАЖЕМ
     */
    compareWithRegularArbitrage(tradingVolume, priceDifferencePercent, pool1LpShare, pool2LpShare) {
        console.log('\n📊 СРАВНЕНИЕ С ОБЫЧНЫМ АРБИТРАЖЕМ:');
        console.log('=' .repeat(50));
        
        const grossProfit = tradingVolume * (priceDifferencePercent / 100);
        
        // Обычный арбитраж (платите комиссии, не получаете)
        const regularFees = tradingVolume * (this.REAL_PARAMS.typical_base_fee + this.REAL_PARAMS.typical_dynamic_fee) * 2;
        const regularNetProfit = grossProfit - regularFees;
        
        // Ваш арбитраж (с собственной ликвидностью)
        const yourResult = this.calculateZeroSlippageArbitrage({
            tradingVolume,
            priceDifferencePercent,
            pool1LpShare,
            pool2LpShare
        });
        
        const advantage = yourResult.netProfit - regularNetProfit;
        
        console.log(`\n🔸 ОБЫЧНЫЙ АРБИТРАЖ:`);
        console.log(`   Валовая прибыль: $${grossProfit.toFixed(2)}`);
        console.log(`   Комиссии: $${regularFees.toFixed(2)}`);
        console.log(`   Чистая прибыль: $${regularNetProfit.toFixed(2)}`);
        
        console.log(`\n🔹 ВАШ АРБИТРАЖ:`);
        console.log(`   Чистая прибыль: $${yourResult.netProfit.toFixed(2)}`);
        
        console.log(`\n💎 ВАШЕ ПРЕИМУЩЕСТВО: $${advantage.toFixed(2)}`);
        
        if (advantage > 0) {
            const advantagePercent = ((advantage / Math.abs(regularNetProfit)) * 100);
            console.log(`   Это на ${advantagePercent.toFixed(2)}% лучше обычного арбитража!`);
        }
        
        return {
            regularNetProfit,
            yourNetProfit: yourResult.netProfit,
            advantage
        };
    }
}

// Пример использования с реальными параметрами
if (require.main === module) {
    const calculator = new MeteoraRealParamsCalculator();
    
    // Расчет арбитража с разницей 0.05%
    const result = calculator.calculateZeroSlippageArbitrage({
        tradingVolume: 1000000, // $1M
        priceDifferencePercent: 0.05, // 0.05%
        pool1LpShare: 75, // 75% доля в Pool 1
        pool2LpShare: 67, // 67% доля в Pool 2
        // Используем типичные комиссии (можно передать кастомные)
        pool1BaseFee: 0.0004, // 0.04%
        pool1DynamicFee: 0.004, // 0.4%
        pool2BaseFee: 0.0004, // 0.04%
        pool2DynamicFee: 0.004 // 0.4%
    });
    
    // Сравнение с обычным арбитражем
    calculator.compareWithRegularArbitrage(1000000, 0.05, 75, 67);
    
    console.log('\n🎯 ЗАКЛЮЧЕНИЕ:');
    console.log('Расчеты основаны на реальных параметрах Meteora DLMM');
    console.log(`Protocol Fee: ${(calculator.REAL_PARAMS.protocol_fee * 100).toFixed(9)}%`);
}
