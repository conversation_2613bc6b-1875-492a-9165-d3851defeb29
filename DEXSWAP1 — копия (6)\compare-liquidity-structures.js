/**
 * 🔍 СРАВНЕНИЕ СТАРОЙ И НОВОЙ СТРУКТУРЫ add_liquidity_by_strategy
 */

// СТАРЫЕ ДАННЫЕ (112-113 bytes)
const oldData1 = '03dd95da6f8d76d50000000000000000e803000000000000f1eeffff00000000f0eeffff00000000060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100';

const oldData2 = '03dd95da6f8d76d50000000000000000286411420f000000f7eeffff03000000f6eefffff6eeffff06000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000100';

// НОВЫЕ ДАННЫЕ (32 bytes)
function createNewData1() {
    const buffer = Buffer.alloc(32);
    
    // Discriminator (8 bytes)
    Buffer.from([3, 221, 149, 218, 111, 141, 118, 213]).copy(buffer, 0);
    
    // Amount X (8 bytes)
    buffer.writeBigUInt64LE(BigInt(0), 8);
    
    // Amount Y (8 bytes)
    buffer.writeBigUInt64LE(BigInt(1000), 16);
    
    // Active ID (4 bytes)
    buffer.writeInt32LE(-4367, 24);
    
    // Max Slippage (4 bytes)
    buffer.writeUInt32LE(0, 28);
    
    return buffer;
}

function createNewData2() {
    const buffer = Buffer.alloc(32);
    
    // Discriminator (8 bytes)
    Buffer.from([3, 221, 149, 218, 111, 141, 118, 213]).copy(buffer, 0);
    
    // Amount X (8 bytes)
    buffer.writeBigUInt64LE(BigInt(0), 8);
    
    // Amount Y (8 bytes)
    buffer.writeBigUInt64LE(BigInt(65532945448), 16);
    
    // Active ID (4 bytes)
    buffer.writeInt32LE(-4361, 24);
    
    // Max Slippage (4 bytes)
    buffer.writeUInt32LE(3, 28);
    
    return buffer;
}

console.log('🔍 СРАВНЕНИЕ СТАРОЙ И НОВОЙ СТРУКТУРЫ add_liquidity_by_strategy');
console.log('=' .repeat(80));

// Анализируем старые данные
console.log('📋 АНАЛИЗ СТАРЫХ ДАННЫХ:');
console.log('');

function analyzeOldData(hexData, name) {
    const buffer = Buffer.from(hexData, 'hex');
    console.log(`🔍 ${name}:`);
    console.log(`   Общий размер: ${buffer.length} bytes`);
    console.log(`   Hex: ${hexData}`);
    console.log('');
    
    // Discriminator (8 bytes)
    const discriminator = buffer.slice(0, 8);
    console.log(`   Discriminator: ${discriminator.toString('hex')} (8 bytes)`);
    
    // Amount X (8 bytes)
    const amountX = buffer.readBigUInt64LE(8);
    console.log(`   Amount X: ${amountX} (8 bytes)`);
    
    // Amount Y (8 bytes)
    const amountY = buffer.readBigUInt64LE(16);
    console.log(`   Amount Y: ${amountY} (8 bytes)`);
    
    // Active ID (4 bytes)
    const activeId = buffer.readInt32LE(24);
    console.log(`   Active ID: ${activeId} (4 bytes)`);
    
    // Max slippage (4 bytes)
    const maxSlippage = buffer.readUInt32LE(28);
    console.log(`   Max Slippage: ${maxSlippage} (4 bytes)`);
    
    // Остальные данные (80+ bytes)
    const remaining = buffer.slice(32);
    console.log(`   Остальные данные: ${remaining.length} bytes`);
    console.log(`   Остальные hex: ${remaining.toString('hex')}`);
    
    // Анализируем остальные данные по 4-байтовым блокам
    console.log(`   🔍 АНАЛИЗ ОСТАЛЬНЫХ ${remaining.length} BYTES:`);
    
    let offset = 0;
    let nonZeroFields = 0;
    let totalFields = 0;
    
    while (offset < remaining.length) {
        if (offset + 4 <= remaining.length) {
            const value = remaining.readUInt32LE(offset);
            const isNonZero = value !== 0;
            if (isNonZero) nonZeroFields++;
            totalFields++;
            
            console.log(`     Offset ${offset.toString().padStart(2)}: ${value.toString().padStart(10)} (0x${value.toString(16).padStart(8, '0')}) ${isNonZero ? '🔥 NON-ZERO' : '⚪ ZERO'}`);
            offset += 4;
        } else {
            const lastBytes = remaining.slice(offset);
            console.log(`     Offset ${offset.toString().padStart(2)}: ${lastBytes.toString('hex')} (${lastBytes.length} bytes)`);
            break;
        }
    }
    
    console.log(`   📊 Статистика: ${nonZeroFields} не-нулевых полей из ${totalFields} (${((nonZeroFields/totalFields)*100).toFixed(1)}%)`);
    console.log('');
    
    return { nonZeroFields, totalFields, remaining };
}

const analysis1 = analyzeOldData(oldData1, 'СТАРЫЕ ДАННЫЕ 1 (Pool 1)');
const analysis2 = analyzeOldData(oldData2, 'СТАРЫЕ ДАННЫЕ 2 (Pool 2)');

// Анализируем новые данные
console.log('📋 АНАЛИЗ НОВЫХ ДАННЫХ:');
console.log('');

function analyzeNewData(buffer, name) {
    console.log(`🔍 ${name}:`);
    console.log(`   Общий размер: ${buffer.length} bytes`);
    console.log(`   Hex: ${buffer.toString('hex')}`);
    console.log('');
    
    // Discriminator (8 bytes)
    const discriminator = buffer.slice(0, 8);
    console.log(`   Discriminator: ${discriminator.toString('hex')} (8 bytes)`);
    
    // Amount X (8 bytes)
    const amountX = buffer.readBigUInt64LE(8);
    console.log(`   Amount X: ${amountX} (8 bytes)`);
    
    // Amount Y (8 bytes)
    const amountY = buffer.readBigUInt64LE(16);
    console.log(`   Amount Y: ${amountY} (8 bytes)`);
    
    // Active ID (4 bytes)
    const activeId = buffer.readInt32LE(24);
    console.log(`   Active ID: ${activeId} (4 bytes)`);
    
    // Max slippage (4 bytes)
    const maxSlippage = buffer.readUInt32LE(28);
    console.log(`   Max Slippage: ${maxSlippage} (4 bytes)`);
    
    console.log('   ✅ Все поля заполнены, лишних данных нет');
    console.log('');
}

const newData1 = createNewData1();
const newData2 = createNewData2();

analyzeNewData(newData1, 'НОВЫЕ ДАННЫЕ 1 (Pool 1)');
analyzeNewData(newData2, 'НОВЫЕ ДАННЫЕ 2 (Pool 2)');

// Сравнение ключевых полей
console.log('🔍 СРАВНЕНИЕ КЛЮЧЕВЫХ ПОЛЕЙ:');
console.log('=' .repeat(80));

function compareKeyFields(oldHex, newBuffer, name) {
    const oldBuffer = Buffer.from(oldHex, 'hex');
    
    console.log(`📊 ${name}:`);
    
    // Discriminator
    const oldDiscriminator = oldBuffer.slice(0, 8).toString('hex');
    const newDiscriminator = newBuffer.slice(0, 8).toString('hex');
    console.log(`   Discriminator: ${oldDiscriminator === newDiscriminator ? '✅ СОВПАДАЕТ' : '❌ ОТЛИЧАЕТСЯ'}`);
    console.log(`     Старый: ${oldDiscriminator}`);
    console.log(`     Новый:  ${newDiscriminator}`);
    
    // Amount X
    const oldAmountX = oldBuffer.readBigUInt64LE(8);
    const newAmountX = newBuffer.readBigUInt64LE(8);
    console.log(`   Amount X: ${oldAmountX === newAmountX ? '✅ СОВПАДАЕТ' : '❌ ОТЛИЧАЕТСЯ'}`);
    console.log(`     Старый: ${oldAmountX}`);
    console.log(`     Новый:  ${newAmountX}`);
    
    // Amount Y
    const oldAmountY = oldBuffer.readBigUInt64LE(16);
    const newAmountY = newBuffer.readBigUInt64LE(16);
    console.log(`   Amount Y: ${oldAmountY === newAmountY ? '✅ СОВПАДАЕТ' : '❌ ОТЛИЧАЕТСЯ'}`);
    console.log(`     Старый: ${oldAmountY}`);
    console.log(`     Новый:  ${newAmountY}`);
    
    // Active ID
    const oldActiveId = oldBuffer.readInt32LE(24);
    const newActiveId = newBuffer.readInt32LE(24);
    console.log(`   Active ID: ${oldActiveId === newActiveId ? '✅ СОВПАДАЕТ' : '❌ ОТЛИЧАЕТСЯ'}`);
    console.log(`     Старый: ${oldActiveId}`);
    console.log(`     Новый:  ${newActiveId}`);
    
    // Max Slippage
    const oldMaxSlippage = oldBuffer.readUInt32LE(28);
    const newMaxSlippage = newBuffer.readUInt32LE(28);
    console.log(`   Max Slippage: ${oldMaxSlippage === newMaxSlippage ? '✅ СОВПАДАЕТ' : '❌ ОТЛИЧАЕТСЯ'}`);
    console.log(`     Старый: ${oldMaxSlippage}`);
    console.log(`     Новый:  ${newMaxSlippage}`);
    
    console.log('');
}

compareKeyFields(oldData1, newData1, 'POOL 1');
compareKeyFields(oldData2, newData2, 'POOL 2');

// Анализ удаленных данных
console.log('🔍 АНАЛИЗ УДАЛЕННЫХ ДАННЫХ:');
console.log('=' .repeat(80));

function analyzeRemovedData(oldHex, name) {
    const oldBuffer = Buffer.from(oldHex, 'hex');
    const removedData = oldBuffer.slice(32); // Все после первых 32 bytes
    
    console.log(`🗑️ ${name} - УДАЛЕННЫЕ ДАННЫЕ (${removedData.length} bytes):`);
    console.log(`   Hex: ${removedData.toString('hex')}`);
    
    // Проверяем, есть ли важные данные
    let hasImportantData = false;
    let nonZeroBytes = 0;
    
    for (let i = 0; i < removedData.length; i++) {
        if (removedData[i] !== 0) {
            nonZeroBytes++;
            if (removedData[i] !== 0xff) { // 0xff часто используется как padding
                hasImportantData = true;
            }
        }
    }
    
    console.log(`   📊 Не-нулевых байт: ${nonZeroBytes} из ${removedData.length} (${((nonZeroBytes/removedData.length)*100).toFixed(1)}%)`);
    console.log(`   🔍 Возможно важные данные: ${hasImportantData ? '⚠️ ДА' : '✅ НЕТ'}`);
    
    // Ищем паттерны
    const patterns = [];
    if (removedData.includes(Buffer.from([0xff, 0xff, 0xff, 0xff]))) {
        patterns.push('0xffffffff (возможно padding)');
    }
    if (removedData.includes(Buffer.from([0x06, 0x00, 0x00, 0x00]))) {
        patterns.push('0x00000006 (возможно bin count)');
    }
    
    if (patterns.length > 0) {
        console.log(`   🔍 Найденные паттерны: ${patterns.join(', ')}`);
    }
    
    console.log('');
    
    return { hasImportantData, nonZeroBytes, totalBytes: removedData.length };
}

const removed1 = analyzeRemovedData(oldData1, 'POOL 1');
const removed2 = analyzeRemovedData(oldData2, 'POOL 2');

// Финальные выводы
console.log('🎯 ФИНАЛЬНЫЕ ВЫВОДЫ:');
console.log('=' .repeat(80));

console.log('✅ КЛЮЧЕВЫЕ ПОЛЯ СОХРАНЕНЫ:');
console.log('   - Discriminator (add_liquidity_by_strategy2)');
console.log('   - Amount X (количество токена X)');
console.log('   - Amount Y (количество токена Y)');
console.log('   - Active ID (активный bin ID)');
console.log('   - Max Slippage (максимальный slippage)');
console.log('');

console.log('🗑️ УДАЛЕННЫЕ ДАННЫЕ:');
console.log(`   - Pool 1: ${removed1.totalBytes} bytes (${removed1.nonZeroBytes} не-нулевых)`);
console.log(`   - Pool 2: ${removed2.totalBytes} bytes (${removed2.nonZeroBytes} не-нулевых)`);
console.log(`   - Общая экономия: ${removed1.totalBytes + removed2.totalBytes} bytes`);
console.log('');

if (removed1.hasImportantData || removed2.hasImportantData) {
    console.log('⚠️ ВНИМАНИЕ: Возможно удалены важные данные!');
    console.log('   Рекомендуется дополнительная проверка.');
} else {
    console.log('✅ БЕЗОПАСНО: Удалены только padding и лишние поля.');
    console.log('   Оптимизация корректна.');
}

console.log('');
console.log('💡 РЕКОМЕНДАЦИЯ:');
if (removed1.hasImportantData || removed2.hasImportantData) {
    console.log('   Протестировать транзакцию перед использованием.');
} else {
    console.log('   Оптимизация безопасна, можно использовать.');
}
