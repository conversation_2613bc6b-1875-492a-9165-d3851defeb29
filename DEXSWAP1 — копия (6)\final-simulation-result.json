{"timestamp": "2025-07-21T10:50:02.355Z", "success": false, "error": "{\"InstructionError\":[3,{\"Custom\":102}]}", "unitsConsumed": 96083, "logs": ["Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA invoke [1]", "Program log: Instruction: LendingAccountStartFlashloan", "Program log: Setting account flag 10", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA consumed 6494 of 1400000 compute units", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA success", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA invoke [1]", "Program log: Instruction: LendingAccountBorrow", "Program data: aHW7nG+aaroAORQvaC/YOISW7L1RBvFceUwkd0M4KPpmQtvr9yADSmEbrR36PLL4Xdf2pmepSE5oFsfHd+k0nVM8c0nc32/fisb6evO+2606PWXzaqvJdDGxu+TC0vbg5HymAgNFL11hJQAAAAAAAACmpf7L//QBQQAAAAAAAAAA", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]", "Program log: Instruction: Transfer", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4645 of 1359020 compute units", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success", "Program data: 32BRCpxjGjsBCNy0G6I68cyptMARg58z0ljolm8v0Rg31R1fRunQT2AnCWp3m0x82+wO+o+StnaYnN1ut4n+Ls1KVKE3YyfWVwjctBuiOvHMqbTAEYOfM9JY6JZvL9EYN9UdX0bp0E9gORQvaC/YOISW7L1RBvFceUwkd0M4KPpmQtvr9yADSmEbrR36PLL4Xdf2pmepSE5oFsfHd+k0nVM8c0nc32/fisb6evO+2606PWXzaqvJdDGxu+TC0vbg5HymAgNFL11hAKicE0YCAAA=", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA consumed 41945 of 1393506 compute units", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA success", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA invoke [1]", "Program log: Instruction: LendingAccountBorrow", "Program data: aHW7nG+aaroAORQvaC/YOISW7L1RBvFceUwkd0M4KPpmQtvr9yADSmGmV00l5I38MACvGayUU9HJ6EaCLxlpMXwmdddPYu+m8gabiFf+q4GE+2h/Y0YYwDXaxDncGus7VZig8AAAAAABBgAAAAAAAAB3ejrar1HpQAAAAAAAAAAA", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]", "Program log: Instruction: Transfer", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4736 of 1316990 compute units", "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success", "Program data: 32BRCpxjGjsBCNy0G6I68cyptMARg58z0ljolm8v0Rg31R1fRunQT2AnCWp3m0x82+wO+o+StnaYnN1ut4n+Ls1KVKE3YyfWVwjctBuiOvHMqbTAEYOfM9JY6JZvL9EYN9UdX0bp0E9gORQvaC/YOISW7L1RBvFceUwkd0M4KPpmQtvr9yADSmGmV00l5I38MACvGayUU9HJ6EaCLxlpMXwmdddPYu+m8gabiFf+q4GE+2h/Y0YYwDXaxDncGus7VZig8AAAAAABAIAopUYHAAA=", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA consumed 42521 of 1351561 compute units", "Program MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA success", "Program LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo invoke [1]", "Program log: Instruction: AddLiquidityByStrategy2", "Program log: AnchorError occurred. Error Code: InstructionDidNotDeserialize. Error Number: 102. Error Message: The program could not deserialize the given instruction.", "Program LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo consumed 5123 of 1309040 compute units", "Program LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo failed: custom program error: 0x66"], "transactionSize": 1000, "instructionCount": 15, "altTables": 2, "readyToSend": false}