/**
 * 🎯 КАЛЬКУЛЯТОР ОПТИМАЛЬНОГО СООТНОШЕНИЯ ЛИКВИДНОСТИ К ПОЗИЦИИ
 * 
 * Находит идеальное соотношение для максимальной прибыли в рамках $1M лимита
 */

class OptimalRatioCalculator {
    constructor() {
        this.MAX_FLASH_LOAN = 1000000; // $1M лимит
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.POOL_FEE = 0.0025; // 0.25% средняя комиссия
        
        console.log('🎯 OptimalRatioCalculator инициализирован');
        console.log(`💰 Лимит Flash Loan: $${this.MAX_FLASH_LOAN.toLocaleString()}`);
    }

    /**
     * 🧮 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ ОТ ПОКУПКИ
     */
    calculatePriceImpact(buyAmount, poolLiquidity) {
        // Процент от ликвидности пула
        const liquidityRatio = buyAmount / poolLiquidity;
        
        // Формула повышения цены (квадратичная зависимость)
        // Чем больше % от пула, тем сильнее растет цена
        const priceIncrease = Math.pow(liquidityRatio, 0.8) * 100; // в процентах
        
        // Slippage потери при покупке
        const slippageLoss = buyAmount * Math.pow(liquidityRatio, 1.2);
        
        // Комиссия
        const fee = buyAmount * this.POOL_FEE;
        
        // Общие затраты на покупку
        const totalCost = buyAmount + slippageLoss + fee;
        
        return {
            buyAmount,
            poolLiquidity,
            liquidityRatio: (liquidityRatio * 100).toFixed(2),
            priceIncrease: priceIncrease.toFixed(4),
            slippageLoss: Math.round(slippageLoss),
            fee: Math.round(fee),
            totalCost: Math.round(totalCost)
        };
    }

    /**
     * 💰 РАСЧЕТ ПРОДАЖИ ПО ПОВЫШЕННОЙ ЦЕНЕ
     */
    calculateSale(sellAmount, priceIncrease, sellPoolLiquidity) {
        // Влияние продажи на большой пул (минимальное)
        const sellRatio = sellAmount / sellPoolLiquidity;
        const sellSlippage = sellAmount * Math.pow(sellRatio, 1.5);
        const sellFee = sellAmount * this.POOL_FEE;
        
        // Доход от повышенной цены
        const priceBonus = sellAmount * (priceIncrease / 100);
        
        // Чистый доход
        const grossRevenue = sellAmount + priceBonus;
        const netRevenue = grossRevenue - sellSlippage - sellFee;
        
        return {
            sellAmount,
            priceBonus: Math.round(priceBonus),
            sellSlippage: Math.round(sellSlippage),
            sellFee: Math.round(sellFee),
            netRevenue: Math.round(netRevenue)
        };
    }

    /**
     * 🎯 ПОЛНЫЙ РАСЧЕТ АРБИТРАЖА
     */
    calculateArbitrage(buyAmount, sellAmount, buyPoolLiquidity, sellPoolLiquidity) {
        // 1. Покупка (поднимаем цену)
        const buyResult = this.calculatePriceImpact(buyAmount, buyPoolLiquidity);
        
        // 2. Продажа (используем повышенную цену)
        const sellResult = this.calculateSale(sellAmount, parseFloat(buyResult.priceIncrease), sellPoolLiquidity);
        
        // 3. Flash Loan расчеты
        const flashLoanFee = buyAmount * this.FLASH_LOAN_FEE;
        
        // 4. Итоговая прибыль
        const totalCosts = buyResult.totalCost + flashLoanFee;
        const totalRevenue = sellResult.netRevenue;
        const netProfit = totalRevenue - totalCosts;
        const roi = (netProfit / buyAmount * 100);
        
        return {
            buyAmount,
            sellAmount,
            buyPoolLiquidity,
            sellPoolLiquidity,
            liquidityRatio: buyResult.liquidityRatio,
            priceIncrease: buyResult.priceIncrease,
            totalCosts: Math.round(totalCosts),
            totalRevenue,
            netProfit: Math.round(netProfit),
            roi: roi.toFixed(4),
            profitable: netProfit > 0,
            details: { buyResult, sellResult, flashLoanFee: Math.round(flashLoanFee) }
        };
    }

    /**
     * 🏆 ПОИСК ОПТИМАЛЬНОГО СООТНОШЕНИЯ
     */
    findOptimalRatio() {
        console.log('\n🏆 ПОИСК ОПТИМАЛЬНОГО СООТНОШЕНИЯ ЛИКВИДНОСТИ К ПОЗИЦИИ');
        console.log('=' .repeat(80));
        
        // Тестируем разные размеры пулов для покупки
        const buyPoolSizes = [
            1000000,   // $1M
            2000000,   // $2M  
            3000000,   // $3M
            5000000,   // $5M
            7000000,   // $7M
            10000000,  // $10M
            15000000,  // $15M
            20000000   // $20M
        ];
        
        // Размер пула для продажи (всегда большой для минимального slippage)
        const sellPoolLiquidity = 44000000; // $44M ORCA
        
        // Тестируем разные размеры позиций (в рамках $1M лимита)
        const positionSizes = [100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 900000, 1000000];
        
        let bestStrategy = { netProfit: -Infinity };
        const results = [];
        
        console.log('📊 АНАЛИЗ РАЗЛИЧНЫХ СООТНОШЕНИЙ:\n');
        
        buyPoolSizes.forEach(buyPoolSize => {
            console.log(`🌊 ПУЛ ДЛЯ ПОКУПКИ: $${buyPoolSize.toLocaleString()}`);
            
            positionSizes.forEach(positionSize => {
                if (positionSize > this.MAX_FLASH_LOAN) return;
                
                // Продаем 80% от купленного
                const sellAmount = Math.round(positionSize * 0.8);
                
                const result = this.calculateArbitrage(
                    positionSize, 
                    sellAmount, 
                    buyPoolSize, 
                    sellPoolLiquidity
                );
                
                results.push(result);
                
                if (result.netProfit > bestStrategy.netProfit) {
                    bestStrategy = result;
                }
                
                const status = result.profitable ? '✅' : '❌';
                const emoji = result.profitable ? '💚' : '🔴';
                
                console.log(`   ${status} Позиция $${positionSize.toLocaleString()} (${result.liquidityRatio}% от пула):`);
                console.log(`      ${emoji} Прибыль: $${result.netProfit.toLocaleString()} (ROI: ${result.roi}%)`);
                console.log(`      📈 Повышение цены: ${result.priceIncrease}%`);
            });
            console.log('');
        });
        
        return { bestStrategy, allResults: results };
    }

    /**
     * 📊 АНАЛИЗ ЛУЧШЕЙ СТРАТЕГИИ
     */
    analyzeBestStrategy(strategy) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ОПТИМАЛЬНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        const ratio = (strategy.buyAmount / strategy.buyPoolLiquidity * 100).toFixed(2);
        
        console.log(`🎯 ОПТИМАЛЬНОЕ СООТНОШЕНИЕ:`);
        console.log(`   💰 Размер позиции: $${strategy.buyAmount.toLocaleString()}`);
        console.log(`   🌊 Ликвидность пула: $${strategy.buyPoolLiquidity.toLocaleString()}`);
        console.log(`   📊 Соотношение: ${ratio}% от ликвидности пула`);
        console.log(`   📈 Повышение цены: ${strategy.priceIncrease}%`);
        
        console.log(`\n💸 ДЕТАЛИ ОПЕРАЦИИ:`);
        console.log(`   🛒 Покупка: $${strategy.buyAmount.toLocaleString()}`);
        console.log(`   💸 Продажа: $${strategy.sellAmount.toLocaleString()}`);
        console.log(`   💰 Затраты: $${strategy.totalCosts.toLocaleString()}`);
        console.log(`   💎 Доходы: $${strategy.totalRevenue.toLocaleString()}`);
        
        console.log(`\n🏆 РЕЗУЛЬТАТ:`);
        console.log(`   💚 Чистая прибыль: $${strategy.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${strategy.roi}%`);
        console.log(`   ⚡ Прибыль на $1 вложений: $${(strategy.netProfit / strategy.buyAmount).toFixed(4)}`);
        
        // Потенциал масштабирования
        const dailyPotential = strategy.netProfit * 20; // 20 сделок в день
        const monthlyPotential = dailyPotential * 30;
        
        console.log(`\n💎 ПОТЕНЦИАЛ МАСШТАБИРОВАНИЯ:`);
        console.log(`   📅 Дневной потенциал (20 сделок): $${dailyPotential.toLocaleString()}`);
        console.log(`   📆 Месячный потенциал: $${monthlyPotential.toLocaleString()}`);
        console.log(`   🚀 Годовой потенциал: $${(monthlyPotential * 12).toLocaleString()}`);
        
        // Рекомендации
        console.log(`\n🎯 РЕКОМЕНДАЦИИ:`);
        console.log(`   • Ищите пулы с ликвидностью ~$${strategy.buyPoolLiquidity.toLocaleString()}`);
        console.log(`   • Оптимальный размер позиции: $${strategy.buyAmount.toLocaleString()}`);
        console.log(`   • Целевое соотношение: ${ratio}% от ликвидности`);
        console.log(`   • Продавайте на максимально большом пуле (ORCA $44M)`);
        
        return strategy;
    }

    /**
     * 📈 ГРАФИК ЗАВИСИМОСТИ ПРИБЫЛИ ОТ СООТНОШЕНИЯ
     */
    generateProfitChart(results) {
        console.log('\n📈 ЗАВИСИМОСТЬ ПРИБЫЛИ ОТ СООТНОШЕНИЯ ПОЗИЦИИ К ЛИКВИДНОСТИ');
        console.log('=' .repeat(80));
        
        // Группируем по соотношениям
        const ratioGroups = {};
        
        results.forEach(result => {
            const ratio = parseFloat(result.liquidityRatio);
            if (!ratioGroups[ratio]) {
                ratioGroups[ratio] = [];
            }
            ratioGroups[ratio].push(result);
        });
        
        // Находим среднюю прибыль для каждого соотношения
        const chartData = Object.entries(ratioGroups).map(([ratio, results]) => {
            const avgProfit = results.reduce((sum, r) => sum + r.netProfit, 0) / results.length;
            const maxProfit = Math.max(...results.map(r => r.netProfit));
            return { ratio: parseFloat(ratio), avgProfit, maxProfit };
        }).sort((a, b) => a.ratio - b.ratio);
        
        console.log('📊 СООТНОШЕНИЕ → СРЕДНЯЯ ПРИБЫЛЬ → МАКСИМАЛЬНАЯ ПРИБЫЛЬ');
        chartData.forEach(data => {
            const bar = '█'.repeat(Math.max(1, Math.round(data.maxProfit / 10000)));
            console.log(`   ${data.ratio.toFixed(1)}% → $${Math.round(data.avgProfit).toLocaleString()} → $${Math.round(data.maxProfit).toLocaleString()} ${bar}`);
        });
        
        return chartData;
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК КАЛЬКУЛЯТОРА ОПТИМАЛЬНОГО СООТНОШЕНИЯ...\n');
    
    const calculator = new OptimalRatioCalculator();
    
    try {
        // Поиск оптимального соотношения
        const { bestStrategy, allResults } = calculator.findOptimalRatio();
        
        // Анализ лучшей стратегии
        const analysis = calculator.analyzeBestStrategy(bestStrategy);
        
        // График зависимости
        const chartData = calculator.generateProfitChart(allResults);
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ОПТИМАЛЬНОЕ СООТНОШЕНИЕ: ${(bestStrategy.buyAmount / bestStrategy.buyPoolLiquidity * 100).toFixed(2)}%`);
        console.log(`💰 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${bestStrategy.netProfit.toLocaleString()}`);
        console.log(`🎯 ОПТИМАЛЬНАЯ ПОЗИЦИЯ: $${bestStrategy.buyAmount.toLocaleString()}`);
        console.log(`🌊 НУЖНАЯ ЛИКВИДНОСТЬ ПУЛА: $${bestStrategy.buyPoolLiquidity.toLocaleString()}`);
        
        return { analysis, chartData, bestStrategy };
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { OptimalRatioCalculator };
