/**
 * 🔥 ПРОСТАЯ ОТПРАВКА lending_account_end_flashloan
 * 
 * Только снимает флаг IN_FLASHLOAN_FLAG без всяких проверок
 */

const { Connection, PublicKey, Keypair, Transaction, TransactionInstruction } = require('@solana/web3.js');
const fs = require('fs');

class SimpleEndFlashLoan {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        this.wallet = null;
        
        // 🔥 MARGINFI КОНСТАНТЫ
        this.MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.MARGINFI_ACCOUNT = new PublicKey('********************************************');

        // 🔥 БАНКИ ДЛЯ HEALTH CHECK (REMAINING ACCOUNTS!)
        this.BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            USDT: new PublicKey('HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV')
        };
        
        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ ОФИЦИАЛЬНОГО IDL!
        this.END_FLASHLOAN_DISCRIMINATOR = Buffer.from([
            105, 124, 201, 106, 153, 2, 8, 156  // ✅ ОФИЦИАЛЬНЫЙ lending_account_end_flashloan
        ]);
    }

    async initialize() {
        try {
            console.log('🔧 ИНИЦИАЛИЗАЦИЯ ПРОСТОГО END FLASHLOAN');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Загружаем wallet
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
            this.wallet = { payer: keypair, publicKey: keypair.publicKey };
            
            console.log(`💼 Wallet загружен: ${this.wallet.publicKey.toString()}`);
            console.log(`🏦 MarginFi Account: ${this.MARGINFI_ACCOUNT.toString()}`);
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка инициализации: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ ФЛАГА
     */
    async checkCurrentFlag() {
        try {
            const accountInfo = await this.connection.getAccountInfo(this.MARGINFI_ACCOUNT);
            if (!accountInfo) {
                throw new Error('MarginFi аккаунт не найден');
            }

            // Флаги находятся в начале данных аккаунта
            const flags = accountInfo.data.readUInt8(0);
            const inFlashLoan = (flags & 0x40) !== 0; // Бит 6 = ACCOUNT_IN_FLASHLOAN

            console.log(`🏁 Текущие флаги: ${flags} (0x${flags.toString(16)})`);
            console.log(`🔥 В состоянии Flash Loan: ${inFlashLoan ? '✅ ДА' : '❌ НЕТ'}`);

            return inFlashLoan;
        } catch (error) {
            console.error(`❌ Ошибка проверки флага: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 СОЗДАНИЕ ПРОСТОЙ lending_account_end_flashloan ИНСТРУКЦИИ
     */
    async createSimpleEndFlashLoanInstruction() {
        try {
            console.log('\n🔧 СОЗДАНИЕ ПРОСТОЙ lending_account_end_flashloan ИНСТРУКЦИИ...');
            
            // 🔥 ПОЛНАЯ СТРУКТУРА АККАУНТОВ + БАНКИ ДЛЯ HEALTH CHECK!
            const accounts = [
                // 0. MarginFi Account (WRITABLE!) - ПЕРВЫЙ!
                { pubkey: this.MARGINFI_ACCOUNT, isSigner: false, isWritable: true },

                // 1. Authority (Signer) - ВТОРОЙ!
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },

                // 🔥 REMAINING ACCOUNTS - БАНКИ ДЛЯ HEALTH CHECK!
                // 2. SOL Bank
                { pubkey: this.BANKS.SOL, isSigner: false, isWritable: false },

                // 3. USDC Bank
                { pubkey: this.BANKS.USDC, isSigner: false, isWritable: false },

                // 4. USDT Bank
                { pubkey: this.BANKS.USDT, isSigner: false, isWritable: false }
            ];

            // 🔥 INSTRUCTION DATA - ТОЛЬКО DISCRIMINATOR!
            const instructionData = Buffer.alloc(8);
            this.END_FLASHLOAN_DISCRIMINATOR.copy(instructionData, 0);

            const instruction = new TransactionInstruction({
                keys: accounts,
                programId: this.MARGINFI_PROGRAM_ID,
                data: instructionData
            });

            console.log('✅ Простая lending_account_end_flashloan инструкция создана');
            console.log(`📊 Аккаунтов: ${accounts.length}`);
            console.log(`📦 Data: ${instructionData.toString('hex')} (${instructionData.length} bytes)`);

            return instruction;
        } catch (error) {
            console.error(`❌ Ошибка создания инструкции: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 ОТПРАВКА ПРОСТОЙ ТРАНЗАКЦИИ
     */
    async sendSimpleEndFlashLoan() {
        try {
            console.log('\n🚀 ОТПРАВКА ПРОСТОЙ lending_account_end_flashloan ТРАНЗАКЦИИ');
            console.log('═══════════════════════════════════════════════════════════════');

            // 1. Проверяем текущее состояние
            const inFlashLoan = await this.checkCurrentFlag();
            if (!inFlashLoan) {
                console.log('✅ Флаг уже сброшен! Транзакция не требуется.');
                return true;
            }

            // 2. Создаем инструкцию
            const endFlashLoanIx = await this.createSimpleEndFlashLoanInstruction();

            // 3. Создаем транзакцию
            const transaction = new Transaction();
            transaction.add(endFlashLoanIx);

            // 4. Получаем recent blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = this.wallet.publicKey;

            // 5. Подписываем транзакцию
            transaction.sign(this.wallet.payer);

            console.log('📤 Отправляем простую транзакцию...');

            // 6. Отправляем транзакцию
            const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
                skipPreflight: false,  // 🔥 С PREFLIGHT для безопасности!
                preflightCommitment: 'processed'
            });

            console.log(`📝 Транзакция отправлена: ${signature}`);
            console.log('⏳ Ждем подтверждения...');

            // 7. Ждем подтверждения
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

            if (confirmation.value.err) {
                throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
            }

            console.log('✅ Транзакция подтверждена!');

            // 8. Проверяем результат
            await new Promise(resolve => setTimeout(resolve, 2000)); // Ждем 2 секунды
            const flagCleared = !(await this.checkCurrentFlag());

            if (flagCleared) {
                console.log('\n🎉 ФЛАГ УСПЕШНО СБРОШЕН!');
                console.log('🚀 MarginFi аккаунт готов к новым flash loan операциям!');
                return true;
            } else {
                console.log('\n⚠️ Флаг все еще установлен. Возможно нужна повторная попытка.');
                return false;
            }

        } catch (error) {
            console.error(`❌ Ошибка отправки транзакции: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    console.log('🔥 ПРОСТАЯ ОТПРАВКА lending_account_end_flashloan');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('💡 ТОЛЬКО СНИМАЕТ ФЛАГ IN_FLASHLOAN_FLAG');
    console.log('🎯 БЕЗ ВСЯКИХ ПРОВЕРОК И ОГРАНИЧЕНИЙ');
    console.log('🔧 ОСНОВАНО НА ИСХОДНОМ КОДЕ MARGINFI');
    console.log('═══════════════════════════════════════════════════════════════');

    const endFlashLoan = new SimpleEndFlashLoan();

    const initialized = await endFlashLoan.initialize();
    if (!initialized) {
        console.log('❌ Не удалось инициализировать');
        return;
    }

    const success = await endFlashLoan.sendSimpleEndFlashLoan();

    if (success) {
        console.log('\n🎉 ПРОСТАЯ ТРАНЗАКЦИЯ ЗАВЕРШЕНА!');
        console.log('✅ Флаг IN_FLASHLOAN_FLAG сброшен!');
        console.log('🚀 Можно запускать: node BMeteora.js');
    } else {
        console.log('\n❌ ПРОСТАЯ ТРАНЗАКЦИЯ НЕ УДАЛАСЬ');
        console.log('💡 Попробуйте повторить или проверьте состояние аккаунта');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimpleEndFlashLoan;
