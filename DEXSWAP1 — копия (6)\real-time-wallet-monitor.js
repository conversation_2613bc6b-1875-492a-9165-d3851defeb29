/**
 * 🔥 REAL-TIME WALLET MONITOR - МОНИТОРИНГ ТРАНЗАКЦИЙ В РЕАЛЬНОМ ВРЕМЕНИ
 * ═══════════════════════════════════════════════════════════════════════════
 * 
 * 🎯 ЦЕЛЬ: Моментальное отслеживание всех транзакций на кошельке через WebSocket
 * 📡 МЕТОД: accountSubscribe + signatureSubscribe для полного покрытия
 * ⚡ СКОРОСТЬ: Получение уведомлений за миллисекунды до появления в блокчейне
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const WebSocket = require('ws');
const colors = require('colors');

class RealTimeWalletMonitor {
    constructor() {
        console.log('🔥 REAL-TIME WALLET MONITOR ЗАПУСКАЕТСЯ...'.red.bold);
        
        // 🔗 RPC ПОДКЛЮЧЕНИЯ (ИСПОЛЬЗУЕМ НЕСКОЛЬКО ДЛЯ НАДЕЖНОСТИ)
        this.connections = {
            helius: new Connection(process.env.HELIUS_RPC_URL || 'https://mainnet.helius-rpc.com/?api-key=YOUR_KEY', {
                wsEndpoint: process.env.HELIUS_WS_URL || 'wss://mainnet.helius-rpc.com/?api-key=YOUR_KEY',
                commitment: 'confirmed'
            }),
            quicknode: new Connection(process.env.QUICKNODE_RPC_URL || 'https://your-quicknode-url.com', {
                wsEndpoint: process.env.QUICKNODE_WS_URL || 'wss://your-quicknode-ws.com',
                commitment: 'confirmed'
            }),
            solana: new Connection('https://api.mainnet-beta.solana.com', {
                wsEndpoint: 'wss://api.mainnet-beta.solana.com',
                commitment: 'confirmed'
            })
        };

        // 🎯 ЦЕЛЕВОЙ КОШЕЛЕК (ТВОЙ АДРЕС)
        this.walletAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'; // ТВОЙ АДРЕС ИЗ СКРИНШОТА
        this.walletPublicKey = new PublicKey(this.walletAddress);
        
        // 📊 СТАТИСТИКА
        this.stats = {
            totalTransactions: 0,
            successfulTransactions: 0,
            failedTransactions: 0,
            startTime: Date.now()
        };

        // 🔔 ПОДПИСКИ
        this.subscriptions = new Map();
        
        console.log(`🎯 Мониторинг кошелька: ${this.walletAddress}`.cyan);
        console.log(`📡 Подключения: ${Object.keys(this.connections).length} RPC endpoints`.green);
    }

    /**
     * 🚀 ЗАПУСК МОНИТОРИНГА
     */
    async start() {
        console.log('\n🚀 ЗАПУСК REAL-TIME МОНИТОРИНГА...'.yellow.bold);
        
        try {
            // 📡 ПОДПИСКА НА ИЗМЕНЕНИЯ АККАУНТА (БАЛАНС, ТОКЕНЫ)
            await this.subscribeToAccountChanges();
            
            // 📝 ПОДПИСКА НА ПОДПИСИ ТРАНЗАКЦИЙ
            await this.subscribeToSignatures();
            
            // 📊 ЗАПУСК СТАТИСТИКИ
            this.startStatsReporting();
            
            console.log('✅ ВСЕ ПОДПИСКИ АКТИВНЫ! Ожидаем транзакции...'.green.bold);
            
        } catch (error) {
            console.error('❌ Ошибка запуска мониторинга:', error);
        }
    }

    /**
     * 📡 ПОДПИСКА НА ИЗМЕНЕНИЯ АККАУНТА
     */
    async subscribeToAccountChanges() {
        console.log('📡 Подписываемся на изменения аккаунта...'.cyan);
        
        for (const [name, connection] of Object.entries(this.connections)) {
            try {
                const subscriptionId = connection.onAccountChange(
                    this.walletPublicKey,
                    (accountInfo, context) => {
                        this.handleAccountChange(name, accountInfo, context);
                    },
                    'confirmed'
                );
                
                this.subscriptions.set(`account_${name}`, subscriptionId);
                console.log(`✅ Подписка на аккаунт через ${name}: ID ${subscriptionId}`.green);
                
            } catch (error) {
                console.error(`❌ Ошибка подписки на аккаунт через ${name}:`, error.message);
            }
        }
    }

    /**
     * 📝 ПОДПИСКА НА ПОДПИСИ ТРАНЗАКЦИЙ
     */
    async subscribeToSignatures() {
        console.log('📝 Подписываемся на подписи транзакций...'.cyan);
        
        for (const [name, connection] of Object.entries(this.connections)) {
            try {
                const subscriptionId = connection.onSignature(
                    this.walletAddress, // Используем как signature для мониторинга
                    (result, context) => {
                        this.handleSignatureUpdate(name, result, context);
                    },
                    'confirmed'
                );
                
                this.subscriptions.set(`signature_${name}`, subscriptionId);
                console.log(`✅ Подписка на подписи через ${name}: ID ${subscriptionId}`.green);
                
            } catch (error) {
                console.error(`❌ Ошибка подписки на подписи через ${name}:`, error.message);
            }
        }
    }

    /**
     * 🔄 ОБРАБОТКА ИЗМЕНЕНИЙ АККАУНТА
     */
    handleAccountChange(rpcName, accountInfo, context) {
        const timestamp = new Date().toISOString();
        const slot = context.slot;
        
        console.log('\n🔄 ИЗМЕНЕНИЕ АККАУНТА ОБНАРУЖЕНО!'.yellow.bold);
        console.log(`📡 RPC: ${rpcName}`.cyan);
        console.log(`⏰ Время: ${timestamp}`.gray);
        console.log(`🎰 Слот: ${slot}`.blue);
        console.log(`💰 Баланс: ${accountInfo.lamports / 1e9} SOL`.green);
        console.log(`👤 Владелец: ${accountInfo.owner.toString()}`.magenta);
        console.log(`📊 Размер данных: ${accountInfo.data.length} байт`.yellow);
        
        this.stats.totalTransactions++;
        
        // 🔍 АНАЛИЗ ИЗМЕНЕНИЙ
        this.analyzeAccountChange(accountInfo, context);
    }

    /**
     * ✍️ ОБРАБОТКА ОБНОВЛЕНИЙ ПОДПИСЕЙ
     */
    handleSignatureUpdate(rpcName, result, context) {
        const timestamp = new Date().toISOString();
        
        console.log('\n✍️ ОБНОВЛЕНИЕ ПОДПИСИ!'.blue.bold);
        console.log(`📡 RPC: ${rpcName}`.cyan);
        console.log(`⏰ Время: ${timestamp}`.gray);
        console.log(`🎰 Слот: ${context.slot}`.blue);
        console.log(`✅ Результат:`, result);
        
        if (result.err) {
            console.log(`❌ Ошибка транзакции:`.red, result.err);
            this.stats.failedTransactions++;
        } else {
            console.log(`✅ Транзакция успешна!`.green);
            this.stats.successfulTransactions++;
        }
    }

    /**
     * 🔍 АНАЛИЗ ИЗМЕНЕНИЙ АККАУНТА
     */
    analyzeAccountChange(accountInfo, context) {
        try {
            // 💰 АНАЛИЗ БАЛАНСА SOL
            const solBalance = accountInfo.lamports / 1e9;
            console.log(`💎 SOL баланс: ${solBalance.toFixed(6)} SOL`.green.bold);
            
            // 🪙 АНАЛИЗ ТОКЕНОВ (если есть данные)
            if (accountInfo.data && accountInfo.data.length > 0) {
                console.log(`🪙 Данные токенов: ${accountInfo.data.length} байт`.yellow);
                
                // Здесь можно добавить парсинг токенов
                // const tokenData = this.parseTokenData(accountInfo.data);
            }
            
            // 📈 ОПРЕДЕЛЕНИЕ ТИПА ОПЕРАЦИИ
            const operationType = this.detectOperationType(accountInfo);
            console.log(`🔄 Тип операции: ${operationType}`.magenta.bold);
            
        } catch (error) {
            console.error('❌ Ошибка анализа изменений:', error.message);
        }
    }

    /**
     * 🔍 ОПРЕДЕЛЕНИЕ ТИПА ОПЕРАЦИИ
     */
    detectOperationType(accountInfo) {
        // Простая логика определения типа операции
        if (accountInfo.lamports === 0) {
            return 'ВЫВОД СРЕДСТВ';
        } else if (accountInfo.lamports > 0) {
            return 'ПОПОЛНЕНИЕ';
        } else {
            return 'ИЗМЕНЕНИЕ ТОКЕНОВ';
        }
    }

    /**
     * 📊 ОТЧЕТ СТАТИСТИКИ
     */
    startStatsReporting() {
        setInterval(() => {
            const uptime = Math.floor((Date.now() - this.stats.startTime) / 1000);
            const minutes = Math.floor(uptime / 60);
            const seconds = uptime % 60;
            
            console.log('\n📊 СТАТИСТИКА МОНИТОРИНГА'.blue.bold);
            console.log(`⏰ Время работы: ${minutes}м ${seconds}с`.gray);
            console.log(`📝 Всего транзакций: ${this.stats.totalTransactions}`.cyan);
            console.log(`✅ Успешных: ${this.stats.successfulTransactions}`.green);
            console.log(`❌ Неудачных: ${this.stats.failedTransactions}`.red);
            console.log(`🔔 Активных подписок: ${this.subscriptions.size}`.yellow);
            
        }, 30000); // Каждые 30 секунд
    }

    /**
     * 🛑 ОСТАНОВКА МОНИТОРИНГА
     */
    async stop() {
        console.log('\n🛑 ОСТАНОВКА МОНИТОРИНГА...'.red.bold);
        
        for (const [name, subscriptionId] of this.subscriptions) {
            try {
                const [type, rpcName] = name.split('_');
                const connection = this.connections[rpcName];
                
                if (type === 'account') {
                    await connection.removeAccountChangeListener(subscriptionId);
                } else if (type === 'signature') {
                    await connection.removeSignatureListener(subscriptionId);
                }
                
                console.log(`✅ Подписка ${name} отменена`.green);
                
            } catch (error) {
                console.error(`❌ Ошибка отмены подписки ${name}:`, error.message);
            }
        }
        
        this.subscriptions.clear();
        console.log('🛑 Все подписки отменены'.red);
    }
}

// 🚀 ЗАПУСК МОНИТОРИНГА
async function main() {
    const monitor = new RealTimeWalletMonitor();
    
    // 🚀 Запуск
    await monitor.start();
    
    // 🛑 Обработка выхода
    process.on('SIGINT', async () => {
        console.log('\n🛑 Получен сигнал остановки...'.red);
        await monitor.stop();
        process.exit(0);
    });
    
    // 🔄 Держим процесс активным
    console.log('\n💡 Нажмите Ctrl+C для остановки мониторинга'.yellow);
}

// Запуск только если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = RealTimeWalletMonitor;
