/**
 * 🔍 ПРОВЕРКА ИСТОРИИ MARGINFI АККАУНТА
 * 
 * Анализируем когда и как использовался MarginFi аккаунт
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class MarginFiHistoryChecker {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        this.MARGINFI_ACCOUNT = new PublicKey('********************************************');
        this.WALLET = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
    }

    /**
     * 🔍 АНАЛИЗ ИСТОРИИ ТРАНЗАКЦИЙ
     */
    async analyzeTransactionHistory() {
        try {
            console.log('🔍 АНАЛИЗ ИСТОРИИ MARGINFI ТРАНЗАКЦИЙ');
            console.log('═══════════════════════════════════════════════════════════════');
            console.log(`📍 Аккаунт: ${this.MARGINFI_ACCOUNT.toString()}`);
            
            // Получаем все транзакции
            const signatures = await this.connection.getSignaturesForAddress(
                this.MARGINFI_ACCOUNT,
                { limit: 50 }
            );
            
            console.log(`📊 Найдено транзакций: ${signatures.length}`);
            
            let successfulBorrows = 0;
            let failedBorrows = 0;
            let deposits = 0;
            let withdrawals = 0;
            
            for (let i = 0; i < signatures.length; i++) {
                const sig = signatures[i];
                console.log(`\n📝 Транзакция ${i + 1}:`);
                console.log(`   Signature: ${sig.signature}`);
                console.log(`   Slot: ${sig.slot}`);
                console.log(`   Time: ${new Date(sig.blockTime * 1000).toISOString()}`);
                console.log(`   Status: ${sig.err ? 'FAILED' : 'SUCCESS'}`);
                
                if (sig.err) {
                    console.log(`   ❌ Ошибка: ${JSON.stringify(sig.err)}`);
                    
                    // Анализируем тип ошибки
                    const errorStr = JSON.stringify(sig.err);
                    if (errorStr.includes('3007')) {
                        failedBorrows++;
                        console.log(`   🚨 ОШИБКА 3007 - LendingAccountBalanceNotFound`);
                    } else if (errorStr.includes('3002')) {
                        console.log(`   🚨 ОШИБКА 3002 - LendingAccountBalanceSlotEmpty`);
                    }
                } else {
                    console.log(`   ✅ УСПЕШНАЯ ТРАНЗАКЦИЯ`);
                    
                    // Пытаемся получить детали успешной транзакции
                    try {
                        const tx = await this.connection.getTransaction(sig.signature, {
                            maxSupportedTransactionVersion: 0
                        });
                        
                        if (tx && tx.transaction.message.instructions) {
                            for (const ix of tx.transaction.message.instructions) {
                                const programId = tx.transaction.message.accountKeys[ix.programIdIndex];
                                
                                if (programId.equals(this.MARGINFI_PROGRAM)) {
                                    const data = Buffer.from(ix.data, 'base64');
                                    const discriminator = data.slice(0, 8).toString('hex');
                                    
                                    // Анализируем тип операции
                                    if (discriminator === 'ab5e6b67524454c8') {
                                        deposits++;
                                        console.log(`   💰 DEPOSIT операция`);
                                    } else if (discriminator === '047e74353005d41f') {
                                        successfulBorrows++;
                                        console.log(`   🏦 BORROW операция`);
                                    } else if (discriminator === '8c3c1e8b1e6e9f2d') {
                                        console.log(`   💸 REPAY операция`);
                                    } else if (discriminator === '7c3e8b1a5c6e9f2d') {
                                        withdrawals++;
                                        console.log(`   📤 WITHDRAW операция`);
                                    } else {
                                        console.log(`   🔍 НЕИЗВЕСТНАЯ операция: ${discriminator}`);
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.log(`   ⚠️ Не удалось получить детали транзакции`);
                    }
                }
                
                // Ограничиваем вывод для читаемости
                if (i >= 20) {
                    console.log(`\n... и еще ${signatures.length - i - 1} транзакций`);
                    break;
                }
            }
            
            console.log(`\n📊 СТАТИСТИКА ОПЕРАЦИЙ:`);
            console.log(`   ✅ Успешные borrow: ${successfulBorrows}`);
            console.log(`   ❌ Провалившиеся borrow: ${failedBorrows}`);
            console.log(`   💰 Депозиты: ${deposits}`);
            console.log(`   📤 Выводы: ${withdrawals}`);
            
            // Анализ
            if (successfulBorrows > 0) {
                console.log(`\n🎯 ВАЖНО: РАНЬШЕ BORROW РАБОТАЛ!`);
                console.log(`   ✅ Найдено ${successfulBorrows} успешных borrow операций`);
                console.log(`   💡 Это означает что lending balance существовал ранее`);
                console.log(`   🤔 Возможно balance был выведен или использован`);
            } else {
                console.log(`\n🚨 BORROW НИКОГДА НЕ РАБОТАЛ В ЭТОМ АККАУНТЕ`);
                console.log(`   💡 Возможно использовался другой MarginFi аккаунт`);
            }
            
            return {
                successfulBorrows,
                failedBorrows,
                deposits,
                withdrawals,
                totalTransactions: signatures.length
            };
            
        } catch (error) {
            console.error(`❌ Ошибка анализа истории: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔍 ПОИСК ДРУГИХ MARGINFI АККАУНТОВ
     */
    async findOtherMarginFiAccounts() {
        try {
            console.log('\n🔍 ПОИСК ДРУГИХ MARGINFI АККАУНТОВ КОШЕЛЬКА');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Ищем транзакции кошелька с MarginFi программой
            const signatures = await this.connection.getSignaturesForAddress(
                this.WALLET,
                { limit: 100 }
            );
            
            const marginfiAccounts = new Set();
            
            for (const sig of signatures) {
                try {
                    const tx = await this.connection.getTransaction(sig.signature, {
                        maxSupportedTransactionVersion: 0
                    });
                    
                    if (tx && tx.transaction.message.instructions) {
                        for (const ix of tx.transaction.message.instructions) {
                            const programId = tx.transaction.message.accountKeys[ix.programIdIndex];
                            
                            if (programId.equals(this.MARGINFI_PROGRAM)) {
                                // Ищем MarginFi аккаунты в списке аккаунтов
                                for (const account of tx.transaction.message.accountKeys) {
                                    if (!account.equals(this.WALLET) && 
                                        !account.equals(this.MARGINFI_PROGRAM)) {
                                        
                                        // Проверяем если это MarginFi аккаунт
                                        const accountInfo = await this.connection.getAccountInfo(account);
                                        if (accountInfo && accountInfo.owner.equals(this.MARGINFI_PROGRAM)) {
                                            marginfiAccounts.add(account.toString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (error) {
                    // Игнорируем ошибки отдельных транзакций
                }
            }
            
            console.log(`📊 Найдено MarginFi аккаунтов: ${marginfiAccounts.size}`);
            
            for (const account of marginfiAccounts) {
                console.log(`   📍 ${account}`);
                
                if (account === this.MARGINFI_ACCOUNT.toString()) {
                    console.log(`      🎯 ЭТО ТЕКУЩИЙ АККАУНТ`);
                } else {
                    console.log(`      🔍 ДРУГОЙ АККАУНТ - возможно использовался раньше`);
                }
            }
            
            return Array.from(marginfiAccounts);
            
        } catch (error) {
            console.error(`❌ Ошибка поиска аккаунтов: ${error.message}`);
            return [];
        }
    }
}

async function main() {
    console.log('🔍 АНАЛИЗ ИСТОРИИ MARGINFI ИСПОЛЬЗОВАНИЯ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Цель: Понять почему раньше borrow работал без lending balance');
    console.log('💡 Ищем успешные операции в прошлом');
    console.log('═══════════════════════════════════════════════════════════════');

    const checker = new MarginFiHistoryChecker();
    
    const stats = await checker.analyzeTransactionHistory();
    const accounts = await checker.findOtherMarginFiAccounts();
    
    console.log('\n🎯 ВЫВОДЫ:');
    
    if (stats && stats.successfulBorrows > 0) {
        console.log('✅ Раньше borrow работал в этом аккаунте');
        console.log('💡 Lending balance существовал, но был выведен/использован');
        console.log('🔧 Решение: Создать новый lending balance через депозит');
    } else {
        console.log('❌ Borrow никогда не работал в этом аккаунте');
        
        if (accounts.length > 1) {
            console.log('🔍 Найдены другие MarginFi аккаунты');
            console.log('💡 Возможно раньше использовался другой аккаунт');
        } else {
            console.log('💡 Возможно использовались другие протоколы или методы');
        }
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MarginFiHistoryChecker;
