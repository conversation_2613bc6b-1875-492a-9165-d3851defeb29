/**
 * 🧮 КАЛЬКУЛЯТОР SLIPPAGE ДЛЯ АРБИТРАЖА
 * 
 * Показывает, как slippage влияет на прибыль в арбитраже
 */

class SlippageCalculator {
  constructor() {
    console.log('🧮 Калькулятор Slippage для арбитража');
  }

  /**
   * 📊 РАСЧЕТ SLIPPAGE ДЛЯ ОДНОЙ СДЕЛКИ
   */
  calculateSlippage(expectedPrice, actualPrice) {
    const slippage = ((expectedPrice - actualPrice) / expectedPrice) * 100;
    return slippage;
  }

  /**
   * 💰 РАСЧЕТ ВЛИЯНИЯ SLIPPAGE НА АРБИТРАЖ
   */
  calculateArbitrageWithSlippage(scenario) {
    const {
      buyExpectedPrice,
      sellExpectedPrice,
      investmentAmount,
      slippageBps // В базисных пунктах (50 = 0.5%)
    } = scenario;

    const slippagePercent = slippageBps / 100; // Конвертируем в проценты

    console.log(`\n🎯 СЦЕНАРИЙ АРБИТРАЖА:`);
    console.log(`   Инвестиция: $${investmentAmount.toLocaleString()}`);
    console.log(`   Цена покупки: $${buyExpectedPrice}`);
    console.log(`   Цена продажи: $${sellExpectedPrice}`);
    console.log(`   Slippage: ${slippagePercent}%`);

    // 🛒 ПОКУПКА
    console.log(`\n🛒 ПОКУПКА:`);
    
    // Ожидаемо
    const expectedTokens = investmentAmount / buyExpectedPrice;
    console.log(`   Ожидаемо: ${expectedTokens.toLocaleString()} токенов за $${investmentAmount.toLocaleString()}`);
    
    // С учетом slippage (цена увеличивается)
    const actualBuyPrice = buyExpectedPrice * (1 + slippagePercent / 100);
    const actualTokens = investmentAmount / actualBuyPrice;
    const buySlippageLoss = expectedTokens - actualTokens;
    
    console.log(`   Реальная цена: $${actualBuyPrice.toFixed(6)} (+${slippagePercent}% slippage)`);
    console.log(`   Реально получим: ${actualTokens.toLocaleString()} токенов`);
    console.log(`   Потеря от slippage: ${buySlippageLoss.toLocaleString()} токенов`);

    // 💸 ПРОДАЖА
    console.log(`\n💸 ПРОДАЖА:`);
    
    // Ожидаемо
    const expectedRevenue = actualTokens * sellExpectedPrice;
    console.log(`   Ожидаемо: $${expectedRevenue.toLocaleString()} за ${actualTokens.toLocaleString()} токенов`);
    
    // С учетом slippage (цена уменьшается)
    const actualSellPrice = sellExpectedPrice * (1 - slippagePercent / 100);
    const actualRevenue = actualTokens * actualSellPrice;
    const sellSlippageLoss = expectedRevenue - actualRevenue;
    
    console.log(`   Реальная цена: $${actualSellPrice.toFixed(6)} (-${slippagePercent}% slippage)`);
    console.log(`   Реально получим: $${actualRevenue.toLocaleString()}`);
    console.log(`   Потеря от slippage: $${sellSlippageLoss.toLocaleString()}`);

    // 📊 ИТОГОВЫЙ РЕЗУЛЬТАТ
    console.log(`\n📊 ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
    
    const totalSlippageLoss = investmentAmount - actualRevenue;
    const totalSlippagePercent = (totalSlippageLoss / investmentAmount) * 100;
    
    // Теоретическая прибыль без slippage
    const theoreticalProfit = (expectedTokens * sellExpectedPrice) - investmentAmount;
    const theoreticalProfitPercent = (theoreticalProfit / investmentAmount) * 100;
    
    // Реальная прибыль с slippage
    const actualProfit = actualRevenue - investmentAmount;
    const actualProfitPercent = (actualProfit / investmentAmount) * 100;
    
    console.log(`   Вложили: $${investmentAmount.toLocaleString()}`);
    console.log(`   Получили: $${actualRevenue.toLocaleString()}`);
    console.log(`   Теоретическая прибыль: $${theoreticalProfit.toLocaleString()} (${theoreticalProfitPercent.toFixed(3)}%)`);
    console.log(`   Реальная прибыль: $${actualProfit.toLocaleString()} (${actualProfitPercent.toFixed(3)}%)`);
    console.log(`   Потеря от slippage: $${totalSlippageLoss.toLocaleString()} (${totalSlippagePercent.toFixed(3)}%)`);
    
    if (actualProfit > 0) {
      console.log(`   ✅ ПРИБЫЛЬНО! Прибыль: $${actualProfit.toLocaleString()}`);
    } else {
      console.log(`   ❌ УБЫТОЧНО! Убыток: $${Math.abs(actualProfit).toLocaleString()}`);
    }

    return {
      investmentAmount,
      actualRevenue,
      theoreticalProfit,
      actualProfit,
      totalSlippageLoss,
      totalSlippagePercent,
      profitable: actualProfit > 0
    };
  }

  /**
   * 🎯 ТЕСТИРОВАНИЕ РАЗНЫХ СЦЕНАРИЕВ
   */
  runScenarios() {
    console.log('🎯 ТЕСТИРОВАНИЕ ВЛИЯНИЯ SLIPPAGE НА АРБИТРАЖ');
    console.log('═══════════════════════════════════════════════════════════');

    const scenarios = [
      {
        name: 'Малый арбитраж (0.5% спред, 0.5% slippage)',
        buyExpectedPrice: 1.000,
        sellExpectedPrice: 1.005,
        investmentAmount: 100000,
        slippageBps: 50 // 0.5%
      },
      {
        name: 'Средний арбитраж (1% спред, 0.5% slippage)',
        buyExpectedPrice: 1.000,
        sellExpectedPrice: 1.010,
        investmentAmount: 100000,
        slippageBps: 50 // 0.5%
      },
      {
        name: 'Большой арбитраж (2% спред, 0.5% slippage)',
        buyExpectedPrice: 1.000,
        sellExpectedPrice: 1.020,
        investmentAmount: 100000,
        slippageBps: 50 // 0.5%
      },
      {
        name: 'Высокий slippage (1% спред, 1% slippage)',
        buyExpectedPrice: 1.000,
        sellExpectedPrice: 1.010,
        investmentAmount: 100000,
        slippageBps: 100 // 1%
      },
      {
        name: 'Низкий slippage (1% спред, 0.1% slippage)',
        buyExpectedPrice: 1.000,
        sellExpectedPrice: 1.010,
        investmentAmount: 100000,
        slippageBps: 10 // 0.1%
      }
    ];

    const results = [];

    scenarios.forEach((scenario, index) => {
      console.log(`\n${'='.repeat(60)}`);
      console.log(`📊 СЦЕНАРИЙ ${index + 1}: ${scenario.name}`);
      console.log(`${'='.repeat(60)}`);
      
      const result = this.calculateArbitrageWithSlippage(scenario);
      results.push({
        name: scenario.name,
        ...result
      });
    });

    // Сравнительный анализ
    this.compareScenarios(results);
  }

  /**
   * 📈 СРАВНИТЕЛЬНЫЙ АНАЛИЗ СЦЕНАРИЕВ
   */
  compareScenarios(results) {
    console.log(`\n${'='.repeat(60)}`);
    console.log('📈 СРАВНИТЕЛЬНЫЙ АНАЛИЗ');
    console.log(`${'='.repeat(60)}`);

    console.log('\n📊 РЕЗУЛЬТАТЫ ПО СЦЕНАРИЯМ:');
    results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.name}:`);
      console.log(`   Прибыль: $${result.actualProfit.toLocaleString()} (${((result.actualProfit / result.investmentAmount) * 100).toFixed(3)}%)`);
      console.log(`   Потеря от slippage: $${result.totalSlippageLoss.toLocaleString()} (${result.totalSlippagePercent.toFixed(3)}%)`);
      console.log(`   Статус: ${result.profitable ? '✅ Прибыльно' : '❌ Убыточно'}`);
    });

    // Лучший и худший сценарии
    const profitableScenarios = results.filter(r => r.profitable);
    
    if (profitableScenarios.length > 0) {
      const bestScenario = profitableScenarios.reduce((best, current) => 
        current.actualProfit > best.actualProfit ? current : best
      );
      
      console.log(`\n🏆 ЛУЧШИЙ СЦЕНАРИЙ: ${bestScenario.name}`);
      console.log(`   Прибыль: $${bestScenario.actualProfit.toLocaleString()}`);
    }

    console.log(`\n💡 ВЫВОДЫ:`);
    console.log(`1. Slippage ВСЕГДА уменьшает прибыль арбитража`);
    console.log(`2. Для прибыльности спред должен быть больше двойного slippage`);
    console.log(`3. Минимальный спред для прибыли при 0.5% slippage: ~1%`);
    console.log(`4. Чем больше сумма, тем больше влияние slippage`);
  }

  /**
   * 🧮 КАЛЬКУЛЯТОР МИНИМАЛЬНОГО СПРЕДА
   */
  calculateMinimumSpread(slippageBps) {
    const slippagePercent = slippageBps / 100;
    
    // Минимальный спред = 2 × slippage (покупка + продажа)
    const minimumSpread = slippagePercent * 2;
    
    console.log(`\n🧮 МИНИМАЛЬНЫЙ СПРЕД ДЛЯ ПРИБЫЛЬНОСТИ:`);
    console.log(`   Slippage: ${slippagePercent}%`);
    console.log(`   Минимальный спред: ${minimumSpread}%`);
    console.log(`   Рекомендуемый спред: ${(minimumSpread * 1.5).toFixed(2)}% (с запасом)`);
    
    return minimumSpread;
  }
}

// 🚀 ЗАПУСК КАЛЬКУЛЯТОРА
function main() {
  const calculator = new SlippageCalculator();
  
  // Запускаем все сценарии
  calculator.runScenarios();
  
  // Рассчитываем минимальный спред для Jupiter (0.5% slippage)
  calculator.calculateMinimumSpread(50);
}

if (require.main === module) {
  main();
}

module.exports = SlippageCalculator;
