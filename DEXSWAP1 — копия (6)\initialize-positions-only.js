/**
 * 🔥 ОТДЕЛЬНАЯ ТРАНЗАКЦИЯ ДЛЯ INITIALIZE_POSITION
 * ТОЛЬКО СОЗДАНИЕ ПОЗИЦИЙ БЕЗ FLASH LOAN
 * РЕШАЕТ КОНФЛИКТ С ЗАЙМОМ
 */

const { Connection, Keypair, PublicKey, TransactionInstruction, ComputeBudgetProgram, TransactionMessage, VersionedTransaction, Transaction, SystemProgram } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const { AnchorProvider, Wallet } = require('@coral-xyz/anchor');
const fs = require('fs');
require('dotenv').config();

class InitializePositionsOnly {
    constructor() {
        // Подключение через QuickNode
        const rpcUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        this.connection = new Connection(rpcUrl, 'confirmed');
        
        // Wallet из wallet.json
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        this.wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        
        console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}...`);
        console.log(`💰 Wallet: ${this.wallet.publicKey.toString()}`);
        
        // Meteora DLMM Program
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        // Пулы для создания позиций (ТЕКУЩИЕ АКТИВНЫЕ BINS ИЗ ОСНОВНОГО КОДА)
        this.POOLS = {
            POOL_1: {
                address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                activeBinId: -4163, // 🔥 ТЕКУЩИЙ АКТИВНЫЙ BIN (из основного кода)
                binStep: 4 // 4 basis points
            },
            POOL_2: {
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                activeBinId: -1666, // 🔥 ТЕКУЩИЙ АКТИВНЫЙ BIN (из основного кода)
                binStep: 10 // 10 basis points
            }
        };
        
        console.log('🔥 Initialize Positions Only - инициализирован');
    }

    /**
     * 🔥 СОЗДАНИЕ INITIALIZE_POSITION ИНСТРУКЦИИ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async createInitializePositionInstructionSDK(positionKeypair, poolAddress, activeBinId) {
        console.log(`🔧 InitializePosition через SDK для пула ${poolAddress.slice(0, 8)}...`);
        console.log(`   Position: ${positionKeypair.publicKey.toString().slice(0, 8)}...`);
        console.log(`   Активный bin ID: ${activeBinId}`);

        try {
            // 🚀 СОЗДАЕМ ANCHOR PROVIDER
            const wallet = new Wallet(this.wallet);
            const provider = new AnchorProvider(this.connection, wallet, {});

            // 🚀 СОЗДАЕМ DLMM POOL ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
            console.log(`   🔧 Создание DLMM pool объекта...`);
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress), {
                cluster: 'mainnet-beta'
            });

            console.log(`   ✅ DLMM pool создан`);

            // 🎯 СОЗДАЕМ ИНСТРУКЦИЮ ЧЕРЕЗ SDK С МАКСИМАЛЬНЫМ ДИАПАЗОНОМ БИНОВ (69 BINS - ЛИМИТ METEORA)
            const minBinId = activeBinId - 34; // Активный бин - 34 (максимальный диапазон)
            const maxBinId = activeBinId + 34; // Активный бин + 34 (максимальный диапазон)
            const width = maxBinId - minBinId + 1; // Ширина = 69 бин (МАКСИМУМ!)

            console.log(`   🔥 МАКСИМАЛЬНЫЙ ДИАПАЗОН БИНОВ (69 BINS - ЛИМИТ METEORA):`);
            console.log(`   Min Bin ID: ${minBinId} (активный - 34)`);
            console.log(`   Max Bin ID: ${maxBinId} (активный + 34)`);
            console.log(`   Width: ${width} бинов (максимально допустимый диапазон)`);

            // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД SDK - initializePositionAndAddLiquidityByStrategy с нулевой ликвидностью
            const BN = require('bn.js');
            const transaction = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
                positionPubKey: positionKeypair.publicKey,
                totalXAmount: new BN(0), // Нулевая ликвидность
                totalYAmount: new BN(0), // Нулевая ликвидность
                strategy: {
                    maxBinId: maxBinId,     // 🔥 ВЕРХНЯЯ ГРАНИЦА ДИАПАЗОНА
                    minBinId: minBinId,     // 🔥 НИЖНЯЯ ГРАНИЦА ДИАПАЗОНА
                    strategyType: 0 // Spot strategy
                },
                user: this.wallet.publicKey,
                slippage: 1 // 1% slippage
            });

            // Извлекаем только initializePosition инструкцию
            const initPositionIx = transaction.instructions.find(ix =>
                ix.programId.toString() === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo' &&
                ix.data.length >= 8 &&
                ix.data[0] === 219 && ix.data[1] === 192 // discriminator для initializePosition
            );

            console.log(`   ✅ SDK Initialize position инструкция создана`);
            return initPositionIx;

        } catch (error) {
            console.error(`   ❌ Ошибка создания SDK инструкции: ${error.message}`);
            console.error(`   📋 Stack: ${error.stack}`);
            throw error;
        }
    }

    /**
     * 🔥 СОЗДАНИЕ INITIALIZE_POSITION ИНСТРУКЦИИ (РУЧНОЙ МЕТОД - БЭКАП)
     */
    createInitializePositionInstruction(positionKeypair, poolAddress, activeBinId) {
        console.log(`🔧 InitializePosition для пула ${poolAddress.slice(0, 8)}...`);
        console.log(`   Position: ${positionKeypair.publicKey.toString().slice(0, 8)}...`);
        console.log(`   Активный bin ID: ${activeBinId}`);

        // 🔥 ОФИЦИАЛЬНЫЙ METEORA DLMM INITIALIZE POSITION DISCRIMINATOR (ИЗ meteora-discriminators.json)!
        const initializePositionDiscriminator = [219, 192, 234, 71, 190, 191, 102, 80];

        // 🔥 ТОЧНАЯ СТРУКТУРА ДАННЫХ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionData = Buffer.alloc(16);
        Buffer.from(initializePositionDiscriminator).copy(instructionData, 0);
        
        // Параметры: lower_bin_id (i32) + width (i32) согласно IDL
        const lowerBinId = activeBinId - 34; // 🔥 НИЖНЯЯ ГРАНИЦА ДИАПАЗОНА (МАКСИМАЛЬНЫЙ)
        const width = 69; // 🔥 ШИРИНА = 69 БИН (максимально допустимый диапазон Meteora)

        // Записываем параметры в little-endian формате (i32 = 4 байта каждый)
        instructionData.writeInt32LE(lowerBinId, 8);  // lower_bin_id (i32)
        instructionData.writeInt32LE(width, 12);      // width (i32)
        
        console.log(`   Lower bin ID: ${activeBinId}`);
        console.log(`   Instruction data: ${instructionData.toString('hex')}`);

        const instruction = new TransactionInstruction({
            keys: [
                // #0 - Position (Signer + Writable) - НОВЫЙ KEYPAIR!
                { pubkey: positionKeypair.publicKey, isSigner: true, isWritable: true },
                // #1 - LB Pair (Writable) - АДРЕС ПУЛА
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                // #2 - User (Signer + Writable) - НАШ WALLET
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
                // #3 - System Program
                { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
                // #4 - Rent Sysvar
                { pubkey: new PublicKey('SysvarRent111111111111111111111111111111111'), isSigner: false, isWritable: false },
                // #5 - Event Authority
                { pubkey: new PublicKey('D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'), isSigner: false, isWritable: false },
                // #6 - Program
                { pubkey: this.METEORA_DLMM_PROGRAM, isSigner: false, isWritable: false }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });

        console.log(`   ✅ Initialize position инструкция создана (7 аккаунтов, 16 байт данных)`);
        return instruction;
    }

    /**
     * 🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ДЛЯ ОБОИХ ПУЛОВ
     */
    async createInitializePositionsTransaction() {
        console.log('\n🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ДЛЯ INITIALIZE POSITIONS...\n');
        
        const instructions = [];
        const signers = [this.wallet];
        const positionKeypairs = [];

        // Добавляем ComputeBudget инструкции
        instructions.push(
            ComputeBudgetProgram.setComputeUnitLimit({ units: 400000 }),
            ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 1000 })
        );

        // Создаем позиции для обоих пулов
        for (const [poolName, poolData] of Object.entries(this.POOLS)) {
            console.log(`📊 ${poolName}: ${poolData.address}`);
            
            // Создаем новый keypair для позиции
            const positionKeypair = new Keypair();
            positionKeypairs.push({
                pool: poolName,
                address: poolData.address,
                positionAddress: positionKeypair.publicKey.toString(),
                secretKey: Array.from(positionKeypair.secretKey), // 🔥 СОХРАНЯЕМ PRIVATE KEY!
                activeBinId: poolData.activeBinId
            });
            
            // Добавляем signer
            signers.push(positionKeypair);
            
            // 🚀 ПРОБУЕМ SDK МЕТОД
            try {
                const initInstruction = await this.createInitializePositionInstructionSDK(
                    positionKeypair,
                    poolData.address,
                    poolData.activeBinId
                );

                instructions.push(initInstruction);
                console.log(`   ✅ ${poolName} initialize_position добавлена (SDK)\n`);

            } catch (sdkError) {
                console.log(`   ⚠️ SDK ошибка: ${sdkError.message}`);
                console.log(`   🔄 Пробуем ручной метод...`);

                // 🔄 FALLBACK НА РУЧНОЙ МЕТОД
                const initInstruction = this.createInitializePositionInstruction(
                    positionKeypair,
                    poolData.address,
                    poolData.activeBinId
                );

                instructions.push(initInstruction);
                console.log(`   ✅ ${poolName} initialize_position добавлена (ручной)\n`);
            }
        }

        console.log(`📊 ИТОГО:`);
        console.log(`   Инструкций: ${instructions.length}`);
        console.log(`   Signers: ${signers.length}`);
        console.log(`   Position keypairs: ${positionKeypairs.length}`);

        // Создаем транзакцию
        const transaction = new Transaction();
        transaction.add(...instructions);
        
        // Получаем recent blockhash
        const { blockhash } = await this.connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = this.wallet.publicKey;

        // Подписываем транзакцию
        transaction.sign(...signers);

        console.log(`\n📊 ТРАНЗАКЦИЯ ГОТОВА:`);
        console.log(`   Размер: ${transaction.serialize().length} байт`);
        console.log(`   Blockhash: ${blockhash.slice(0, 8)}...`);

        return {
            transaction,
            positionKeypairs,
            signers
        };
    }

    /**
     * 🚀 ОТПРАВКА ТРАНЗАКЦИИ
     */
    async sendInitializePositionsTransaction() {
        try {
            console.log('🚀 СОЗДАНИЕ И ОТПРАВКА INITIALIZE POSITIONS ТРАНЗАКЦИИ...\n');
            
            // Создаем транзакцию
            const { transaction, positionKeypairs } = await this.createInitializePositionsTransaction();
            
            // ПРОПУСКАЕМ СИМУЛЯЦИЮ - ОТПРАВЛЯЕМ НАПРЯМУЮ!
            console.log('⚡ ПРОПУСКАЕМ СИМУЛЯЦИЮ - ОТПРАВЛЯЕМ НАПРЯМУЮ!');
            
            // Отправляем транзакцию
            console.log('\n📤 ОТПРАВКА ТРАНЗАКЦИИ...');
            const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
                skipPreflight: false,
                preflightCommitment: 'confirmed'
            });
            
            console.log(`✅ ТРАНЗАКЦИЯ ОТПРАВЛЕНА!`);
            console.log(`   Signature: ${signature}`);
            
            // Ждем подтверждения
            console.log('⏳ ОЖИДАНИЕ ПОДТВЕРЖДЕНИЯ...');
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
            
            if (confirmation.value.err) {
                console.error('❌ ОШИБКА ПОДТВЕРЖДЕНИЯ:', confirmation.value.err);
                return null;
            }
            
            console.log('🎉 ТРАНЗАКЦИЯ ПОДТВЕРЖДЕНА!');
            
            // Сохраняем адреса позиций
            const positionData = {
                timestamp: new Date().toISOString(),
                signature: signature,
                positions: positionKeypairs
            };
            
            fs.writeFileSync('position-addresses.json', JSON.stringify(positionData, null, 2));
            console.log('\n💾 Адреса позиций сохранены в position-addresses.json');
            
            // Выводим результат
            console.log('\n📋 СОЗДАННЫЕ ПОЗИЦИИ:');
            positionKeypairs.forEach((pos, i) => {
                console.log(`   ${pos.pool}: ${pos.positionAddress}`);
                console.log(`     Pool: ${pos.address}`);
                console.log(`     Active Bin ID: ${pos.activeBinId}`);
            });
            
            return {
                signature,
                positionKeypairs,
                success: true
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА СОЗДАНИЯ ПОЗИЦИЙ:', error.message);
            return null;
        }
    }
}

// 🚀 ЗАПУСК
async function runInitializePositions() {
    try {
        console.log('🔥 INITIALIZE POSITIONS ONLY - ЗАПУСК\n');
        
        const initializer = new InitializePositionsOnly();
        const result = await initializer.sendInitializePositionsTransaction();
        
        if (result && result.success) {
            console.log('\n🎉 ВСЕ ПОЗИЦИИ УСПЕШНО СОЗДАНЫ!');
            console.log(`   Signature: ${result.signature}`);
            console.log(`   Позиций создано: ${result.positionKeypairs.length}`);
        } else {
            console.log('\n❌ ОШИБКА СОЗДАНИЯ ПОЗИЦИЙ');
        }
        
    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    }
}

// Запускаем если файл вызван напрямую
if (require.main === module) {
    runInitializePositions();
}

module.exports = { InitializePositionsOnly, runInitializePositions };
