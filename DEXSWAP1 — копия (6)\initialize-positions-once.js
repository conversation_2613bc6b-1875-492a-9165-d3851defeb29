/**
 * 🔥 ОДНОРАЗОВЫЙ СКРИПТ ИНИЦИАЛИЗАЦИИ POSITION АККАУНТОВ
 * ВЫПОЛНЯЕТСЯ ОДИН РАЗ, СОЗДАЕТ POSITION ДЛЯ ВСЕХ ПУЛОВ
 * СОХРАНЯЕТ АДРЕСА В ФАЙЛ ДЛЯ ОСНОВНОГО КОДА
 */

const { Connection, Keypair, sendAndConfirmTransaction } = require('@solana/web3.js');
const fs = require('fs');
const SimpleFlashLoan = require('./simple-flash-loan');

async function initializePositionsOnce() {
    try {
        console.log('🚀 ОДНОРАЗОВАЯ ИНИЦИАЛИЗАЦИЯ POSITION АККАУНТОВ...\n');

        // 🔧 НАСТРОЙКА
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        const marginfiAccountAddress = '********************************************';

        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);
        console.log(`🏦 MarginFi: ${marginfiAccountAddress}\n`);

        // 🔧 ПРОВЕРЯЕМ СУЩЕСТВУЮЩИЕ POSITION
        const positionFile = 'position-addresses.json';
        if (fs.existsSync(positionFile)) {
            console.log('⚠️  НАЙДЕН ФАЙЛ С СУЩЕСТВУЮЩИМИ POSITION!');
            const existing = JSON.parse(fs.readFileSync(positionFile, 'utf8'));
            console.log('📋 Существующие position:');
            existing.positions.forEach((pos, i) => {
                console.log(`   Pool ${i + 1}: ${pos.address}`);
            });
            
            const readline = require('readline').createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            const answer = await new Promise(resolve => {
                readline.question('\n❓ Создать новые position? (y/N): ', resolve);
            });
            readline.close();
            
            if (answer.toLowerCase() !== 'y') {
                console.log('✅ Используем существующие position аккаунты');
                return;
            }
        }

        // 🔥 СОЗДАНИЕ FLASH LOAN
        console.log('🔥 Создание Flash Loan для инициализации position...');
        const flashLoan = new SimpleFlashLoan(wallet, marginfiAccountAddress, connection);

        // 🔧 ВЫПОЛНЕНИЕ FLASH LOAN
        console.log('📤 Отправка транзакции Flash Loan...');
        const signature = await flashLoan.executeFlashLoan();
        
        console.log(`✅ FLASH LOAN ВЫПОЛНЕН!`);
        console.log(`📝 Signature: ${signature}`);

        // 🔧 ПОЛУЧАЕМ РЕАЛЬНЫЕ СОЗДАННЫЕ POSITION АДРЕСА
        const createdPositionData = flashLoan.getCreatedPositionAddresses();

        console.log('\n📋 Созданные position аккаунты:');
        createdPositionData.forEach(pos => {
            console.log(`   Pool ${pos.poolIndex}: ${pos.positionAddress}`);
        });

        const createdPositions = createdPositionData.map(pos => ({
            poolIndex: pos.poolIndex,
            poolAddress: pos.poolAddress,
            address: pos.positionAddress,
            signature: signature,
            timestamp: new Date().toISOString()
        }));

        // 💾 СОХРАНЕНИЕ РЕЗУЛЬТАТОВ
        const resultData = {
            created: new Date().toISOString(),
            signature: signature,
            wallet: wallet.publicKey.toString(),
            marginfiAccount: marginfiAccountAddress,
            positions: createdPositions
        };

        fs.writeFileSync(positionFile, JSON.stringify(resultData, null, 2));
        
        console.log('\n🎉 ИНИЦИАЛИЗАЦИЯ ЗАВЕРШЕНА!');
        console.log(`💾 Результаты сохранены в ${positionFile}`);

        console.log('\n💡 ТЕПЕРЬ МОЖНО ИСПОЛЬЗОВАТЬ ОСНОВНОЙ КОД!');
        console.log('💡 Position аккаунты готовы для добавления ликвидности');

        // 🔧 СОЗДАНИЕ КОНФИГУРАЦИОННОГО ФАЙЛА ДЛЯ ОСНОВНОГО КОДА
        const configForMainCode = {
            positionAddresses: createdPositions.map(pos => pos.address),
            poolAddresses: createdPositions.map(pos => pos.poolAddress),
            lastUpdated: new Date().toISOString()
        };

        fs.writeFileSync('position-config.json', JSON.stringify(configForMainCode, null, 2));
        console.log('💾 Конфигурация для основного кода: position-config.json');

    } catch (error) {
        console.error('❌ ОШИБКА ИНИЦИАЛИЗАЦИИ:', error);
        
        if (error.message.includes('insufficient lamports')) {
            console.log('\n💡 РЕШЕНИЕ: Пополните кошелек SOL');
        }
        
        if (error.message.includes('simulation failed')) {
            console.log('\n💡 РЕШЕНИЕ: Проверьте правильность аккаунтов и данных');
        }
        
        process.exit(1);
    }
}

// 🚀 ЗАПУСК СКРИПТА
if (require.main === module) {
    console.log('🔥 ОДНОРАЗОВЫЙ СКРИПТ ИНИЦИАЛИЗАЦИИ POSITION АККАУНТОВ');
    console.log('⚠️  ВНИМАНИЕ: Этот скрипт выполняется ОДИН РАЗ!');
    console.log('⚠️  После успешного выполнения position аккаунты будут готовы\n');
    
    initializePositionsOnce().catch(console.error);
}

module.exports = { initializePositionsOnce };
