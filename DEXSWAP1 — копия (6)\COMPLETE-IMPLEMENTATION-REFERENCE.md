# 🎯 COMPLETE IMPLEMENTATION REFERENCE
## Полный справочник правильной реализации всех функций

---

## 🚀 1. JUPITER API - ПРАВИЛЬНАЯ РЕАЛИЗАЦИЯ

### 📊 Quote API (правильные параметры):

```javascript
const getJupiterQuote = async (inputMint, outputMint, amount) => {
  const url = `https://lite-api.jup.ag/swap/v1/quote?` +
    `inputMint=${inputMint}&` +
    `outputMint=${outputMint}&` +
    `amount=${amount}&` +
    `slippageBps=50&` +
    `restrictIntermediateTokens=true&` +
    `maxAccounts=84&` +
    `onlyDirectRoutes=false&` +
    `asLegacyTransaction=false`;

  const response = await fetch(url);
  return response.json();
};
```

### 🔧 Swap Instructions API (правильные параметры):

```javascript
const getJupiterSwapInstructions = async (quote, userPublicKey) => {
  const response = await fetch('https://lite-api.jup.ag/swap/v1/swap-instructions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      quoteResponse: quote,
      userPublicKey: userPublicKey.toString(),
      wrapAndUnwrapSol: true,
      skipUserAccountsRpcCalls: true,
      dynamicComputeUnitLimit: true,
      dynamicSlippage: true,
      prioritizationFeeLamports: {
        priorityLevelWithMaxLamports: {
          maxLamports: 1000000,
          priorityLevel: "veryHigh"
        }
      }
    })
  });

  return response.json();
};
```

### 📋 Правильная обработка ответа:

```javascript
const processJupiterResponse = (result) => {
  // ПРАВИЛЬНО - отдельные поля, НЕ массив instructions
  const {
    computeBudgetInstructions,    // Массив инструкций
    setupInstructions,           // Массив инструкций
    swapInstruction,            // Одна инструкция (объект)
    cleanupInstruction,         // Одна инструкция или null
    addressLookupTableAddresses // Массив строк ALT адресов
  } = result;

  // Построение правильной последовательности
  const allInstructions = [];

  if (computeBudgetInstructions) {
    allInstructions.push(...computeBudgetInstructions);
  }

  if (setupInstructions) {
    allInstructions.push(...setupInstructions);
  }

  if (swapInstruction) {
    allInstructions.push(swapInstruction);
  }

  if (cleanupInstruction) {
    allInstructions.push(cleanupInstruction);
  }

  return { allInstructions, addressLookupTableAddresses };
};
```

---

## 🏦 2. MARGINFI FLASH LOANS - ПРАВИЛЬНАЯ РЕАЛИЗАЦИЯ

### 📊 Инициализация MarginFi:

```javascript
const initializeMarginFi = async () => {
  const config = {
    environment: "mainnet-beta",
    cluster: "mainnet-beta",
    programId: "MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdHdC",
    confirmTransactionInitialTimeout: 20000,
    commitment: "confirmed"
  };

  const client = await MarginfiClient.fetch(config, wallet, connection);
  const accounts = await client.getMarginfiAccountsForAuthority();

  if (accounts.length === 0) {
    throw new Error("No MarginFi account found");
  }

  return { client, account: accounts[0] };
};
```

### ⚡ Flash Loan создание:

```javascript
const createFlashLoan = async (amount, tokenSymbol) => {
  const { client, account } = await initializeMarginFi();
  const bank = client.getBankByTokenSymbol(tokenSymbol);

  if (!bank) {
    throw new Error(`Bank for ${tokenSymbol} not found`);
  }

  // ✅ ПРАВИЛЬНО! ИСПОЛЬЗУЙТЕ ТОЛЬКО buildFlashLoanTx!
  // makeBorrowIx и makeRepayIx УДАЛЕНЫ НАВСЕГДА!

  // УДАЛЕНО! НЕ ВОЗВРАЩАЕМ BORROW/REPAY ИНСТРУКЦИИ!
  return {
    account,
    bank
  };
};
```

---

## ⚡ 3. ATOMIC TRANSACTIONS - ПРАВИЛЬНАЯ РЕАЛИЗАЦИЯ

### 🔧 Полная атомарная транзакция:

```javascript
const buildAtomicTransaction = async (flashLoanAmount, swapParams) => {
  // 1. Получить Jupiter инструкции
  const quote = await getJupiterQuote(
    swapParams.inputMint,
    swapParams.outputMint,
    flashLoanAmount
  );

  const jupiterResult = await getJupiterSwapInstructions(quote, wallet.publicKey);

  // 2. Получить MarginFi flash loan инструкции
  const marginfiResult = await createFlashLoan(flashLoanAmount, "USDC");

  // 3. Построить правильную последовательность
  const atomicInstructions = [
    // Compute Budget (Jupiter)
    ...jupiterResult.computeBudgetInstructions,

    // Setup ATA (Jupiter)
    ...jupiterResult.setupInstructions,

    // Flash Loan Borrow (MarginFi - 44 аккаунта)
    ...marginfiResult.borrowInstructions,

    // Swap (Jupiter - 40 аккаунтов)
    jupiterResult.swapInstruction,

    // Flash Loan Repay (MarginFi - 44 аккаунта)
    ...marginfiResult.repayInstructions,

    // Cleanup (Jupiter)
    ...(jupiterResult.cleanupInstruction ? [jupiterResult.cleanupInstruction] : [])
  ];

  // 4. Объединить ALT адреса
  const allALTAddresses = [
    ...jupiterResult.addressLookupTableAddresses,
    // MarginFi ALT адреса (если есть)
  ];

  return {
    instructions: atomicInstructions,
    addressLookupTableAddresses: allALTAddresses,
    totalAccounts: 44 + 40 + 44 // MarginFi + Jupiter + MarginFi
  };
};
```

---

## 🗜️ 4. ADDRESS LOOKUP TABLES - ПРАВИЛЬНАЯ РЕАЛИЗАЦИЯ

### 📊 Загрузка ALT:

```javascript
const loadAddressLookupTables = async (addresses) => {
  const addressLookupTableAccountInfos = await connection.getMultipleAccountsInfo(
    addresses.map(addr => new PublicKey(addr))
  );

  return addressLookupTableAccountInfos.reduce((acc, accountInfo, index) => {
    if (accountInfo) {
      const addressLookupTableAccount = new AddressLookupTableAccount({
        key: new PublicKey(addresses[index]),
        state: AddressLookupTableAccount.deserialize(accountInfo.data)
      });
      acc.push(addressLookupTableAccount);
    }
    return acc;
  }, []);
};
```

### 🔧 Создание Versioned Transaction:

```javascript
const createVersionedTransaction = async (instructions, altAddresses) => {
  const { blockhash } = await connection.getLatestBlockhash();
  const addressLookupTableAccounts = await loadAddressLookupTables(altAddresses);

  const messageV0 = new TransactionMessage({
    payerKey: wallet.publicKey,
    recentBlockhash: blockhash,
    instructions: instructions.map(ix => ({
      programId: new PublicKey(ix.programId),
      accounts: ix.accounts.map(acc => ({
        pubkey: new PublicKey(acc.pubkey),
        isSigner: acc.isSigner,
        isWritable: acc.isWritable
      })),
      data: Buffer.from(ix.data, 'base64')
    }))
  }).compileToV0Message(addressLookupTableAccounts);

  const transaction = new VersionedTransaction(messageV0);
  transaction.sign([wallet]);

  return transaction;
};
```

---

## 🚨 5. КРИТИЧЕСКИЕ ПРАВИЛА

### ✅ ОБЯЗАТЕЛЬНО:
1. **Jupiter maxAccounts: 84** для flash loans
2. **MarginFi repayAll: true** для атомарных операций
3. **ALT сжатие** для транзакций >64 аккаунтов
4. **Timeout 20 секунд** для всех операций
5. **Прямые API запросы** (не кэш) для Jupiter

### ❌ НИКОГДА НЕ ДЕЛАТЬ:
1. Не искать массив `instructions` в Jupiter ответе
2. Не кэшировать Jupiter котировки
3. Не использовать `asLegacyTransaction: true`
4. Не сжимать System Program инструкции
5. Не использовать `repayAll: false` для flash loans

---

## 📊 6. МОНИТОРИНГ И ОТЛАДКА

### 🔍 Проверка размера транзакции:

```javascript
const validateTransaction = (transaction) => {
  const size = transaction.serialize().length;
  const maxSize = 1232;

  console.log(`📊 Размер транзакции: ${size}/${maxSize} байт`);

  if (size > maxSize) {
    throw new Error(`Транзакция слишком большая: ${size} > ${maxSize}`);
  }

  if (size > 1200) {
    console.warn('⚠️ Транзакция близка к лимиту размера!');
  }

  return true;
};
```

### 📈 Анализ аккаунтов:

```javascript
const analyzeAccounts = (instructions) => {
  let totalAccounts = 0;

  instructions.forEach((ix, index) => {
    const accountCount = ix.accounts?.length || 0;
    totalAccounts += accountCount;
    console.log(`📍 Инструкция ${index}: ${accountCount} аккаунтов`);
  });

  console.log(`📊 Всего аккаунтов: ${totalAccounts}`);

  if (totalAccounts > 64) {
    console.log('🗜️ Требуется ALT сжатие');
  }

  return totalAccounts;
};
```
