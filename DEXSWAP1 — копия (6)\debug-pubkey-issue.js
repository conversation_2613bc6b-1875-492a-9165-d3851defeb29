const { <PERSON><PERSON><PERSON>, S<PERSON>SVAR_INSTRUCTIONS_PUBKEY, SYSVAR_CLOCK_PUBKEY, SYSVAR_RENT_PUBKEY } = require('@solana/web3.js');

console.log('🔍 ОТЛАДКА ПРОБЛЕМЫ С PUBLICKEY...');

// Проверяем официальные Sysvar адреса из @solana/web3.js
console.log('\n📋 ОФИЦИАЛЬНЫЕ SYSVAR АДРЕСА:');

const officialSysvars = {
    'SYSVAR_INSTRUCTIONS_PUBKEY': SYSVAR_INSTRUCTIONS_PUBKEY,
    'SYSVAR_CLOCK_PUBKEY': SY<PERSON>AR_CLOCK_PUBKEY,
    'SYSVAR_RENT_PUBKEY': SYSVAR_RENT_PUBKEY
};

Object.entries(officialSysvars).forEach(([name, pubkey]) => {
    console.log(`${name}:`);
    console.log(`  Адрес: ${pubkey.toString()}`);
    console.log(`  Длина: ${pubkey.toString().length} символов`);
    console.log(`  Валидный: ${pubkey.toString().length === 44 ? '✅ ДА' : '❌ НЕТ'}`);
});

// Проверяем наш wallet
console.log('\n🔑 ПРОВЕРКА НАШЕГО WALLET:');
const walletString = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
console.log(`Wallet строка: ${walletString}`);
console.log(`Длина строки: ${walletString.length} символов`);

try {
    const walletPubkey = new PublicKey(walletString);
    console.log(`Wallet PublicKey: ${walletPubkey.toString()}`);
    console.log(`Длина PublicKey: ${walletPubkey.toString().length} символов`);
    console.log(`Валидный: ${walletPubkey.toString().length === 44 ? '✅ ДА' : '❌ НЕТ'}`);
} catch (error) {
    console.log(`❌ Ошибка создания PublicKey: ${error.message}`);
}

// Проверяем, что происходит с нашим неправильным Instructions Sysvar
console.log('\n🚨 ПРОВЕРКА НЕПРАВИЛЬНОГО INSTRUCTIONS SYSVAR:');
const wrongInstructionsSysvar = 'Sysvar1nstructions1111111111111111111111111';
console.log(`Неправильный адрес: ${wrongInstructionsSysvar}`);
console.log(`Длина: ${wrongInstructionsSysvar.length} символов`);

try {
    const wrongPubkey = new PublicKey(wrongInstructionsSysvar);
    console.log(`PublicKey: ${wrongPubkey.toString()}`);
    console.log(`Длина PublicKey: ${wrongPubkey.toString().length} символов`);
    console.log(`Валидный: ${wrongPubkey.toString().length === 44 ? '✅ ДА' : '❌ НЕТ'}`);
} catch (error) {
    console.log(`❌ Ошибка создания PublicKey: ${error.message}`);
}

// Сравниваем правильный и неправильный
console.log('\n🔍 СРАВНЕНИЕ:');
console.log(`Правильный Instructions Sysvar: ${SYSVAR_INSTRUCTIONS_PUBKEY.toString()}`);
console.log(`Неправильный Instructions Sysvar: ${wrongInstructionsSysvar}`);
console.log(`Одинаковые: ${SYSVAR_INSTRUCTIONS_PUBKEY.toString() === wrongInstructionsSysvar ? '✅ ДА' : '❌ НЕТ'}`);

console.log('\n🎯 РЕШЕНИЕ:');
console.log('Используем ПРАВИЛЬНЫЙ адрес из @solana/web3.js:');
console.log(`SYSVAR_INSTRUCTIONS_PUBKEY: ${SYSVAR_INSTRUCTIONS_PUBKEY.toString()}`);
