#!/usr/bin/env node

/**
 * 🔥 ФИНАЛЬНАЯ ПРОВЕРКА КРИТИЧЕСКИХ SIMULATION ОШИБОК
 * 
 * Проверяет только критические проблемы:
 * 1. Реальные вызовы simulateTransaction
 * 2. skipPreflight: false
 * 3. sendTransaction без skipPreflight: true в основных файлах
 */

const fs = require('fs');

class FinalSimulationChecker {
    constructor() {
        this.criticalErrors = [];
        this.mainFiles = [
            'custom-flash-loan-system.js',
            'real-trading-executor.js', 
            'BMeteora.js',
            'stealth-meteora-bypass.js'
        ];
    }

    /**
     * 🔍 ПРОВЕРКА КРИТИЧЕСКИХ ОШИБОК
     */
    async checkCriticalErrors() {
        console.log('🔥 ФИНАЛЬНАЯ ПРОВЕРКА КРИТИЧЕСКИХ SIMULATION ОШИБОК...\n');

        for (const fileName of this.mainFiles) {
            if (fs.existsSync(fileName)) {
                console.log(`🔍 Проверяем критические ошибки в: ${fileName}`);
                await this.checkCriticalFile(fileName);
            }
        }

        this.printCriticalReport();
        return this.criticalErrors.length === 0;
    }

    /**
     * 🔍 ПРОВЕРКА КРИТИЧЕСКИХ ОШИБОК В ФАЙЛЕ
     */
    async checkCriticalFile(fileName) {
        try {
            const content = fs.readFileSync(fileName, 'utf8');
            const lines = content.split('\n');

            lines.forEach((line, index) => {
                // 1. Реальные вызовы simulateTransaction (не заглушки)
                if (line.includes('connection.simulateTransaction') || 
                    line.includes('.simulateTransaction(') && !line.includes('//')) {
                    this.criticalErrors.push({
                        file: fileName,
                        line: index + 1,
                        type: 'REAL simulateTransaction CALL',
                        content: line.trim(),
                        severity: 'CRITICAL'
                    });
                }

                // 2. skipPreflight: false
                if (line.includes('skipPreflight') && line.includes('false')) {
                    this.criticalErrors.push({
                        file: fileName,
                        line: index + 1,
                        type: 'skipPreflight: false',
                        content: line.trim(),
                        severity: 'CRITICAL'
                    });
                }

                // 3. sendTransaction без skipPreflight в основных функциях
                if (line.includes('sendTransaction') && !line.includes('//') &&
                    !line.includes('sendRawTransaction') && !line.includes('console.log') &&
                    !line.includes('async sendTransaction') && !line.includes('method === \'sendTransaction\'') &&
                    !line.includes('this.sendTransaction') && !line.includes('sendTransactionToNetwork')) {

                    // Проверяем следующие 10 строк на skipPreflight: true или sendRawTransaction
                    let hasSkipPreflight = false;
                    let hasSendRawTransaction = false;

                    for (let j = index; j < Math.min(index + 10, lines.length); j++) {
                        if (lines[j].includes('skipPreflight') && lines[j].includes('true')) {
                            hasSkipPreflight = true;
                            break;
                        }
                        if (lines[j].includes('sendRawTransaction')) {
                            hasSendRawTransaction = true;
                            break;
                        }
                    }

                    if (!hasSkipPreflight && !hasSendRawTransaction) {
                        this.criticalErrors.push({
                            file: fileName,
                            line: index + 1,
                            type: 'sendTransaction без skipPreflight: true',
                            content: line.trim(),
                            severity: 'HIGH'
                        });
                    }
                }
            });

        } catch (error) {
            console.log(`❌ Ошибка чтения файла ${fileName}: ${error.message}`);
        }
    }

    /**
     * 📊 ПЕЧАТЬ КРИТИЧЕСКОГО ОТЧЕТА
     */
    printCriticalReport() {
        console.log('\n' + '='.repeat(80));
        console.log('📊 ФИНАЛЬНЫЙ ОТЧЕТ О КРИТИЧЕСКИХ SIMULATION ОШИБКАХ');
        console.log('='.repeat(80));

        if (this.criticalErrors.length === 0) {
            console.log('✅ ОТЛИЧНО! НИКАКИХ КРИТИЧЕСКИХ SIMULATION ОШИБОК!');
            console.log('🔥 ОСНОВНЫЕ ФАЙЛЫ ПОЛНОСТЬЮ НИЗКОУРОВНЕВЫЕ!');
            console.log('🚀 БОТ ГОТОВ К ЗАПУСКУ БЕЗ SIMULATION!');
            return;
        }

        console.log(`❌ НАЙДЕНО ${this.criticalErrors.length} КРИТИЧЕСКИХ ОШИБОК:\n`);

        this.criticalErrors.forEach((error, index) => {
            const severity = error.severity === 'CRITICAL' ? '🔥' : '⚠️';
            console.log(`${severity} ${index + 1}. ${error.type}`);
            console.log(`   📁 Файл: ${error.file}:${error.line}`);
            console.log(`   📝 Код: ${error.content}`);
            console.log('');
        });

        console.log('🔧 КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ:');
        console.log('1. 🔥 Замените connection.simulateTransaction на заглушку');
        console.log('2. 🔥 Измените skipPreflight: false на skipPreflight: true');
        console.log('3. 🔥 Добавьте skipPreflight: true к sendTransaction');
        console.log('4. 🔥 Используйте sendRawTransaction для максимальной скорости');
    }

    /**
     * 🎯 ПРОВЕРКА ГОТОВНОСТИ К ЗАПУСКУ
     */
    async checkReadiness() {
        console.log('🎯 ПРОВЕРКА ГОТОВНОСТИ БОТА К ЗАПУСКУ...\n');

        const isReady = await this.checkCriticalErrors();

        if (isReady) {
            console.log('\n🎉 БОТ ГОТОВ К ЗАПУСКУ!');
            console.log('✅ Все критические simulation ошибки исправлены');
            console.log('✅ Код полностью низкоуровневый');
            console.log('✅ Никаких simulation вызовов');
            console.log('✅ skipPreflight: true везде');
            console.log('\n🚀 МОЖНО ЗАПУСКАТЬ: node BMeteora.js');
        } else {
            console.log('\n❌ БОТ НЕ ГОТОВ К ЗАПУСКУ!');
            console.log('🔧 Исправьте критические ошибки выше');
        }

        return isReady;
    }
}

// 🚀 ЗАПУСК ФИНАЛЬНОЙ ПРОВЕРКИ
async function main() {
    const checker = new FinalSimulationChecker();
    
    console.log('🔥 ФИНАЛЬНАЯ ПРОВЕРКА SIMULATION ОШИБОК');
    console.log('🎯 ЦЕЛЬ: УБЕДИТЬСЯ ЧТО БОТ ГОТОВ К ЗАПУСКУ\n');

    await checker.checkReadiness();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = FinalSimulationChecker;
