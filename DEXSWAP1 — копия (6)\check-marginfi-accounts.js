const { Connection, PublicKey } = require('@solana/web3.js');

/**
 * 🔧 ПРОВЕРКА СУЩЕСТВОВАНИЯ MARGINFI АККАУНТОВ
 * Цель: найти какой аккаунт не существует
 */

async function checkMarginFiAccounts() {
    try {
        console.log('🔧 ПРОВЕРКА СУЩЕСТВОВАНИЯ MARGINFI АККАУНТОВ...');
        
        // Подключение к Solana через публичный RPC
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        
        // 🎯 MARGINFI КОНСТАНТЫ
        const accounts = {
            'MarginFi Program': 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
            'MarginFi Group': '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8',
            'MarginFi Account': '********************************************',
            'USDC Bank': '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB',
            'User Wallet': 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
            'USDC Token Account': '********************************************'
        };
        
        console.log('\n📋 ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ АККАУНТОВ...');
        
        for (const [name, address] of Object.entries(accounts)) {
            try {
                const pubkey = new PublicKey(address);
                const accountInfo = await connection.getAccountInfo(pubkey);
                
                if (accountInfo) {
                    console.log(`   ✅ ${name}: СУЩЕСТВУЕТ`);
                    console.log(`      Адрес: ${address}`);
                    console.log(`      Owner: ${accountInfo.owner.toString()}`);
                    console.log(`      Lamports: ${accountInfo.lamports}`);
                    console.log(`      Data length: ${accountInfo.data.length}`);
                    console.log(`      Executable: ${accountInfo.executable}`);
                    
                    // Проверяем, принадлежит ли аккаунт MarginFi программе
                    if (accountInfo.owner.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                        console.log(`      🎯 ПРИНАДЛЕЖИТ MARGINFI ПРОГРАММЕ!`);
                    }
                    
                } else {
                    console.log(`   ❌ ${name}: НЕ СУЩЕСТВУЕТ`);
                    console.log(`      Адрес: ${address}`);
                }
                
                console.log('');
                
                // Небольшая задержка между запросами
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.log(`   ❌ ${name}: ОШИБКА - ${error.message}`);
                console.log(`      Адрес: ${address}`);
                console.log('');
            }
        }
        
        console.log('🔧 ПОИСК ПРАВИЛЬНЫХ MARGINFI БАНКОВ...');
        
        // Известные MarginFi банки (попробуем найти правильные)
        const knownBanks = [
            { name: 'USDC Bank (наш)', address: '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB' },
            { name: 'SOL Bank (наш)', address: 'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh' },
            // Попробуем другие возможные банки
            { name: 'USDC Bank (alt1)', address: 'BFBVrMmJJLdVKdqyFQzjQFcRXEqhTBNHXaFBtYeURAhz' },
            { name: 'SOL Bank (alt1)', address: '3Ux1jBrhCfVPJoXrmKdBZTBYdwHqvJJp8bYmFchGZBNG' },
        ];
        
        for (const bank of knownBanks) {
            try {
                const pubkey = new PublicKey(bank.address);
                const accountInfo = await connection.getAccountInfo(pubkey);
                
                if (accountInfo) {
                    console.log(`   ✅ ${bank.name}: СУЩЕСТВУЕТ`);
                    console.log(`      Owner: ${accountInfo.owner.toString()}`);
                    
                    if (accountInfo.owner.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                        console.log(`      🎯 ПРИНАДЛЕЖИТ MARGINFI ПРОГРАММЕ!`);
                    }
                } else {
                    console.log(`   ❌ ${bank.name}: НЕ СУЩЕСТВУЕТ`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.log(`   ❌ ${bank.name}: ОШИБКА - ${error.message}`);
            }
        }
        
        console.log('\n🔧 ПОИСК ПРАВИЛЬНЫХ MARGINFI GROUPS...');
        
        // Известные MarginFi группы
        const knownGroups = [
            { name: 'Group (наш)', address: '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8' },
            { name: 'Group (alt1)', address: 'JBu1AL4obBcCMqKBBxhpWCNUt136ijcuMZLFvTP7iWdB' },
            { name: 'Group (alt2)', address: '3Ux1jBrhCfVPJoXrmKdBZTBYdwHqvJJp8bYmFchGZBNG' },
        ];
        
        for (const group of knownGroups) {
            try {
                const pubkey = new PublicKey(group.address);
                const accountInfo = await connection.getAccountInfo(pubkey);
                
                if (accountInfo) {
                    console.log(`   ✅ ${group.name}: СУЩЕСТВУЕТ`);
                    console.log(`      Owner: ${accountInfo.owner.toString()}`);
                    
                    if (accountInfo.owner.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                        console.log(`      🎯 ПРИНАДЛЕЖИТ MARGINFI ПРОГРАММЕ!`);
                    }
                } else {
                    console.log(`   ❌ ${group.name}: НЕ СУЩЕСТВУЕТ`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.log(`   ❌ ${group.name}: ОШИБКА - ${error.message}`);
            }
        }
        
        console.log('\n✅ ПРОВЕРКА ЗАВЕРШЕНА!');
        
    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Запускаем проверку
checkMarginFiAccounts();
