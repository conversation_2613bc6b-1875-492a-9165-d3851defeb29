const { Connection, PublicKey } = require('@solana/web3.js');

async function findUsdcOracle() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    console.log('🔮 ПОИСК ПРАВИЛЬНОГО ORACLE ДЛЯ USDC БАНКА...\n');
    
    const usdcBank = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
    
    // Получаем bank data
    const bankInfo = await connection.getAccountInfo(usdcBank);
    if (!bankInfo) {
        console.log('❌ Банк не найден');
        return;
    }
    
    console.log('🔍 АНАЛИЗ BANK DATA ДЛЯ ПОИСКА ORACLE:');
    console.log(`   Data length: ${bankInfo.data.length}`);
    
    // Ищем все возможные PublicKey в bank data
    console.log('\n🔍 ВСЕ ВОЗМОЖНЫЕ PUBLICKEY В BANK DATA:');
    const foundKeys = [];
    
    for (let offset = 0; offset < bankInfo.data.length - 32; offset += 8) {
        try {
            const keyBytes = bankInfo.data.slice(offset, offset + 32);
            const pubkey = new PublicKey(keyBytes);
            const keyStr = pubkey.toString();
            
            // Пропускаем нулевые ключи и System Program
            if (!keyStr.startsWith('11111111111111111111111111111111')) {
                foundKeys.push({ offset, key: keyStr, pubkey });
            }
        } catch (e) {
            // Игнорируем невалидные ключи
        }
    }
    
    console.log(`   Найдено ${foundKeys.length} возможных ключей:`);
    
    // Проверяем каждый ключ
    for (const { offset, key, pubkey } of foundKeys) {
        console.log(`\n   Offset ${offset}: ${key}`);
        
        try {
            const accountInfo = await connection.getAccountInfo(pubkey);
            if (accountInfo) {
                console.log(`     ✅ Существует! Owner: ${accountInfo.owner.toString()}`);
                console.log(`     📊 Data length: ${accountInfo.data.length}`);
                
                // Проверяем известные программы
                const owner = accountInfo.owner.toString();
                if (owner === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                    console.log(`     🏦 MarginFi Program Account`);
                } else if (owner === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                    console.log(`     🪙 Token Program Account`);
                } else if (owner === 'SW1TCH7qEPTdLsDHRgPuMQjbQxKdH2aBStViMFnt64f') {
                    console.log(`     🔮 SWITCHBOARD ORACLE!`);
                } else if (owner === '5uQHSona1Moncw1NctZGXTPoWdaeTvbF19yCVvc8EqBb') {
                    console.log(`     🔮 PYTH ORACLE!`);
                } else if (owner.includes('oracle') || owner.includes('Oracle')) {
                    console.log(`     🔮 ВОЗМОЖНЫЙ ORACLE: ${owner}`);
                } else {
                    console.log(`     ❓ Неизвестная программа: ${owner}`);
                }
            } else {
                console.log(`     ❌ Не существует`);
            }
        } catch (e) {
            console.log(`     ❌ Ошибка проверки: ${e.message}`);
        }
    }
    
    // Проверяем известные oracle программы
    console.log('\n🔮 ПРОВЕРКА ИЗВЕСТНЫХ ORACLE ПРОГРАММ:');
    const oraclePrograms = [
        { name: 'Switchboard', id: 'SW1TCH7qEPTdLsDHRgPuMQjbQxKdH2aBStViMFnt64f' },
        { name: 'Pyth', id: '5uQHSona1Moncw1NctZGXTPoWdaeTvbF19yCVvc8EqBb' },
        { name: 'Chainlink', id: 'HEvSKofvBgfaexv23kMabbYqxasxU3mQ4ibBMEmJWHny' }
    ];
    
    for (const oracle of oraclePrograms) {
        const oracleAccounts = foundKeys.filter(async ({ pubkey }) => {
            try {
                const info = await connection.getAccountInfo(pubkey);
                return info && info.owner.toString() === oracle.id;
            } catch (e) {
                return false;
            }
        });
        
        if (oracleAccounts.length > 0) {
            console.log(`   ✅ ${oracle.name} oracle найден: ${oracleAccounts.length} аккаунтов`);
        }
    }
    
    console.log('\n🎉 ПОИСК ORACLE ЗАВЕРШЕН!');
}

findUsdcOracle().catch(console.error);
