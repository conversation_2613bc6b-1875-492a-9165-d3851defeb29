#!/usr/bin/env node

/**
 * 🔍 DLMM POOL FINDER
 * 
 * ПОИСК РЕАЛЬНЫХ DLMM ПУЛОВ SOL/USDC
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config();

class DLMMPoolFinder {
    constructor() {
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        this.METEORA_DLMM_PROGRAM = new PublicKey('Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        this.SOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
        
        console.log('🔍 DLMM POOL FINDER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔍 ПОИСК ВСЕХ DLMM ПУЛОВ
     */
    async findAllDLMMPools() {
        console.log('\n🔍 ПОИСК ВСЕХ DLMM ПУЛОВ...');
        
        try {
            // Получаем все аккаунты программы Meteora DLMM
            const accounts = await this.connection.getProgramAccounts(
                this.METEORA_DLMM_PROGRAM,
                {
                    filters: [
                        {
                            dataSize: 904 // Размер данных LB Pair
                        }
                    ]
                }
            );
            
            console.log(`   📊 Найдено DLMM аккаунтов: ${accounts.length}`);
            
            const pools = [];
            
            for (const account of accounts) {
                try {
                    // Парсим данные пула (упрощенно)
                    const data = account.account.data;
                    
                    // Проверяем что это LB Pair
                    if (data.length === 904) {
                        pools.push({
                            address: account.pubkey.toString(),
                            pubkey: account.pubkey,
                            lamports: account.account.lamports,
                            dataLength: data.length
                        });
                    }
                } catch (error) {
                    // Пропускаем ошибочные аккаунты
                }
            }
            
            console.log(`   ✅ Валидных DLMM пулов: ${pools.length}`);
            
            return pools;
            
        } catch (error) {
            console.error('❌ ОШИБКА ПОИСКА ПУЛОВ:', error.message);
            return [];
        }
    }

    /**
     * 🎯 ПОИСК SOL/USDC ПУЛОВ
     */
    async findSOLUSDCPools() {
        console.log('\n🎯 ПОИСК SOL/USDC DLMM ПУЛОВ...');
        
        // Известные SOL/USDC DLMM пулы из Meteora
        const knownPools = [
            'ARwi1S4DaiTG5DX7S4M4ZsrXqpMD1MrTmbu9ue2tpmEq',
            '58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2',
            'BbZjQanvSaE9me4adAitmTTaSgASVDiVAKkLTyTdEGZu',
            'FwEHs3kJEdMa2qZHv7SgzCiFXUQPEycEXksfBkwmS8gj',
            '2BnZ1tpzAzQk8BEFGnKYNYXYFYbvvyKzYiUJhToCvGWz',
            'Hs1X5YtXwZACueUtS9azZyXFDWVxAMLvm3tttubpK7ph',
            '5C1k9yV7y4CjMnKv8eGYDgWND8P89Pdfj79Trk2qmfGo',
            'DdpuaJgjB2RptGMnfnCZVmC4vkKsMV6ytRa2gggQtCWt'
        ];
        
        const validPools = [];
        
        for (const poolAddress of knownPools) {
            console.log(`   🔍 Проверка ${poolAddress}...`);
            
            try {
                const poolPubkey = new PublicKey(poolAddress);
                const poolInfo = await this.connection.getAccountInfo(poolPubkey);
                
                if (poolInfo && poolInfo.owner.toString() === this.METEORA_DLMM_PROGRAM.toString()) {
                    console.log(`      ✅ Валидный DLMM пул`);
                    console.log(`      📊 Данных: ${poolInfo.data.length} байт`);
                    console.log(`      💰 Lamports: ${poolInfo.lamports}`);
                    
                    // Дополнительная проверка - парсим базовую информацию
                    const poolData = this.parsePoolData(poolInfo.data);
                    
                    validPools.push({
                        address: poolAddress,
                        pubkey: poolPubkey,
                        lamports: poolInfo.lamports,
                        dataLength: poolInfo.data.length,
                        parsed: poolData
                    });
                } else {
                    console.log(`      ❌ Не валидный или не существует`);
                }
                
            } catch (error) {
                console.log(`      💥 Ошибка: ${error.message}`);
            }
        }
        
        console.log(`\n   📊 Найдено валидных SOL/USDC пулов: ${validPools.length}`);
        
        return validPools;
    }

    /**
     * 📊 ПАРСИНГ ДАННЫХ ПУЛА (УПРОЩЕННЫЙ)
     */
    parsePoolData(data) {
        try {
            // Упрощенный парсинг основных полей
            return {
                activeId: data.readUInt32LE(8),
                binStep: data.readUInt16LE(12),
                baseFactor: data.readUInt16LE(14)
            };
        } catch (error) {
            return null;
        }
    }

    /**
     * 🎯 ВЫБОР ЛУЧШЕГО ПУЛА
     */
    selectBestPools(pools) {
        console.log('\n🎯 ВЫБОР ЛУЧШИХ ПУЛОВ...');
        
        if (pools.length === 0) {
            console.log('   ❌ Нет доступных пулов');
            return { large: null, medium: null };
        }
        
        // Сортируем по TVL (lamports)
        const sortedPools = pools.sort((a, b) => b.lamports - a.lamports);
        
        console.log('   📊 Топ пулы по TVL:');
        sortedPools.forEach((pool, index) => {
            const tvlSOL = pool.lamports / 1e9;
            console.log(`      ${index + 1}. ${pool.address} - ${tvlSOL.toFixed(2)} SOL TVL`);
        });
        
        // Выбираем два лучших пула
        const largePools = sortedPools.filter(p => p.lamports > 50 * 1e9); // >50 SOL TVL
        const mediumPools = sortedPools.filter(p => p.lamports > 10 * 1e9 && p.lamports <= 50 * 1e9); // 10-50 SOL TVL
        
        const selectedLarge = largePools[0] || sortedPools[0];
        const selectedMedium = largePools[1] || mediumPools[0] || sortedPools[1] || sortedPools[0];
        
        console.log('\n   🎯 ВЫБРАННЫЕ ПУЛЫ:');
        console.log(`      Большой пул: ${selectedLarge?.address || 'НЕ НАЙДЕН'}`);
        console.log(`      Средний пул: ${selectedMedium?.address || 'НЕ НАЙДЕН'}`);
        
        return {
            large: selectedLarge,
            medium: selectedMedium
        };
    }

    /**
     * 🚀 ПОЛНЫЙ ПОИСК И ВЫБОР
     */
    async findAndSelectPools() {
        console.log('🚀 ПОЛНЫЙ ПОИСК И ВЫБОР DLMM ПУЛОВ');
        console.log('=' .repeat(80));

        try {
            // 1. Сначала пробуем известные пулы
            let pools = await this.findSOLUSDCPools();

            // 2. Если не найдены, ищем все DLMM пулы
            if (pools.length === 0) {
                console.log('\n🔍 ПОИСК ВСЕХ DLMM ПУЛОВ (FALLBACK)...');
                const allPools = await this.findAllDLMMPools();

                // Берем первые несколько для тестирования
                pools = allPools.slice(0, 5);
                console.log(`   📊 Используем первые ${pools.length} пулов для тестирования`);
            }

            if (pools.length === 0) {
                throw new Error('Не найдены валидные DLMM пулы');
            }
            
            // 2. Выбор лучших пулов
            const selected = this.selectBestPools(pools);
            
            if (!selected.large || !selected.medium) {
                throw new Error('Не удалось выбрать подходящие пулы');
            }
            
            console.log('\n🎉 ПУЛЫ УСПЕШНО НАЙДЕНЫ И ВЫБРАНЫ!');
            
            return {
                success: true,
                pools: selected,
                allPools: pools
            };
            
        } catch (error) {
            console.error('💥 ОШИБКА ПОИСКА ПУЛОВ:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// 🧪 ЗАПУСК
if (require.main === module) {
    async function main() {
        const finder = new DLMMPoolFinder();
        const result = await finder.findAndSelectPools();
        
        if (result.success) {
            console.log('\n🎉 ПОИСК ПУЛОВ УСПЕШЕН!');
            console.log(`🏊 Большой пул: ${result.pools.large.address}`);
            console.log(`🏊 Средний пул: ${result.pools.medium.address}`);
        } else {
            console.log('\n❌ ПОИСК ПУЛОВ ПРОВАЛЕН!');
            console.log(`❌ Ошибка: ${result.error}`);
        }
    }
    
    main().catch(console.error);
}

module.exports = DLMMPoolFinder;
