#!/usr/bin/env node

/**
 * 🔥 ФИНАЛЬНЫЙ ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР С УЧЕТОМ КОМИССИЙ LP
 * 
 * КЛЮЧЕВОЕ ПОНИМАНИЕ:
 * - МЫ ЕДИНСТВЕННЫЕ LP в активированных bins
 * - ПОЛУЧАЕМ 100% КОМИССИЙ от всей торговли
 * - АТОМАРНАЯ ТРАНЗАКЦИЯ - никто не может вмешаться
 * - ВСЕ КОМИССИИ LP ИДУТ НАМ
 */

class FinalCorrectCalculator {
    constructor() {
        // 🎯 ПРАВИЛЬНЫЕ ПАРАМЕТРЫ С СОБЛЮДЕНИЕМ ПРАВИЛА 30%
        this.STRATEGY = {
            flash_loan: 1800000,      // $1.8M USDC займ
            liquidity_add: 1400000,   // $1.4M USDC в ликвидность
            trading_amount: 420000,   // $420K USDC на торговлю (30% от ликвидности!)
        };

        // 📊 ДАННЫЕ ПУЛОВ
        this.POOLS = {
            large: {
                name: 'Большой пул (покупка SOL)',
                tvl: 7000000,             // $7M TVL
                sol_price: 171.01,        // Текущая цена SOL
                slippage: 0.001           // 0.1% slippage
            },
            medium: {
                name: 'Средний пул (наша ликвидность)',
                tvl: 3000000,             // $3M TVL
                sol_price: 171.01,        // Базовая цена SOL
                bin_step: 10,             // 10 bps (0.1%)
                dynamic_fee: 0.005        // 0.5% комиссия
            }
        };

        console.log('🔥 ФИНАЛЬНЫЙ ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР С УЧЕТОМ КОМИССИЙ LP');
    }

    /**
     * 🎯 РАСЧЕТ СДВИГА АКТИВНОГО BIN
     */
    calculateActiveBinShift() {
        console.log('\n🎯 РАСЧЕТ СДВИГА АКТИВНОГО BIN...');
        
        const currentPrice = this.POOLS.medium.sol_price;
        const binStep = this.POOLS.medium.bin_step / 10000; // 0.001
        const ourLiquidity = this.STRATEGY.liquidity_add;
        const poolTvl = this.POOLS.medium.tvl;
        
        const liquidityRatio = ourLiquidity / poolTvl;
        console.log(`   Наша ликвидность: $${ourLiquidity.toLocaleString()}`);
        console.log(`   TVL пула: $${poolTvl.toLocaleString()}`);
        console.log(`   Наша доля: ${(liquidityRatio * 100).toFixed(1)}%`);
        
        // РЕАЛЬНОЕ влияние: 46.7% ликвидности = ОГРОМНЫЙ сдвиг
        const binShift = Math.floor(liquidityRatio * 200); // 93 bins сдвиг
        const newActiveBinPrice = currentPrice * Math.pow(1 + binStep, binShift);
        const priceIncrease = newActiveBinPrice - currentPrice;
        const priceIncreasePercent = (priceIncrease / currentPrice) * 100;
        
        console.log(`   🚀 СДВИГ АКТИВНОГО BIN:`);
        console.log(`   Bins сдвиг: ${binShift} bins вправо`);
        console.log(`   Новая цена: $${currentPrice} → $${newActiveBinPrice.toFixed(2)}`);
        console.log(`   Рост цены: +$${priceIncrease.toFixed(2)} (+${priceIncreasePercent.toFixed(1)}%)`);
        
        return {
            binShift: binShift,
            oldPrice: currentPrice,
            newActivePrice: newActiveBinPrice,
            priceIncrease: priceIncrease,
            priceIncreasePercent: priceIncreasePercent
        };
    }

    /**
     * 💰 ПРАВИЛЬНЫЙ РАСЧЕТ ПРИБЫЛЬНОСТИ С КОМИССИЯМИ LP
     */
    calculateProfitabilityWithLPFees(binShiftData) {
        console.log('\n💰 ПРАВИЛЬНЫЙ РАСЧЕТ ПРИБЫЛЬНОСТИ С КОМИССИЯМИ LP...');
        
        const tradingAmount = this.STRATEGY.trading_amount;
        const buyPrice = this.POOLS.large.sol_price;
        const solToBuy = tradingAmount / buyPrice;
        
        console.log('\n📋 ПОШАГОВЫЙ РАСЧЕТ:');
        
        // ШАГ 1: Покупка SOL в большом пуле
        const buySlippage = tradingAmount * 0.001;
        const totalBuyCost = tradingAmount + buySlippage;
        
        console.log(`   1️⃣ ПОКУПКА SOL В БОЛЬШОМ ПУЛЕ:`);
        console.log(`      ${solToBuy.toFixed(2)} SOL по $${buyPrice} = $${tradingAmount.toLocaleString()}`);
        console.log(`      Slippage: $${buySlippage.toFixed(0)}`);
        console.log(`      Общая стоимость: $${totalBuyCost.toLocaleString()}`);
        
        // ШАГ 2: Добавление ликвидности → СДВИГ BIN
        console.log(`   2️⃣ ДОБАВЛЕНИЕ ЛИКВИДНОСТИ → МГНОВЕННЫЙ СДВИГ BIN:`);
        console.log(`      Добавляем $${this.STRATEGY.liquidity_add.toLocaleString()} USDC`);
        console.log(`      Активный bin сдвигается на ${binShiftData.binShift} bins`);
        console.log(`      Цена мгновенно: $${binShiftData.oldPrice} → $${binShiftData.newActivePrice.toFixed(2)}`);
        console.log(`      🔥 МЫ ЕДИНСТВЕННЫЕ LP В АКТИВИРОВАННЫХ BINS!`);
        
        // ШАГ 3: Продажа SOL по новой цене
        const avgSellPrice = (binShiftData.oldPrice + binShiftData.newActivePrice) / 2;
        const sellRevenue = solToBuy * avgSellPrice;
        
        console.log(`   3️⃣ ПРОДАЖА SOL В НАШИХ BINS:`);
        console.log(`      ${solToBuy.toFixed(2)} SOL по средней цене $${avgSellPrice.toFixed(2)}`);
        console.log(`      Выручка: $${sellRevenue.toLocaleString()}`);
        
        // ШАГ 4: КОМИССИИ LP ОТ ВСЕЙ ТОРГОВЛИ
        console.log(`   4️⃣ КОМИССИИ LP (КЛЮЧЕВАЯ ПРИБЫЛЬ!):`);
        
        // Вся торговля проходит через наши bins
        const totalTradingVolume = sellRevenue;
        
        // КОМИССИИ LP: 0.5% от всего объема торговли
        const lpFeesFromTrading = totalTradingVolume * this.POOLS.medium.dynamic_fee;
        
        // МЫ ПОЛУЧАЕМ 100% КОМИССИЙ т.к. единственные LP в активированных bins
        const ourLPFees = lpFeesFromTrading * 1.0; // 100%
        
        console.log(`      Объем торговли в наших bins: $${totalTradingVolume.toLocaleString()}`);
        console.log(`      LP комиссии (0.5%): $${lpFeesFromTrading.toFixed(0)}`);
        console.log(`      🔥 НАША ДОЛЯ: $${ourLPFees.toFixed(0)} (100% т.к. единственные LP!)`);
        
        // ШАГ 5: ДОПОЛНИТЕЛЬНЫЕ КОМИССИИ ОТ ЛИКВИДНОСТИ
        console.log(`   5️⃣ ДОПОЛНИТЕЛЬНЫЕ КОМИССИИ ОТ ЛИКВИДНОСТИ:`);
        
        // Когда мы добавляем ликвидность, мы также получаем долю от всех комиссий пула
        const poolDailyVolume = this.POOLS.medium.tvl * 0.1; // 10% от TVL в день (консервативно)
        const poolDailyFees = poolDailyVolume * this.POOLS.medium.dynamic_fee;
        const ourLiquidityShare = this.STRATEGY.liquidity_add / (this.POOLS.medium.tvl + this.STRATEGY.liquidity_add);
        const ourShareOfPoolFees = poolDailyFees * ourLiquidityShare;
        
        // Мы держим ликвидность только во время транзакции, но получаем пропорциональную долю
        const instantPoolFees = ourShareOfPoolFees / (24 * 60 * 60); // За секунду
        
        console.log(`      Наша доля в пуле: ${(ourLiquidityShare * 100).toFixed(1)}%`);
        console.log(`      Мгновенные комиссии от пула: $${instantPoolFees.toFixed(2)}`);
        
        // ШАГ 6: Прибыль от арбитража
        const arbitrageProfit = sellRevenue - totalBuyCost;
        
        console.log(`   6️⃣ ПРИБЫЛЬ ОТ АРБИТРАЖА:`);
        console.log(`      $${sellRevenue.toLocaleString()} - $${totalBuyCost.toLocaleString()} = $${arbitrageProfit.toFixed(0)}`);
        
        // ШАГ 7: ОБЩАЯ ПРИБЫЛЬ
        const totalLPFees = ourLPFees + instantPoolFees;
        const totalProfit = arbitrageProfit + totalLPFees;
        const gasCost = 0.01;
        const netProfit = totalProfit - gasCost;
        const roi = (netProfit / this.STRATEGY.flash_loan) * 100;
        
        console.log(`   7️⃣ ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
        console.log(`      💰 Прибыль от арбитража: $${arbitrageProfit.toFixed(0)}`);
        console.log(`      💰 LP комиссии от торговли: $${ourLPFees.toFixed(0)}`);
        console.log(`      💰 Мгновенные комиссии пула: $${instantPoolFees.toFixed(2)}`);
        console.log(`      💰 Общие LP комиссии: $${totalLPFees.toFixed(0)}`);
        console.log(`      💰 Общая прибыль: $${totalProfit.toFixed(0)}`);
        console.log(`      💸 Gas расходы: $${gasCost}`);
        console.log(`      🎯 ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(0)}`);
        console.log(`      📈 ROI: ${roi.toFixed(2)}%`);
        console.log(`      ${roi >= 3.0 ? '🔥 ВЫСОКОПРИБЫЛЬНО!' : roi >= 1.0 ? '✅ ПРИБЫЛЬНО' : '❌ НЕ ПРИБЫЛЬНО'}`);
        
        // ШАГ 8: РАЗБИВКА ИСТОЧНИКОВ ПРИБЫЛИ
        console.log(`\n   📊 РАЗБИВКА ИСТОЧНИКОВ ПРИБЫЛИ:`);
        const arbitragePercent = (arbitrageProfit / totalProfit) * 100;
        const lpFeesPercent = (totalLPFees / totalProfit) * 100;
        
        console.log(`      Арбитраж: ${arbitragePercent.toFixed(1)}% ($${arbitrageProfit.toFixed(0)})`);
        console.log(`      LP комиссии: ${lpFeesPercent.toFixed(1)}% ($${totalLPFees.toFixed(0)})`);
        console.log(`      🔥 LP КОМИССИИ - КЛЮЧЕВОЙ ИСТОЧНИК ПРИБЫЛИ!`);
        
        return {
            arbitrageProfit: arbitrageProfit,
            lpFeesFromTrading: ourLPFees,
            instantPoolFees: instantPoolFees,
            totalLPFees: totalLPFees,
            totalProfit: totalProfit,
            netProfit: netProfit,
            roi: roi,
            isProfitable: roi >= 3.0,
            details: {
                buyPrice: buyPrice,
                avgSellPrice: avgSellPrice,
                priceIncrease: binShiftData.priceIncrease,
                priceIncreasePercent: binShiftData.priceIncreasePercent,
                tradingVolume: totalTradingVolume,
                ourLiquidityShare: ourLiquidityShare
            }
        };
    }

    /**
     * 🚀 ПОЛНЫЙ РАСЧЕТ С УЧЕТОМ КОМИССИЙ LP
     */
    async calculateWithLPFees() {
        console.log('🚀 ФИНАЛЬНЫЙ РАСЧЕТ С УЧЕТОМ КОМИССИЙ LP');
        console.log('=' .repeat(80));
        
        try {
            // 1. Расчет сдвига активного bin
            const binShiftData = this.calculateActiveBinShift();
            
            // 2. Расчет прибыльности с LP комиссиями
            const profitability = this.calculateProfitabilityWithLPFees(binShiftData);
            
            console.log('\n🔥 ФИНАЛЬНЫЙ РАСЧЕТ ЗАВЕРШЕН!');
            console.log(`💰 Чистая прибыль: $${profitability.netProfit.toFixed(0)}`);
            console.log(`📈 ROI: ${profitability.roi.toFixed(2)}%`);
            console.log(`🎯 Статус: ${profitability.isProfitable ? 'ВЫСОКОПРИБЫЛЬНО!' : 'ПРИБЫЛЬНО'}`);
            console.log(`🔥 LP комиссии: $${profitability.totalLPFees.toFixed(0)} (${((profitability.totalLPFees / profitability.totalProfit) * 100).toFixed(1)}% от прибыли)`);
            
            return {
                binShiftData,
                profitability,
                success: true
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА:', error.message);
            return {
                error: error.message,
                success: false
            };
        }
    }
}

// 🧪 ЗАПУСК ФИНАЛЬНОГО РАСЧЕТА
if (require.main === module) {
    async function runFinalCalculation() {
        const calculator = new FinalCorrectCalculator();
        const result = await calculator.calculateWithLPFees();
        
        if (result.success) {
            console.log('\n🔥 ФИНАЛЬНЫЙ РАСЧЕТ УСПЕШЕН!');
            console.log('🎯 ТЕПЕРЬ УЧИТЫВАЕМ ВСЕ LP КОМИССИИ!');
            console.log('💡 LP КОМИССИИ - КЛЮЧЕВОЙ ИСТОЧНИК ПРИБЫЛИ В НАШЕЙ СТРАТЕГИИ!');
        } else {
            console.log('\n❌ РАСЧЕТ ПРОВАЛЕН!');
        }
    }
    
    runFinalCalculation().catch(console.error);
}

module.exports = FinalCorrectCalculator;
