/**
 * 🔍 TRANSACTION SIZE VALIDATOR
 * 
 * Проверяет размер транзакций Solana и предотвращает ошибку "encoding overruns Uint8Array"
 * 
 * ИСТОЧНИКИ:
 * - https://solana.com/docs/core/transactions (лимит 1232 байта)
 * - https://github.com/jup-ag/jupiter-swap-api/issues/37 (encoding overruns Uint8Array)
 */

const { VersionedTransaction } = require('@solana/web3.js');

class TransactionSizeValidator {
  constructor() {
    this.SOLANA_TRANSACTION_LIMIT = 1232; // байт
    this.stats = {
      totalChecked: 0,
      oversized: 0,
      valid: 0,
      errors: 0
    };
  }

  /**
   * 🔍 ПРОВЕРКА РАЗМЕРА BASE64 ТРАНЗАКЦИИ
   */
  validateBase64Transaction(base64Transaction, source = 'Unknown') {
    try {
      this.stats.totalChecked++;
      
      console.log(`🔍 Проверяем размер транзакции от ${source}...`);
      
      // Декодируем base64 в буфер
      const transactionBuffer = Buffer.from(base64Transaction, 'base64');
      const sizeBytes = transactionBuffer.length;
      
      console.log(`📊 Размер транзакции: ${sizeBytes} байт (лимит: ${this.SOLANA_TRANSACTION_LIMIT} байт)`);
      
      // Проверяем лимит
      if (sizeBytes > this.SOLANA_TRANSACTION_LIMIT) {
        this.stats.oversized++;
        
        console.error(`❌ ТРАНЗАКЦИЯ ПРЕВЫШАЕТ ЛИМИТ SOLANA!`);
        console.error(`   📊 Размер: ${sizeBytes} байт`);
        console.error(`   🚫 Лимит: ${this.SOLANA_TRANSACTION_LIMIT} байт`);
        console.error(`   📈 Превышение: ${sizeBytes - this.SOLANA_TRANSACTION_LIMIT} байт`);
        console.error(`   🔧 РЕШЕНИЕ: Уменьшите сумму или используйте onlyDirectRoutes=true`);
        
        return {
          valid: false,
          size: sizeBytes,
          limit: this.SOLANA_TRANSACTION_LIMIT,
          excess: sizeBytes - this.SOLANA_TRANSACTION_LIMIT,
          error: `Transaction too large: ${sizeBytes} > ${this.SOLANA_TRANSACTION_LIMIT} bytes`
        };
      }
      
      this.stats.valid++;
      console.log(`✅ Размер транзакции в пределах лимита (${sizeBytes} байт)`);
      
      return {
        valid: true,
        size: sizeBytes,
        limit: this.SOLANA_TRANSACTION_LIMIT,
        excess: 0
      };
      
    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Ошибка проверки размера транзакции: ${error.message}`);
      
      return {
        valid: false,
        size: 0,
        limit: this.SOLANA_TRANSACTION_LIMIT,
        excess: 0,
        error: error.message
      };
    }
  }

  /**
   * 🔧 БЕЗОПАСНАЯ ДЕСЕРИАЛИЗАЦИЯ VERSIONED TRANSACTION
   */
  safeDeserializeVersionedTransaction(base64Transaction, source = 'Unknown') {
    try {
      // Сначала проверяем размер
      const sizeCheck = this.validateBase64Transaction(base64Transaction, source);
      
      if (!sizeCheck.valid) {
        throw new Error(sizeCheck.error || 'Transaction size validation failed');
      }
      
      // Если размер OK, десериализуем
      console.log(`🔧 Десериализуем VersionedTransaction от ${source}...`);
      
      const transactionBuffer = Buffer.from(base64Transaction, 'base64');
      const transaction = VersionedTransaction.deserialize(transactionBuffer);
      
      console.log(`✅ VersionedTransaction успешно десериализована`);
      console.log(`   📊 Инструкций: ${transaction.message.compiledInstructions.length}`);
      console.log(`   📊 Аккаунтов: ${transaction.message.staticAccountKeys.length}`);
      
      if (transaction.message.addressTableLookups) {
        console.log(`   📋 Address Lookup Tables: ${transaction.message.addressTableLookups.length}`);
      }
      
      return {
        success: true,
        transaction,
        size: sizeCheck.size
      };
      
    } catch (error) {
      console.error(`❌ Ошибка десериализации VersionedTransaction: ${error.message}`);
      
      // Специальная обработка "encoding overruns Uint8Array"
      if (error.message.includes('encoding overruns') || 
          error.message.includes('Uint8Array')) {
        console.error(`🚨 ОБНАРУЖЕНА ОШИБКА "encoding overruns Uint8Array"`);
        console.error(`💡 ПРИЧИНА: Транзакция превышает лимиты Solana`);
        console.error(`🔧 РЕШЕНИЯ:`);
        console.error(`   1. Уменьшите сумму swap`);
        console.error(`   2. Используйте onlyDirectRoutes=true в Jupiter Quote`);
        console.error(`   3. Используйте maxAccounts=30 или меньше`);
        console.error(`   4. Избегайте сложных маршрутов с множественными DEX`);
        
        return {
          success: false,
          error: 'Transaction exceeds Solana size limits (encoding overruns Uint8Array)',
          solution: 'Reduce swap amount or use simpler routes'
        };
      }
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    const successRate = this.stats.totalChecked > 0 ? 
      Math.round((this.stats.valid / this.stats.totalChecked) * 100) : 0;
    
    return {
      totalChecked: this.stats.totalChecked,
      valid: this.stats.valid,
      oversized: this.stats.oversized,
      errors: this.stats.errors,
      successRate: `${successRate}%`,
      limit: this.SOLANA_TRANSACTION_LIMIT
    };
  }

  /**
   * 🧹 ОЧИСТКА СТАТИСТИКИ
   */
  clearStats() {
    this.stats = {
      totalChecked: 0,
      oversized: 0,
      valid: 0,
      errors: 0
    };
    console.log('🧹 Статистика валидатора размера транзакций очищена');
  }

  /**
   * 📋 ВЫВОД СТАТИСТИКИ
   */
  logStats() {
    console.log('\n📊 СТАТИСТИКА ВАЛИДАТОРА РАЗМЕРА ТРАНЗАКЦИЙ:');
    console.log('═══════════════════════════════════════════════════════════════');
    
    const stats = this.getStats();
    
    console.log(`   📊 Всего проверено: ${stats.totalChecked}`);
    console.log(`   ✅ Валидных: ${stats.valid}`);
    console.log(`   ❌ Превышающих лимит: ${stats.oversized}`);
    console.log(`   🚫 Ошибок: ${stats.errors}`);
    console.log(`   📈 Успешность: ${stats.successRate}`);
    console.log(`   🔒 Лимит Solana: ${stats.limit} байт`);
    
    console.log('═══════════════════════════════════════════════════════════════\n');
  }

  /**
   * 💡 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ
   */
  getOptimizationRecommendations(transactionSize) {
    const recommendations = [];
    
    if (transactionSize > this.SOLANA_TRANSACTION_LIMIT) {
      recommendations.push('🔧 Уменьшите сумму swap');
      recommendations.push('🎯 Используйте onlyDirectRoutes=true в Jupiter Quote');
      recommendations.push('📊 Установите maxAccounts=30 или меньше');
      recommendations.push('🚫 Избегайте сложных маршрутов через множественные DEX');
      recommendations.push('⚡ Используйте простые пары токенов (SOL/USDC, SOL/USDT)');
    } else if (transactionSize > this.SOLANA_TRANSACTION_LIMIT * 0.9) {
      recommendations.push('⚠️ Транзакция близка к лимиту - рассмотрите оптимизацию');
      recommendations.push('🎯 Используйте onlyDirectRoutes=true для безопасности');
    } else {
      recommendations.push('✅ Размер транзакции оптимален');
    }
    
    return recommendations;
  }
}

// Создаем единственный экземпляр
const transactionSizeValidator = new TransactionSizeValidator();

module.exports = {
  TransactionSizeValidator,
  transactionSizeValidator
};
