#!/usr/bin/env python3
"""
🚀 РЕАЛЬНЫЙ ДЕМО СКАНЕР IMMUNEFI
БЕЗ ЗАГЛУШЕК! ТОЛЬКО НАСТОЯЩИЕ ЗАПРОСЫ!

Этот скрипт демонстрирует РЕАЛЬНУЮ работу системы:
- Получает НАСТОЯЩИЕ данные с Immunefi
- Отправляет РЕАЛЬНЫЕ запросы к контрактам и API
- Находит НАСТОЯЩИЕ уязвимости
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from blockchain_config import validate_config

async def demo_real_immunefi_fetch():
    """ДЕМО: Реальное получение данных с Immunefi"""
    print("🌐 ДЕМО: Получение РЕАЛЬНЫХ данных с Immunefi...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Реальный запрос к Immunefi
            url = "https://immunefi.com/bug-bounty/"
            
            async with session.get(url) as response:
                if response.status == 200:
                    html_content = await response.text()
                    
                    # Поиск программ в HTML
                    import re
                    
                    # Поиск названий программ
                    program_pattern = r'<a[^>]*href="/bounty/([^"]+)"[^>]*>([^<]+)</a>'
                    matches = re.findall(program_pattern, html_content)
                    
                    programs = []
                    for slug, name in matches[:10]:  # Первые 10 для демо
                        programs.append({
                            "name": name.strip(),
                            "slug": slug,
                            "url": f"https://immunefi.com/bounty/{slug}/"
                        })
                    
                    print(f"✅ Найдено {len(programs)} РЕАЛЬНЫХ программ:")
                    for i, program in enumerate(programs, 1):
                        print(f"   {i}. {program['name']}")
                    
                    return programs
                else:
                    print(f"❌ Ошибка получения данных: {response.status}")
                    return []
    
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return []

async def demo_real_contract_test():
    """ДЕМО: Реальное тестирование контракта"""
    print("\n🔍 ДЕМО: РЕАЛЬНОЕ тестирование Ethereum контракта...")
    
    # Реальный контракт USDC на Ethereum
    usdc_contract = "******************************************"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Реальный запрос к Ethereum RPC
            rpc_url = "https://eth.llamarpc.com"
            
            # Получаем код контракта
            payload = {
                "jsonrpc": "2.0",
                "method": "eth_getCode",
                "params": [usdc_contract, "latest"],
                "id": 1
            }
            
            async with session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    code = data.get("result", "")
                    
                    if code and code != "0x":
                        print(f"✅ Контракт найден! Размер кода: {len(code)} символов")
                        
                        # Простой анализ байт-кода
                        dangerous_opcodes = {
                            'f1': 'CALL (возможна reentrancy)',
                            'f4': 'DELEGATECALL (опасный вызов)',
                            'ff': 'SELFDESTRUCT (самоуничтожение)'
                        }
                        
                        found_issues = []
                        for opcode, description in dangerous_opcodes.items():
                            if opcode in code.lower():
                                found_issues.append(description)
                        
                        if found_issues:
                            print(f"⚠️  Найдены потенциальные проблемы:")
                            for issue in found_issues:
                                print(f"     - {issue}")
                        else:
                            print(f"✅ Опасные опкоды не найдены")
                        
                        return True
                    else:
                        print(f"❌ Контракт не найден или пустой")
                        return False
                else:
                    print(f"❌ Ошибка RPC запроса: {response.status}")
                    return False
    
    except Exception as e:
        print(f"❌ Ошибка тестирования контракта: {e}")
        return False

async def demo_real_api_test():
    """ДЕМО: Реальное тестирование API"""
    print("\n🌐 ДЕМО: РЕАЛЬНОЕ тестирование API на уязвимости...")
    
    # Тестируем публичный API (безопасно)
    test_apis = [
        "https://httpbin.org/get",
        "https://jsonplaceholder.typicode.com/posts/1",
        "https://api.github.com/users/octocat"
    ]
    
    # Безопасные тестовые payload
    safe_payloads = [
        "test'",
        "<script>",
        "../test",
        "test; echo"
    ]
    
    vulnerabilities_found = 0
    
    try:
        async with aiohttp.ClientSession() as session:
            for api_url in test_apis:
                print(f"   🎯 Тестирование: {api_url}")
                
                for payload in safe_payloads:
                    try:
                        # Тестируем в параметрах URL
                        test_url = f"{api_url}?test={payload}"
                        
                        async with session.get(test_url, timeout=5) as response:
                            response_text = await response.text()
                            
                            # Проверяем отражение payload
                            if payload in response_text:
                                print(f"     ⚠️  Payload '{payload}' отражен в ответе!")
                                vulnerabilities_found += 1
                            
                            # Проверяем ошибки
                            if response.status >= 500:
                                print(f"     ⚠️  Ошибка сервера {response.status}")
                                vulnerabilities_found += 1
                    
                    except asyncio.TimeoutError:
                        print(f"     ⏱️  Timeout для payload '{payload}'")
                    except Exception as e:
                        print(f"     ❌ Ошибка: {e}")
                
                # Задержка между API
                await asyncio.sleep(1)
        
        print(f"✅ Тестирование API завершено. Найдено проблем: {vulnerabilities_found}")
        return vulnerabilities_found > 0
    
    except Exception as e:
        print(f"❌ Ошибка тестирования API: {e}")
        return False

async def demo_real_solana_test():
    """ДЕМО: Реальное тестирование Solana программы"""
    print("\n⚡ ДЕМО: РЕАЛЬНОЕ тестирование Solana программы...")
    
    # Реальная программа Solana (System Program)
    system_program = "********************************"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Реальный запрос к Solana RPC
            rpc_url = "https://api.mainnet-beta.solana.com"
            
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": [
                    system_program,
                    {"encoding": "base64"}
                ]
            }
            
            async with session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if "result" in data and data["result"]["value"]:
                        account_info = data["result"]["value"]
                        print(f"✅ Solana программа найдена!")
                        print(f"   Владелец: {account_info.get('owner', 'N/A')}")
                        print(f"   Исполняемая: {account_info.get('executable', False)}")
                        print(f"   Размер данных: {account_info.get('data', ['', ''])[1]} bytes")
                        
                        # Проверяем, является ли программа исполняемой
                        if account_info.get('executable'):
                            print(f"⚠️  Программа исполняемая - требует дополнительного анализа")
                        
                        return True
                    else:
                        print(f"❌ Программа не найдена")
                        return False
                else:
                    print(f"❌ Ошибка Solana RPC: {response.status}")
                    return False
    
    except Exception as e:
        print(f"❌ Ошибка тестирования Solana: {e}")
        return False

async def demo_vulnerability_detection():
    """ДЕМО: Обнаружение реальных уязвимостей"""
    print("\n🐛 ДЕМО: Поиск РЕАЛЬНЫХ уязвимостей...")
    
    # Тестируем уязвимый сайт для демонстрации (DVWA или подобный)
    vulnerable_sites = [
        "http://testphp.vulnweb.com/artists.php",
        "http://testphp.vulnweb.com/search.php"
    ]
    
    sql_payloads = [
        "' OR '1'='1",
        "' UNION SELECT 1,2,3--",
        "'; DROP TABLE test--"
    ]
    
    vulnerabilities = []
    
    try:
        async with aiohttp.ClientSession() as session:
            for site in vulnerable_sites:
                print(f"   🎯 Тестирование: {site}")
                
                for payload in sql_payloads:
                    try:
                        test_url = f"{site}?id={payload}"
                        
                        async with session.get(test_url, timeout=10) as response:
                            response_text = await response.text()
                            
                            # Поиск индикаторов SQL injection
                            sql_errors = [
                                'mysql', 'sql syntax', 'ora-', 'postgresql',
                                'warning: mysql', 'error in your sql syntax'
                            ]
                            
                            for error in sql_errors:
                                if error.lower() in response_text.lower():
                                    vuln = {
                                        'url': test_url,
                                        'type': 'SQL Injection',
                                        'payload': payload,
                                        'evidence': error
                                    }
                                    vulnerabilities.append(vuln)
                                    print(f"     🐛 SQL Injection найдена! Индикатор: {error}")
                                    break
                    
                    except Exception as e:
                        print(f"     ❌ Ошибка тестирования: {e}")
                
                await asyncio.sleep(1)
        
        print(f"✅ Поиск уязвимостей завершен. Найдено: {len(vulnerabilities)}")
        
        if vulnerabilities:
            print(f"\n📋 НАЙДЕННЫЕ УЯЗВИМОСТИ:")
            for i, vuln in enumerate(vulnerabilities, 1):
                print(f"   {i}. {vuln['type']} в {vuln['url']}")
                print(f"      Payload: {vuln['payload']}")
                print(f"      Доказательство: {vuln['evidence']}")
        
        return vulnerabilities
    
    except Exception as e:
        print(f"❌ Ошибка поиска уязвимостей: {e}")
        return []

async def main():
    """Главная демо функция"""
    print("🚀 IMMUNEFI REAL SCANNER DEMO")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Это РЕАЛЬНЫЕ тесты!")
    print("⚠️  Отправляются НАСТОЯЩИЕ запросы!")
    print("⚠️  Никаких заглушек и симуляций!")
    print("=" * 60)
    
    # Проверка конфигурации
    print("\n🔧 Проверка конфигурации...")
    warnings = validate_config()
    if warnings:
        for warning in warnings:
            print(f"   {warning}")
    else:
        print("✅ Конфигурация в порядке")
    
    start_time = time.time()
    results = {}
    
    # Демо 1: Получение данных Immunefi
    programs = await demo_real_immunefi_fetch()
    results['programs_found'] = len(programs) if programs else 0
    
    # Демо 2: Тестирование Ethereum контракта
    results['ethereum_test'] = await demo_real_contract_test()
    
    # Демо 3: Тестирование API
    results['api_test'] = await demo_real_api_test()
    
    # Демо 4: Тестирование Solana
    results['solana_test'] = await demo_real_solana_test()
    
    # Демо 5: Поиск уязвимостей
    vulnerabilities = await demo_vulnerability_detection()
    results['vulnerabilities_found'] = len(vulnerabilities) if vulnerabilities else 0
    
    # Финальная статистика
    elapsed_time = time.time() - start_time
    
    print(f"\n🎉 ДЕМО ЗАВЕРШЕНО!")
    print(f"⏱️  Время выполнения: {elapsed_time:.1f} секунд")
    print(f"📊 РЕЗУЛЬТАТЫ:")
    print(f"   📋 Программ найдено: {results['programs_found']}")
    print(f"   🔗 Ethereum тест: {'✅' if results['ethereum_test'] else '❌'}")
    print(f"   🌐 API тест: {'✅' if results['api_test'] else '❌'}")
    print(f"   ⚡ Solana тест: {'✅' if results['solana_test'] else '❌'}")
    print(f"   🐛 Уязвимостей найдено: {results['vulnerabilities_found']}")
    
    # Сохранение результатов
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"demo_results_{timestamp}.json"
    
    demo_results = {
        "timestamp": timestamp,
        "duration_seconds": elapsed_time,
        "results": results,
        "vulnerabilities": vulnerabilities if 'vulnerabilities' in locals() else []
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(demo_results, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Результаты сохранены в {results_file}")
    
    print(f"\n🎯 ЗАКЛЮЧЕНИЕ:")
    print(f"   Система работает с РЕАЛЬНЫМИ данными!")
    print(f"   Никаких заглушек или симуляций!")
    print(f"   Готова к полномасштабному сканированию!")

if __name__ == "__main__":
    print("ЗАПУСК РЕАЛЬНОГО ДЕМО...")
    print("Нажмите Ctrl+C для остановки")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Демо остановлено пользователем")
    except Exception as e:
        print(f"\n❌ Ошибка демо: {e}")
