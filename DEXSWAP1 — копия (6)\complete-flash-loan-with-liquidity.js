#!/usr/bin/env node

/**
 * 🔥 ПОЛНАЯ FLASH LOAN ТРАНЗАКЦИЯ С ЛИКВИДНОСТЬЮ
 * 
 * ВОССТАНАВЛИВАЕТ ИЗНАЧАЛЬНУЮ СТРУКТУРУ ИЗ FLASHMeteora.js:
 * 1-2. Compute Budget (2 инструкции)
 * 3. Flash Loan Start
 * 4. Borrow USDC
 * 5. USDC → SOL Conversion
 * 6. Add Liquidity ← НОВОЕ
 * 7. First Arbitrage Swap (USDC → SOL)
 * 8. Second Arbitrage Swap (SOL → USDC)
 * 9. Remove Liquidity ← НОВОЕ
 * 10. SOL → USDC Reconversion
 * 11. Repay USDC
 * 12. Flash Loan End (endIndex = 11)
 */

const { 
    Connection, 
    PublicKey, 
    Keypair, 
    Transaction,
    ComputeBudgetProgram
} = require('@solana/web3.js');
const { 
    getAssociatedTokenAddress,
    createAssociatedTokenAccountInstruction
} = require('@solana/spl-token');
const bs58 = require('bs58');
const BN = require('bn.js');

// 🔥 ИМПОРТИРУЕМ РАБОЧИЕ КОМПОНЕНТЫ
const LowLevelMarginFiIntegration = require('./low-level-marginfi-integration.js');
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');

// 🎭 ИМПОРТИРУЕМ СИСТЕМУ ОБФУСКАЦИИ
const DynamicObfuscationManager = require('./dynamic-obfuscation-manager.js');
const CustomObfuscationManager = require('./custom-obfuscation-manager.js');

// 🔥 ИМПОРТИРУЕМ MASTER TRANSACTION CONTROLLER С 4 ALT ТАБЛИЦАМИ
const MasterTransactionController = require('./master-transaction-controller.js');

require('dotenv').config();

// 🔥 ИМПОРТ DLMM SDK ПОСЛЕ ВСЕХ ДРУГИХ ИМПОРТОВ
const DLMM = require('@meteora-ag/dlmm').default;

class CompleteFlashLoanWithLiquidity {
    constructor(connection = null, binCacheManager = null) {
        // 🌐 CONNECTION
        this.connection = connection || new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );

        // 🚀 BIN CACHE MANAGER (ПЕРЕДАЕТСЯ ИЗ BMETEORA.js)
        this.binCacheManager = binCacheManager;

        // 🔑 WALLET
        this.wallet = null;
        
        // 🏊 РАБОЧИЙ DLMM ПУЛ (ПРОВЕРЕННЫЙ)
        this.POOL_ADDRESS = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
        
        // 🪙 ТОКЕНЫ
        this.TOKENS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112')
        };
        
        // 💰 УВЕЛИЧИВАЕМ СУММУ ДЛЯ SOL RENT: $200 ВМЕСТО $20!
        this.STRATEGY = {
            FLASH_LOAN_AMOUNT: 1820200 * 1e6,     // $1.82M + $200 USDC Flash Loan (для SOL rent)
            FIRST_SWAP_AMOUNT: 420000 * 1e6,      // $420K USDC → SOL (первый swap)
            LIQUIDITY_AMOUNT: 1400000 * 1e6,      // $1.4M USDC односторонняя ликвидность (ТОЛЬКО USDC!)
            SOL_RENT_AMOUNT: 200 * 1e6,           // $200 USDC → WSOL для rent DLMM позиции (0.036+ SOL)
            USE_ALL_SOL_BALANCE: true,             // Второй swap: использовать ВСЕ SOL от первого swap
            REMOVE_ALL_LIQUIDITY: true,            // Забрать ВСЮ ликвидность USDC
            EXPECTED_PROFIT: 100000                // $100K ожидаемая прибыль + остаток ~$199.89
        };
        
        // 🔥 РАБОЧИЕ КОМПОНЕНТЫ
        this.lowLevelMarginFi = null;
        this.meteoraSDK = null;
        
        console.log('🔥 COMPLETE FLASH LOAN WITH LIQUIDITY ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ LOW-LEVEL MARGINFI С BIN CACHE MANAGER!
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ LOW-LEVEL MARGINFI С BIN CACHE MANAGER...');
        this.lowLevelMarginFi = new LowLevelMarginFiIntegration(this.connection, this.wallet, this.binCacheManager);
        this.lowLevelMarginFi.parentBot = this;
        await this.lowLevelMarginFi.initialize();

        if (this.binCacheManager) {
            console.log('✅ BinCacheManager передан в LowLevelMarginFiIntegration!');
        } else {
            console.log('⚠️ BinCacheManager НЕ передан в LowLevelMarginFiIntegration!');
        }
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK...');
        this.meteoraSDK = new MeteoraHybridImplementation(this.connection, this.wallet);

        // 🎭 ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ ОБФУСКАЦИИ
        console.log('🎭 ИНИЦИАЛИЗАЦИЯ СИСТЕМЫ ОБФУСКАЦИИ...');
        this.dynamicObfuscator = new DynamicObfuscationManager(this.connection, this.wallet);
        this.customObfuscator = new CustomObfuscationManager(this.connection, this.wallet);

        // 🔥 ИНИЦИАЛИЗАЦИЯ MASTER TRANSACTION CONTROLLER С 4 ALT ТАБЛИЦАМИ
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ MASTER TRANSACTION CONTROLLER...');
        this.masterController = new MasterTransactionController(this.connection, this.wallet);

        // 🔧 ПЕРЕДАЕМ ССЫЛКУ НА LOW-LEVEL MARGINFI ДЛЯ ОБНОВЛЕНИЯ endIndex
        this.masterController.lowLevelMarginFi = this.lowLevelMarginFi;

        console.log('   ✅ Готов к работе с обфускацией и 4 ALT таблицами');
    }

    /**
     * 🔧 СОЗДАНИЕ TOKEN ACCOUNTS ЕСЛИ НЕ СУЩЕСТВУЮТ
     */
    async ensureTokenAccountsExist() {
        console.log('\n🔧 ПРОВЕРКА И СОЗДАНИЕ TOKEN ACCOUNTS...');
        
        const userUSDC = await getAssociatedTokenAddress(this.TOKENS.USDC, this.wallet.publicKey);
        const userSOL = await getAssociatedTokenAddress(this.TOKENS.SOL, this.wallet.publicKey);
        
        console.log(`   User USDC: ${userUSDC.toString()}`);
        console.log(`   User SOL: ${userSOL.toString()}`);
        
        const usdcAccountInfo = await this.connection.getAccountInfo(userUSDC);
        const solAccountInfo = await this.connection.getAccountInfo(userSOL);
        
        console.log(`   USDC Account существует: ${usdcAccountInfo ? 'ДА' : 'НЕТ'}`);
        console.log(`   SOL Account существует: ${solAccountInfo ? 'ДА' : 'НЕТ'}`);
        
        const ataInstructions = [];
        
        if (!usdcAccountInfo) {
            console.log('🔧 СОЗДАЕМ USDC TOKEN ACCOUNT...');
            const createUSDCIx = createAssociatedTokenAccountInstruction(
                this.wallet.publicKey, userUSDC, this.wallet.publicKey, this.TOKENS.USDC
            );
            ataInstructions.push(createUSDCIx);
        }
        
        if (!solAccountInfo) {
            console.log('🔧 СОЗДАЕМ SOL TOKEN ACCOUNT...');
            const createSOLIx = createAssociatedTokenAccountInstruction(
                this.wallet.publicKey, userSOL, this.wallet.publicKey, this.TOKENS.SOL
            );
            ataInstructions.push(createSOLIx);
        }
        
        // Отправляем ATA транзакцию если нужно
        if (ataInstructions.length > 0) {
            console.log(`🚀 ОТПРАВКА ATA ТРАНЗАКЦИИ (${ataInstructions.length} инструкций)...`);
            
            const ataTransaction = new Transaction();
            ataInstructions.forEach(ix => ataTransaction.add(ix));
            
            const { blockhash } = await this.connection.getLatestBlockhash();
            ataTransaction.recentBlockhash = blockhash;
            ataTransaction.feePayer = this.wallet.publicKey;
            
            ataTransaction.sign(this.wallet);
            
            const signature = await this.connection.sendRawTransaction(
                ataTransaction.serialize(),
                { skipPreflight: false, preflightCommitment: 'processed' }
            );
            
            console.log(`✅ ATA транзакция отправлена: ${signature}`);
            await this.connection.confirmTransaction(signature, 'processed');
            console.log(`✅ ATA транзакция подтверждена`);
        }
        
        return { userUSDC, userSOL };
    }

    /**
     * 🏊 ДОБАВЛЕНИЕ ЛИКВИДНОСТИ В ПУЛ
     */
    async addLiquidityToPool(poolAddress, solAmount, usdcAmount, userSolAccount, userUsdcAccount) {
        console.log('\n🏊 ДОБАВЛЕНИЕ ЛИКВИДНОСТИ В ПУЛ...');
        console.log(`   Пул: ${poolAddress}`);
        console.log(`   SOL: ${solAmount / 1e9} SOL`);
        console.log(`   USDC: ${usdcAmount / 1e6} USDC`);

        try {
            // 🔥 ПРОВЕРЯЕМ И СОЗДАЕМ SOL TOKEN ACCOUNT ЕСЛИ НУЖНО
            console.log('🔍 ПРОВЕРКА SOL TOKEN ACCOUNT...');
            const { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction } = require('@solana/spl-token');
            const { PublicKey } = require('@solana/web3.js');

            const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
            const expectedSOLAccount = await getAssociatedTokenAddress(WSOL_MINT, this.wallet.publicKey);

            console.log(`   Ожидаемый SOL account: ${expectedSOLAccount.toString()}`);
            console.log(`   Переданный SOL account: ${userSolAccount.toString()}`);

            // Проверяем существование SOL account
            const solAccountInfo = await this.connection.getAccountInfo(expectedSOLAccount);
            const createSOLAccountInstructions = [];

            if (!solAccountInfo) {
                console.log('❌ SOL TOKEN ACCOUNT НЕ СУЩЕСТВУЕТ!');
                throw new Error(`SOL token account не существует: ${expectedSOLAccount.toString()}`);
            } else {
                console.log('✅ SOL TOKEN ACCOUNT УЖЕ СУЩЕСТВУЕТ - ИСПОЛЬЗУЕМ ЕГО');
                console.log('🔥 НЕ СОЗДАЕМ ДОПОЛНИТЕЛЬНЫХ ИНСТРУКЦИЙ - АККАУНТ ГОТОВ');
            }

            // 🔥 ИСПОЛЬЗУЕМ METEORA SDK ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
            const addLiquidityResult = await this.meteoraSDK.addLiquidityToDLMMPool(
                poolAddress,
                solAmount,
                usdcAmount,
                expectedSOLAccount, // Используем правильный SOL account
                userUsdcAccount
            );

            if (addLiquidityResult.success) {
                console.log(`✅ Ликвидность добавлена: ${addLiquidityResult.instructions.length} инструкций`);

                // 🔥 СОХРАНЯЕМ BIN ARRAYS ДЛЯ ALT ТАБЛИЦ
                if (addLiquidityResult.binArrays) {
                    console.log(`🔥 Получены bin arrays для ALT: ${addLiquidityResult.binArrays.length}`);
                    this.liquidityBinArrays = addLiquidityResult.binArrays;
                } else {
                    console.log(`⚠️ Bin arrays не получены из addLiquidityResult`);
                    this.liquidityBinArrays = [];
                }

                // 🔥 ОБЪЕДИНЯЕМ ИНСТРУКЦИИ СОЗДАНИЯ SOL ACCOUNT И ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
                const allInstructions = [
                    ...createSOLAccountInstructions, // Сначала создаем SOL account если нужно
                    ...addLiquidityResult.instructions // Потом добавляем ликвидность
                ];

                console.log(`✅ Итого инструкций: ${allInstructions.length} (${createSOLAccountInstructions.length} создание + ${addLiquidityResult.instructions.length} ликвидность)`);
                return allInstructions;
            } else {
                throw new Error(`Добавление ликвидности провалено: ${addLiquidityResult.error}`);
            }
            
        } catch (error) {
            console.error(`❌ Ошибка добавления ликвидности: ${error.message}`);
            
            // 🔧 FALLBACK: Создаем пустую инструкцию-заглушку
            console.log('🔧 ИСПОЛЬЗУЕМ ЗАГЛУШКУ ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ');
            return []; // Пустой массив инструкций
        }
    }

    /**
     * 🏊 УДАЛЕНИЕ ЛИКВИДНОСТИ ИЗ ПУЛА
     */
    async removeLiquidityFromPool(poolAddress, lpTokenAmount, userSolAccount, userUsdcAccount) {
        console.log('\n🏊 УДАЛЕНИЕ ЛИКВИДНОСТИ ИЗ ПУЛА...');
        console.log(`   Пул: ${poolAddress}`);
        console.log(`   LP Tokens: ${lpTokenAmount}`);
        
        try {
            // 🔥 ИСПОЛЬЗУЕМ METEORA SDK ДЛЯ УДАЛЕНИЯ ЛИКВИДНОСТИ
            const removeLiquidityResult = await this.meteoraSDK.removeLiquidityFromDLMMPool(
                poolAddress,
                lpTokenAmount,
                userSolAccount,
                userUsdcAccount
            );
            
            if (removeLiquidityResult.success) {
                console.log(`✅ Ликвидность удалена: ${removeLiquidityResult.instructions.length} инструкций`);
                return removeLiquidityResult.instructions;
            } else {
                throw new Error(`Удаление ликвидности провалено: ${removeLiquidityResult.error}`);
            }
            
        } catch (error) {
            console.error(`❌ Ошибка удаления ликвидности: ${error.message}`);
            
            // 🔧 FALLBACK: Создаем пустую инструкцию-заглушку
            console.log('🔧 ИСПОЛЬЗУЕМ ЗАГЛУШКУ ДЛЯ УДАЛЕНИЯ ЛИКВИДНОСТИ');
            return []; // Пустой массив инструкций
        }
    }

    /**
     * 🔥 СОЗДАНИЕ 2 АРБИТРАЖНЫХ SWAP ИНСТРУКЦИЙ (БЕЗ ЛИШНИХ КОНВЕРТАЦИЙ!)
     */
    async create2ArbitrageSwaps(userUSDC, userSOL) {
        console.log('\n🔥 СОЗДАНИЕ 2 АРБИТРАЖНЫХ SWAP ИНСТРУКЦИЙ...');

        const instructions = [];

        // 🔥 АЛЬТЕРНАТИВНОЕ РЕШЕНИЕ: ИСПОЛЬЗУЕМ SYNCNATIVE ДЛЯ ПЕРЕДАЧИ ВСЕХ ТОКЕНОВ!
        console.log('🔥 АЛЬТЕРНАТИВНОЕ РЕШЕНИЕ: ИСПОЛЬЗУЕМ SYNCNATIVE ДЛЯ ПЕРЕДАЧИ ВСЕХ ТОКЕНОВ!');
        console.log('💰 Первый swap → userSOL, SyncNative обновляет баланс, второй swap использует ВСЕ');

        // Целевой аккаунт - это основной userSOL для обоих swap
        const targetSOLAccount = userSOL;

        console.log(`💰 SOL account для обоих swap: ${targetSOLAccount.toString()}`);
        console.log(`🔥 SyncNative обновит баланс между swap для точного расчета`);

        try {
            const POOL_ADDRESS = this.POOL_ADDRESS.toString();
            
            // ❌ УБИРАЕМ ЛИШНЮЮ КОНВЕРТАЦИЮ! НАМ НУЖЕН ТОЛЬКО АРБИТРАЖ!

            // 1️⃣ ПЕРВЫЙ АРБИТРАЖНЫЙ SWAP (USDC → SOL)
            console.log('1️⃣ СОЗДАНИЕ ПЕРВОГО АРБИТРАЖНОГО SWAP (USDC → SOL)...');
            console.log(`💰 Первый swap: USDC → SOL аккаунт ${targetSOLAccount.toString()}`);

            const firstSwapResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                this.STRATEGY.FIRST_SWAP_AMOUNT,
                null, // minAmountOut рассчитается SDK
                true, // USDC → SOL
                userUSDC,
                targetSOLAccount  // 🔥 Используем ОСНОВНОЙ SOL аккаунт
            );

            if (firstSwapResult.success) {
                instructions.push(...firstSwapResult.instructions);
                console.log(`   ✅ Первый арбитражный swap: ${firstSwapResult.instructions.length} инструкций`);
            } else {
                throw new Error(`Первый арбитражный swap провален: ${firstSwapResult.error}`);
            }

            // 🔥 CLOSEACCOUNT БУДЕТ СОЗДАНА ПОСЛЕ ВСЕХ SWAP'ОВ
            console.log('🔥 CloseAccount инструкция будет создана после всех swap для передачи ВСЕХ SOL');
            console.log('💰 CloseAccount передаст ВСЕ SOL с первого swap на второй');

            // 2️⃣ ВТОРОЙ АРБИТРАЖНЫЙ SWAP (ВСЕ ДОСТУПНЫЕ SOL → USDC)
            console.log('2️⃣ СОЗДАНИЕ ВТОРОГО АРБИТРАЖНОГО SWAP (ВСЕ ДОСТУПНЫЕ SOL → USDC)...');
            console.log('🔥 ИСПОЛЬЗУЕМ РАСЧЕТНОЕ КОЛИЧЕСТВО SOL (METEORA АВТОМАТИЧЕСКИ ИСПОЛЬЗУЕТ ВСЕ)');
            console.log('💰 После SyncNative Meteora будет использовать весь доступный баланс');

            // 🔥 ВТОРОЙ SWAP ИСПОЛЬЗУЕТ ВСЕ SOL ПОСЛЕ CLOSEACCOUNT
            console.log('🔥 ВТОРОЙ SWAP БУДЕТ ИСПОЛЬЗОВАТЬ ВСЕ SOL ПОСЛЕ CLOSEACCOUNT');
            console.log('💰 CloseAccount передаст ВСЕ SOL на wallet, второй swap использует их');

            // Используем глобально созданный targetSOLAccount
            console.log(`💰 Второй swap будет использовать целевой SOL account (куда CloseAccount передал ВСЕ SOL)`);

            // 🔥 ИСПОЛЬЗУЕМ ТОЧНОЕ КОЛИЧЕСТВО SOL ОТ ПЕРВОГО SWAP
            console.log('🔥 ИСПОЛЬЗУЕМ ТОЧНОЕ КОЛИЧЕСТВО SOL ОТ ПЕРВОГО SWAP');

            // Используем точное количество SOL от первого swap
            const realSOLAmount = firstSwapResult.quote.outAmount;
            console.log(`🔥 Точное количество SOL от первого swap: ${realSOLAmount.toString()}`);

            // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНОЕ КОЛИЧЕСТВО SOL + SYNCNATIVE ДЛЯ ОБНОВЛЕНИЯ БАЛАНСА
            console.log('🔥 ИСПОЛЬЗУЕМ РЕАЛЬНОЕ КОЛИЧЕСТВО SOL + SYNCNATIVE ДЛЯ ОБНОВЛЕНИЯ БАЛАНСА');
            console.log('💰 SyncNative обновит баланс, затем второй swap использует точное количество');

            const secondSwapResult = await this.meteoraSDK.createStableSwapInstruction(
                POOL_ADDRESS,
                realSOLAmount.toString(), // Точное количество SOL от первого swap
                null, // minAmountOut рассчитается SDK
                false, // SOL → USDC
                targetSOLAccount, // FROM: Целевой SOL account
                userUSDC,         // TO: User USDC account
                false             // 🔥 useAllBalance = false (используем точное количество)
            );

            if (secondSwapResult.success) {
                // 🔥 УБИРАЕМ SYNCNATIVE ИЗ ВТОРОГО SWAP - ИСПОЛЬЗУЕМ ТОЧНОЕ КОЛИЧЕСТВО
                console.log('🔥 УБИРАЕМ SyncNative из второго swap - используем точное количество SOL...');
                console.log('💰 Второй swap будет использовать точное количество SOL от первого swap');

                // ФИЛЬТРУЕМ SyncNative инструкции из второго swap
                const filteredSecondSwapInstructions = secondSwapResult.instructions.filter(ix => {
                    // Проверяем, является ли инструкция SyncNative
                    const isSyncNative = ix.data && ix.data.length === 1 && ix.data[0] === 17;
                    if (isSyncNative) {
                        console.log('🚫 Убираем SyncNative инструкцию - используем точное количество');
                        return false;
                    }
                    return true;
                });

                instructions.push(...filteredSecondSwapInstructions);
                console.log(`   ✅ Второй арбитражный swap: ${filteredSecondSwapInstructions.length} инструкций (было ${secondSwapResult.instructions.length}, убрали ${secondSwapResult.instructions.length - filteredSecondSwapInstructions.length} SyncNative)`);
            } else {
                throw new Error(`Второй арбитражный swap провален: ${secondSwapResult.error}`);
            }

            // ❌ УБИРАЕМ ЛИШНЮЮ ОБРАТНУЮ КОНВЕРТАЦИЮ! НАМ НУЖЕН ТОЛЬКО АРБИТРАЖ!
            
            console.log(`🎉 ВСЕ 4 SWAP ИНСТРУКЦИИ СОЗДАНЫ: ${instructions.length} инструкций`);
            
            return {
                success: true,
                instructions: instructions,
                count: instructions.length,
                targetSOLAccount: targetSOLAccount,  // 🔥 Целевой аккаунт для обоих swap
                firstSwapResult: firstSwapResult     // 🔥 Результат первого swap для CloseAccount
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ SWAP ИНСТРУКЦИЙ: ${error.message}`);
            return {
                success: false,
                error: error.message,
                instructions: []
            };
        }
    }

    /**
     * 🔥 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ С ЛИКВИДНОСТЬЮ
     *
     * ВОССТАНАВЛИВАЕТ ИЗНАЧАЛЬНУЮ СТРУКТУРУ ИЗ FLASHMeteora.js
     */
    async createCompleteFlashLoanTransaction() {
        console.log('\n🔥 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ С ЛИКВИДНОСТЬЮ...');
        console.log('📋 ВОССТАНАВЛИВАЕМ ИЗНАЧАЛЬНУЮ СТРУКТУРУ ИЗ FLASHMeteora.js');

        try {
            // 1️⃣ ПОДГОТОВКА TOKEN ACCOUNTS
            const { userUSDC, userSOL } = await this.ensureTokenAccountsExist();

            // 2️⃣ СОЗДАНИЕ 2 АРБИТРАЖНЫХ SWAP ИНСТРУКЦИЙ
            const swapResult = await this.create2ArbitrageSwaps(userUSDC, userSOL);

            if (!swapResult.success) {
                throw new Error(`Swap инструкции провалены: ${swapResult.error}`);
            }

            console.log(`✅ Swap инструкции готовы: ${swapResult.count}`);

            // 🔥 ПОЛУЧАЕМ ДАННЫЕ ИЗ РЕЗУЛЬТАТА SWAP
            const targetSOLAccount = swapResult.targetSOLAccount;
            const firstSwapResult = swapResult.firstSwapResult;
            console.log(`💰 Получен targetSOLAccount для обоих swap: ${targetSOLAccount.toString()}`);
            console.log(`💰 Получен firstSwapResult для CloseAccount`);

            // 3️⃣ АВТОМАТИЧЕСКОЕ ПОПОЛНЕНИЕ SOL ДЛЯ DLMM ПОЗИЦИИ
            console.log('\n💰 АВТОМАТИЧЕСКОЕ ПОПОЛНЕНИЕ SOL ДЛЯ DLMM ПОЗИЦИИ...');

            // Проверяем текущий баланс SOL пользователя
            const currentSOLBalance = await this.connection.getBalance(this.wallet.publicKey);
            console.log(`💰 Текущий баланс SOL: ${currentSOLBalance / 1e9} SOL`);

            // Рассчитываем необходимый SOL для DLMM позиции (точный расчет)
            const dlmmPositionSize = 912; // ~912 байт для DLMM позиции (из расчета 0.057 SOL)
            const requiredSOLForRent = await this.connection.getMinimumBalanceForRentExemption(dlmmPositionSize);
            console.log(`🔧 Необходимо SOL для DLMM позиции: ${requiredSOLForRent / 1e9} SOL`);

            // Добавляем небольшой буфер для безопасности
            const bufferSOL = 5000000; // 0.005 SOL буфер
            const totalRequiredSOL = requiredSOLForRent + bufferSOL;
            console.log(`🛡️ Общая потребность в SOL (с буфером): ${totalRequiredSOL / 1e9} SOL`);

            let solTopUpInstructions = [];

            if (currentSOLBalance < totalRequiredSOL) {
                const neededSOL = totalRequiredSOL - currentSOLBalance;
                console.log(`⚠️ НЕ ХВАТАЕТ SOL: ${neededSOL / 1e9} SOL`);
                console.log(`🔄 КОНВЕРТИРУЕМ $20 USDC ИЗ ЗАЙМА → WSOL ДЛЯ RENT`);

                // 🔥 ИСПОЛЬЗУЕМ $20 ИЗ ЗАЙМА: ГАРАНТИРОВАННО ХВАТИТ НА RENT
                const fixedUSDCAmount = this.STRATEGY.SOL_RENT_AMOUNT; // $20 USDC
                console.log(`💰 Конвертируем $20 USDC → WSOL (остаток ~$19.89 упадет на счет)`);
                console.log(`🎯 Цель: Получить 0.057+ SOL для rent DLMM позиции`);

                // 🔥 СОЗДАЕМ SWAP $20 USDC → WSOL ЧЕРЕЗ ПРЯМОЙ ВЫЗОВ METEORA SDK
                console.log('🔄 Создаем swap $20 USDC → WSOL через прямой вызов Meteora SDK...');

                try {
                    // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНУЮ ФУНКЦИЮ createStableSwapInstruction!
                    console.log('🔍 Используем существующую функцию createStableSwapInstruction...');

                    // Определяем направление swap: USDC → SOL
                    const swapYtoX = true; // USDC (tokenY) → SOL (tokenX)

                    // Используем существующую функцию из MeteoraHybridImplementation
                    const topUpSwapResult = await this.meteoraSDK.createStableSwapInstruction(
                        this.POOL_ADDRESS.toString(),
                        fixedUSDCAmount, // $20 USDC
                        null, // minAmountOut = пусть Meteora рассчитает
                        swapYtoX, // USDC → SOL
                        userUSDC, // fromAccount
                        userSOL, // toAccount
                        false // useAllBalance = false
                    );

                    if (topUpSwapResult && topUpSwapResult.instructions) {
                        solTopUpInstructions = topUpSwapResult.instructions;
                        console.log(`✅ Создано ${solTopUpInstructions.length} инструкций для конвертации $20 USDC → WSOL`);
                    } else {
                        console.log('⚠️ Не удалось создать swap для конвертации USDC → WSOL, пропускаем');
                        solTopUpInstructions = [];
                    }
                } catch (swapError) {
                    console.log(`⚠️ Ошибка создания swap для конвертации USDC → WSOL: ${swapError.message}`);
                    console.log('🔧 ПРОПУСКАЕМ КОНВЕРТАЦИЮ USDC → WSOL');
                    solTopUpInstructions = [];
                }
                console.log(`✅ Создано ${solTopUpInstructions.length} инструкций для пополнения SOL`);
            } else {
                console.log(`✅ SOL баланс достаточный: ${currentSOLBalance / 1e9} SOL`);
            }

            // 4️⃣ СОЗДАНИЕ ИНСТРУКЦИЙ ЛИКВИДНОСТИ
            console.log('\n🏊 СОЗДАНИЕ ИНСТРУКЦИЙ ЛИКВИДНОСТИ...');

            // Добавление ОДНОСТОРОННЕЙ ликвидности (ТОЛЬКО USDC)
            console.log('🏊 ДОБАВЛЯЕМ ОДНОСТОРОННЮЮ ЛИКВИДНОСТЬ: ТОЛЬКО USDC');
            const addLiquidityInstructions = await this.addLiquidityToPool(
                this.POOL_ADDRESS.toString(),
                0,  // 0 SOL - односторонняя ликвидность
                this.STRATEGY.LIQUIDITY_AMOUNT, // $1.4M USDC односторонняя ликвидность
                userSOL,
                userUSDC
            );

            // Удаление ВСЕЙ ликвидности (получаем обратно USDC)
            console.log('🏊 ЗАБИРАЕМ ВСЮ ЛИКВИДНОСТЬ USDC');
            const removeLiquidityInstructions = await this.removeLiquidityFromPool(
                this.POOL_ADDRESS.toString(),
                'ALL', // ВСЕ LP токены
                userSOL,
                userUSDC
            );

            console.log(`✅ Ликвидность: +${addLiquidityInstructions.length} -${removeLiquidityInstructions.length} инструкций`);
            console.log(`✅ SOL пополнение: ${solTopUpInstructions.length} инструкций`);

            // 4️⃣ СОЗДАЕМ ПРАВИЛЬНУЮ ПОСЛЕДОВАТЕЛЬНОСТЬ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
            console.log('\n🔥 СОЗДАЕМ ПРАВИЛЬНУЮ ПОСЛЕДОВАТЕЛЬНОСТЬ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ...');
            console.log('💰 CloseAccount → CreateATA → SystemTransfer → SyncNative → SecondSwap');

            // Получаем количество SOL от первого swap
            const solAmountFromFirstSwap = firstSwapResult.quote.outAmount;
            console.log(`💰 SOL от первого swap: ${solAmountFromFirstSwap.toString()}`);

            // 1. CloseAccount - закрывает WSOL token account и передает SOL на основной wallet
            const { createCloseAccountInstruction } = require('@solana/spl-token');
            const closeAccountIx = createCloseAccountInstruction(
                targetSOLAccount,         // account (WSOL token account)
                this.wallet.publicKey,    // destination (основной wallet)
                this.wallet.publicKey     // authority (владелец)
            );
            console.log('✅ CloseAccount инструкция создана');

            // 2. CreateIdempotent - создает НОВЫЙ WSOL token account
            const { createAssociatedTokenAccountIdempotentInstruction } = require('@solana/spl-token');
            const { PublicKey } = require('@solana/web3.js');
            const WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');

            const createNewATAIx = createAssociatedTokenAccountIdempotentInstruction(
                this.wallet.publicKey,    // payer
                targetSOLAccount,         // associatedToken (тот же адрес)
                this.wallet.publicKey,    // owner
                WSOL_MINT                 // mint
            );
            console.log('✅ CreateIdempotent инструкция создана');

            // 3. System Transfer - переводит SOL с основного wallet в новый WSOL token account
            const { SystemProgram } = require('@solana/web3.js');
            const systemTransferIx = SystemProgram.transfer({
                fromPubkey: this.wallet.publicKey,
                toPubkey: targetSOLAccount,
                lamports: solAmountFromFirstSwap // Точное количество SOL от первого swap
            });
            console.log('✅ System Transfer инструкция создана');

            // 4. SyncNative - синхронизирует WSOL token account с SOL балансом
            const { createSyncNativeInstruction } = require('@solana/spl-token');
            const syncNativeIx = createSyncNativeInstruction(targetSOLAccount);
            console.log('✅ SyncNative инструкция создана');

            console.log('🔥 ВСЕ ИНСТРУКЦИИ ПЕРЕДАЧИ СОЗДАНЫ ПО ОБРАЗЦУ УСПЕШНОЙ ТРАНЗАКЦИИ!');

            // 5️⃣ СОЗДАНИЕ ПОЛНОЙ ПОСЛЕДОВАТЕЛЬНОСТИ ИНСТРУКЦИЙ
            console.log('\n🔥 СБОРКА ПОЛНОЙ ПОСЛЕДОВАТЕЛЬНОСТИ...');

            // 🔥 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ:
            console.log('🔥 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ');
            console.log('💰 Последовательность: AddLiquidity → Swap1 → CloseAccount → CreateATA → SystemTransfer → SyncNative → Swap2 → RemoveLiquidity');

            // 🔥 ПРАВИЛЬНАЯ ИНИЦИАЛИЗАЦИЯ SOL TOKEN ACCOUNT ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ!
            const solInitInstructions = [];

            if (solTopUpInstructions.length > 0) {
                console.log('🔥 СОЗДАЕМ ПРАВИЛЬНУЮ ИНИЦИАЛИЗАЦИЮ SOL TOKEN ACCOUNT...');
                console.log('📋 Последовательность: createAssociatedTokenAccount → SystemTransfer → SyncNative');

                // Импортируем необходимые функции
                const {
                    createAssociatedTokenAccountInstruction,
                    getAssociatedTokenAddress,
                    createSyncNativeInstruction,
                    NATIVE_MINT
                } = require('@solana/spl-token');
                const { SystemProgram } = require('@solana/web3.js');

                // Рассчитываем сколько SOL нужно для rent
                const neededSOLLamports = ********; // Из ошибки: need ******** lamports

                // ПРОБЛЕМА: SOL TOKEN ACCOUNT ПРИНАДЛЕЖИТ SYSTEM PROGRAM, А НЕ TOKEN PROGRAM!
                console.log('🚨 ПРОБЛЕМА: SOL token account принадлежит System Program, а не Token Program!');
                console.log('🔧 РЕШЕНИЕ: Создаем правильный Associated Token Account для wrapped SOL');

                // Используем уже импортированные функции из @solana/spl-token
                // createAssociatedTokenAccountInstruction, createSyncNativeInstruction, NATIVE_MINT уже импортированы

                // ПРАВИЛЬНОЕ РЕШЕНИЕ: createAssociatedTokenAccountIdempotent!
                console.log('🔥 ПРАВИЛЬНОЕ РЕШЕНИЕ: createAssociatedTokenAccountIdempotent');
                console.log('💰 Создаст правильный ATA для wrapped SOL, если его нет');

                // Создаем правильный Associated Token Account для wrapped SOL (idempotent)
                // createAssociatedTokenAccountIdempotentInstruction и NATIVE_MINT уже импортированы
                const createATAIdempotentInstruction = createAssociatedTokenAccountIdempotentInstruction(
                    this.wallet.publicKey, // payer
                    userSOL, // associatedToken (destination)
                    this.wallet.publicKey, // owner
                    NATIVE_MINT // mint (wrapped SOL)
                );

                // Добавляем System Transfer для пополнения lamports на ATA
                const systemTransferInstruction = SystemProgram.transfer({
                    fromPubkey: this.wallet.publicKey,
                    toPubkey: userSOL, // SOL token account (ATA)
                    lamports: neededSOLLamports
                });

                // Добавляем SyncNative для правильной синхронизации wrapped SOL
                const syncNativeInstruction = createSyncNativeInstruction(userSOL);

                solInitInstructions.push(createATAIdempotentInstruction);
                solInitInstructions.push(systemTransferInstruction);
                solInitInstructions.push(syncNativeInstruction);
                // 3 инструкции: createATA + SystemTransfer + SyncNative для правильного wrapped SOL

                console.log(`✅ Создано ${solInitInstructions.length} инструкций для полной инициализации SOL token account`);
                console.log('🎯 createATA + SystemTransfer + SyncNative для правильного wrapped SOL');
            }

            const allArbitrageInstructions = [
                // 🔥 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ: SOL ПОПОЛНЕНИЕ → ИНИЦИАЛИЗАЦИЯ → ЛИКВИДНОСТЬ → АРБИТРАЖ!
                ...solTopUpInstructions,                   // 0+. АВТОМАТИЧЕСКОЕ ПОПОЛНЕНИЕ SOL ($20 USDC → SOL)
                ...solInitInstructions,                    // 1+. ИНИЦИАЛИЗАЦИЯ SOL TOKEN ACCOUNT (System Transfer + SyncNative)
                ...addLiquidityInstructions,               // N+. ДОБАВЛЯЕМ ЛИКВИДНОСТЬ (используем USDC от Flash Loan)
                ...swapResult.instructions.slice(0, 3),    // N+. Первый swap: 420K USDC → WSOL token account
                closeAccountIx,                            // N+. CloseAccount: закрывает WSOL account, передает SOL на wallet
                createNewATAIx,                            // N+. CreateIdempotent: создает НОВЫЙ WSOL token account
                systemTransferIx,                          // N+. System Transfer: переводит SOL с wallet в новый WSOL account
                syncNativeIx,                              // N+. SyncNative: синхронизирует WSOL account с SOL балансом
                ...swapResult.instructions.slice(3, 6),    // N+. Второй swap: ВСЕ WSOL → USDC (БЕЗ SyncNative)
                ...removeLiquidityInstructions             // N+. ЗАБИРАЕМ ВСЮ ЛИКВИДНОСТЬ (получаем USDC + прибыль)
            ];

            console.log(`📊 Полная последовательность: ${allArbitrageInstructions.length} инструкций`);
            console.log(`   - Swaps: ${swapResult.count}`);
            console.log(`   - Add Liquidity: ${addLiquidityInstructions.length}`);
            console.log(`   - Remove Liquidity: ${removeLiquidityInstructions.length}`);

            // 5️⃣ СОЗДАНИЕ FLASH LOAN ЧЕРЕЗ ОФИЦИАЛЬНЫЙ MARGINFI SDK
            console.log('\n🔥 СОЗДАНИЕ FLASH LOAN ЧЕРЕЗ LOW-LEVEL MARGINFI...');

            const flashLoanResult = await this.lowLevelMarginFi.createOfficialFlashLoan(
                this.STRATEGY.FLASH_LOAN_AMOUNT / 1e6, // Конвертируем в доллары
                allArbitrageInstructions // 🔥 ПЕРЕДАЕМ ПОЛНУЮ ПОСЛЕДОВАТЕЛЬНОСТЬ!
            );

            if (flashLoanResult.transaction || flashLoanResult.success) {
                console.log(`🎉 ПОЛНАЯ FLASH LOAN ТРАНЗАКЦИЯ СОЗДАНА УСПЕШНО!`);
                console.log(`📊 Flash Loan инструкций: ${flashLoanResult.instructions?.length || 'N/A'}`);
                console.log(`📊 Арбитражных инструкций: ${allArbitrageInstructions.length}`);
                console.log(`📊 Всего в транзакции: ${flashLoanResult.count || 'N/A'}`);

                // 6️⃣ ОБРАБОТКА РЕЗУЛЬТАТА ОФИЦИАЛЬНОГО SDK
                console.log('\n🔥 СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ С COMPUTE BUDGET...');

                let transaction;

                if (flashLoanResult.transaction) {
                    // Новый официальный SDK возвращает готовую VersionedTransaction
                    console.log('✅ ИСПОЛЬЗУЕМ ГОТОВУЮ VERSIONED TRANSACTION ИЗ ОФИЦИАЛЬНОГО SDK');

                    // Конвертируем VersionedTransaction в обычную Transaction для совместимости
                    transaction = new Transaction();

                    // 1-2. COMPUTE BUDGET (КАК В FLASHMeteora.js)
                    transaction.add(
                        ComputeBudgetProgram.setComputeUnitLimit({ units: 1400000 }),
                        ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 50000 })
                    );

                    // Добавляем инструкции из официального Flash Loan
                    if (flashLoanResult.instructions) {
                        const filteredInstructions = flashLoanResult.instructions.filter(ix => {
                            const programId = ix.programId.toString();
                            return programId !== 'ComputeBudget111111111111111111111111111111';
                        });
                        filteredInstructions.forEach(ix => transaction.add(ix));
                    }

                } else {
                    // Старый формат (fallback)
                    transaction = new Transaction();

                // 1-2. COMPUTE BUDGET (КАК В FLASHMeteora.js)
                transaction.add(
                    ComputeBudgetProgram.setComputeUnitLimit({ units: 1400000 }),
                    ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 50000 })
                );

                // 3-N. FLASH LOAN ИНСТРУКЦИИ (ФИЛЬТРУЕМ ДУБЛИКАТЫ COMPUTE BUDGET)
                const filteredInstructions = flashLoanResult.instructions.filter(ix => {
                    const programId = ix.programId.toString();
                    return programId !== 'ComputeBudget111111111111111111111111111111';
                });

                filteredInstructions.forEach(ix => transaction.add(ix));

                console.log(`✅ БАЗОВАЯ ТРАНЗАКЦИЯ СОЗДАНА (БЕЗ ДУБЛИКАТОВ):`);
                console.log(`   - Compute Budget: 2 инструкции`);
                console.log(`   - Flash Loan: ${filteredInstructions.length} инструкций (было ${flashLoanResult.instructions.length})`);
                console.log(`   - ВСЕГО: ${transaction.instructions.length} инструкций`);
                }

                // 🔥 ПРИМЕНЯЕМ MASTER TRANSACTION CONTROLLER С 4 ALT ТАБЛИЦАМИ
                console.log('\n🔥 ПРИМЕНЯЕМ MASTER TRANSACTION CONTROLLER С 4 ALT ТАБЛИЦАМИ...');

                // Загружаем 4 локальных ALT таблицы
                const altTables = await this.masterController.getLoadedALTTables();
                console.log(`✅ Загружено ALT таблиц: ${altTables.length}`);

                // 🚫 УБРАНО: Передача bin arrays в Master Controller
                // Все bin arrays должны быть заранее добавлены в ALT таблицы через add-meteora-to-alt.js
                if (this.liquidityBinArrays && this.liquidityBinArrays.length > 0) {
                    console.log(`💡 Найдено ${this.liquidityBinArrays.length} bin arrays - они должны быть в ALT таблицах`);
                }

                // Применяем оптимизацию через Master Controller с обфускацией
                const optimizedResult = await this.masterController.optimizeTransaction(
                    transaction.instructions,
                    altTables,
                    'FLASH_LOAN_WITH_LIQUIDITY',
                    false, // includeMarginFi = false (уже включен в инструкции)
                    {
                        enableObfuscation: true // 🎭 ВКЛЮЧАЕМ ОБФУСКАЦИЮ
                        // 🚫 УБРАНО: additionalAccounts - все аккаунты должны быть в ALT заранее
                    }
                );

                // Заменяем инструкции на оптимизированные
                transaction.instructions = optimizedResult.instructions;

                console.log(`✅ ПОЛНАЯ ОПТИМИЗИРОВАННАЯ ТРАНЗАКЦИЯ СОЗДАНА:`);
                console.log(`   🎭 Уровень обфускации: ${optimizedResult.obfuscationLevel}/5`);
                console.log(`   📊 Финальный размер: ${optimizedResult.finalSize} байт`);
                console.log(`   🗜️ ALT таблиц: ${altTables.length}`);
                console.log(`   📏 Запас размера: ${optimizedResult.margin || 'N/A'} байт`);
                console.log(`   - ИТОГО ИНСТРУКЦИЙ: ${transaction.instructions.length}`);

                return {
                    success: true,
                    transaction: transaction,
                    instructions: transaction.instructions,
                    swapCount: swapResult.count,
                    liquidityCount: addLiquidityInstructions.length + removeLiquidityInstructions.length,
                    totalCount: transaction.instructions.length,
                    // 🔥 ДАННЫЕ ОПТИМИЗАЦИИ MASTER CONTROLLER
                    optimization: {
                        level: optimizedResult.obfuscationLevel,
                        finalSize: optimizedResult.finalSize,
                        altTables: altTables.length,
                        margin: optimizedResult.margin,
                        error: optimizedResult.error
                    },
                    // 🗜️ ALT ТАБЛИЦЫ
                    altTables: altTables
                };

            } else {
                throw new Error(`Flash Loan провален: ${flashLoanResult.error || 'Неизвестная ошибка'}`);
            }

        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ ПОЛНОЙ FLASH LOAN: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🎭 ПРИМЕНЕНИЕ ДИНАМИЧЕСКОЙ ОБФУСКАЦИИ К ТРАНЗАКЦИИ
     */
    async applyObfuscation(instructions, obfuscationType = 'dynamic') {
        console.log('\n🎭 ПРИМЕНЕНИЕ ДИНАМИЧЕСКОЙ ОБФУСКАЦИИ...');
        console.log(`   Тип обфускации: ${obfuscationType}`);
        console.log(`   Базовых инструкций: ${instructions.length}`);

        try {
            let obfuscationResult;

            if (obfuscationType === 'dynamic') {
                // 🔥 ДИНАМИЧЕСКАЯ ОБФУСКАЦИЯ (РЕКОМЕНДУЕТСЯ)
                console.log('🔥 ИСПОЛЬЗУЕМ ДИНАМИЧЕСКУЮ ОБФУСКАЦИЮ...');
                obfuscationResult = await this.dynamicObfuscator.applyDynamicObfuscation(instructions);

                console.log(`✅ ДИНАМИЧЕСКАЯ ОБФУСКАЦИЯ ПРИМЕНЕНА:`);
                console.log(`   🎭 Уровень: ${obfuscationResult.obfuscationLevel}/5`);
                console.log(`   📊 Размер: ${obfuscationResult.finalSize} байт`);
                console.log(`   🔧 Добавлено инструкций: ${obfuscationResult.obfuscationInstructions || 0}`);
                console.log(`   📏 Запас: ${obfuscationResult.margin} байт`);

            } else if (obfuscationType === 'custom') {
                // 🛡️ КАСТОМНАЯ ОБФУСКАЦИЯ (МАКСИМАЛЬНАЯ МАСКИРОВКА)
                console.log('🛡️ ИСПОЛЬЗУЕМ КАСТОМНУЮ ОБФУСКАЦИЮ...');

                // Создаем временную транзакцию для обфускации
                const tempTransaction = new Transaction();
                tempTransaction.instructions = instructions;

                const obfuscatedTx = await this.customObfuscator.applyMaximumObfuscation(
                    tempTransaction,
                    20000 // priority fee
                );

                obfuscationResult = {
                    instructions: obfuscatedTx.instructions,
                    obfuscationLevel: 5, // Максимальный уровень
                    finalSize: this.estimateTransactionSize(obfuscatedTx.instructions),
                    obfuscationInstructions: obfuscatedTx.instructions.length - instructions.length
                };

                console.log(`✅ КАСТОМНАЯ ОБФУСКАЦИЯ ПРИМЕНЕНА:`);
                console.log(`   🎭 Уровень: MAXIMUM (5/5)`);
                console.log(`   📊 Размер: ${obfuscationResult.finalSize} байт`);
                console.log(`   🔧 Добавлено инструкций: ${obfuscationResult.obfuscationInstructions}`);

            } else {
                throw new Error(`Неподдерживаемый тип обфускации: ${obfuscationType}`);
            }

            return obfuscationResult;

        } catch (error) {
            console.error(`❌ Ошибка применения обфускации: ${error.message}`);

            // 🔧 FALLBACK: Возвращаем исходные инструкции
            console.log('🔧 FALLBACK: Используем исходные инструкции без обфускации');
            return {
                instructions: instructions,
                obfuscationLevel: 0,
                finalSize: this.estimateTransactionSize(instructions),
                obfuscationInstructions: 0,
                error: error.message
            };
        }
    }

    /**
     * 📏 ТОЧНАЯ ОЦЕНКА РАЗМЕРА ТРАНЗАКЦИИ
     */
    estimateTransactionSize(instructions) {
        // Точный расчет размера транзакции
        let size = 64; // Точный размер заголовка транзакции

        instructions.forEach(ix => {
            // 1 байт - индекс программы
            size += 1;

            // 1 байт - количество аккаунтов
            size += 1;

            // По 1 байту на каждый аккаунт (индекс в таблице аккаунтов)
            size += (ix.keys?.length || 0);

            // 4 байта - длина данных
            size += 4;

            // Данные инструкции
            size += (ix.data?.length || 0);
        });

        return size;
    }

    /**
     * 🧪 ТЕСТ ПОЛНОЙ СИСТЕМЫ С ОБФУСКАЦИЕЙ
     */
    async testCompleteSystem() {
        console.log('\n🧪 ТЕСТ ПОЛНОЙ СИСТЕМЫ С ЛИКВИДНОСТЬЮ...');

        try {
            // Создаем полную транзакцию
            const result = await this.createCompleteFlashLoanTransaction();

            if (result.success) {
                console.log(`🎉 ПОЛНАЯ СИСТЕМА С ЛИКВИДНОСТЬЮ И 4 ALT ТАБЛИЦАМИ РАБОТАЕТ!`);
                console.log(`✅ Swap инструкций: ${result.swapCount}`);
                console.log(`✅ Liquidity инструкций: ${result.liquidityCount}`);
                console.log(`✅ Всего инструкций: ${result.totalCount}`);
                console.log(`✅ Flash Loan: СОЗДАН`);
                console.log(`✅ Транзакция: ГОТОВА`);

                // 🔥 ОТОБРАЖАЕМ ДАННЫЕ ОПТИМИЗАЦИИ MASTER CONTROLLER
                if (result.optimization) {
                    console.log(`\n🔥 MASTER CONTROLLER ОПТИМИЗАЦИЯ ПРИМЕНЕНА:`);
                    console.log(`   🎯 Уровень обфускации: ${result.optimization.level}/5`);
                    console.log(`   📊 Размер: ${result.optimization.finalSize} байт`);
                    console.log(`   🗜️ ALT таблиц: ${result.optimization.altTables}`);
                    console.log(`   📏 Запас: ${result.optimization.margin || 'N/A'} байт`);

                    if (result.optimization.error) {
                        console.log(`   ⚠️ Ошибка: ${result.optimization.error}`);
                    }
                }

                // 🗜️ ОТОБРАЖАЕМ ДАННЫЕ ALT ТАБЛИЦ
                if (result.altTables) {
                    console.log(`\n🗜️ ALT ТАБЛИЦЫ ЗАГРУЖЕНЫ:`);
                    result.altTables.forEach((alt, index) => {
                        const addressCount = alt.state?.addresses?.length || 0;
                        console.log(`   ${index + 1}. ${alt.key.toString().slice(0, 8)}... (${addressCount} адресов)`);
                    });
                }

                // 🧪 СИМУЛЯЦИЯ (ОПЦИОНАЛЬНО)
                if (result.transaction) {
                    console.log('\n🧪 ЗАПУСК СИМУЛЯЦИИ...');

                    try {
                        // 🔥 ПОДГОТАВЛИВАЕМ ТРАНЗАКЦИЮ ДЛЯ СИМУЛЯЦИИ
                        const { blockhash } = await this.connection.getLatestBlockhash();
                        result.transaction.recentBlockhash = blockhash;
                        result.transaction.feePayer = this.wallet.publicKey;

                        // 🔥 СИМУЛЯЦИЯ С ПРАВИЛЬНЫМИ ПАРАМЕТРАМИ
                        const simulation = await this.connection.simulateTransaction(
                            result.transaction,
                            {
                                sigVerify: false,
                                commitment: 'processed',
                                replaceRecentBlockhash: true
                            }
                        );

                        if (simulation.value.err) {
                            console.log(`❌ Симуляция провалена: ${JSON.stringify(simulation.value.err)}`);
                            console.log(`📊 Logs:`, simulation.value.logs?.slice(-5) || []);
                        } else {
                            console.log(`✅ СИМУЛЯЦИЯ УСПЕШНА!`);
                            console.log(`   Compute Units: ${simulation.value.unitsConsumed || 'N/A'}`);
                            console.log(`   Logs: ${simulation.value.logs?.length || 0} записей`);
                        }
                    } catch (simError) {
                        console.log(`⚠️ Симуляция недоступна: ${simError.message}`);
                    }
                }

                return {
                    success: true,
                    instructions: result.instructions,
                    altTables: altTables, // 🔥 ПЕРЕДАЕМ ALT ТАБЛИЦЫ!
                    result
                };
            } else {
                console.log(`❌ СИСТЕМА НЕ РАБОТАЕТ: ${result.error}`);
                return { success: false, error: result.error };
            }

        } catch (error) {
            console.error(`💥 КРИТИЧЕСКАЯ ОШИБКА: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

// 🚀 ЗАПУСК
if (require.main === module) {
    async function main() {
        const flashLoan = new CompleteFlashLoanWithLiquidity();

        try {
            await flashLoan.initialize();
            const result = await flashLoan.testCompleteSystem();

            if (result.success) {
                console.log('\n🎉 ПОЛНАЯ СИСТЕМА С ЛИКВИДНОСТЬЮ РАБОТАЕТ ИДЕАЛЬНО!');
                console.log('🔥 ГОТОВА К РЕАЛЬНОМУ ИСПОЛЬЗОВАНИЮ!');
            } else {
                console.log('\n❌ СИСТЕМА ТРЕБУЕТ ДОПОЛНИТЕЛЬНЫХ ИСПРАВЛЕНИЙ');
            }

        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        }
    }

    main().catch(console.error);
}

module.exports = CompleteFlashLoanWithLiquidity;
