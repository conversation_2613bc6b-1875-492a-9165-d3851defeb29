#!/usr/bin/env node

/**
 * 🧮 КАЛЬКУЛЯТОР РАЗМЕРА ТРАНЗАКЦИИ ПЕРЕД СЕРИАЛИЗАЦИЕЙ
 */

const { Connection, PublicKey, Keypair, TransactionMessage } = require('@solana/web3.js');
const MasterTransactionController = require('./master-transaction-controller');
const fs = require('fs');
const path = require('path');

function calculateTransactionSize(staticKeys, lookupKeys, altTables, instructions) {
  // Структура V0 транзакции:
  // 1. Заголовок: ~64 байта
  // 2. Статические ключи: количество * 32 байта
  // 3. Lookup ключи: количество * 1 байт (индексы)
  // 4. ALT ссылки: количество_таблиц * 40 байт
  // 5. Инструкции: количество * ~50 байт (средний размер)
  
  const headerSize = 64;
  const staticKeysSize = staticKeys * 32;
  const lookupKeysSize = lookupKeys * 1;
  const altTablesSize = altTables * 40;
  const instructionsSize = instructions * 50;
  
  const totalSize = headerSize + staticKeysSize + lookupKeysSize + altTablesSize + instructionsSize;
  
  return {
    header: headerSize,
    staticKeys: staticKeysSize,
    lookupKeys: lookupKeysSize,
    altTables: altTablesSize,
    instructions: instructionsSize,
    total: totalSize,
    limit: 1232,
    fits: totalSize <= 1232,
    margin: 1232 - totalSize
  };
}

async function testTransactionSizes() {
  try {
    // Загружаем wallet
    function base58Decode(str) {
      const alphabet = '**********************************************************';
      let decoded = 0n;
      let multi = 1n;
      for (let i = str.length - 1; i >= 0; i--) {
        const char = str[i];
        const index = alphabet.indexOf(char);
        if (index === -1) throw new Error(`Invalid character: ${char}`);
        decoded += BigInt(index) * multi;
        multi *= 58n;
      }
      const bytes = [];
      while (decoded > 0n) {
        bytes.unshift(Number(decoded % 256n));
        decoded = decoded / 256n;
      }
      for (let i = 0; i < str.length && str[i] === '1'; i++) {
        bytes.unshift(0);
      }
      return new Uint8Array(bytes);
    }
    
    const envPath = path.join(__dirname, '.env.solana');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const walletMatch = envContent.match(/WALLET_PRIVATE_KEY=([^\n\r]+)/);
    const privateKeyBase58 = walletMatch[1].trim();
    const privateKeyBytes = base58Decode(privateKeyBase58);
    const wallet = Keypair.fromSecretKey(privateKeyBytes);

    const connection = new Connection('https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/', 'confirmed');
    const controller = new MasterTransactionController(connection, wallet);
    
    console.log('🧮 КАЛЬКУЛЯТОР РАЗМЕРА ТРАНЗАКЦИИ');
    console.log('═══════════════════════════════════════════════════════════');

    // Получаем Jupiter swaps
    console.log('📋 Получаем Jupiter данные...');
    
    const swap1Quote = await fetch(`https://quote-api.jup.ag/v6/quote?inputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&outputMint=So11111111111111111111111111111111111111112&amount=*********&slippageBps=50`);
    const swap1Data = await swap1Quote.json();
    
    const swap1Body = {
      quoteResponse: swap1Data,
      userPublicKey: wallet.publicKey.toString(),
      wrapAndUnwrapSol: true,
      skipUserAccountsRpcCalls: false,
      dynamicComputeUnitLimit: true
    };
    
    const swap1Response = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(swap1Body)
    });
    const swap1Instructions = await swap1Response.json();
    
    const swap2Quote = await fetch(`https://quote-api.jup.ag/v6/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=*********0&slippageBps=50`);
    const swap2Data = await swap2Quote.json();
    
    const swap2Body = {
      quoteResponse: swap2Data,
      userPublicKey: wallet.publicKey.toString(),
      wrapAndUnwrapSol: true,
      skipUserAccountsRpcCalls: false,
      dynamicComputeUnitLimit: true
    };
    
    const swap2Response = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(swap2Body)
    });
    const swap2Instructions = await swap2Response.json();

    // Обрабатываем
    const jupiterResult = {
      swap1: {
        swapData: swap1Instructions,
        setupInstructions: swap1Instructions.setupInstructions || [],
        swapInstruction: swap1Instructions.swapInstruction,
        cleanupInstruction: swap1Instructions.cleanupInstruction,
        addressLookupTableAddresses: swap1Instructions.addressLookupTableAddresses || []
      },
      swap2: {
        swapData: swap2Instructions,
        setupInstructions: swap2Instructions.setupInstructions || [],
        swapInstruction: swap2Instructions.swapInstruction,
        cleanupInstruction: swap2Instructions.cleanupInstruction,
        addressLookupTableAddresses: swap2Instructions.addressLookupTableAddresses || []
      }
    };
    
    const processed = await controller.processJupiterInstructions(jupiterResult);
    const jupiterALT = await controller.loadAddressLookupTables(processed.altAddresses);

    // Flash Loan симуляция
    const flashLoanKeys = [];
    for (let i = 0; i < 44; i++) {
      flashLoanKeys.push(Keypair.generate().publicKey);
    }
    
    const flashLoanInstructions = [
      {
        programId: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'),
        keys: flashLoanKeys.slice(0, 15).map(key => ({ pubkey: key, isSigner: false, isWritable: true })),
        data: Buffer.alloc(8)
      },
      {
        programId: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'),
        keys: flashLoanKeys.slice(15, 30).map(key => ({ pubkey: key, isSigner: false, isWritable: true })),
        data: Buffer.alloc(8)
      },
      {
        programId: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'),
        keys: flashLoanKeys.slice(30, 44).map(key => ({ pubkey: key, isSigner: false, isWritable: true })),
        data: Buffer.alloc(8)
      }
    ];
    
    const allInstructions = [
      ...flashLoanInstructions,
      ...processed.instructions
    ];

    console.log(`✅ Данные получены`);
    console.log(`   Flash Loan инструкций: ${flashLoanInstructions.length}`);
    console.log(`   Jupiter инструкций: ${processed.instructions.length}`);
    console.log(`   ВСЕГО инструкций: ${allInstructions.length}`);
    console.log(`   Jupiter ALT таблиц: ${jupiterALT.length}`);

    // СОЗДАЕМ LEGACY MESSAGE ДЛЯ ПОДСЧЕТА ВСЕХ КЛЮЧЕЙ
    const { blockhash } = await connection.getLatestBlockhash();
    
    const legacyMessage = new TransactionMessage({
      payerKey: wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: allInstructions
    }).compileToLegacyMessage();
    
    const totalKeys = legacyMessage.accountKeys.length;
    
    console.log(`\n📊 БАЗОВЫЕ ДАННЫЕ:`);
    console.log(`   Всего ключей: ${totalKeys}`);
    console.log(`   Инструкций: ${allInstructions.length}`);
    console.log(`   Jupiter ALT: ${jupiterALT.length} таблиц`);

    // СЦЕНАРИЙ 1: БЕЗ ALT (LEGACY)
    console.log(`\n🔍 СЦЕНАРИЙ 1: БЕЗ ALT (LEGACY)`);
    const legacySize = calculateTransactionSize(totalKeys, 0, 0, allInstructions.length);
    
    console.log(`   Заголовок: ${legacySize.header} байт`);
    console.log(`   Статические ключи: ${totalKeys} × 32 = ${legacySize.staticKeys} байт`);
    console.log(`   Lookup ключи: 0 байт`);
    console.log(`   ALT таблицы: 0 байт`);
    console.log(`   Инструкции: ${allInstructions.length} × 50 = ${legacySize.instructions} байт`);
    console.log(`   ИТОГО: ${legacySize.total} байт`);
    console.log(`   Поместится: ${legacySize.fits ? 'ДА' : 'НЕТ'}`);
    console.log(`   Запас/Превышение: ${legacySize.margin} байт`);

    // СЦЕНАРИЙ 2: С JUPITER ALT (ОПТИМИСТИЧНЫЙ)
    console.log(`\n🔍 СЦЕНАРИЙ 2: С JUPITER ALT (ОПТИМИСТИЧНЫЙ 70% СЖАТИЕ)`);
    const compressedKeys = Math.floor(totalKeys * 0.7); // 70% сжимаются
    const staticKeys = totalKeys - compressedKeys; // 30% остаются статическими
    
    const jupiterAltSize = calculateTransactionSize(staticKeys, compressedKeys, jupiterALT.length, allInstructions.length);
    
    console.log(`   Заголовок: ${jupiterAltSize.header} байт`);
    console.log(`   Статические ключи: ${staticKeys} × 32 = ${jupiterAltSize.staticKeys} байт`);
    console.log(`   Lookup ключи: ${compressedKeys} × 1 = ${jupiterAltSize.lookupKeys} байт`);
    console.log(`   ALT таблицы: ${jupiterALT.length} × 40 = ${jupiterAltSize.altTables} байт`);
    console.log(`   Инструкции: ${allInstructions.length} × 50 = ${jupiterAltSize.instructions} байт`);
    console.log(`   ИТОГО: ${jupiterAltSize.total} байт`);
    console.log(`   Поместится: ${jupiterAltSize.fits ? 'ДА' : 'НЕТ'}`);
    console.log(`   Запас/Превышение: ${jupiterAltSize.margin} байт`);

    // СЦЕНАРИЙ 3: С JUPITER ALT (РЕАЛИСТИЧНЫЙ)
    console.log(`\n🔍 СЦЕНАРИЙ 3: С JUPITER ALT (РЕАЛИСТИЧНЫЙ 50% СЖАТИЕ)`);
    const realisticCompressed = Math.floor(totalKeys * 0.5); // 50% сжимаются
    const realisticStatic = totalKeys - realisticCompressed; // 50% остаются статическими
    
    const realisticSize = calculateTransactionSize(realisticStatic, realisticCompressed, jupiterALT.length, allInstructions.length);
    
    console.log(`   Заголовок: ${realisticSize.header} байт`);
    console.log(`   Статические ключи: ${realisticStatic} × 32 = ${realisticSize.staticKeys} байт`);
    console.log(`   Lookup ключи: ${realisticCompressed} × 1 = ${realisticSize.lookupKeys} байт`);
    console.log(`   ALT таблицы: ${jupiterALT.length} × 40 = ${realisticSize.altTables} байт`);
    console.log(`   Инструкции: ${allInstructions.length} × 50 = ${realisticSize.instructions} байт`);
    console.log(`   ИТОГО: ${realisticSize.total} байт`);
    console.log(`   Поместится: ${realisticSize.fits ? 'ДА' : 'НЕТ'}`);
    console.log(`   Запас/Превышение: ${realisticSize.margin} байт`);

    // СЦЕНАРИЙ 4: С СОБСТВЕННОЙ ALT (90% СЖАТИЕ)
    console.log(`\n🔍 СЦЕНАРИЙ 4: С СОБСТВЕННОЙ ALT (90% СЖАТИЕ)`);
    const customCompressed = Math.floor(totalKeys * 0.9); // 90% сжимаются
    const customStatic = totalKeys - customCompressed; // 10% остаются статическими
    const customAltTables = jupiterALT.length + 1; // +1 собственная ALT
    
    const customSize = calculateTransactionSize(customStatic, customCompressed, customAltTables, allInstructions.length);
    
    console.log(`   Заголовок: ${customSize.header} байт`);
    console.log(`   Статические ключи: ${customStatic} × 32 = ${customSize.staticKeys} байт`);
    console.log(`   Lookup ключи: ${customCompressed} × 1 = ${customSize.lookupKeys} байт`);
    console.log(`   ALT таблицы: ${customAltTables} × 40 = ${customSize.altTables} байт`);
    console.log(`   Инструкции: ${allInstructions.length} × 50 = ${customSize.instructions} байт`);
    console.log(`   ИТОГО: ${customSize.total} байт`);
    console.log(`   Поместится: ${customSize.fits ? 'ДА' : 'НЕТ'}`);
    console.log(`   Запас/Превышение: ${customSize.margin} байт`);

    // ИТОГОВЫЕ ВЫВОДЫ
    console.log(`\n📊 ИТОГОВЫЕ ВЫВОДЫ:`);
    console.log(`   Legacy (без ALT): ${legacySize.fits ? '✅' : '❌'} ${legacySize.total} байт`);
    console.log(`   Jupiter ALT 70%: ${jupiterAltSize.fits ? '✅' : '❌'} ${jupiterAltSize.total} байт`);
    console.log(`   Jupiter ALT 50%: ${realisticSize.fits ? '✅' : '❌'} ${realisticSize.total} байт`);
    console.log(`   Собственная ALT 90%: ${customSize.fits ? '✅' : '❌'} ${customSize.total} байт`);
    
    if (customSize.fits) {
      console.log(`\n🎉 РЕШЕНИЕ: СОБСТВЕННАЯ ALT РАБОТАЕТ!`);
      console.log(`   Запас: ${customSize.margin} байт`);
    } else {
      console.log(`\n⚠️ НУЖНА ДОПОЛНИТЕЛЬНАЯ ОПТИМИЗАЦИЯ!`);
    }
    
  } catch (error) {
    console.error(`❌ Ошибка: ${error.message}`);
  }
}

testTransactionSizes().catch(console.error);
