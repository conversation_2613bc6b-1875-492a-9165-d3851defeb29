#!/usr/bin/env node

/**
 * 🔧 COMPLETE FLASH LOAN ADAPTER
 * 
 * Адаптер для интеграции complete-flash-loan-structure.js с существующей системой
 * Обеспечивает совместимость со всеми существующими интерфейсами
 * 
 * ПРИНЦИПЫ:
 * - НЕ ЛОМАТЬ существующую функциональность
 * - СОХРАНИТЬ все существующие интерфейсы
 * - ТОЛЬКО ПЕРЕНАПРАВЛЯТЬ вызовы на новый сборщик
 */

const CompleteFlashLoanStructure = require('./complete-flash-loan-structure.js');
const { PublicKey } = require('@solana/web3.js');

class CompleteFlashLoanAdapter {
    constructor(connection, wallet, marginfiAccount) {
        console.log('🔧 Инициализация Complete Flash Loan Adapter...');
        
        this.connection = connection;
        this.wallet = wallet;
        this.marginfiAccount = marginfiAccount;
        
        // Создаем экземпляр нашего нового сборщика
        this.completeStructure = new CompleteFlashLoanStructure(
            wallet,
            marginfiAccount,
            connection
        );
        
        console.log('✅ Complete Flash Loan Adapter готов');
    }

    /**
     * 🔄 СОВМЕСТИМОСТЬ С MASTER-TRANSACTION-CONTROLLER
     * Метод: buildFlashLoanTx()
     */
    async buildFlashLoanTx(marginfiAccount, instructions, altTables, options = {}) {
        console.log('🔄 [ADAPTER] buildFlashLoanTx() - перенаправление на новый сборщик...');
        
        try {
            // Конвертируем параметры в формат нового сборщика
            const params = this.convertMasterControllerParams(instructions, options);
            
            // Вызываем новый сборщик
            const result = await this.completeStructure.createCompleteFlashLoanTransactionWithALT();
            
            // Конвертируем результат в ожидаемый формат
            return this.convertToMasterControllerFormat(result);
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка в buildFlashLoanTx:', error.message);
            throw error;
        }
    }

    /**
     * 🔄 СОВМЕСТИМОСТЬ С ATOMIC-TRANSACTION-BUILDER
     * Метод: createFlashLoanTransaction()
     */
    async createFlashLoanTransaction(marginfiAccount, amount, bank, instructions, altTables) {
        console.log('🔄 [ADAPTER] createFlashLoanTransaction() - перенаправление на новый сборщик...');
        
        try {
            // Конвертируем параметры
            const params = this.convertAtomicBuilderParams(amount, bank, instructions);
            
            // Вызываем новый сборщик
            const result = await this.completeStructure.createCompleteFlashLoanTransactionWithALT();
            
            // Конвертируем результат
            return this.convertToAtomicBuilderFormat(result);
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка в createFlashLoanTransaction:', error.message);
            throw error;
        }
    }

    /**
     * 🔄 СОВМЕСТИМОСТЬ С REAL-MARGINFI-DLMM-EXECUTOR
     * Метод: executeArbitrage()
     */
    async executeArbitrage(params = {}) {
        console.log('🔄 [ADAPTER] executeArbitrage() - перенаправление на новый сборщик...');
        
        try {
            // Конвертируем параметры
            const convertedParams = this.convertExecutorParams(params);
            
            // Вызываем новый сборщик
            const result = await this.completeStructure.createCompleteFlashLoanTransactionWithALT();
            
            // Конвертируем результат
            return this.convertToExecutorFormat(result);
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка в executeArbitrage:', error.message);
            throw error;
        }
    }

    /**
     * 🔄 СОВМЕСТИМОСТЬ С SOLANA-TRANSACTION-BUILDER
     * Метод: buildCompleteTransaction()
     */
    async buildCompleteTransaction(calculationResult) {
        console.log('🔄 [ADAPTER] buildCompleteTransaction() - перенаправление на новый сборщик...');
        
        try {
            // Конвертируем параметры
            const params = this.convertSolanaBuilderParams(calculationResult);
            
            // Вызываем новый сборщик
            const result = await this.completeStructure.createCompleteFlashLoanTransactionWithALT();
            
            // Конвертируем результат
            return this.convertToSolanaBuilderFormat(result);
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка в buildCompleteTransaction:', error.message);
            throw error;
        }
    }

    /**
     * 🔄 УНИВЕРСАЛЬНЫЙ МЕТОД ДЛЯ ВСЕХ СЛУЧАЕВ
     */
    async createTransaction(params = {}) {
        console.log('🔄 [ADAPTER] createTransaction() - универсальный метод...');
        
        try {
            // Определяем тип вызова по параметрам
            const callType = this.detectCallType(params);
            console.log(`🔍 [ADAPTER] Определен тип вызова: ${callType}`);
            
            // Вызываем соответствующий метод
            switch (callType) {
                case 'master-controller':
                    return await this.buildFlashLoanTx(params.marginfiAccount, params.instructions, params.altTables, params.options);
                
                case 'atomic-builder':
                    return await this.createFlashLoanTransaction(params.marginfiAccount, params.amount, params.bank, params.instructions, params.altTables);
                
                case 'executor':
                    return await this.executeArbitrage(params);
                
                case 'solana-builder':
                    return await this.buildCompleteTransaction(params.calculationResult);
                
                default:
                    // Прямой вызов нового сборщика
                    return await this.completeStructure.createCompleteFlashLoanTransactionWithALT();
            }
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка в createTransaction:', error.message);
            throw error;
        }
    }

    // ========================================
    // МЕТОДЫ КОНВЕРТАЦИИ ПАРАМЕТРОВ
    // ========================================

    convertMasterControllerParams(instructions, options) {
        return {
            borrowAmountUSDC: options.borrowAmountUSDC || 2500000,
            borrowAmountSOL: options.borrowAmountSOL || *************,
            meteoraPool: options.meteoraPool || '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            customInstructions: instructions || []
        };
    }

    convertAtomicBuilderParams(amount, bank, instructions) {
        return {
            borrowAmountUSDC: bank === 'USDC' ? amount : 2500000,
            borrowAmountSOL: bank === 'SOL' ? amount : *************,
            meteoraPool: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            customInstructions: instructions || []
        };
    }

    convertExecutorParams(params) {
        return {
            borrowAmountUSDC: params.usdcAmount || 2500000,
            borrowAmountSOL: params.solAmount || *************,
            meteoraPool: params.pool || '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
        };
    }

    convertSolanaBuilderParams(calculationResult) {
        return {
            borrowAmountUSDC: calculationResult?.transactionParams?.flashLoan?.amount || 2500000,
            borrowAmountSOL: calculationResult?.transactionParams?.flashLoan?.solAmount || *************,
            meteoraPool: calculationResult?.transactionParams?.pool || '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
        };
    }

    // ========================================
    // МЕТОДЫ КОНВЕРТАЦИИ РЕЗУЛЬТАТОВ
    // ========================================

    convertToMasterControllerFormat(result) {
        return {
            transaction: result.transaction,
            addressLookupTables: result.addressLookupTables,
            instructions: result.instructions,
            signers: result.signers,
            success: true,
            metadata: {
                instructionCount: result.instructions?.length || 0,
                altCount: result.addressLookupTables?.length || 0,
                source: 'complete-flash-loan-structure'
            }
        };
    }

    convertToAtomicBuilderFormat(result) {
        return {
            flashLoanTx: result.transaction,
            altTables: result.addressLookupTables,
            signers: result.signers,
            success: true,
            instructionCount: result.instructions?.length || 0
        };
    }

    convertToExecutorFormat(result) {
        return {
            transaction: result.transaction,
            signature: null, // Будет заполнено после отправки
            success: true,
            profit: 0, // Будет рассчитано после выполнения
            executionTime: Date.now(),
            metadata: result.metadata
        };
    }

    convertToSolanaBuilderFormat(result) {
        return {
            transactionData: {
                transaction: result.transaction,
                addressLookupTables: result.addressLookupTables,
                signers: result.signers
            },
            success: true,
            instructionCount: result.instructions?.length || 0,
            estimatedFee: 0.01 // SOL
        };
    }

    // ========================================
    // ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ
    // ========================================

    detectCallType(params) {
        if (params.instructions && params.altTables && params.options) {
            return 'master-controller';
        }
        if (params.amount && params.bank) {
            return 'atomic-builder';
        }
        if (params.usdcAmount || params.solAmount) {
            return 'executor';
        }
        if (params.calculationResult) {
            return 'solana-builder';
        }
        return 'direct';
    }

    /**
     * 🔍 ДИАГНОСТИКА И ОТЛАДКА
     */
    async validateIntegration() {
        console.log('🔍 [ADAPTER] Проверка интеграции...');
        
        try {
            // Проверяем доступность нового сборщика
            if (!this.completeStructure) {
                throw new Error('CompleteFlashLoanStructure не инициализирован');
            }
            
            // Проверяем основные методы
            const methods = ['createCompleteFlashLoanTransactionWithALT'];
            for (const method of methods) {
                if (typeof this.completeStructure[method] !== 'function') {
                    throw new Error(`Метод ${method} не найден в CompleteFlashLoanStructure`);
                }
            }
            
            console.log('✅ [ADAPTER] Интеграция валидна');
            return true;
            
        } catch (error) {
            console.error('❌ [ADAPTER] Ошибка валидации:', error.message);
            return false;
        }
    }
}

module.exports = CompleteFlashLoanAdapter;
