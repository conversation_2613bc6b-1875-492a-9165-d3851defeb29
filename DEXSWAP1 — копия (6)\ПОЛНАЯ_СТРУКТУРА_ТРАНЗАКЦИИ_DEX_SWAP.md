# 🚀 ПОЛНАЯ СТРУКТУРА ТРАНЗАКЦИИ DEX SWAP

## 📊 ОБЩАЯ АРХИТЕКТУРА СИСТЕМЫ

### 🏗️ Основные Компоненты:
```
DEX SWAP СИСТЕМА
├── 1. FLASH LOAN ТРАНЗАКЦИИ (MarginFi)
├── 2. ARBITRAGE ТРАНЗАКЦИИ (Multi-DEX)
├── 3. METEORA DLMM SWAPS
├── 4. JUPITER AGGREGATOR SWAPS
├── 5. ORCA WHIRLPOOL SWAPS
├── 6. RAYDIUM CLMM SWAPS
└── 7. ATOMIC TRANSACTION BUILDER
```

---

## 🔥 1. FLASH LOAN ARBITRAGE ТРАНЗАКЦИЯ

### 📋 Структура (4 инструкции):
```typescript
Transaction {
  instructions: [
    1. lending_account_start_flashloan    // MarginFi Start
    2. swap_instruction_buy               // DEX Buy (дешево)
    3. swap_instruction_sell              // DEX Sell (дорого)
    4. lending_account_end_flashloan      // MarginFi End
  ],
  recentBlockhash: string,
  feePayer: PublicKey,
  signatures: Signature[]
}
```

### 🔧 Инструкция #1: START FLASH LOAN
```typescript
{
  programId: "MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA",
  keys: [
    { pubkey: marginfiAccount, isSigner: false, isWritable: true },
    { pubkey: marginfiGroup, isSigner: false, isWritable: false },
    { pubkey: signer, isSigner: true, isWritable: false },
    { pubkey: bank, isSigner: false, isWritable: true },
    { pubkey: signerTokenAccount, isSigner: false, isWritable: true },
    { pubkey: bankLiquidityVault, isSigner: false, isWritable: true },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: bankLiquidityVaultAuthority, isSigner: false, isWritable: false }
  ],
  data: Buffer.from([
    0x8c, 0x97, 0x25, 0x8c, 0x17, 0x39, 0x0f, 0x5c, // Discriminator
    ...amountBytes,                                    // Amount (8 bytes)
    ...endIndexBytes                                   // End Index (4 bytes)
  ])
}
```

### 🔧 Инструкция #2: BUY SWAP (Meteora DLMM)
```typescript
{
  programId: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo",
  keys: [
    { pubkey: lbPair, isSigner: false, isWritable: true },
    { pubkey: binArrayBitmapExtension, isSigner: false, isWritable: true },
    { pubkey: reserveX, isSigner: false, isWritable: true },
    { pubkey: reserveY, isSigner: false, isWritable: true },
    { pubkey: userTokenX, isSigner: false, isWritable: true },
    { pubkey: userTokenY, isSigner: false, isWritable: true },
    { pubkey: tokenXMint, isSigner: false, isWritable: false },
    { pubkey: tokenYMint, isSigner: false, isWritable: false },
    { pubkey: oracle, isSigner: false, isWritable: false },
    { pubkey: hostFeeAccount, isSigner: false, isWritable: true },
    { pubkey: user, isSigner: true, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: PROGRAM_ID, isSigner: false, isWritable: false },
    // Bin Arrays (динамические)
    ...binArrayAccounts
  ],
  data: Buffer.from([
    0x41, 0x4b, 0x3f, 0x4c, 0xeb, 0x5b, 0x5b, 0x88, // Discriminator
    ...amountInBytes,                                  // Amount In (8 bytes)
    ...minAmountOutBytes                              // Min Amount Out (8 bytes)
  ])
}
```

### 🔧 Инструкция #3: SELL SWAP (Meteora DLMM)
```typescript
{
  programId: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo",
  keys: [
    // Аналогично BUY SWAP, но с обратным направлением
    { pubkey: lbPair, isSigner: false, isWritable: true },
    { pubkey: binArrayBitmapExtension, isSigner: false, isWritable: true },
    { pubkey: reserveX, isSigner: false, isWritable: true },
    { pubkey: reserveY, isSigner: false, isWritable: true },
    { pubkey: userTokenX, isSigner: false, isWritable: true },
    { pubkey: userTokenY, isSigner: false, isWritable: true },
    { pubkey: tokenXMint, isSigner: false, isWritable: false },
    { pubkey: tokenYMint, isSigner: false, isWritable: false },
    { pubkey: oracle, isSigner: false, isWritable: false },
    { pubkey: hostFeeAccount, isSigner: false, isWritable: true },
    { pubkey: user, isSigner: true, isWritable: false },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: PROGRAM_ID, isSigner: false, isWritable: false },
    ...binArrayAccounts
  ],
  data: Buffer.from([
    0x41, 0x4b, 0x3f, 0x4c, 0xeb, 0x5b, 0x5b, 0x88, // Discriminator
    ...amountInBytes,                                  // Amount In (8 bytes)
    ...minAmountOutBytes                              // Min Amount Out (8 bytes)
  ])
}
```

### 🔧 Инструкция #4: END FLASH LOAN
```typescript
{
  programId: "MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA",
  keys: [
    { pubkey: marginfiAccount, isSigner: false, isWritable: true },
    { pubkey: marginfiGroup, isSigner: false, isWritable: false },
    { pubkey: signer, isSigner: true, isWritable: false },
    { pubkey: bank, isSigner: false, isWritable: true },
    { pubkey: signerTokenAccount, isSigner: false, isWritable: true },
    { pubkey: bankLiquidityVault, isSigner: false, isWritable: true },
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
    { pubkey: bankLiquidityVaultAuthority, isSigner: false, isWritable: false }
  ],
  data: Buffer.from([
    0x8c, 0x97, 0x25, 0x8c, 0x17, 0x39, 0x0f, 0x5c  // Discriminator
  ])
}
```

---

## 🌟 2. JUPITER AGGREGATOR SWAP

### 📋 Структура Jupiter Swap:
```typescript
{
  programId: "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
  keys: [
    { pubkey: tokenProgram, isSigner: false, isWritable: false },
    { pubkey: userTransferAuthority, isSigner: true, isWritable: false },
    { pubkey: userSourceTokenAccount, isSigner: false, isWritable: true },
    { pubkey: userDestinationTokenAccount, isSigner: false, isWritable: true },
    { pubkey: destinationTokenAccount, isSigner: false, isWritable: true },
    { pubkey: sourceMint, isSigner: false, isWritable: false },
    { pubkey: destinationMint, isSigner: false, isWritable: false },
    { pubkey: platformFeeAccount, isSigner: false, isWritable: true },
    // Дополнительные аккаунты для маршрутизации
    ...routeAccounts
  ],
  data: Buffer // Данные маршрута от Jupiter API
}
```

### 🔧 Jupiter API Integration:
```typescript
// 1. Получение котировки
const quote = await fetch(`https://quote-api.jup.ag/v6/quote?${params}`);

// 2. Получение swap инструкций
const swapResponse = await fetch('https://quote-api.jup.ag/v6/swap', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    quoteResponse: quote,
    userPublicKey: wallet.publicKey.toString(),
    wrapAndUnwrapSol: true,
    prioritizationFeeLamports: 300000
  })
});
```

---

## 🌊 3. ORCA WHIRLPOOL SWAP

### 📋 Структура Orca Swap:
```typescript
{
  programId: "whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc",
  keys: [
    { pubkey: tokenProgram, isSigner: false, isWritable: false },
    { pubkey: tokenAuthority, isSigner: true, isWritable: false },
    { pubkey: whirlpool, isSigner: false, isWritable: true },
    { pubkey: tokenOwnerAccountA, isSigner: false, isWritable: true },
    { pubkey: tokenVaultA, isSigner: false, isWritable: true },
    { pubkey: tokenOwnerAccountB, isSigner: false, isWritable: true },
    { pubkey: tokenVaultB, isSigner: false, isWritable: true },
    { pubkey: tickArray0, isSigner: false, isWritable: true },
    { pubkey: tickArray1, isSigner: false, isWritable: true },
    { pubkey: tickArray2, isSigner: false, isWritable: true },
    { pubkey: oracle, isSigner: false, isWritable: false }
  ],
  data: Buffer.from([
    0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8, // Discriminator
    ...amountBytes,                                    // Amount (8 bytes)
    ...otherAmountThresholdBytes,                     // Other Amount Threshold (8 bytes)
    ...sqrtPriceLimitBytes,                           // Sqrt Price Limit (16 bytes)
    amountSpecifiedIsInput,                           // Amount Specified Is Input (1 byte)
    aToB                                              // A to B (1 byte)
  ])
}
```

---

## ⚡ 4. RAYDIUM CLMM SWAP

### 📋 Структура Raydium Swap:
```typescript
{
  programId: "CAMMCzo5YL8w4VFF8KVHrK22GGUQpMAS4K4orSa9tGP",
  keys: [
    { pubkey: payer, isSigner: true, isWritable: true },
    { pubkey: ammConfig, isSigner: false, isWritable: false },
    { pubkey: poolState, isSigner: false, isWritable: true },
    { pubkey: inputTokenAccount, isSigner: false, isWritable: true },
    { pubkey: outputTokenAccount, isSigner: false, isWritable: true },
    { pubkey: inputVault, isSigner: false, isWritable: true },
    { pubkey: outputVault, isSigner: false, isWritable: true },
    { pubkey: observationState, isSigner: false, isWritable: true },
    { pubkey: tokenProgram, isSigner: false, isWritable: false },
    { pubkey: tickArrayMap, isSigner: false, isWritable: true },
    ...tickArrayAccounts
  ],
  data: Buffer.from([
    0x09, 0x5e, 0xa7, 0xb3, 0x99, 0x61, 0xba, 0x32, // Discriminator
    ...amountBytes,                                    // Amount (8 bytes)
    ...otherAmountThresholdBytes,                     // Other Amount Threshold (8 bytes)
    ...sqrtPriceLimitX64Bytes,                        // Sqrt Price Limit X64 (16 bytes)
    isBaseInput                                       // Is Base Input (1 byte)
  ])
}
```

---

## 🔧 5. ATOMIC TRANSACTION BUILDER

### 📋 Основной Класс:
```typescript
class AtomicTransactionBuilder {
  constructor(connection, wallet) {
    this.connection = connection;
    this.wallet = wallet;
    this.transaction = new Transaction();
  }

  // Добавление Flash Loan Start
  addFlashLoanStart(marginfiAccount, amount, bank) {
    const instruction = this.createFlashLoanStartInstruction(
      marginfiAccount, amount, bank
    );
    this.transaction.add(instruction);
  }

  // Добавление Swap инструкций
  addSwapInstruction(dex, params) {
    let instruction;
    switch(dex) {
      case 'meteora':
        instruction = this.createMeteoraSwapInstruction(params);
        break;
      case 'jupiter':
        instruction = this.createJupiterSwapInstruction(params);
        break;
      case 'orca':
        instruction = this.createOrcaSwapInstruction(params);
        break;
      case 'raydium':
        instruction = this.createRaydiumSwapInstruction(params);
        break;
    }
    this.transaction.add(instruction);
  }

  // Добавление Flash Loan End
  addFlashLoanEnd(marginfiAccount, bank) {
    const instruction = this.createFlashLoanEndInstruction(
      marginfiAccount, bank
    );
    this.transaction.add(instruction);
  }

  // Финализация и отправка
  async buildAndSend() {
    this.transaction.recentBlockhash = (
      await this.connection.getLatestBlockhash()
    ).blockhash;
    this.transaction.feePayer = this.wallet.publicKey;
    
    this.transaction.sign(this.wallet);
    
    const signature = await this.connection.sendRawTransaction(
      this.transaction.serialize()
    );
    
    return signature;
  }
}
```

---

## 📊 6. ПОДДЕРЖИВАЕМЫЕ DEX

### 🔥 Основные DEX:
1. **Meteora DLMM** - Concentrated Liquidity
2. **Jupiter Aggregator** - Route Optimization
3. **Orca Whirlpool** - Concentrated Liquidity
4. **Raydium CLMM** - Concentrated Liquidity
5. **Phoenix** - Order Book
6. **OpenBook** - Serum Fork
7. **Lifinity** - Proactive Market Making
8. **Saber** - Stable Swaps

### 🎯 Токенные Пары:
- **USDC/SOL** - Основная пара
- **USDT/USDC** - Стейблкоины
- **SOL/mSOL** - Liquid Staking
- **BONK/SOL** - Мемкоины
- **JUP/SOL** - Governance токены

---

## ⚡ 7. КРИТИЧЕСКИЕ ПАРАМЕТРЫ

### 🔧 Размеры Транзакций:
- **Максимум инструкций**: 24
- **Максимум аккаунтов**: 256 (с ALT)
- **Максимум данных**: 1232 байта
- **Compute Units**: 1,400,000 максимум

### 💰 Комиссии:
- **Base Fee**: 5,000 lamports
- **Priority Fee**: 300,000 micro-lamports
- **Compute Unit Price**: 1,000 micro-lamports

### ⏱️ Тайминги:
- **Slot Time**: ~400ms
- **Confirmation**: 1-2 слота
- **Finalization**: 32 слота

---

## 🔥 8. ПРАКТИЧЕСКИЕ ПРИМЕРЫ КОДА

### 📋 Полный Flash Loan Arbitrage:
```typescript
import { Connection, Transaction, PublicKey, Keypair } from '@solana/web3.js';
import { BN } from '@coral-xyz/anchor';

class FlashLoanArbitrageExecutor {
  constructor(connection, wallet) {
    this.connection = connection;
    this.wallet = wallet;

    // MarginFi Program
    this.MARGINFI_PROGRAM = new PublicKey("MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA");

    // Meteora DLMM Program
    this.METEORA_PROGRAM = new PublicKey("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

    // Token Programs
    this.TOKEN_PROGRAM = new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA");
  }

  async executeArbitrage(params) {
    const {
      marginfiAccount,
      usdcBank,
      buyPool,
      sellPool,
      amount,
      expectedProfit
    } = params;

    console.log(`🚀 Выполнение Flash Loan Arbitrage:`);
    console.log(`   Сумма: $${amount}`);
    console.log(`   Ожидаемая прибыль: $${expectedProfit}`);

    const transaction = new Transaction();

    // 1. START FLASH LOAN
    const startFlashLoanIx = await this.createStartFlashLoanInstruction(
      marginfiAccount,
      usdcBank,
      new BN(amount * 1_000_000) // USDC имеет 6 decimals
    );
    transaction.add(startFlashLoanIx);

    // 2. BUY SWAP (USDC → SOL в дешевом пуле)
    const buySwapIx = await this.createMeteoraSwapInstruction(
      buyPool,
      new BN(amount * 1_000_000),
      true, // USDC to SOL
      new BN(0) // Минимальный выход (рассчитывается отдельно)
    );
    transaction.add(buySwapIx);

    // 3. SELL SWAP (SOL → USDC в дорогом пуле)
    const sellSwapIx = await this.createMeteoraSwapInstruction(
      sellPool,
      new BN(0), // Весь полученный SOL
      false, // SOL to USDC
      new BN((amount + expectedProfit) * 1_000_000) // Минимум для покрытия займа + прибыль
    );
    transaction.add(sellSwapIx);

    // 4. END FLASH LOAN
    const endFlashLoanIx = await this.createEndFlashLoanInstruction(
      marginfiAccount,
      usdcBank
    );
    transaction.add(endFlashLoanIx);

    // Подготовка и отправка транзакции
    const { blockhash } = await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = this.wallet.publicKey;

    // Подписание
    transaction.sign(this.wallet);

    // Отправка
    const signature = await this.connection.sendRawTransaction(
      transaction.serialize(),
      {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
        maxRetries: 3
      }
    );

    console.log(`✅ Транзакция отправлена: ${signature}`);

    // Ожидание подтверждения
    const confirmation = await this.connection.confirmTransaction(signature);

    if (confirmation.value.err) {
      throw new Error(`Транзакция провалилась: ${confirmation.value.err}`);
    }

    console.log(`🎉 Flash Loan Arbitrage успешно выполнен!`);
    return signature;
  }

  async createStartFlashLoanInstruction(marginfiAccount, bank, amount) {
    const discriminator = Buffer.from([0x8c, 0x97, 0x25, 0x8c, 0x17, 0x39, 0x0f, 0x5c]);
    const amountBytes = amount.toArrayLike(Buffer, 'le', 8);
    const endIndexBytes = Buffer.from([3, 0, 0, 0]); // End at instruction 3

    const data = Buffer.concat([discriminator, amountBytes, endIndexBytes]);

    return new TransactionInstruction({
      programId: this.MARGINFI_PROGRAM,
      keys: [
        { pubkey: marginfiAccount, isSigner: false, isWritable: true },
        { pubkey: this.getMarginfiGroup(), isSigner: false, isWritable: false },
        { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
        { pubkey: bank, isSigner: false, isWritable: true },
        { pubkey: this.getUserUSDCAccount(), isSigner: false, isWritable: true },
        { pubkey: this.getBankLiquidityVault(bank), isSigner: false, isWritable: true },
        { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
        { pubkey: this.getBankLiquidityVaultAuthority(bank), isSigner: false, isWritable: false }
      ],
      data
    });
  }

  async createMeteoraSwapInstruction(poolAddress, amountIn, swapYtoX, minAmountOut) {
    const discriminator = Buffer.from([0x41, 0x4b, 0x3f, 0x4c, 0xeb, 0x5b, 0x5b, 0x88]);
    const amountInBytes = amountIn.toArrayLike(Buffer, 'le', 8);
    const minAmountOutBytes = minAmountOut.toArrayLike(Buffer, 'le', 8);

    const data = Buffer.concat([discriminator, amountInBytes, minAmountOutBytes]);

    // Получаем bin arrays для пула
    const binArrays = await this.getBinArraysForPool(poolAddress);

    return new TransactionInstruction({
      programId: this.METEORA_PROGRAM,
      keys: [
        { pubkey: poolAddress, isSigner: false, isWritable: true },
        { pubkey: this.getBinArrayBitmapExtension(poolAddress), isSigner: false, isWritable: true },
        { pubkey: this.getReserveX(poolAddress), isSigner: false, isWritable: true },
        { pubkey: this.getReserveY(poolAddress), isSigner: false, isWritable: true },
        { pubkey: this.getUserTokenX(), isSigner: false, isWritable: true },
        { pubkey: this.getUserTokenY(), isSigner: false, isWritable: true },
        { pubkey: this.getTokenXMint(), isSigner: false, isWritable: false },
        { pubkey: this.getTokenYMint(), isSigner: false, isWritable: false },
        { pubkey: this.getOracle(poolAddress), isSigner: false, isWritable: false },
        { pubkey: this.getHostFeeAccount(), isSigner: false, isWritable: true },
        { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
        { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
        { pubkey: this.METEORA_PROGRAM, isSigner: false, isWritable: false },
        ...binArrays.map(ba => ({ pubkey: ba, isSigner: false, isWritable: true }))
      ],
      data
    });
  }

  async createEndFlashLoanInstruction(marginfiAccount, bank) {
    const discriminator = Buffer.from([0x8c, 0x97, 0x25, 0x8c, 0x17, 0x39, 0x0f, 0x5c]);

    return new TransactionInstruction({
      programId: this.MARGINFI_PROGRAM,
      keys: [
        { pubkey: marginfiAccount, isSigner: false, isWritable: true },
        { pubkey: this.getMarginfiGroup(), isSigner: false, isWritable: false },
        { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
        { pubkey: bank, isSigner: false, isWritable: true },
        { pubkey: this.getUserUSDCAccount(), isSigner: false, isWritable: true },
        { pubkey: this.getBankLiquidityVault(bank), isSigner: false, isWritable: true },
        { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
        { pubkey: this.getBankLiquidityVaultAuthority(bank), isSigner: false, isWritable: false }
      ],
      data: discriminator
    });
  }

  // Вспомогательные методы для получения адресов
  getMarginfiGroup() {
    return new PublicKey("4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8");
  }

  getUserUSDCAccount() {
    // Возвращает Associated Token Account для USDC
    return this.getAssociatedTokenAddress(
      new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), // USDC Mint
      this.wallet.publicKey
    );
  }

  getBankLiquidityVault(bank) {
    // PDA для liquidity vault банка
    return PublicKey.findProgramAddressSync(
      [Buffer.from("liquidity_vault"), bank.toBuffer()],
      this.MARGINFI_PROGRAM
    )[0];
  }

  getBankLiquidityVaultAuthority(bank) {
    // PDA для authority liquidity vault
    return PublicKey.findProgramAddressSync(
      [Buffer.from("liquidity_vault_auth"), bank.toBuffer()],
      this.MARGINFI_PROGRAM
    )[0];
  }

  async getBinArraysForPool(poolAddress) {
    // Получаем активные bin arrays для пула
    // Это требует запроса к RPC для получения состояния пула
    const poolData = await this.connection.getAccountInfo(poolAddress);
    // Парсим данные пула и возвращаем необходимые bin arrays
    // Упрощенная версия - возвращаем фиксированные адреса
    return [
      new PublicKey("BinArray1111111111111111111111111111111111"),
      new PublicKey("BinArray2222222222222222222222222222222222"),
      new PublicKey("BinArray3333333333333333333333333333333333")
    ];
  }
}

// Использование:
const executor = new FlashLoanArbitrageExecutor(connection, wallet);

await executor.executeArbitrage({
  marginfiAccount: new PublicKey("MarginFiAccount111111111111111111111111111"),
  usdcBank: new PublicKey("USDCBank1111111111111111111111111111111111"),
  buyPool: new PublicKey("BuyPool11111111111111111111111111111111111"),
  sellPool: new PublicKey("SellPool1111111111111111111111111111111111"),
  amount: 1000, // $1000
  expectedProfit: 50 // $50 прибыль
});
```

---

## 🎯 ЗАКЛЮЧЕНИЕ

Эта структура обеспечивает:
✅ **Атомарность** - Все операции в одной транзакции
✅ **Эффективность** - Оптимизированные маршруты
✅ **Безопасность** - Flash loan гарантии
✅ **Масштабируемость** - Поддержка множества DEX
✅ **Прибыльность** - Арбитражные возможности
