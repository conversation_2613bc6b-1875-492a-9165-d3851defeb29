{"name": "dexswap-price-monitor", "version": "2.0.0", "description": "🔴 DEXSWAP - Мониторинг цен на 16+ DEX и биржах в реальном времени с выявлением арбитражных возможностей", "main": "multi-dex-price-monitor.js", "scripts": {"start": "node multi-dex-price-monitor.js", "monitor": "node multi-dex-price-monitor.js", "simple": "node dex-price-monitor.js", "test-binance": "node test-binance-matic-pol.js", "install-deps": "npm install axios @meteora-ag/dynamic-amm-sdk @meteora-ag/dlmm"}, "keywords": ["dex", "arbitrage", "crypto", "price-monitor", "solana", "jupiter", "orca", "raydium", "meteora", "defi", "trading", "mempool"], "author": "Augment Agent", "license": "MIT", "dependencies": {"@coral-xyz/anchor": "^0.29.0", "@ellipsis-labs/phoenix-sdk": "^2.0.3", "@jup-ag/api": "^6.0.44", "@meteora-ag/dlmm": "^1.5.4", "@meteora-ag/dynamic-amm-sdk": "^1.3.8", "@mrgnlabs/marginfi-client-v2": "^6.1.0", "@mrgnlabs/mrgn-common": "^2.0.3", "@openbook-dex/openbook-v2": "^0.2.10", "@orca-so/common-sdk": "^0.6.11", "@orca-so/sdk": "^1.2.26", "@orca-so/whirlpools": "^2.2.0", "@orca-so/whirlpools-client": "^2.0.0", "@orca-so/whirlpools-core": "^2.0.0", "@orca-so/whirlpools-sdk": "^0.15.0", "@project-serum/anchor": "^0.26.0", "@project-serum/serum": "^0.13.65", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "@raydium-io/raydium-sdk-v2": "^0.2.4-alpha", "@saberhq/stableswap-sdk": "^3.0.0", "@solana/buffer-layout": "^4.0.1", "@solana/spl-token": "^0.4.13", "@solana/spl-token-registry": "^0.2.4574", "@solana/web3.js": "^1.98.2", "axios": "^0.27.2", "base58-js": "^3.0.2", "bn.js": "^5.2.2", "bs58": "^6.0.0", "colors": "^1.4.0", "decimal.js": "^10.6.0", "dotenv": "^16.6.1", "express": "^5.1.0", "node-fetch": "^2.7.0", "socket.io": "^4.8.1", "ws": "^8.18.2"}, "overrides": {"jito-ts": {"@solana/web3.js": "1.98.2"}, "@pythnetwork/solana-utils": {"jito-ts": "4.2.0", "@solana/web3.js": "1.98.2"}}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "README.md"}