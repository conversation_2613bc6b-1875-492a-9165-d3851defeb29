const { Connection, PublicKey } = require('@solana/web3.js');
const BN = require('bn.js');

/**
 * 🔍 ДИАГНОСТИЧЕСКИЙ ИНСТРУМЕНТ ДЛЯ METEORA SWAP ОШИБКИ 6002
 * 
 * На основе официальной документации Meteora DLMM SDK:
 * https://www.npmjs.com/package/@meteora-ag/dlmm
 * 
 * Ошибка: InvalidInput (6002) в swap2.rs:300
 * Причины:
 * 1. Неправильные параметры swapQuote
 * 2. Неправильные параметры swap
 * 3. Устаревшие bin arrays
 * 4. Неправильный порядок аккаунтов
 */
class MeteoraSwapDiagnostic {
    constructor() {
        this.connection = new Connection('https://api.mainnet-beta.solana.com');
        this.METEORA_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
    }

    /**
     * 🔍 АНАЛИЗ METEORA SWAP ИНСТРУКЦИИ НА ОСНОВЕ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ
     */
    analyzeSwapInstruction(instruction) {
        console.log('🔍 АНАЛИЗ METEORA SWAP ИНСТРУКЦИИ...');
        console.log('═══════════════════════════════════════════════════════════');

        if (instruction.programId.toString() !== this.METEORA_PROGRAM_ID) {
            console.log('❌ Это не Meteora DLMM инструкция');
            return { isMeteoraSwap: false };
        }

        console.log('✅ Подтверждено: Meteora DLMM инструкция');
        console.log(`📊 Program ID: ${instruction.programId.toString()}`);
        console.log(`📊 Аккаунтов: ${instruction.keys.length}`);
        console.log(`📊 Данных: ${instruction.data.length} байт`);

        // Анализ discriminator
        if (instruction.data.length >= 8) {
            const discriminator = instruction.data.slice(0, 8);
            console.log(`🔍 Discriminator: [${Array.from(discriminator).join(', ')}]`);
            console.log(`🔍 Discriminator (hex): ${discriminator.toString('hex')}`);
            
            // Известные discriminators для Meteora
            const knownDiscriminators = {
                'f8c69e91e17587c8': 'Swap',
                '2b2a1b8e1e1e1e1e': 'Swap2', // Примерный
            };
            
            const discriminatorHex = discriminator.toString('hex');
            if (knownDiscriminators[discriminatorHex]) {
                console.log(`✅ Распознан как: ${knownDiscriminators[discriminatorHex]}`);
            } else {
                console.log(`⚠️ Неизвестный discriminator: ${discriminatorHex}`);
            }
        }

        // Анализ аккаунтов согласно официальной документации
        console.log('\n🔍 АНАЛИЗ АККАУНТОВ (СОГЛАСНО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ):');
        
        // Ожидаемые аккаунты для swap согласно SDK:
        const expectedAccounts = [
            'LB Pair',           // 0
            'Bin Array Bitmap Extension', // 1
            'Reserve X',         // 2
            'Reserve Y',         // 3
            'Token X Mint',      // 4
            'Token Y Mint',      // 5
            'Oracle',            // 6
            'Host Fee',          // 7
            'User Token In',     // 8
            'User Token Out',    // 9
            'Token X Program',   // 10
            'Token Y Program',   // 11
            'User',              // 12
            'System Program',    // 13
            'Token Program',     // 14
            // Bin Arrays (переменное количество)
        ];

        instruction.keys.forEach((key, index) => {
            const flags = [];
            if (key.isSigner) flags.push('signer');
            if (key.isWritable) flags.push('writable');
            
            const expectedName = expectedAccounts[index] || `Bin Array ${index - expectedAccounts.length + 1}`;
            console.log(`   ${index}: ${key.pubkey.toString().slice(0, 8)}...${key.pubkey.toString().slice(-8)} ${flags.length > 0 ? `(${flags.join(', ')})` : ''} - ${expectedName}`);
        });

        return {
            isMeteoraSwap: true,
            accountsCount: instruction.keys.length,
            dataSize: instruction.data.length,
            discriminator: instruction.data.length >= 8 ? instruction.data.slice(0, 8).toString('hex') : null,
            analysis: this.analyzeSwapData(instruction.data)
        };
    }

    /**
     * 🔍 АНАЛИЗ ДАННЫХ SWAP ИНСТРУКЦИИ
     */
    analyzeSwapData(data) {
        console.log('\n🔍 АНАЛИЗ ДАННЫХ SWAP ИНСТРУКЦИИ:');
        
        if (data.length < 8) {
            console.log('❌ Недостаточно данных для анализа');
            return { valid: false, reason: 'Insufficient data' };
        }

        const discriminator = data.slice(0, 8);
        console.log(`🔍 Discriminator: ${discriminator.toString('hex')}`);

        // Анализ остальных данных
        if (data.length > 8) {
            const payload = data.slice(8);
            console.log(`📊 Payload размер: ${payload.length} байт`);
            console.log(`📊 Payload (hex): ${payload.toString('hex')}`);

            // Попытка парсинга как swap данные
            if (payload.length >= 16) {
                try {
                    // Первые 8 байт - amount (u64)
                    const amount = payload.readBigUInt64LE(0);
                    console.log(`💰 Amount: ${amount.toString()}`);

                    // Следующие 8 байт - min_out_amount (u64)
                    if (payload.length >= 16) {
                        const minOutAmount = payload.readBigUInt64LE(8);
                        console.log(`📉 Min Out Amount: ${minOutAmount.toString()}`);
                        
                        // Проверка на разумность значений
                        if (minOutAmount === 0n) {
                            console.log('⚠️ ПРОБЛЕМА: minOutAmount = 0 (может вызвать InvalidInput)');
                        } else if (minOutAmount === 1n) {
                            console.log('⚠️ ПРОБЛЕМА: minOutAmount = 1 (слишком низкое значение)');
                        } else {
                            console.log('✅ minOutAmount выглядит разумно');
                        }
                    }
                } catch (error) {
                    console.log(`❌ Ошибка парсинга данных: ${error.message}`);
                }
            }
        }

        return { valid: true, discriminator: discriminator.toString('hex') };
    }

    /**
     * 🔍 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ НА ОСНОВЕ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ
     */
    provideFixRecommendations(analysis) {
        console.log('\n💡 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ (ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ):');
        console.log('═══════════════════════════════════════════════════════════');

        console.log('📚 ПРАВИЛЬНЫЙ СПОСОБ СОЗДАНИЯ SWAP (ИЗ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ):');
        console.log(`
const swapAmount = new BN(0.1 * 10 ** 9);
// Swap quote
const swapYtoX = true;
const binArrays = await dlmmPool.getBinArrayForSwap(swapYtoX);

const swapQuote = await dlmmPool.swapQuote(
  swapAmount,           // 1. amount (BN)
  swapYtoX,            // 2. swapYtoX (boolean)
  new BN(100),         // 3. slippage (BN) - 100 basis points = 1%
  binArrays            // 4. binArrays
);

// Swap
const swapTx = await dlmmPool.swap({
  inToken: dlmmPool.tokenX.publicKey,
  binArraysPubkey: swapQuote.binArraysPubkey,
  inAmount: swapAmount,
  lbPair: dlmmPool.pubkey,
  user: user.publicKey,
  minOutAmount: swapQuote.minOutAmount,  // ✅ ИСПОЛЬЗУЕМ ИЗ QUOTE!
  outToken: dlmmPool.tokenY.publicKey,
});
        `);

        console.log('\n🚨 ЧАСТЫЕ ОШИБКИ, ВЫЗЫВАЮЩИЕ InvalidInput (6002):');
        console.log('1. Использование неправильного slippage в swapQuote');
        console.log('2. Использование minOutAmount = 1 вместо значения из quote');
        console.log('3. Устаревшие bin arrays');
        console.log('4. Неправильный порядок аккаунтов');
        console.log('5. Неправильные флаги аккаунтов (signer/writable)');

        console.log('\n🔧 КОНКРЕТНЫЕ ИСПРАВЛЕНИЯ:');
        console.log('1. ✅ Используйте new BN(100) для 1% slippage в swapQuote');
        console.log('2. ✅ Используйте swapQuote.minOutAmount в swap, НЕ 1');
        console.log('3. ✅ Получайте свежие binArrays через getBinArrayForSwap');
        console.log('4. ✅ Используйте точно такой же порядок аккаунтов как в SDK');
        console.log('5. ✅ Проверьте, что все writable аккаунты помечены правильно');

        return {
            recommendations: [
                'Use correct slippage in swapQuote (new BN(100) for 1%)',
                'Use swapQuote.minOutAmount instead of hardcoded 1',
                'Get fresh binArrays with getBinArrayForSwap',
                'Follow exact account order from official SDK',
                'Verify writable flags on accounts'
            ]
        };
    }

    /**
     * 🔍 ПОЛНЫЙ ДИАГНОСТИЧЕСКИЙ АНАЛИЗ
     */
    async diagnoseSwapError(instructions) {
        console.log('🔍 ПОЛНЫЙ ДИАГНОСТИЧЕСКИЙ АНАЛИЗ METEORA SWAP...');
        console.log('═══════════════════════════════════════════════════════════');

        const meteoraInstructions = instructions.filter(ix => 
            ix.programId.toString() === this.METEORA_PROGRAM_ID
        );

        if (meteoraInstructions.length === 0) {
            console.log('❌ Meteora инструкции не найдены');
            return { found: false };
        }

        console.log(`📊 Найдено Meteora инструкций: ${meteoraInstructions.length}`);

        const results = [];
        for (let i = 0; i < meteoraInstructions.length; i++) {
            console.log(`\n🔍 АНАЛИЗ METEORA ИНСТРУКЦИИ ${i + 1}:`);
            const analysis = this.analyzeSwapInstruction(meteoraInstructions[i]);
            const recommendations = this.provideFixRecommendations(analysis);
            
            results.push({
                instruction: meteoraInstructions[i],
                analysis,
                recommendations
            });
        }

        return {
            found: true,
            count: meteoraInstructions.length,
            results
        };
    }
}

module.exports = MeteoraSwapDiagnostic;
