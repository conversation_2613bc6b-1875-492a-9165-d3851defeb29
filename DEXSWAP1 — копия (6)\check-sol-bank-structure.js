/**
 * 🔍 ПРОВЕРКА РЕАЛЬНОЙ СТРУКТУРЫ SOL БАНКА
 * 
 * Анализируем структуру SOL банка для правильного извлечения vault
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class SOLBankAnalyzer {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        this.SOL_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');
    }

    /**
     * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ SOL БАНКА
     */
    async analyzeSOLBank() {
        try {
            console.log('🔍 ДЕТАЛЬНЫЙ АНАЛИЗ SOL БАНКА');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const bankInfo = await this.connection.getAccountInfo(this.SOL_BANK);
            if (!bankInfo) {
                throw new Error('S<PERSON> банк не найден');
            }
            
            console.log('✅ SOL банк найден');
            console.log(`📏 Размер данных: ${bankInfo.data.length} bytes`);
            console.log(`👤 Владелец: ${bankInfo.owner.toString()}`);
            
            const data = bankInfo.data;
            
            console.log('\n📊 АНАЛИЗ СТРУКТУРЫ БАНКА:');
            
            // Показываем первые 200 байт в hex для анализа
            console.log('🔍 Первые 200 байт (hex):');
            for (let i = 0; i < Math.min(200, data.length); i += 32) {
                const chunk = data.slice(i, i + 32);
                const hex = Array.from(chunk).map(b => b.toString(16).padStart(2, '0')).join(' ');
                console.log(`   ${i.toString().padStart(3, '0')}: ${hex}`);
            }
            
            console.log('\n🔍 ПОИСК VAULT АККАУНТОВ:');
            
            // Ищем все возможные PublicKey в данных банка
            const possibleVaults = [];
            for (let i = 0; i <= data.length - 32; i++) {
                try {
                    const pubkeyBytes = data.slice(i, i + 32);
                    const pubkey = new PublicKey(pubkeyBytes);
                    
                    // Проверяем что это валидный аккаунт
                    const accountInfo = await this.connection.getAccountInfo(pubkey);
                    if (accountInfo) {
                        possibleVaults.push({
                            offset: i,
                            pubkey: pubkey.toString(),
                            owner: accountInfo.owner.toString(),
                            lamports: accountInfo.lamports,
                            dataSize: accountInfo.data.length
                        });
                        
                        console.log(`   ✅ Offset ${i}: ${pubkey.toString()}`);
                        console.log(`      Owner: ${accountInfo.owner.toString()}`);
                        console.log(`      Lamports: ${accountInfo.lamports}`);
                        console.log(`      Data size: ${accountInfo.data.length} bytes`);
                        
                        // Проверяем если это token account
                        if (accountInfo.data.length === 165) {
                            console.log('      🪙 Возможно Token Account!');
                        }
                    }
                } catch (error) {
                    // Игнорируем невалидные pubkey
                }
            }
            
            console.log(`\n📊 Найдено возможных vault аккаунтов: ${possibleVaults.length}`);
            
            // Анализируем известные offset'ы
            console.log('\n🔍 АНАЛИЗ ИЗВЕСТНЫХ OFFSET\'ОВ:');
            
            const knownOffsets = [8, 40, 72, 104, 136, 168, 200];
            for (const offset of knownOffsets) {
                if (offset + 32 <= data.length) {
                    try {
                        const pubkey = new PublicKey(data.slice(offset, offset + 32));
                        console.log(`   Offset ${offset}: ${pubkey.toString()}`);
                        
                        const accountInfo = await this.connection.getAccountInfo(pubkey);
                        if (accountInfo) {
                            console.log(`      ✅ Существует! Owner: ${accountInfo.owner.toString()}`);
                            if (accountInfo.data.length === 165) {
                                console.log('      🪙 Token Account!');
                            }
                        } else {
                            console.log('      ❌ Не существует');
                        }
                    } catch (error) {
                        console.log(`   Offset ${offset}: Невалидный PublicKey`);
                    }
                }
            }
            
            // Ищем mint
            console.log('\n🔍 ПОИСК SOL MINT:');
            const solMint = new PublicKey('So11111111111111111111111111111111111111112');
            for (let i = 0; i <= data.length - 32; i++) {
                const pubkeyBytes = data.slice(i, i + 32);
                const pubkey = new PublicKey(pubkeyBytes);
                if (pubkey.equals(solMint)) {
                    console.log(`   ✅ SOL Mint найден на offset ${i}`);
                }
            }
            
            return possibleVaults;
            
        } catch (error) {
            console.error(`❌ Ошибка анализа: ${error.message}`);
            return [];
        }
    }

    /**
     * 🔧 ТЕСТИРОВАНИЕ РАЗНЫХ VAULT АДРЕСОВ
     */
    async testVaultAddresses(possibleVaults) {
        console.log('\n🔧 ТЕСТИРОВАНИЕ VAULT АДРЕСОВ');
        console.log('═══════════════════════════════════════════════════════════════');
        
        for (const vault of possibleVaults) {
            console.log(`\n🧪 Тестируем vault: ${vault.pubkey}`);
            console.log(`   Offset: ${vault.offset}`);
            console.log(`   Owner: ${vault.owner}`);
            
            // Проверяем если это token account с SOL
            if (vault.dataSize === 165) {
                try {
                    const vaultInfo = await this.connection.getAccountInfo(new PublicKey(vault.pubkey));
                    if (vaultInfo) {
                        const data = vaultInfo.data;
                        
                        // Token account структура:
                        // 0-32: mint
                        // 32-64: owner
                        // 64-72: amount
                        
                        const mint = new PublicKey(data.slice(0, 32));
                        const owner = new PublicKey(data.slice(32, 64));
                        const amount = data.readBigUInt64LE(64);
                        
                        console.log(`   🪙 Mint: ${mint.toString()}`);
                        console.log(`   👤 Owner: ${owner.toString()}`);
                        console.log(`   💰 Amount: ${amount.toString()}`);
                        
                        const solMint = new PublicKey('So11111111111111111111111111111111111111112');
                        if (mint.equals(solMint)) {
                            console.log('   🎯 ЭТО SOL TOKEN ACCOUNT! ВОЗМОЖНЫЙ VAULT!');
                        }
                    }
                } catch (error) {
                    console.log(`   ❌ Ошибка анализа token account: ${error.message}`);
                }
            }
        }
    }
}

async function main() {
    console.log('🔍 АНАЛИЗ СТРУКТУРЫ SOL БАНКА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Ищем правильный vault для исправления ошибки 0xbc4');
    console.log('💡 Анализируем реальную структуру SOL банка MarginFi');
    console.log('═══════════════════════════════════════════════════════════════');

    const analyzer = new SOLBankAnalyzer();
    const possibleVaults = await analyzer.analyzeSOLBank();
    
    if (possibleVaults.length > 0) {
        await analyzer.testVaultAddresses(possibleVaults);
    }

    console.log('\n🎯 ИТОГ:');
    console.log('Используй найденные vault адреса для исправления deposit инструкции');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SOLBankAnalyzer;
