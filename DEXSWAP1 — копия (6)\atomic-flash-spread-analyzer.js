// 🎯 АТОМАРНЫЙ FLASH LOAN АНАЛИЗАТОР СПРЕДОВ
// Специально для интеграции в BMeteora.js
// Учитывает атомарные транзакции БЕЗ залогов, долгов и комиссий

const colors = require('colors');

/**
 * 🎯 АТОМАРНЫЙ АНАЛИЗАТОР СПРЕДОВ ДЛЯ FLASH LOAN АРБИТРАЖА
 * 
 * ОСОБЕННОСТИ:
 * - Атомарные транзакции (все или ничего)
 * - Flash loans БЕЗ залогов и долгов
 * - БЕЗ комиссий flash loan (только swap комиссии)
 * - Мгновенное исполнение в одной транзакции
 */
class AtomicFlashSpreadAnalyzer {
    constructor(connection, wallet, config = {}) {
        this.connection = connection;
        this.wallet = wallet;
        
        // 🎯 КОНФИГУРАЦИЯ ДЛЯ АТОМАРНОГО АРБИТРАЖА
        this.config = {
            minSpreadPercent: config.minSpreadPercent || 0.05,    // 0.05% минимальный спред
            maxFlashLoanUSD: config.maxFlashLoanUSD || 50000,     // $50k максимальный займ
            minFlashLoanUSD: config.minFlashLoanUSD || 100,       // $100 минимальный займ
            maxSlippagePercent: config.maxSlippagePercent || 0.5, // 0.5% максимальный slippage
            emergencySpreadPercent: config.emergencySpreadPercent || 0.1, // 0.1% для экстренного режима
            ...config
        };
        
        // 📊 СТАТИСТИКА АНАЛИЗА
        this.stats = {
            totalAnalyzed: 0,
            profitableFound: 0,
            executedSuccessfully: 0,
            totalProfit: 0,
            lastAnalysisTime: 0,
            averageAnalysisTime: 0
        };
        
        // 🔥 КЭШИРОВАНИЕ ДЛЯ УЛЬТРА-БЫСТРОГО АНАЛИЗА
        this.priceCache = new Map();
        this.liquidityCache = new Map();
        this.feeCache = new Map();
        this.lastCacheUpdate = 0;
        this.CACHE_DURATION = 1000; // 1 секунда кэш
        
        console.log('🎯 Атомарный Flash Spread Analyzer инициализирован');
        console.log(`   Минимальный спред: ${this.config.minSpreadPercent}%`);
        console.log(`   Максимальный займ: $${this.config.maxFlashLoanUSD.toLocaleString()}`);
    }

    /**
     * 🔍 ОСНОВНОЙ АНАЛИЗ СПРЕДОВ МЕЖДУ ПУЛАМИ
     * Возвращает лучшие атомарные арбитражные возможности
     */
    async analyzeAtomicArbitrageOpportunities(pools) {
        const startTime = performance.now();
        this.stats.totalAnalyzed++;
        
        try {
            console.log(`🔍 Анализ ${pools.length} пулов для атомарного арбитража...`);
            
            // 🚀 БЫСТРАЯ ПРОВЕРКА КЭША
            if (this.shouldUseCachedResults()) {
                return this.getCachedOpportunities();
            }
            
            const opportunities = [];
            
            // 📊 ПОЛУЧАЕМ АКТУАЛЬНЫЕ ЦЕНЫ И ЛИКВИДНОСТЬ
            const poolData = await this.getPoolDataOptimized(pools);
            
            // 🎯 АНАЛИЗИРУЕМ ВСЕ ВОЗМОЖНЫЕ ПАРЫ ПУЛОВ
            for (let i = 0; i < poolData.length; i++) {
                for (let j = i + 1; j < poolData.length; j++) {
                    const poolA = poolData[i];
                    const poolB = poolData[j];
                    
                    // 🔄 ПРОВЕРЯЕМ ОБА НАПРАВЛЕНИЯ АРБИТРАЖА
                    const opportunityAB = await this.analyzePoolPair(poolA, poolB, 'A->B');
                    const opportunityBA = await this.analyzePoolPair(poolB, poolA, 'B->A');
                    
                    if (opportunityAB) opportunities.push(opportunityAB);
                    if (opportunityBA) opportunities.push(opportunityBA);
                }
            }
            
            // 📈 СОРТИРУЕМ ПО ПРИБЫЛЬНОСТИ
            opportunities.sort((a, b) => b.expectedProfit.net - a.expectedProfit.net);
            
            // 🎯 ФИЛЬТРУЕМ ТОЛЬКО АТОМАРНО ИСПОЛНИМЫЕ
            const atomicOpportunities = opportunities.filter(opp => this.isAtomicallyExecutable(opp));
            
            // 📊 ОБНОВЛЯЕМ СТАТИСТИКУ
            this.stats.profitableFound += atomicOpportunities.length;
            const analysisTime = performance.now() - startTime;
            this.stats.lastAnalysisTime = analysisTime;
            this.stats.averageAnalysisTime = (this.stats.averageAnalysisTime + analysisTime) / 2;
            
            console.log(`✅ Найдено ${atomicOpportunities.length} атомарных возможностей за ${analysisTime.toFixed(2)}ms`);
            
            // 🔥 КЭШИРУЕМ РЕЗУЛЬТАТЫ
            this.cacheOpportunities(atomicOpportunities);
            
            return atomicOpportunities;
            
        } catch (error) {
            console.error('❌ Ошибка анализа спредов:', error.message);
            return [];
        }
    }

    /**
     * 🎯 АНАЛИЗ КОНКРЕТНОЙ ПАРЫ ПУЛОВ
     */
    async analyzePoolPair(sellPool, buyPool, direction) {
        try {
            // 📊 РАСЧЕТ СПРЕДА
            const spread = this.calculateSpread(sellPool, buyPool);
            
            // ❌ ОТФИЛЬТРОВЫВАЕМ НЕПРИБЫЛЬНЫЕ СПРЕДЫ
            if (spread.percent < this.config.minSpreadPercent) {
                return null;
            }
            
            // 💰 РАСЧЕТ ОПТИМАЛЬНОГО РАЗМЕРА FLASH LOAN
            const optimalLoanSize = this.calculateOptimalFlashLoanSize(sellPool, buyPool, spread);
            
            // 🔍 ПРОВЕРКА ЛИКВИДНОСТИ ДЛЯ АТОМАРНОГО ИСПОЛНЕНИЯ
            const liquidityCheck = this.checkAtomicLiquidity(sellPool, buyPool, optimalLoanSize);
            if (!liquidityCheck.sufficient) {
                return null;
            }
            
            // 💸 РАСЧЕТ КОМИССИЙ (ТОЛЬКО SWAP, БЕЗ FLASH LOAN)
            const fees = this.calculateAtomicFees(sellPool, buyPool, optimalLoanSize);
            
            // 💰 РАСЧЕТ ЧИСТОЙ ПРИБЫЛИ
            const grossProfit = optimalLoanSize * (spread.percent / 100);
            const netProfit = grossProfit - fees.total;
            
            // ❌ ОТФИЛЬТРОВЫВАЕМ УБЫТОЧНЫЕ ПОСЛЕ КОМИССИЙ
            if (netProfit <= 0) {
                return null;
            }
            
            // 🎯 СОЗДАЕМ ВОЗМОЖНОСТЬ АТОМАРНОГО АРБИТРАЖА
            const opportunity = {
                type: 'ATOMIC_FLASH_ARBITRAGE',
                direction,
                sellPool: {
                    address: sellPool.address,
                    name: sellPool.name,
                    price: sellPool.price,
                    liquidity: sellPool.liquidity,
                    fee: sellPool.fee
                },
                buyPool: {
                    address: buyPool.address,
                    name: buyPool.name,
                    price: buyPool.price,
                    liquidity: buyPool.liquidity,
                    fee: buyPool.fee
                },
                spread: {
                    absolute: spread.absolute,
                    percent: spread.percent
                },
                flashLoan: {
                    amountUSD: optimalLoanSize,
                    amountNative: optimalLoanSize / sellPool.price, // В SOL
                    noCollateral: true,
                    noDebt: true,
                    noFees: true // Flash loan БЕЗ комиссий
                },
                expectedProfit: {
                    gross: grossProfit,
                    net: netProfit,
                    roi: (netProfit / optimalLoanSize) * 100
                },
                fees: fees,
                liquidity: liquidityCheck,
                execution: {
                    atomic: true,
                    estimatedGas: this.estimateAtomicGasCost(),
                    maxSlippage: this.config.maxSlippagePercent,
                    timeToExecute: '<100ms'
                },
                timestamp: Date.now(),
                priority: this.calculatePriority(netProfit, spread.percent)
            };
            
            return opportunity;
            
        } catch (error) {
            console.error(`❌ Ошибка анализа пары ${direction}:`, error.message);
            return null;
        }
    }

    /**
     * 📊 РАСЧЕТ СПРЕДА МЕЖДУ ПУЛАМИ
     */
    calculateSpread(sellPool, buyPool) {
        const absolute = buyPool.price - sellPool.price;
        const percent = (absolute / sellPool.price) * 100;
        
        return {
            absolute,
            percent,
            sellPrice: sellPool.price,
            buyPrice: buyPool.price
        };
    }

    /**
     * 💰 РАСЧЕТ ОПТИМАЛЬНОГО РАЗМЕРА FLASH LOAN
     */
    calculateOptimalFlashLoanSize(sellPool, buyPool, spread) {
        // 🎯 БАЗОВЫЙ РАЗМЕР НА ОСНОВЕ ЛИКВИДНОСТИ
        const maxByLiquidity = Math.min(sellPool.liquidity, buyPool.liquidity) * 0.01; // 1% от минимальной ликвидности
        
        // 🎯 МАКСИМАЛЬНЫЙ РАЗМЕР ПО КОНФИГУРАЦИИ
        const maxByConfig = this.config.maxFlashLoanUSD;
        
        // 🎯 ОПТИМАЛЬНЫЙ РАЗМЕР ДЛЯ ДАННОГО СПРЕДА
        const optimalBySpread = spread.percent > 1.0 ? maxByConfig : maxByConfig * (spread.percent / 1.0);
        
        // 🎯 ВЫБИРАЕМ МИНИМАЛЬНЫЙ ИЗ ВСЕХ ОГРАНИЧЕНИЙ
        const optimalSize = Math.min(maxByLiquidity, maxByConfig, optimalBySpread);
        
        // 🎯 НЕ МЕНЬШЕ МИНИМАЛЬНОГО
        return Math.max(optimalSize, this.config.minFlashLoanUSD);
    }

    /**
     * 🔍 ПРОВЕРКА ЛИКВИДНОСТИ ДЛЯ АТОМАРНОГО ИСПОЛНЕНИЯ
     */
    checkAtomicLiquidity(sellPool, buyPool, loanSize) {
        const sellLiquidityNeeded = loanSize;
        const buyLiquidityNeeded = loanSize * 1.1; // +10% запас
        
        const sellSufficient = sellPool.liquidity >= sellLiquidityNeeded;
        const buySufficient = buyPool.liquidity >= buyLiquidityNeeded;
        
        return {
            sufficient: sellSufficient && buySufficient,
            sellPool: {
                needed: sellLiquidityNeeded,
                available: sellPool.liquidity,
                sufficient: sellSufficient
            },
            buyPool: {
                needed: buyLiquidityNeeded,
                available: buyPool.liquidity,
                sufficient: buySufficient
            }
        };
    }

    /**
     * 💸 РАСЧЕТ КОМИССИЙ ДЛЯ АТОМАРНОЙ ТРАНЗАКЦИИ
     */
    calculateAtomicFees(sellPool, buyPool, loanSize) {
        // 🔥 FLASH LOAN БЕЗ КОМИССИЙ!
        const flashLoanFee = 0;
        
        // 💸 КОМИССИИ SWAP (ТОЛЬКО ЗА ОБМЕНЫ)
        const sellSwapFee = loanSize * (sellPool.fee / 100);
        const buySwapFee = loanSize * (buyPool.fee / 100);
        
        // ⛽ КОМИССИИ СЕТИ (GAS)
        const networkFee = 0.01; // ~$0.01 в SOL
        
        return {
            flashLoan: flashLoanFee,
            sellSwap: sellSwapFee,
            buySwap: buySwapFee,
            network: networkFee,
            total: flashLoanFee + sellSwapFee + buySwapFee + networkFee
        };
    }

    /**
     * ⚡ ПРОВЕРКА АТОМАРНОЙ ИСПОЛНИМОСТИ
     */
    isAtomicallyExecutable(opportunity) {
        // ✅ ПРОВЕРКИ ДЛЯ АТОМАРНОГО ИСПОЛНЕНИЯ
        const checks = {
            profitPositive: opportunity.expectedProfit.net > 0,
            liquiditySufficient: opportunity.liquidity.sufficient,
            spreadAdequate: opportunity.spread.percent >= this.config.minSpreadPercent,
            loanSizeValid: opportunity.flashLoan.amountUSD >= this.config.minFlashLoanUSD &&
                          opportunity.flashLoan.amountUSD <= this.config.maxFlashLoanUSD,
            roiAcceptable: opportunity.expectedProfit.roi >= 0.1 // Минимум 0.1% ROI
        };
        
        const executable = Object.values(checks).every(check => check === true);
        
        if (!executable) {
            const failedChecks = Object.entries(checks)
                .filter(([key, value]) => !value)
                .map(([key]) => key);
            console.log(`❌ Возможность не атомарно исполнима: ${failedChecks.join(', ')}`);
        }
        
        return executable;
    }

    /**
     * 🎯 РАСЧЕТ ПРИОРИТЕТА ВОЗМОЖНОСТИ
     */
    calculatePriority(netProfit, spreadPercent) {
        // 🎯 ПРИОРИТЕТ НА ОСНОВЕ ПРИБЫЛИ И СПРЕДА
        const profitScore = Math.min(netProfit / 1000, 10); // Максимум 10 баллов за прибыль
        const spreadScore = Math.min(spreadPercent * 2, 10); // Максимум 10 баллов за спред
        
        return Math.round(profitScore + spreadScore);
    }

    /**
     * ⚡ ОЦЕНКА СТОИМОСТИ GAS ДЛЯ АТОМАРНОЙ ТРАНЗАКЦИИ
     */
    estimateAtomicGasCost() {
        // 🔥 АТОМАРНАЯ ТРАНЗАКЦИЯ: Flash Loan + 2 Swaps + Repay
        const baseUnits = 200000; // Базовые compute units
        const flashLoanUnits = 50000; // Flash loan операции
        const swapUnits = 100000; // Каждый swap
        
        const totalUnits = baseUnits + flashLoanUnits + (swapUnits * 2);
        const priorityFee = 0.000001; // 1 microlamport
        
        return {
            computeUnits: totalUnits,
            estimatedCostSOL: totalUnits * priorityFee,
            estimatedCostUSD: totalUnits * priorityFee * 160 // Примерная цена SOL
        };
    }

    /**
     * 📊 ПОЛУЧЕНИЕ ОПТИМИЗИРОВАННЫХ ДАННЫХ ПУЛОВ
     */
    async getPoolDataOptimized(pools) {
        const now = Date.now();
        
        // 🚀 ИСПОЛЬЗУЕМ КЭШ ЕСЛИ АКТУАЛЕН
        if (now - this.lastCacheUpdate < this.CACHE_DURATION) {
            const cachedData = [];
            for (const pool of pools) {
                const cached = this.priceCache.get(pool.address);
                if (cached) {
                    cachedData.push(cached);
                }
            }
            if (cachedData.length === pools.length) {
                console.log('🚀 Используем кэшированные данные пулов');
                return cachedData;
            }
        }
        
        // 📊 ПОЛУЧАЕМ СВЕЖИЕ ДАННЫЕ
        console.log('📊 Получение свежих данных пулов...');
        const poolData = [];
        
        for (const pool of pools) {
            try {
                // Здесь должен быть реальный код получения данных пула
                // Для примера используем моковые данные
                const data = {
                    address: pool.address,
                    name: pool.name || `Pool ${pool.address.slice(0, 8)}`,
                    price: 163.75 + (Math.random() - 0.5) * 0.5, // Моковая цена SOL
                    liquidity: Math.random() * 1000000 + 500000, // Моковая ликвидность
                    fee: 0.25 + Math.random() * 0.5 // Моковая комиссия 0.25-0.75%
                };
                
                poolData.push(data);
                this.priceCache.set(pool.address, data);
                
            } catch (error) {
                console.error(`❌ Ошибка получения данных пула ${pool.address}:`, error.message);
            }
        }
        
        this.lastCacheUpdate = now;
        return poolData;
    }

    /**
     * 🔥 КЭШИРОВАНИЕ ВОЗМОЖНОСТЕЙ
     */
    cacheOpportunities(opportunities) {
        this.cachedOpportunities = opportunities;
        this.cachedOpportunitiesTime = Date.now();
    }

    /**
     * 🚀 ПРОВЕРКА АКТУАЛЬНОСТИ КЭША
     */
    shouldUseCachedResults() {
        return this.cachedOpportunities && 
               (Date.now() - this.cachedOpportunitiesTime) < this.CACHE_DURATION;
    }

    /**
     * 📊 ПОЛУЧЕНИЕ КЭШИРОВАННЫХ ВОЗМОЖНОСТЕЙ
     */
    getCachedOpportunities() {
        console.log('🚀 Используем кэшированные возможности');
        return this.cachedOpportunities || [];
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ АНАЛИЗАТОРА
     */
    getStats() {
        return {
            ...this.stats,
            cacheHitRate: this.priceCache.size > 0 ? 
                (this.stats.totalAnalyzed / this.priceCache.size) : 0,
            averageOpportunitiesPerAnalysis: this.stats.totalAnalyzed > 0 ? 
                (this.stats.profitableFound / this.stats.totalAnalyzed) : 0
        };
    }

    /**
     * 🧹 ОЧИСТКА КЭШЕЙ
     */
    clearCaches() {
        this.priceCache.clear();
        this.liquidityCache.clear();
        this.feeCache.clear();
        this.cachedOpportunities = null;
        console.log('🧹 Кэши очищены');
    }
}

module.exports = AtomicFlashSpreadAnalyzer;
