#!/usr/bin/env node

/**
 * 🔧 ПРОВЕРКА ДОЛГА В БЛОКЧЕЙНЕ (ТОЛЬКО SOL, USDC, USDT)
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Проверить реальное состояние в блокчейне после устранения phantom debt
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { Wallet } = require('@coral-xyz/anchor');
const fs = require('fs');

class BlockchainDebtChecker {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
    this.targetBanks = ['SOL', 'USDC', 'USDT'];
  }

  async initialize() {
    try {
      console.log('🔧 ПРОВЕРКА ДОЛГА В БЛОКЧЕЙНЕ');
      console.log('═══════════════════════════════════════════════════════════════');
      console.log('🎯 Проверяем только: SOL, USDC, USDT');

      // Подключение к RPC
      this.connection = new Connection(
        'https://solana-mainnet.g.alchemy.com/v2/alch-demo',
        'confirmed'
      );
      console.log('✅ RPC подключение создано');

      // Загрузка wallet
      const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const { Keypair } = require('@solana/web3.js');
      const keypair = Keypair.fromSecretKey(new Uint8Array(secretKey));
      this.wallet = new Wallet(keypair);
      console.log(`✅ Wallet загружен: ${this.wallet.publicKey.toString()}`);

      // Инициализация MarginFi
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client инициализирован');

      // Поиск аккаунтов
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority(this.wallet.publicKey);
      if (accounts.length === 0) {
        throw new Error('MarginFi аккаунты не найдены');
      }

      this.marginfiAccount = accounts[0];
      console.log(`✅ MarginFi аккаунт найден: ${this.marginfiAccount.address.toString()}`);

      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  async getTargetBanks() {
    try {
      console.log('\n🏦 ПОИСК ЦЕЛЕВЫХ БАНКОВ (SOL, USDC, USDT)');
      console.log('═══════════════════════════════════════════════════════════════');

      const banks = Array.from(this.marginfiClient.banks.values());
      const targetBanks = {};

      for (const bank of banks) {
        const symbol = bank.config.assetSymbol;
        
        if (this.targetBanks.includes(symbol)) {
          targetBanks[symbol] = bank;
          console.log(`✅ ${symbol}: ${bank.address.toString()}`);
        }
      }

      console.log(`📊 Найдено целевых банков: ${Object.keys(targetBanks).length}/3`);
      
      if (Object.keys(targetBanks).length === 0) {
        console.log('❌ Целевые банки не найдены');
        return null;
      }

      return targetBanks;
    } catch (error) {
      console.error(`❌ Ошибка поиска банков: ${error.message}`);
      return null;
    }
  }

  async checkBlockchainState() {
    try {
      console.log('\n📊 ПРОВЕРКА СОСТОЯНИЯ В БЛОКЧЕЙНЕ');
      console.log('═══════════════════════════════════════════════════════════════');

      // Принудительно перезагружаем аккаунт
      await this.marginfiAccount.reload();

      const balances = this.marginfiAccount.activeBalances;
      console.log(`💰 Активных балансов в аккаунте: ${balances.length}`);

      if (balances.length === 0) {
        console.log('✅ НЕТ АКТИВНЫХ БАЛАНСОВ - АККАУНТ ЧИСТЫЙ!');
        return { hasDebt: false, totalAssets: 0, totalLiabilities: 0 };
      }

      // Получаем целевые банки
      const targetBanks = await this.getTargetBanks();
      if (!targetBanks) {
        return { hasDebt: false, totalAssets: 0, totalLiabilities: 0 };
      }

      let totalAssets = 0;
      let totalLiabilities = 0;
      let hasTargetBalances = false;

      console.log('\n🔍 АНАЛИЗ БАЛАНСОВ ПО ЦЕЛЕВЫМ БАНКАМ:');

      for (const balance of balances) {
        try {
          const bank = this.marginfiClient.getBankByPk(balance.bankPk);
          
          if (bank && this.targetBanks.includes(bank.config.assetSymbol)) {
            hasTargetBalances = true;
            const symbol = bank.config.assetSymbol;
            const assetShares = balance.assetShares.toNumber();
            const liabilityShares = balance.liabilityShares.toNumber();
            
            console.log(`\n🏦 ${symbol}:`);
            console.log(`   💰 Asset Shares: ${assetShares}`);
            console.log(`   💸 Liability Shares: ${liabilityShares}`);
            
            if (assetShares > 0) {
              try {
                const assetValue = balance.computeUsdValue(bank, 'asset').toNumber();
                totalAssets += assetValue;
                console.log(`   💵 Asset Value: $${assetValue.toFixed(6)}`);
              } catch (assetError) {
                console.log(`   ⚠️ Ошибка расчета asset value: ${assetError.message}`);
              }
            }
            
            if (liabilityShares > 0) {
              try {
                const liabilityValue = balance.computeUsdValue(bank, 'liability').toNumber();
                totalLiabilities += liabilityValue;
                console.log(`   💸 Liability Value: $${liabilityValue.toFixed(6)}`);
              } catch (liabilityError) {
                console.log(`   ⚠️ Ошибка расчета liability value: ${liabilityError.message}`);
              }
            }
          }
        } catch (balanceError) {
          console.log(`   ❌ Ошибка обработки баланса: ${balanceError.message}`);
        }
      }

      if (!hasTargetBalances) {
        console.log('✅ НЕТ БАЛАНСОВ В ЦЕЛЕВЫХ БАНКАХ (SOL, USDC, USDT)');
      }

      return {
        hasDebt: totalLiabilities > 0.000001,
        totalAssets,
        totalLiabilities,
        hasTargetBalances
      };

    } catch (error) {
      console.error(`❌ Ошибка проверки блокчейна: ${error.message}`);
      return { hasDebt: false, totalAssets: 0, totalLiabilities: 0 };
    }
  }

  async testArbitrageReadiness() {
    try {
      console.log('\n🚀 ТЕСТ ГОТОВНОСТИ К АРБИТРАЖУ');
      console.log('═══════════════════════════════════════════════════════════════');

      const state = await this.checkBlockchainState();

      console.log(`\n📊 ИТОГОВОЕ СОСТОЯНИЕ:`);
      console.log(`   💰 Общие активы: $${state.totalAssets.toFixed(6)}`);
      console.log(`   💸 Общие долги: $${state.totalLiabilities.toFixed(6)}`);
      console.log(`   📈 Чистая стоимость: $${(state.totalAssets - state.totalLiabilities).toFixed(6)}`);

      if (!state.hasDebt) {
        console.log('\n🎉 ОТЛИЧНО! ГОТОВ К АРБИТРАЖУ!');
        console.log('✅ Долгов нет - можно выполнять Flash Loan операции');
        console.log('✅ Phantom debt устранен');
        console.log('🚀 Система готова к автоматическому арбитражу');
        return true;
      } else {
        console.log('\n⚠️ ЕСТЬ ДОЛГИ - АРБИТРАЖ ЗАБЛОКИРОВАН');
        console.log(`💸 Общий долг: $${state.totalLiabilities.toFixed(6)}`);
        
        if (state.totalAssets > 0) {
          const healthRatio = (state.totalAssets / state.totalLiabilities) * 100;
          console.log(`📊 Health Ratio: ${healthRatio.toFixed(2)}%`);
          
          if (healthRatio > 110) {
            console.log('💡 Health достаточный, но долг блокирует Flash Loans');
          } else {
            console.log('🚨 Health критический - нужно пополнение');
          }
        }
        
        console.log('🔧 Рекомендация: Погасить долг перед арбитражем');
        return false;
      }

    } catch (error) {
      console.error(`❌ Ошибка теста готовности: ${error.message}`);
      return false;
    }
  }
}

async function main() {
  console.log('🔧 ПРОВЕРКА ДОЛГА В БЛОКЧЕЙНЕ ПОСЛЕ УСТРАНЕНИЯ PHANTOM DEBT');
  console.log('🎯 Проверяем только целевые банки: SOL, USDC, USDT');
  console.log('💡 Цель: Убедиться что арбитраж готов к работе');
  
  const checker = new BlockchainDebtChecker();
  
  if (await checker.initialize()) {
    const isReady = await checker.testArbitrageReadiness();
    
    if (isReady) {
      console.log('\n🎯 СЛЕДУЮЩИЙ ШАГ: Запустить арбитражную систему');
      console.log('💻 Команда: node real-solana-rpc-websocket.js');
    } else {
      console.log('\n🔧 СЛЕДУЮЩИЙ ШАГ: Устранить оставшиеся долги');
      console.log('💡 Возможно, потребуется repay операция');
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = BlockchainDebtChecker;
