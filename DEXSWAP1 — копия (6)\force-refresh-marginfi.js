#!/usr/bin/env node

/**
 * 🔧 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ MARGINFI ДАННЫХ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Получить РЕАЛЬНЫЕ данные из блокчейна, минуя кэш SDK
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { Wallet } = require('@coral-xyz/anchor');
const fs = require('fs');

class ForceRefreshManager {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      console.log('🔧 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ MARGINFI ДАННЫХ');
      console.log('═══════════════════════════════════════════════════════════════');

      // Подключение к RPC с принудительным обновлением
      this.connection = new Connection(
        'https://solana-mainnet.g.alchemy.com/v2/alch-demo',
        {
          commitment: 'confirmed',
          confirmTransactionInitialTimeout: 60000,
          disableRetryOnRateLimit: false,
          httpHeaders: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }
      );
      console.log('✅ RPC подключение создано (без кэша)');

      // Загрузка wallet
      const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const { Keypair } = require('@solana/web3.js');
      const keypair = Keypair.fromSecretKey(new Uint8Array(secretKey));
      this.wallet = new Wallet(keypair);
      console.log(`✅ Wallet загружен: ${this.wallet.publicKey.toString()}`);

      // Инициализация MarginFi с принудительным обновлением
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client инициализирован');

      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  async getAccountDataDirectly() {
    try {
      console.log('\n🔍 ПРЯМОЕ ЧТЕНИЕ ДАННЫХ ИЗ БЛОКЧЕЙНА');
      console.log('═══════════════════════════════════════════════════════════════');

      // Ищем аккаунты напрямую через RPC
      const accounts = await this.connection.getProgramAccounts(
        new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'), // MarginFi Program ID
        {
          filters: [
            {
              memcmp: {
                offset: 8, // После discriminator
                bytes: this.wallet.publicKey.toBase58()
              }
            }
          ]
        }
      );

      console.log(`📊 Найдено MarginFi аккаунтов: ${accounts.length}`);

      for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(`\n📋 АККАУНТ ${i + 1}: ${account.pubkey.toString()}`);
        
        try {
          // Принудительно обновляем данные аккаунта
          const accountInfo = await this.connection.getAccountInfo(account.pubkey, 'confirmed');
          
          if (accountInfo) {
            console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
            console.log(`   👤 Владелец: ${accountInfo.owner.toString()}`);
            console.log(`   💰 Lamports: ${accountInfo.lamports}`);
            
            // Пытаемся загрузить через MarginFi SDK с принудительным обновлением
            try {
              const marginfiAccount = await this.marginfiClient.getMarginfiAccount(account.pubkey, true); // force refresh
              
              if (marginfiAccount) {
                console.log(`   ✅ MarginFi аккаунт загружен через SDK`);
                
                // Принудительно перезагружаем данные
                await marginfiAccount.reload();
                
                const balances = marginfiAccount.activeBalances;
                console.log(`   💰 Активных балансов: ${balances.length}`);
                
                let totalAssets = 0;
                let totalLiabilities = 0;
                
                for (const balance of balances) {
                  try {
                    const bankPk = balance.bankPk;
                    const bank = this.marginfiClient.getBankByPk(bankPk);
                    
                    if (bank) {
                      const symbol = bank.config.assetSymbol;
                      const assetShares = balance.assetShares.toNumber();
                      const liabilityShares = balance.liabilityShares.toNumber();
                      
                      console.log(`\n   🏦 ${symbol}:`);
                      console.log(`      💰 Asset Shares: ${assetShares}`);
                      console.log(`      💸 Liability Shares: ${liabilityShares}`);
                      
                      if (assetShares > 0) {
                        const assetValue = balance.computeUsdValue(bank, 'asset').toNumber();
                        totalAssets += assetValue;
                        console.log(`      💵 Asset Value: $${assetValue.toFixed(6)}`);
                      }
                      
                      if (liabilityShares > 0) {
                        const liabilityValue = balance.computeUsdValue(bank, 'liability').toNumber();
                        totalLiabilities += liabilityValue;
                        console.log(`      💸 Liability Value: $${liabilityValue.toFixed(6)}`);
                      }
                    }
                  } catch (balanceError) {
                    console.log(`      ❌ Ошибка обработки баланса: ${balanceError.message}`);
                  }
                }
                
                console.log(`\n   📊 ИТОГО ПО АККАУНТУ:`);
                console.log(`      💰 Общие активы: $${totalAssets.toFixed(6)}`);
                console.log(`      💸 Общие долги: $${totalLiabilities.toFixed(6)}`);
                console.log(`      📈 Чистая стоимость: $${(totalAssets - totalLiabilities).toFixed(6)}`);
                
                if (totalLiabilities > 0.000001) { // Учитываем микро-долги
                  console.log(`      🚨 ОБНАРУЖЕН ДОЛГ: $${totalLiabilities.toFixed(6)}`);
                  
                  if (totalAssets > 0) {
                    const healthRatio = (totalAssets / totalLiabilities) * 100;
                    console.log(`      📊 Health Ratio: ${healthRatio.toFixed(2)}%`);
                  } else {
                    console.log(`      🚨 КРИТИЧЕСКИЙ HEALTH: 0% (нет активов)`);
                  }
                } else {
                  console.log(`      ✅ ДОЛГОВ НЕТ`);
                }
                
                this.marginfiAccount = marginfiAccount;
              }
            } catch (sdkError) {
              console.log(`   ❌ Ошибка загрузки через SDK: ${sdkError.message}`);
            }
          }
        } catch (accountError) {
          console.log(`   ❌ Ошибка чтения аккаунта: ${accountError.message}`);
        }
      }

      return accounts.length > 0;
    } catch (error) {
      console.error(`❌ Ошибка прямого чтения: ${error.message}`);
      return false;
    }
  }

  async checkRealTimeHealth() {
    try {
      console.log('\n🏥 ПРОВЕРКА HEALTH В РЕАЛЬНОМ ВРЕМЕНИ');
      console.log('═══════════════════════════════════════════════════════════════');

      if (!this.marginfiAccount) {
        console.log('❌ MarginFi аккаунт не загружен');
        return false;
      }

      // Принудительно обновляем данные
      await this.marginfiAccount.reload();

      // Получаем свежие данные о здоровье
      try {
        const healthData = this.marginfiAccount.getHealthComponents();
        const assets = healthData.assets.toNumber();
        const liabilities = healthData.liabilities.toNumber();
        
        console.log(`💰 Assets (сырые): ${assets}`);
        console.log(`💸 Liabilities (сырые): ${liabilities}`);
        
        const assetsUsd = assets / 1e6;
        const liabilitiesUsd = liabilities / 1e6;
        
        console.log(`💰 Assets (USD): $${assetsUsd.toFixed(6)}`);
        console.log(`💸 Liabilities (USD): $${liabilitiesUsd.toFixed(6)}`);
        
        if (liabilitiesUsd > 0.000001) {
          const healthRatio = (assetsUsd / liabilitiesUsd) * 100;
          console.log(`📊 Health Ratio: ${healthRatio.toFixed(2)}%`);
          
          if (healthRatio < 110) {
            console.log('🚨 КРИТИЧЕСКИЙ HEALTH! Транзакции будут отклонены!');
            return false;
          } else {
            console.log('✅ Health достаточный для транзакций');
            return true;
          }
        } else {
          console.log('✅ Долгов нет - Health в норме');
          return true;
        }
        
      } catch (healthError) {
        console.log(`❌ Ошибка получения health: ${healthError.message}`);
        console.log('💡 Возможно, используется старая версия SDK');
        return false;
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки health: ${error.message}`);
      return false;
    }
  }

  async performCompleteCheck() {
    try {
      // 1. Прямое чтение данных из блокчейна
      const hasAccounts = await this.getAccountDataDirectly();
      
      if (!hasAccounts) {
        console.log('\n❌ MarginFi аккаунты не найдены');
        return false;
      }

      // 2. Проверка health в реальном времени
      const isHealthy = await this.checkRealTimeHealth();

      // 3. Выводы и рекомендации
      console.log('\n💡 ВЫВОДЫ И РЕКОМЕНДАЦИИ:');
      console.log('═══════════════════════════════════════════════════════════════');
      
      if (isHealthy) {
        console.log('✅ Аккаунт готов к транзакциям');
        console.log('💡 Можно выполнять Flash Loan операции');
      } else {
        console.log('🚨 Аккаунт НЕ готов к транзакциям');
        console.log('💡 Требуется пополнение депозита или погашение долга');
        console.log('🔧 Рекомендуется выполнить repay операцию');
      }

      return isHealthy;

    } catch (error) {
      console.error(`❌ Ошибка комплексной проверки: ${error.message}`);
      return false;
    }
  }
}

async function main() {
  const manager = new ForceRefreshManager();
  
  if (await manager.initialize()) {
    await manager.performCompleteCheck();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ForceRefreshManager;
