{"total_vulnerabilities": 90, "verified_count": 90, "real_vulnerabilities": 45, "false_positives": 45, "verification_details": [{"vulnerability_id": "42aa609e293aad8472bbb152907cd6af", "target_name": "Velodrome", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6872246696035225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "dd57d096e9c91332ad4c4560ae2172cb", "target_name": "K<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.54017857142857, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.540 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "e67607a3670e1ba02b9dbeec58a16812", "target_name": "Lyra", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.612612612612613, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.613 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "9b74ff06363610a48167c1a57cafd2e7", "target_name": "Polygon", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.822784810126583, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.823 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "84ea03894dcb7c2f1dddb5d8aab560d1", "target_name": "El<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6651583710407225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.665 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "1702ff80c40b8404ba280a15f84acfaf", "target_name": "Biswap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.716894977168951, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.717 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "73075581e4aa5913a4b12128074e847b", "target_name": "Alpaca Finance", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.629955947136561, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.630 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "ae1a1b61eb2aff90bbf963e51d41d7ff", "target_name": "Venus", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.4862385321100895, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.486 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "e6b0dadf9706d4ff524fa4216606501f", "target_name": "GMX", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.816143497757849, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.816 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "aaaba393ccc2a88347c3b3744768421a", "target_name": "Trader <PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.597402597402597, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.597 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "c7782d1ad2ceb42bca8dff49f6da5816", "target_name": "PancakeSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.793248945147679, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.793 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "bda58cb6f01d9bf4cb5580e9c93c4561", "target_name": "SushiSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.687242798353906, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "4fb6cece61b01365473ed42f3d8b258e", "target_name": "Chainlink", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.654320987654319, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.654 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "35dc4361df5944f4f1e70671a193ae67", "target_name": "Velodrome", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6872246696035225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "529593efa538469491d0a490bc9d9703", "target_name": "K<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.54017857142857, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.540 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "e65781f3ced171816f3e15a8d1888570", "target_name": "Lyra", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.612612612612613, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.613 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "22a9c43255a6d2e86f28f1e859cb4f67", "target_name": "Polygon", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.822784810126583, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.823 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "6ed4a4731b882a51abfe232b9e45c4b2", "target_name": "El<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6651583710407225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.665 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "882816f590682528c253bc003d139896", "target_name": "Biswap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.716894977168951, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.717 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "5295f899c577c304ce3564c5787a2434", "target_name": "Alpaca Finance", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.629955947136561, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.630 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d3adde0f8c0073a9ac7cb916715eb547", "target_name": "Venus", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.4862385321100895, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.486 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "c8f8f36b56c97c71c04deeceabda165b", "target_name": "GMX", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.816143497757849, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.816 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "49843082fca24bce0849715249b006b8", "target_name": "Trader <PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.597402597402597, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.597 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "3dca2ee661ce06ec64e9f4145d4d1717", "target_name": "PancakeSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.793248945147679, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.793 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "734000eb49e578becb1ea8b2a2dc8f4e", "target_name": "SushiSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.687242798353906, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "37dde28c3dc168502fe1a50eb4f40f97", "target_name": "Chainlink", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.654320987654319, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.654 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "e2e13af233a78699e8cf0487cb2dc179", "target_name": "Velodrome", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6872246696035225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "a4627792cb985370753ac1a7665dd3a8", "target_name": "K<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.54017857142857, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.540 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "a65febf1bc077e9adee08741e95bf8c2", "target_name": "Lyra", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.612612612612613, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.613 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "37bb3520084fb2ca901ca234fe965c00", "target_name": "Polygon", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.822784810126583, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.823 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "b93018bdb5abfcd45953f0cb32ee942f", "target_name": "El<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6651583710407225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.665 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "bdadbccd4ef219cf0ad9ee9976c58ce7", "target_name": "Biswap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.716894977168951, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.717 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d318eef17812c3471526a69bcbfa3329", "target_name": "Alpaca Finance", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.629955947136561, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.630 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "2bba41dddf6312cd9cbd3a02c2cd29c0", "target_name": "Venus", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.4862385321100895, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.486 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d1da05e8b22dd0972df9e6838125406e", "target_name": "GMX", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.816143497757849, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.816 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "********************************", "target_name": "Trader <PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.597402597402597, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.597 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "c4fb701b2333059061be60477b512cb5", "target_name": "PancakeSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.793248945147679, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.793 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "f815ebb7ed83a2167c7c9ae81d7edc70", "target_name": "SushiSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.687242798353906, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "777d3f134d38c2ead65773e2d50c7c02", "target_name": "Chainlink", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.654320987654319, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.654 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "1214c421cd1d71149a086ae66f1708ca", "target_name": "El<PERSON><PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.6651583710407225, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.665 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "79c8bab7e22bb799862d41ce590532a5", "target_name": "Trader <PERSON>", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.597402597402597, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.597 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "faad5955017b013351f3e4e619bce088", "target_name": "GMX", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.816143497757849, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.816 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d90b8c7eb5f691a030f006a25bb0564b", "target_name": "PancakeSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.793248945147679, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.793 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "1e4e75fb32194fb1162dbeaa0421006a", "target_name": "SushiSwap", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.687242798353906, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.687 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d6f775781b6b7945748597294ea3243d", "target_name": "Chainlink", "strategy_name": "shannon_entropy_analysis", "verified": true, "real_vulnerability": true, "verification_method": "entropy_analysis", "details": {"entropy_value": 4.654320987654319, "threshold": 0.7, "analysis_type": "mathematical_entropy"}, "confidence_assessment": "Энтропия 4.654 превышает критическому уровню", "recommendation": "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"}, {"vulnerability_id": "d85b4aa74b3a802cdb829af3a5e00569", "target_name": "Velodrome", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "1f064633c4609cf3d0bd52ad6b43a895", "target_name": "K<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "1761cce9e520af70a441445f3c618495", "target_name": "Lyra", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "1b00dd6aa4918a7ff3df552a10922dd7", "target_name": "Polygon", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "aa57abe5fbca24788f599fc13e8d1ef0", "target_name": "El<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "b90a5a4357251a12a6344b11fd874e78", "target_name": "Biswap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "6d54ac996f23505c0a28535a2dea18cd", "target_name": "Alpaca Finance", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "f94506c22db041fcdce9b472344adc01", "target_name": "Venus", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "6455320e248717c19e562aaae9a84dca", "target_name": "GMX", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "bac262302e9653ad5ab088816b358865", "target_name": "Trader <PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "5e67ecc55f37a5f2d633392d6c96cf78", "target_name": "PancakeSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "3a3c091cd0e25abe9f32b31d7c9a0c48", "target_name": "SushiSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "17c0fe037e0b4f99ed56706eb401da35", "target_name": "Chainlink", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "829c9d24171e3ad84015e934fa3757ea", "target_name": "Velodrome", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "d3d1549b9e1f2501c3ba427235c40b50", "target_name": "K<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "2ea351d05f4b684aa4a87da784a4b301", "target_name": "Lyra", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "ba3d3298e8ca8ef7f394478839b4d63a", "target_name": "Polygon", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "dc04c3036e676882cb2bf16a78d47829", "target_name": "El<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "b0cd47c486a47fd6890b5f45826b58d0", "target_name": "Biswap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "918a17b4bd2561f6d3799bdb51458274", "target_name": "Alpaca Finance", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "91ba0cc012148c153808a086f23efa23", "target_name": "Venus", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "8847739222fd248e1fdcfc92ef6095e7", "target_name": "GMX", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "bbae872769ac2d52ee3c4019b4b7ba3b", "target_name": "Trader <PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "014e6a0c72d7c82ab1715a097c955c2f", "target_name": "PancakeSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "580288a083a6369810cfaba4847d39d9", "target_name": "SushiSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "6f836ddc7957bf9e0e18a2b9151345b3", "target_name": "Chainlink", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "f265ebfc16e8c77080231fe408b768a2", "target_name": "Velodrome", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "740681a231e553476a634df988d234bf", "target_name": "K<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "a3f9523d1d424ef4524b7ea53cc3e6cf", "target_name": "Lyra", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "7a06a0be684b1f53f4ff004d00e791ac", "target_name": "Polygon", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "b180609617f3f121693e31f897dacf86", "target_name": "El<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "9bb40b41b8144b0f58f181952d451e38", "target_name": "Biswap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "82447649e01b267883b59a4c51610cc3", "target_name": "Alpaca Finance", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "e11d595df556d377f94044620db98ca5", "target_name": "Venus", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "a5fc50b3adb57f0562e5621c77a98a97", "target_name": "GMX", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "65f0c8451c7e8c909f067ac7a965c115", "target_name": "Trader <PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "899edb5d3b840958ce451ce661775b81", "target_name": "PancakeSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "bf39de7fff931818b822896a3c9d5276", "target_name": "SushiSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "55deb1c9607d9c9836960f96062133ac", "target_name": "Chainlink", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "9942b41ab2c1f668812a5891c16bb1e0", "target_name": "El<PERSON><PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "3b8b99ffa28fc060fd38149f2bb58788", "target_name": "Trader <PERSON>", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "5050d2ed38a3fd94a3b95be0ae02b960", "target_name": "GMX", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "015049e05654cf5aa716e9d076b29b2e", "target_name": "PancakeSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "780310b94e98cb8003061af133a2101d", "target_name": "SushiSwap", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}, {"vulnerability_id": "a4f09baba69e67b9be947dd73f53fc1c", "target_name": "Chainlink", "strategy_name": "quantum_superposition_fuzzing", "verified": true, "real_vulnerability": false, "verification_method": "quantum_analysis", "details": {"quantum_states": 8, "superposition_analysis": true, "analysis_type": "quantum_experimental"}, "confidence_assessment": "Обнаружено 8 квантовых состояний", "recommendation": "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"}], "summary_by_strategy": {"shannon_entropy_analysis": {"total": 45, "verified": 45, "real": 45, "false_positive": 0}, "quantum_superposition_fuzzing": {"total": 45, "verified": 45, "real": 0, "false_positive": 45}}, "summary_by_target": {"Velodrome": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Kwenta": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Lyra": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Polygon": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Ellipsis": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}, "Biswap": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Alpaca Finance": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "Venus": {"total": 6, "verified": 6, "real": 3, "false_positive": 3}, "GMX": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}, "Trader Joe": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}, "PancakeSwap": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}, "SushiSwap": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}, "Chainlink": {"total": 8, "verified": 8, "real": 4, "false_positive": 4}}, "metrics": {"accuracy_rate": 0.5, "false_positive_rate": 0.5, "verification_rate": 1.0}}