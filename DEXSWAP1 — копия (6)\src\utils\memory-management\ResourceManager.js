class ResourceManager {
  constructor() {
    this.resources = new Set();
    this.cleanupCallbacks = new Set();
    this.isCleaningUp = false;
  }
  
  register(resource, cleanupFn) {
    this.resources.add(resource);
    if (cleanupFn && typeof cleanupFn === 'function') {
      this.cleanupCallbacks.add(cleanupFn);
    }
  }
  
  unregister(resource) {
    this.resources.delete(resource);
  }
  
  addCleanupCallback(cleanupFn) {
    if (typeof cleanupFn === 'function') {
      this.cleanupCallbacks.add(cleanupFn);
    }
  }
  
  async cleanup() {
    if (this.isCleaningUp) {
      console.log('🔄 ResourceManager: cleanup уже выполняется...');
      return;
    }
    
    this.isCleaningUp = true;
    console.log(`🧹 ResourceManager: начинаем cleanup ${this.cleanupCallbacks.size} callbacks и ${this.resources.size} ресурсов`);
    
    for (const cleanupFn of this.cleanupCallbacks) {
      try {
        await cleanupFn();
      } catch (error) {
        console.error('Cleanup callback error:', error);
      }
    }
    
    for (const resource of this.resources) {
      if (resource && typeof resource.cleanup === 'function') {
        try {
          await resource.cleanup();
        } catch (error) {
          console.error('Resource cleanup error:', error);
        }
      }
      
      if (resource && typeof resource.destroy === 'function') {
        try {
          await resource.destroy();
        } catch (error) {
          console.error('Resource destroy error:', error);
        }
      }
      
      if (resource && typeof resource.close === 'function') {
        try {
          await resource.close();
        } catch (error) {
          console.error('Resource close error:', error);
        }
      }
    }
    
    this.resources.clear();
    this.cleanupCallbacks.clear();
    this.isCleaningUp = false;
    
    console.log('✅ ResourceManager: cleanup завершен');
  }
  
  getStats() {
    return {
      resources: this.resources.size,
      cleanupCallbacks: this.cleanupCallbacks.size,
      isCleaningUp: this.isCleaningUp
    };
  }
}

module.exports = ResourceManager;